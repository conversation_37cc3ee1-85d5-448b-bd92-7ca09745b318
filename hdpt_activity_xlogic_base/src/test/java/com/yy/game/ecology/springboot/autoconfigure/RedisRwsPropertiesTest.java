//package com.yy.game.ecology.springboot.autoconfigure;
//
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.boot.test.context.TestConfiguration;
//import org.springframework.context.annotation.Bean;
//
///**
// * <AUTHOR>
// * @since 2021/8/24
// */
////@TestConfiguration
//public class RedisRwsPropertiesTest {
//
//  @Bean
//  @ConfigurationProperties(prefix = "yy.game.ecology.redis.group2")
//  public RedisReadWriteSplitProperties redisReadWriteSplitProperties2(){
//    return new RedisReadWriteSplitProperties();
//  }
//
//  @Bean
//  @ConfigurationProperties(prefix = "yy.game.ecology.redis.group1")
//  public RedisReadWriteSplitProperties redisReadWriteSplitProperties1(){
//    return new RedisReadWriteSplitProperties();
//  }
//}
