//package com.yy.game.ecology.springboot.autoconfigure;
//
//import com.yy.game.ecology.spring.StringRedisRwsTemplate;
//import com.yy.gameecology.hdzj.element.component.BaseTest;
//import org.apache.commons.lang3.RandomStringUtils;
//import org.junit.Assert;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
//import org.springframework.context.ApplicationContext;
//import org.springframework.test.context.TestPropertySource;
//
///**
// * <AUTHOR>
// * @since 2021/8/23
// */
//@ImportAutoConfiguration({RedisRwsAutoConfiguration.class})
//@TestPropertySource(locations = "classpath:redis-rws.properties")
//public class RedisRwsAutoConfigurationTest extends BaseTest {
//
//  @Autowired
//  ApplicationContext applicationContext;
//
//  @Autowired
//  @Qualifier("group2StringRedisRwsTemplate")
//  StringRedisRwsTemplate stringRedisRwsTemplate;
//
//  @Autowired
//  StringRedisRwsTemplate redis;
//
//  @Test
//  public void testGet() {
//    String key = RandomStringUtils.randomAlphabetic(10);
//    stringRedisRwsTemplate.opsForValue().set(key, "1");
//    String value = stringRedisRwsTemplate.boundValueOps(key, true).get();
//    Assert.assertEquals("1", value);
//    stringRedisRwsTemplate.delete(key);
//
//    redis.opsForValue().set(key, "1");
//    value = redis.opsForValue().get(key);
//    Assert.assertEquals("1", value);
//    redis.delete(key);
//  }
//
//}
