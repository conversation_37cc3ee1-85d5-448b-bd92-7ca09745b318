package com.yy.gameecology;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.yy.gameecology.common.utils.MyMapUtil;
import com.yy.protocol.pb.GameecologyActivity;
import lombok.Data;
import org.junit.Test;
import org.springframework.util.Base64Utils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalTime;
import java.util.Comparator;
import java.util.Map;

public class SimpleTest {


    @Test
    public void testSortByMapKey() {
        Map<Integer, String> map = ImmutableMap.of(4, "4", 3, "3", 1, "1", 2, "2", 5, "5");


        Map<Integer, String> res = MyMapUtil.sortByKey(map, Comparator.comparingInt(o -> o));

        System.out.println(res);
    }

    @Test
    public void testSortByValuePriorKey() {
        Map<String, Long> map = ImmutableMap.of("member1|2", 10000L, "member1|1", 10000L, "member2|2", 20000L, "member3|2", 15000L, "member3|1", 5000L);
        System.out.println(MyMapUtil.sortByValuePriorKey(map, String::compareTo, Long::compareTo));
    }

    @Test
    public void test() throws Exception {
        RestTemplate restTemplate = new RestTemplate();
        String encoded = restTemplate.getForObject("http://localhost:7036/hdzt_rank/queryLayerInfoHex?actId=2022101001&sid=28706714&ssid=2741609394", String.class, ImmutableMap.of());
        System.out.println(encoded);
        byte[] bytes = Base64Utils.decodeFromString(encoded);
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.parseFrom(bytes);
        System.out.println(msg.getUri());
        System.out.println(JSON.toJSONString(msg.getLayerBroadcast()));
    }

    @Test
    public void test0() throws Exception {
        String data = "CKGNBoICngMIATCo0/7Z0jE4ieDZxAdCtgISBjE1MjAwNBpBaHR0cHM6Ly9wZWl3YW4uYnMyY2RuLnl5LmNvbS82NWYxZDU4NWQ3NGQ0OWJjODM2MGVkZWMxYTk3ZGQwMi5KUEciGDVyaUY1ckt6NWJDUDZMZXZNK1dQdHc9PSoP5oqA6IO95Y2h5oi/6Ze0MOzt5AU4AECcmtoHSABQAFgAYARoAHAAeK5iggEAigEAkAEAmAELsgEBMLgBAMIBOQoJ6aOO5bCa5qacEAsYgIL8zdIxINiK/9/SMSiAgvzN0jEw2KGS5dIxOgBCDOebm+WFuOmjjuWwmsgBANIBANgBAOABAOoBBW1peGVk+gEJ6aOO5bCa5qacgAIAigIAkAILmAIBoAIAqAIAsAIEuAJkwgICe33KAgDSAgnpo47lsJrmppzaAgJ7feACm/kESNiK/9/SMVDYoZLl0jFY5sGU/MoxYIC95eLQMWiAveXi0DFyMXsiZnJvbSI6IndlYiIsInNzaWQiOjE0NTQwNTQyMjQsInNpZCI6MTQ1NDA1NDIyNH0=";
        byte[] bytes = Base64Utils.decodeFromString(data);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.parseFrom(bytes);
        System.out.println(msg.getUri());
        System.out.println(msg.getLayerBroadcast().getExtMemberItem(0).getViewStatus());
    }

    @Test
    public void test1() {
        String json = "{\"startTime\": \"00:12:13\"}";

        Foo foo = JSON.parseObject(json, Foo.class);
        System.out.println(foo);
    }

    @Test
    public void test2() {
        Map<Long, String> map = Map.of(1L, "LV1", 1000000L, "LV2", 3000000L, "LV3", 5000000L, "LV4");

        System.out.println(MyMapUtil.floor(map, 1L));

        System.out.println(MyMapUtil.floor(map, 999999L));

        System.out.println(MyMapUtil.floor(map, 1000000L));

        System.out.println(MyMapUtil.floor(map, 1000001L));

        System.out.println(MyMapUtil.floor(map, 3000000L));

        System.out.println(MyMapUtil.floor(map, 5000000L));

        System.out.println(MyMapUtil.floor(map, 7000000L));
    }

    @Data
    public static class Foo {
        protected LocalTime startTime;
    }
}
