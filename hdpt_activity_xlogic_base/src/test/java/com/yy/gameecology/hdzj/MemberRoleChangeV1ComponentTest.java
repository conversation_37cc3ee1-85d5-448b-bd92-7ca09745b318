package com.yy.gameecology.hdzj;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.hdzj.element.history.MemberRoleChangeV1Component;
import com.yy.gameecology.hdzj.element.history.attr.MemberRoleChangeV1ComponentAttr;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties","classpath:env/local/group-setting-3.properties"})
public class MemberRoleChangeV1ComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private MemberRoleChangeV1Component memberRoleChangeV1Component;

    @Test
    public void doNotify() throws Exception {

        MemberRoleChangeV1ComponentAttr attr = memberRoleChangeV1Component.getComponentAttr(2021103002L, 1);

        PromotTimeEnd event = new PromotTimeEnd();
        event.setActId(2021103002L);
        event.setRankId(1);
        event.setPhaseId(12);
        event.setTimeKey(0L);
        event.setEndTime("2021-05-24 23:59:59");

        event.setTimestamp("2021-05-24 23:59:59");
        event.setIndex(1);
        while (true){
            memberRoleChangeV1Component.adjustEliminatedMemberRole(event,attr);
        }




    }




}
