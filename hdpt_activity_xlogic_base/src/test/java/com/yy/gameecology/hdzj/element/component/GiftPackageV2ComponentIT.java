package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.CacheService;
import com.yy.gameecology.common.db.model.gameecology.HdzjComponent;
import com.yy.gameecology.hdzj.element.history.GiftPackageV2Component;
import com.yy.gameecology.hdzj.element.history.attr.GiftPackageV2ComponentAttr;
import com.yy.thrift.hdztranking.BusiId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * integration-test for {@link GiftPackageV2Component}
 *
 * <AUTHOR>
 * @since 2021/7/7
 */
@Transactional
@Slf4j
public class GiftPackageV2ComponentIT extends BaseTest {

    @Autowired
    private GiftPackageV2Component component;

    @Autowired
    private ComponentTestHelper helper;
    @Autowired
    private ActRedisGroupDao redis;

    @Autowired
    private CacheService cacheService;

//    @MockBean
//    protected HdztRankingThriftClient hdztRankingThriftClient;

    @Test
    public void awardsTest(){
        long uid = 50048144L;
        Response<String> response = component.buyGiftPackage(activityId, 500L, uid, "127.0.0.1");
        assertNotNull(response);
        assertEquals(Response.OK, response.getResult());
    }

    @Test
    public void giftPackageStatusTest() {
        HdzjComponent cp = initSuccess(activityId, component.getComponentId());
        long uid = 50048144L;
        Response<JSONObject> response = component.giftPackageStatus(cp.getActId(), cp.getCmptUseInx(), uid);
        assertNotNull(response);
        assertEquals(Response.OK, response.getResult());
        int canBuy = response.getData().getIntValue("canBuy");
        assertEquals(1, canBuy);
    }


    @Test
    public void buyGiftPackageTest() {
        HdzjComponent cp = initSuccess(activityId, component.getComponentId());

        long uid = 50048144L;
        Response<String> response = component.buyGiftPackage(cp.getActId(), cp.getCmptUseInx(), uid, "127.0.0.1");
        log.info("resp:{}", response);
        cleanRedisData(cp);

        assertNotNull(response);
        assertEquals(Response.OK, response.getResult());
    }

    @Test
    public void whenLimitBuyCountIsZeroShouldReturnFail() {
        HdzjComponent cp = init(activityId, component.getComponentId());

        Map<String, String> map = Maps.newHashMap();
        map.put("limitQualification", "false");
        map.put("limitBuyCount", "1");
        map.put("limitBuyCountTip", "购买超限");
        map.put("busiId", String.valueOf(BusiId.YUE_ZHAN.getValue()));
        map.put("productType", String.valueOf(117));
        map.put("awardKey", "2021QixiGift-yz");
        helper.addComponentAttr(cp, map);
        cacheService.forceReloadActivityAndCmp();
        cleanRedisData(cp);
        long uid = 50048144L;

        GiftPackageV2ComponentAttr attr = component.getComponentAttr(cp.getActId(), cp.getCmptUseInx());

        redis.zAdd(RedisConfigManager.OLD_ACT_GROUP_CODE, component.getRedisKey(attr, "buyRecord"), String.valueOf(uid), 10);

        Response<String> response = component.buyGiftPackage(cp.getActId(), cp.getCmptUseInx(), uid, "127.0.0.1");
        log.info("resp:{}", response);
        cleanRedisData(cp);

        assertNotNull(response);
        assertEquals(2, response.getResult());
        assertEquals(map.get("limitBuyCountTip"), response.getReason());

    }

    private HdzjComponent init(long activityId, long cpId) {
        return helper.init(activityId, cpId, RandomUtils.nextInt(1, 10));
    }


    private HdzjComponent initSuccess(long activityId, long cpId) {
        HdzjComponent cp = helper.init(activityId, cpId, RandomUtils.nextInt(100, 200));
        Map<String, String> map = Maps.newHashMap();
        map.put("limitQualification", "false");
        map.put("limitBuyCount", "1");
        map.put("busiId", String.valueOf(BusiId.YUE_ZHAN.getValue()));
        map.put("productType", String.valueOf(117));
        map.put("awardKey", "2021QixiGift-yz");
        helper.addComponentAttr(cp, map);
        cacheService.forceReloadActivityAndCmp();
        cleanRedisData(cp);
        return cp;
    }

    private void cleanRedisData(HdzjComponent cp) {
        //解决getComponentAttr  NPE
//        ActivityInfoVo activityInfoVo = new ActivityInfoVo();
//        Mockito.doReturn(activityInfoVo).when(hdztRankingThriftClient).queryActivityInfo(cp.getActId());
        GiftPackageV2ComponentAttr attr = component.getComponentAttr(cp.getActId(), cp.getCmptUseInx());
        helper.cleanCpRedis(attr);
    }


}
