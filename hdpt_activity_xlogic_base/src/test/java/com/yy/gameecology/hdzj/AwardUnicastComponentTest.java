package com.yy.gameecology.hdzj;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardLotteryMsg;
import com.yy.gameecology.hdzj.element.component.AwardUnicastComponent;
import com.yy.gameecology.hdzj.element.component.attr.AwardUnicastComponentAttr;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-3.properties"})
public class AwardUnicastComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AwardUnicastComponent awardUnicastComponent;

    @Test
    public void doNotify() throws Exception {

        AwardUnicastComponentAttr attr = awardUnicastComponent.getComponentAttr(2021053001L, 1);

        HdztAwardLotteryMsg event =  new HdztAwardLotteryMsg();
        event.setActId(2021053001L);
        event.setBusiId(400L);
        event.setSeq("123456");
        event.setUid(3);
        event.setTaskId(30108);
        HdztAwardLotteryMsg.Award award1 = new HdztAwardLotteryMsg.Award();
        award1.setPackageId(47L);
        award1.setGiftName("test1");
        HdztAwardLotteryMsg.Award award2 = new HdztAwardLotteryMsg.Award();
        award2.setPackageId(48L);
        award2.setGiftName("test2");
        HdztAwardLotteryMsg.Award award3 = new HdztAwardLotteryMsg.Award();
        award3.setPackageId(49L);
        award3.setGiftName("test3");
        event.setData(Lists.newArrayList(award1, award2,award3));

        while (true) {
            try {
                awardUnicastComponent.unicastAward(event, attr);
            } catch (Exception e){
                log.error(" {}",e.getMessage(),e);
            }

        }


    }


}
