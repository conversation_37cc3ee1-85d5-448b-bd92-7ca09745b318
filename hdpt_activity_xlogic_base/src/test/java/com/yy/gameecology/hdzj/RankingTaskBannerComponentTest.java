package com.yy.gameecology.hdzj;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.hdzj.element.component.RankingTaskBannerComponent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-06-01 10:43
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-3.properties"
        , "classpath:env/local/application-inner.properties"})
public class RankingTaskBannerComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "3");
    }

    @Autowired
    private RankingTaskBannerComponent rankingTaskBannerComponent;

    @Test
    public void testBro() {
        //rankingTaskBannerComponent.testBroAppBanner(2023053001, 400, 1, 42, "50042952");
        //yy://pd-[sid=1353441873&subid=1353441873]
//        rankingTaskBannerComponent.testBroAppBanner(2023053001, 401, 2, 99921,"1353441873", "1353441873_1353441873", "40050");
    }

    @Test
    public void bro() {
        String test = "{\"actId\":2023053001,\"actors\":{40016:\"2710314999\",40050:\"1459037660_1459037660\",80003:\"2711427642\",40003:\"2711427642\",40004:\"1459037660\"},\"busiId\":400,\"currRound\":1,\"currTaskIndex\":3,\"ekey\":\"\",\"index\":0,\"itemCurrNumMap\":{\"MRRW\":3000270},\"itemLevelPassMap\":{\"MRRW\":[1000000,1000000,1000000,1000000]},\"member\":\"2710314999\",\"occurTime\":\"2023-05-25 00:00:00\",\"phaseId\":21,\"phaseScore\":3000270,\"rankId\":99942,\"rankScore\":3000270,\"roundComplete\":0,\"seq\":\"1db32e53-96f5-450a-ba0e-9854a9a622a2\",\"startTaskIndex\":2,\"timeKey\":1,\"timestamp\":\"2023-05-25 00:00:00\",\"uri\":2001}";
        TaskProgressChanged taskProgressChanged = JSON.parseObject(test,TaskProgressChanged.class);

        rankingTaskBannerComponent.onTaskChangeEvent(taskProgressChanged,rankingTaskBannerComponent.getComponentAttr(2023053001L,403));
    }
}
