package com.yy.gameecology.hdzj;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.history.attr.JyUserChannelLabelComponentAttr;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties","classpath:env/local/group-setting-3.properties"})
public class HdzjComponentCollaboratorTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdzjComponentCollaborator hdzjComponentCollaborator;

    @Test
    public void doNotify() throws Exception {
        long a = 1L;
        hdzjComponentCollaborator.callComponent(2021053001L, ComponentId.JY_USER_CHANNEL_COUNT_LABEL,"getUserLabel",new JyUserChannelLabelComponentAttr(),2L);
        hdzjComponentCollaborator.callComponent(2021053001L, ComponentId.JY_USER_CHANNEL_COUNT_LABEL,"getUserLabel",new ComponentAttr(),2L);
    }
}
