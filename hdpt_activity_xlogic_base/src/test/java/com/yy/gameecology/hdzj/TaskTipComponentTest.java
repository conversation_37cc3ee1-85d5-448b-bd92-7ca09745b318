package com.yy.gameecology.hdzj;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.service.ActPlayService;
import com.yy.gameecology.hdzj.element.component.AnchorTaskTipComponent;
import com.yy.gameecology.hdzj.element.component.attr.AnchorTaskTipComponentAttr;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-3.properties"})
public class TaskTipComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AnchorTaskTipComponent taskTipComponent;
    @Autowired
    private ActPlayService actPlayService;


    @Test
    public void doNotify() throws Exception {

        AnchorTaskTipComponentAttr attr = taskTipComponent.getComponentAttr(2021053001L, 1);



        TaskProgressChanged event = new TaskProgressChanged();
        event = JSON.parseObject("{\"actId\":2021053001,\"actors\":{60001:\"50046298\"},\"busiId\":600,\"currRound\":1,\"currTaskIndex\":3,\"ekey\":\"\",\"index\":0,\"itemCurrNumMap\":{\"YJXZ_001\":1315},\"member\":\"50046298\",\"occurTime\":\"2021-05-17 00:00:06\",\"phaseId\":10,\"rankId\":13,\"roundComplete\":0,\"seq\":\"c3092f64-d526-4045-bc87-b387ab1cdc5f\",\"startTaskIndex\":0,\"timeKey\":0,\"timestamp\":\"2021-05-17 00:00:06\",\"uri\":2001}",TaskProgressChanged.class);



        while (true) {
            try {
                taskTipComponent.anchorAccomplishTask(event, attr);
            } catch (Exception e){
                log.error(" {}",e.getMessage(),e);
            }

        }


    }

    @Test
    public void doNotify2() throws Exception {

        AnchorTaskTipComponentAttr attr = taskTipComponent.getComponentAttr(2021053001L, 2);



        TaskProgressChanged event = new TaskProgressChanged();
        event = JSON.parseObject("{\"actId\":2021053001,\"actors\":{60001:\"50046298\"},\"busiId\":400,\"currRound\":1,\"currTaskIndex\":3,\"ekey\":\"\",\"index\":0,\"itemCurrNumMap\":{\"YJXZ_001\":1315},\"member\":\"50046298\",\"occurTime\":\"2021-05-17 00:00:06\",\"phaseId\":10,\"rankId\":14,\"roundComplete\":0,\"seq\":\"c3092f64-d526-4045-bc87-b387ab1cdc5f\",\"startTaskIndex\":0,\"timeKey\":0,\"timestamp\":\"2021-05-17 00:00:06\",\"uri\":2001}",TaskProgressChanged.class);



        while (true) {
            try {
                taskTipComponent.anchorAccomplishTask(event, attr);
            } catch (Exception e){
                log.error(" {}",e.getMessage(),e);
            }

        }


    }


    @Test
    public void doNotify3() throws Exception {

        AnchorTaskTipComponentAttr attr = taskTipComponent.getComponentAttr(2021053001L, 3);



        TaskProgressChanged event = new TaskProgressChanged();
        event = JSON.parseObject("{\"actId\":2021053001,\"actors\":{60001:\"50046298\"},\"busiId\":600,\"currRound\":1,\"currTaskIndex\":3,\"ekey\":\"\",\"index\":0,\"itemCurrNumMap\":{\"YJXZ_001\":1315},\"member\":\"50046298\",\"occurTime\":\"2021-05-17 00:00:06\",\"phaseId\":10,\"rankId\":12,\"roundComplete\":0,\"seq\":\"c3092f64-d526-4045-bc87-b387ab1cdc5f\",\"startTaskIndex\":0,\"timeKey\":0,\"timestamp\":\"2021-05-17 00:00:06\",\"uri\":2001}",TaskProgressChanged.class);



        while (true) {
            try {
                taskTipComponent.anchorAccomplishTask(event, attr);
            } catch (Exception e){
                log.error(" {}",e.getMessage(),e);
            }

        }


    }


}
