package com.yy.gameecology.hdzj.element.component;

import com.google.common.collect.ImmutableMap;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.common.consts.GeParamName;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-05-07 17:33
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-3.properties", "classpath:env/local/application-inner.properties"})
public class RuliuTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private static final String TEAM_GROUP_END_NOTIFY = """
            满足   ${teamMinMember} 人参赛队数量：${teamAmount}
            不满足 ${teamMinMember} 人参赛队数量：${notEnoughTeamAmount}
            最小开赛队伍数量配置：${startGameMinTeam}
            赛事是否成功开启：${startGame}
            """;

    @Autowired
    private CommonService commonService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    static {
        System.setProperty("group", "3");
    }

    @Test
    public void test(){
                String content = commonService.renderTemplate(TEAM_GROUP_END_NOTIFY,
                ImmutableMap.of("teamMinMember", 1,
                        "teamAmount", 1,
                        "notEnoughTeamAmount", 1,
                        "startGameMinTeam", 1,
                        "startGame", 1 >= 2?"是":"否"));

        String msg = commonService.buildActRuliuMsg(2025, false, "和平精英巅峰赛分组完成", content);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_ACT_MOBILE_USER_GROWTH, msg, Collections.emptyList());
    }
}
