package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.Main;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2022/7/18 14:11
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {"group=1"})
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties"})
public class ComponentDocGeneratorTest extends BaseTest {
//    static {
//        System.setProperty("group", "1");
//    }
//
//    @Autowired
//    private ComponentDocGenerator componentDocGenerator;
//
//    @Test
//    public void docGeneratorTest() {
//        componentDocGenerator.init();
//        componentDocGenerator.initDoc();
//        ComponentDocBean componentDocBean = componentDocGenerator.buildComponentDoc("Rank2RankComponent");
//        componentDocGenerator.attachAttrVo(componentDocBean);
//        System.out.println(JSON.toJSONString(componentDocBean));
//
////        List<ComponentDocBean> allComponentDoc = componentDocGenerator.buildAllComponent();
////        for (ComponentDocBean componentDocBean : allComponentDoc) {
////            System.out.println(componentDocBean.getComponentId() + ":" + JSON.toJSONString(componentDocBean).length());
////        }
//    }
//
//    @Test
//    public void synAllComponentDocToDBTest() {
//        componentDocGenerator.init();
//        componentDocGenerator.initDoc();
//
//        List<Class<?>> components = new ArrayList<>();
//        components.add(KnockoutAnchorTransferComponent.class);
//        components.add(MatchResultOnlineBroComponent.class);
//        components.add(PKPlunderComponent.class);
//        components.add(PhaseTopnSlipAwardComponent.class);
//        components.add(PureWhiteListPromoteComponent.class);
//        components.add(SeedAnchorV2Component.class);
//        components.add(SubChannelTaskComponent.class);
//        components.add(SunshineTaskComponentV3.class);
//        components.add(SupperHonorBannerComponent.class);
//        components.add(TopNDoNotPKComponent.class);
//        components.add(WhiteListGroupComponent.class);
//        components.add(Act2022094001Component.class);
//        components.add(Act2022104001Component.class);
//        components.add(Act2022115001Component.class);
//
//        componentDocGenerator.synAllComponentDocToDB(components);
//    }
//
//    @Test
//    public void getComponentDocBeanTest() {
//        long componentId = 5006L;
//        ComponentDocBean doc = componentDocGenerator.getComponentDocBean(componentId);
//        System.out.println(JSON.toJSONString(doc));
//    }
}
