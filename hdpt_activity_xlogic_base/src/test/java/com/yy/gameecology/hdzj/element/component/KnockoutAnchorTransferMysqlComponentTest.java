package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.hdzj.element.component.attr.KnockoutAnchorTransferComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.KnockoutAnchorTransferMysqlDao;
import com.yy.gameecology.hdzj.element.component.entity.KnockoutAnchorInfo;
import com.yy.gameecology.hdzj.element.component.entity.KnockoutPhasePromotInfo;
import com.yy.gameecology.hdzj.element.component.entity.KnockoutRankEventRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * KnockoutAnchorTransferMysqlComponent 测试类
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@ExtendWith(MockitoExtension.class)
class KnockoutAnchorTransferMysqlComponentTest {

    @Mock
    private KnockoutAnchorTransferMysqlDao knockoutDao;

    @InjectMocks
    private KnockoutAnchorTransferMysqlComponent component;

    private KnockoutAnchorTransferComponentAttr attr;
    private long actId = 12345L;
    private long cmptUseInx = 1L;
    private long phaseId = 100L;
    private long hdzkRankId = 200L;

    @BeforeEach
    void setUp() {
        attr = new KnockoutAnchorTransferComponentAttr();
        attr.setActId(actId);
        attr.setCmptUseInx(cmptUseInx);
        attr.setHdzkRankId(hdzkRankId);
        attr.setStartSavePhaseId(50L);
        
        // 设置监听的榜单ID
        List<Long> scoreRankIds = Arrays.asList(1001L, 1002L);
        attr.setScoreRankId(scoreRankIds);
        
        // 设置阶段包含的榜单ID
        Map<Long, List<Long>> phaseContainRankId = new HashMap<>();
        phaseContainRankId.put(phaseId, scoreRankIds);
        attr.setPhaseContainRankId(phaseContainRankId);
        
        // 设置角色转换映射
        Map<Long, Long> roleTransferMap = new HashMap<>();
        roleTransferMap.put(200L, 400L); // 主播角色 -> 分赛场角色
        attr.setRoleTransferMap(roleTransferMap);
    }

    @Test
    void testOnRankingScoreChangeEvent_FirstPhase() {
        // 测试第一阶段只记录主播，不记录事件
        attr.setStartSavePhaseId(150L); // 大于当前阶段
        
        RankingScoreChanged event = createRankingScoreChangedEvent();
        
        when(knockoutDao.addAnchorIfNotExists(actId, cmptUseInx, "anchor1", phaseId))
                .thenReturn(true);
        
        component.onRankingScoreChangeEvent(event, attr);
        
        verify(knockoutDao).addAnchorIfNotExists(actId, cmptUseInx, "anchor1", phaseId);
        verify(knockoutDao, never()).addRankEventRecord(any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    void testOnRankingScoreChangeEvent_RecordEvent() {
        // 测试记录事件
        RankingScoreChanged event = createRankingScoreChangedEvent();
        
        when(knockoutDao.addAnchorIfNotExists(actId, cmptUseInx, "anchor1", phaseId))
                .thenReturn(true);
        when(knockoutDao.getPhasePromotInfo(actId, cmptUseInx, phaseId, hdzkRankId))
                .thenReturn(null); // 阶段未完成晋级
        
        component.onRankingScoreChangeEvent(event, attr);
        
        verify(knockoutDao).addAnchorIfNotExists(actId, cmptUseInx, "anchor1", phaseId);
        verify(knockoutDao).addRankEventRecord(eq(actId), eq(cmptUseInx), eq(phaseId), eq(hdzkRankId), 
                eq("anchor1"), anyString(), eq("seq123"));
    }

    @Test
    void testOnRankingScoreChangeEvent_PromotCompleted() {
        // 测试晋级已完成的情况
        RankingScoreChanged event = createRankingScoreChangedEvent();
        
        KnockoutPhasePromotInfo promotInfo = new KnockoutPhasePromotInfo();
        promotInfo.setPromotStatus(KnockoutPhasePromotInfo.PROMOT_STATUS_COMPLETED);
        promotInfo.setPromotAnchors("anchor2,anchor3"); // 不包含当前主播
        
        when(knockoutDao.addAnchorIfNotExists(actId, cmptUseInx, "anchor1", phaseId))
                .thenReturn(true);
        when(knockoutDao.getPhasePromotInfo(actId, cmptUseInx, phaseId, hdzkRankId))
                .thenReturn(promotInfo);
        
        component.onRankingScoreChangeEvent(event, attr);
        
        verify(knockoutDao).addAnchorIfNotExists(actId, cmptUseInx, "anchor1", phaseId);
        // 应该处理被淘汰主播的数据，但由于方法是private，这里只验证不会记录事件
        verify(knockoutDao, never()).addRankEventRecord(any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    void testIncrementRankCountAndCheck() {
        // 测试榜单计数增加和检查
        when(knockoutDao.incrementRankCountAndCheck(actId, cmptUseInx, phaseId, hdzkRankId, 2))
                .thenReturn(false) // 第一次调用返回false
                .thenReturn(true); // 第二次调用返回true
        
        boolean result1 = knockoutDao.incrementRankCountAndCheck(actId, cmptUseInx, phaseId, hdzkRankId, 2);
        boolean result2 = knockoutDao.incrementRankCountAndCheck(actId, cmptUseInx, phaseId, hdzkRankId, 2);
        
        assertFalse(result1);
        assertTrue(result2);
    }

    @Test
    void testGetAllActiveAnchors() {
        // 测试获取所有活跃主播
        List<KnockoutAnchorInfo> anchors = Arrays.asList(
                createAnchorInfo("anchor1"),
                createAnchorInfo("anchor2"),
                createAnchorInfo("anchor3")
        );
        
        when(knockoutDao.getAllActiveAnchors(actId, cmptUseInx))
                .thenReturn(anchors);
        
        List<KnockoutAnchorInfo> result = knockoutDao.getAllActiveAnchors(actId, cmptUseInx);
        
        assertEquals(3, result.size());
        assertEquals("anchor1", result.get(0).getMemberId());
    }

    @Test
    void testHasRoleChanged() {
        // 测试角色变更检查
        when(knockoutDao.hasRoleChanged(actId, cmptUseInx, "anchor1"))
                .thenReturn(false);
        when(knockoutDao.hasRoleChanged(actId, cmptUseInx, "anchor2"))
                .thenReturn(true);
        
        assertFalse(knockoutDao.hasRoleChanged(actId, cmptUseInx, "anchor1"));
        assertTrue(knockoutDao.hasRoleChanged(actId, cmptUseInx, "anchor2"));
    }

    @Test
    void testGetUnprocessedEvents() {
        // 测试获取未处理事件
        List<KnockoutRankEventRecord> events = Arrays.asList(
                createEventRecord("anchor1", "event1"),
                createEventRecord("anchor2", "event2")
        );
        
        when(knockoutDao.getUnprocessedEvents(actId, cmptUseInx, phaseId, hdzkRankId))
                .thenReturn(events);
        
        List<KnockoutRankEventRecord> result = knockoutDao.getUnprocessedEvents(actId, cmptUseInx, phaseId, hdzkRankId);
        
        assertEquals(2, result.size());
        assertEquals("anchor1", result.get(0).getMemberId());
    }

    @Test
    void testComponentId() {
        // 测试组件ID
        assertEquals(Long.valueOf(5159L), component.getComponentId());
    }

    // 辅助方法
    private RankingScoreChanged createRankingScoreChangedEvent() {
        RankingScoreChanged event = new RankingScoreChanged();
        event.setRankId(1001L);
        event.setPhaseId(phaseId);
        event.setMember("anchor1");
        event.setSeq("seq123");
        event.setItemScore(100L);
        event.setItemCount(1L);
        
        Map<Long, String> actors = new HashMap<>();
        actors.put(200L, "anchor1");
        event.setActors(actors);
        
        return event;
    }

    private KnockoutAnchorInfo createAnchorInfo(String memberId) {
        KnockoutAnchorInfo info = new KnockoutAnchorInfo();
        info.setId(1L);
        info.setActId(actId);
        info.setCmptUseInx(cmptUseInx);
        info.setMemberId(memberId);
        info.setPhaseId(phaseId);
        info.setStatus(KnockoutAnchorInfo.STATUS_ACTIVE);
        info.setCreateTime(new Date());
        info.setUpdateTime(new Date());
        return info;
    }

    private KnockoutRankEventRecord createEventRecord(String memberId, String eventData) {
        KnockoutRankEventRecord record = new KnockoutRankEventRecord();
        record.setId(1L);
        record.setActId(actId);
        record.setCmptUseInx(cmptUseInx);
        record.setPhaseId(phaseId);
        record.setHdzkRankId(hdzkRankId);
        record.setMemberId(memberId);
        record.setEventData(eventData);
        record.setProcessed(KnockoutRankEventRecord.PROCESSED_NO);
        record.setSeq("seq123");
        record.setCreateTime(new Date());
        return record;
    }

    private PromotTimeEnd createPromotTimeEndEvent() {
        PromotTimeEnd event = new PromotTimeEnd();
        event.setPhaseId(phaseId);
        event.setRankId(1001L);
        event.setSeq("promot_seq123");
        event.setTimeKey("20250711");
        return event;
    }
}
