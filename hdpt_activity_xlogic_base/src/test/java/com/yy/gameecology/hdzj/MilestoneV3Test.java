package com.yy.gameecology.hdzj;

import com.google.common.collect.ImmutableMap;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2021.11.10 16:52
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties","classpath:env/local/group-setting-4.properties"})
public class MilestoneV3Test {

    @Autowired
    private HdzjEventDispatcher hdzjEventDispatcher;

    @Test
    public void taskProgressChanged() throws InterruptedException {
        TaskProgressChanged changed = new TaskProgressChanged();
        changed.setActId(2021114001);
        changed.setItemCurrNumMap(ImmutableMap.of("PW_ORDER",2000000L));
        changed.setMember("999");
        changed.setRankId(20);
        changed.setPhaseId(50);
        changed.setStartTaskIndex(0);
        changed.setCurrTaskIndex(1);
        changed.setTimeKey(1);
        changed.setOccurTime("2021-11-30 22:00:00");

        hdzjEventDispatcher.notify(changed);
        Thread.sleep(500 * 33000);

    }

}
