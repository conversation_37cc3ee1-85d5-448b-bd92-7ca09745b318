package com.yy.gameecology.service;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.service.AggregationService2;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-06-23 21:23
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties", "classpath:env/local/application-inner.properties"})
public class AggregationService2Test {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    static {
        System.setProperty("group", "1");
    }

    @Autowired
    private AggregationService2 aggregationService2;

    @Test
    public void test() {
        long result;
        result = aggregationService2.getNextInvokeSeconds(2025051001L,"layerTest",1000);
        log.info("aggregationInvoke reuslt---->{}", result);
        result = aggregationService2.getNextInvokeSeconds(2025051001L,"layerTest",1000);
        log.info("aggregationInvoke reuslt---->{}", result);
        result = aggregationService2.getNextInvokeSeconds(2025051001L,"layerTest",1000);
        log.info("aggregationInvoke reuslt---->{}", result);
        result = aggregationService2.getNextInvokeSeconds(2025051001L,"layerTest",1000);
        log.info("aggregationInvoke reuslt---->{}", result);
        result = aggregationService2.getNextInvokeSeconds(2025051001L,"layerTest",1000);
        log.info("aggregationInvoke reuslt---->{}", result);
        result = aggregationService2.getNextInvokeSeconds(2025051001L,"layerTest",1000);
        log.info("aggregationInvoke reuslt---->{}", result);
    }

}
