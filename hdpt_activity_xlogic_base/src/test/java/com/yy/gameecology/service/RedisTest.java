package com.yy.gameecology.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.thrift.hdztranking.Rank;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-12-03 11:07
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-2.properties",
        "classpath:env/local/application-inner.properties"})
public class RedisTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    static {
        System.setProperty("group", "2");
    }

    @Autowired
    private ActRedisGroupDao actRedisGroupDao;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Test
    public void test() {
        Clock clock = new Clock();
        List<Object> hashKey = Lists.newArrayList();
        for (int i = 0; i < 30; i++) {
            hashKey.add(Convert.toString(i));
            actRedisGroupDao.hset("1", "hash_big_key_test", Convert.toString(i), "[{\"compereUid\":65416781,\"effectAnchorId\":[65416781,63306241,2849833658,503136,3023310958,66498073],\"guestList\":[63306241,2849833658,503136,3023310958,66498073],\"layerBabyType\":3,\"playMode\":-1,\"sid\":36158250,\"ssid\":2828409267,\"templateType\":810},{\"compereUid\":11337943,\"effectAnchorId\":[11337943,962538931,1721498438],\"guestList\":[962538931,1721498438],\"layerBabyType\":2,\"playMode\":-1,\"sid\":14041540,\"ssid\":2827126148,\"templateType\":810},{\"compereUid\":61149038,\"effectAnchorId\":[61149038,2982523396,762454820,1729463384,1729463498],\"guestList\":[2982523396,762454820,1729463384,1729463498],\"layerBabyType\":3,\"playMode\":-1,\"sid\":3099150,\"ssid\":2829931913,\"templateType\":810},{\"compereList\":[14317742],\"compereUid\":14317742,\"effectAnchorId\":[14317742],\"guestList\":[],\"layerBabyType\":1,\"playMode\":0,\"sid\":37327480,\"ssid\":2791571373,\"templateType\":1},{\"compereUid\":3005571247,\"effectAnchorId\":[3005571247,3005389394,3019305924,3024214389,2982222165,1815303861,2570853030,3005410025,3009742587],\"guestList\":[3005389394,3019305924,3024214389,2982222165,1815303861,2570853030,3005410025,3009742587],\"layerBabyType\":3,\"playMode\":-1,\"sid\":1463656560,\"ssid\":2829591964,\"templateType\":810},{\"compereUid\":1743533698,\"effectAnchorId\":[1743533698,1743535368,1743535366,1743535367,1743533703],\"guestList\":[1743535368,1743535366,1743535367,1743533703],\"layerBabyType\":3,\"playMode\":-1,\"sid\":1461730670,\"ssid\":2818724400,\"templateType\":810},{\"compereUid\":2453428448,\"effectAnchorId\":[2453428448,63010465,63898958,62801014,63428237],\"guestList\":[63010465,63898958,62801014,63428237],\"layerBabyType\":3,\"playMode\":-1,\"sid\":1461730670,\"ssid\":2822711307,\"templateType\":810},{\"compereUid\":1743533711,\"effectAnchorId\":[1743533711,1743533708,1743535373,1743535374,1743533710,1743535369],\"guestList\":[1743533708,1743535373,1743535374,1743533710,1743535369],\"layerBabyType\":3,\"playMode\":-1,\"sid\":1461730670,\"ssid\":2821067920,\"templateType\":810},{\"compereUid\":258960532,\"effectAnchorId\":[258960532,2677615370,2988631354,2997761541,2702085701],\"guestList\":[2677615370,2988631354,2997761541,2702085701],\"layerBabyType\":3,\"playMode\":-1,\"sid\":1461730670,\"ssid\":2819546999,\"templateType\":810},{\"compereUid\":2997441594,\"effectAnchorId\":[2997441594,2456053184,1727677518,3029690094,61437749,2970914633],\"guestList\":[2456053184,1727677518,3029690094,61437749,2970914633],\"layerBabyType\":3,\"playMode\":-1,\"sid\":1461730670,\"ssid\":2818346214,\"templateType\":810},{\"compereList\":[3046677],\"compereUid\":3046677,\"effectAnchorId\":[3046677],\"guestList\":[],\"layerBabyType\":1,\"playMode\":0,\"sid\":1457725110,\"ssid\":1457725110,\"templateType\":1},{\"compereUid\":80256647,\"effectAnchorId\":[80256647],\"guestList\":[],\"layerBabyType\":1,\"playMode\":-1,\"sid\":92755930,\"ssid\":2762260091,\"templateType\":810},{\"compereUid\":2826758554,\"effectAnchorId\":[2826758554,507198441,151848862,2472769769,3975712,3023662868,16602736,144110809],\"guestList\":[507198441,151848862,2472769769,3975712,3023662868,16602736,144110809],\"layerBabyType\":3,\"playMode\":-1,\"sid\":1455419100,\"ssid\":2822491691,\"templateType\":810},{\"compereList\":[59794697],\"compereUid\":59794697,\"effectAnchorId\":[59794697],\"guestList\":[],\"layerBabyType\":1,\"playMode\":0,\"sid\":22289970,\"ssid\":2791829531,\"templateType\":1},{\"compereUid\":73518641,\"effectAnchorId\":[73518641,66001180,6177430,1725374157,3273788],\"guestList\":[66001180,6177430,1725374157,3273788],\"layerBabyType\":3,\"playMode\":-1,\"sid\":178540,\"ssid\":2760115952,\"templateType\":810},{\"compereList\":[3016012695],\"compereUid\":3016012695,\"effectAnchorId\":[3016012695],\"guestList\":[],\"layerBabyType\":1,\"playMode\":0,\"sid\":1460853600,\"ssid\":1460853600,\"templateType\":1},{\"compereUid\":14783439,\"effectAnchorId\":[14783439,8634182,2664220778,12769364,3027886663,1749387883,811797277],\"guestList\":[8634182,2664220778,12769364,3027886663,1749387883,811797277],\"layerBabyType\":3,\"playMode\":-1,\"sid\":55463480,\"ssid\":2827848681,\"templateType\":810},{\"compereUid\":5946587,\"effectAnchorId\":[5946587,3025833575,3013039210,2218531315],\"guestList\":[3025833575,3013039210,2218531315],\"layerBabyType\":3,\"playMode\":-1,\"sid\":55463480,\"ssid\":2825015092,\"templateType\":810},{\"compereUid\":1824088756,\"effectAnchorId\":[1824088756,4571894,205681367,10715851],\"guestList\":[4571894,205681367,10715851],\"layerBabyType\":3,\"playMode\":-1,\"sid\":55463480,\"ssid\":2779446866,\"templateType\":810},{\"compereUid\":77124040,\"effectAnchorId\":[77124040,1774200,2196489021],\"guestList\":[1774200,2196489021],\"layerBabyType\":2,\"playMode\":-1,\"sid\":69595230,\"ssid\":2811734175,\"templateType\":810},{\"compereUid\":60051369,\"effectAnchorId\":[60051369,3029693285,3028133454,3027910636,3019644304,66245134,1657689952],\"guestList\":[3029693285,3028133454,3027910636,3019644304,66245134,1657689952],\"layerBabyType\":3,\"playMode\":-1,\"sid\":1354816070,\"ssid\":2828616572,\"templateType\":810},{\"compereUid\":3007641353,\"effectAnchorId\":[3007641353,6681429,60532510,3026172650,3022516816,214814916,113626751,3022587732,59713361],\"guestList\":[6681429,60532510,3026172650,3022516816,214814916,113626751,3022587732,59713361],\"layerBabyType\":3,\"playMode\":-1,\"sid\":1354816070,\"ssid\":2822345413,\"templateType\":810},{\"compereUid\":7155275,\"effectAnchorId\":[7155275,65980266,479032667,15760457,565778356,1210604982],\"guestList\":[65980266,479032667,15760457,565778356,1210604982],\"layerBabyType\":3,\"playMode\":-1,\"sid\":17926790,\"ssid\":2827383513,\"templateType\":810},{\"compereList\":[794148488],\"compereUid\":794148488,\"effectAnchorId\":[794148488],\"guestList\":[],\"layerBabyType\":1,\"playMode\":0,\"sid\":59292200,\"ssid\":2797668340,\"templateType\":1},{\"compereList\":[129235542],\"compereUid\":129235542,\"effectAnchorId\":[129235542],\"guestList\":[],\"layerBabyType\":1,\"playMode\":0,\"sid\":1458173520,\"ssid\":2822119635,\"templateType\":1},{\"compereList\":[2538573024],\"compereUid\":2538573024,\"effectAnchorId\":[2538573024],\"guestList\":[],\"layerBabyType\":1,\"playMode\":0,\"sid\":1353966960,\"ssid\":1353966960,\"templateType\":1},{\"compereList\":[69226660],\"compereUid\":69226660,\"effectAnchorId\":[69226660],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2808713003,\"templateType\":1},{\"compereList\":[2455023220],\"compereUid\":2455023220,\"effectAnchorId\":[2455023220],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2821410152,\"templateType\":1},{\"compereList\":[2975049038],\"compereUid\":2975049038,\"effectAnchorId\":[2975049038],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2814376025,\"templateType\":1},{\"compereList\":[4489822],\"compereUid\":4489822,\"effectAnchorId\":[4489822],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2808924837,\"templateType\":1},{\"compereList\":[1872042816],\"compereUid\":1872042816,\"effectAnchorId\":[1872042816],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2810254538,\"templateType\":1},{\"compereList\":[1721560881],\"compereUid\":1721560881,\"effectAnchorId\":[1721560881],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2808712965,\"templateType\":1},{\"compereList\":[65000629],\"compereUid\":65000629,\"effectAnchorId\":[65000629],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2814598572,\"templateType\":1},{\"compereList\":[1346999821],\"compereUid\":1346999821,\"effectAnchorId\":[1346999821],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2808712922,\"templateType\":1},{\"compereList\":[2889215693],\"compereUid\":2889215693,\"effectAnchorId\":[2889215693],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2808840787,\"templateType\":1},{\"compereList\":[2526054097],\"compereUid\":2526054097,\"effectAnchorId\":[2526054097],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2809284968,\"templateType\":1},{\"compereList\":[65626617],\"compereUid\":65626617,\"effectAnchorId\":[65626617],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2814376036,\"templateType\":1},{\"compereList\":[2975049070],\"compereUid\":2975049070,\"effectAnchorId\":[2975049070],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2808713042,\"templateType\":1},{\"compereList\":[2975182010],\"compereUid\":2975182010,\"effectAnchorId\":[2975182010],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2820697691,\"templateType\":1},{\"compereList\":[62167888],\"compereUid\":62167888,\"effectAnchorId\":[62167888],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2808712951,\"templateType\":1},{\"compereList\":[2836310277],\"compereUid\":2836310277,\"effectAnchorId\":[2836310277],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2810367901,\"templateType\":1},{\"compereList\":[2382322848],\"compereUid\":2382322848,\"effectAnchorId\":[2382322848],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":5180550,\"ssid\":2808920467,\"templateType\":1},{\"compereList\":[2577620566],\"compereUid\":2577620566,\"effectAnchorId\":[2577620566],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":27752790,\"ssid\":2828787563,\"templateType\":1},{\"compereList\":[797182029],\"compereUid\":797182029,\"effectAnchorId\":[797182029],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":27752790,\"ssid\":2828787562,\"templateType\":1},{\"compereList\":[2388232300],\"compereUid\":2388232300,\"effectAnchorId\":[2388232300],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":27752790,\"ssid\":2829205728,\"templateType\":1},{\"compereList\":[65131230],\"compereUid\":65131230,\"effectAnchorId\":[65131230],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":27752790,\"ssid\":2828874933,\"templateType\":1},{\"compereList\":[65361829],\"compereUid\":65361829,\"effectAnchorId\":[65361829],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":27752790,\"ssid\":2828835492,\"templateType\":1},{\"compereList\":[64813268],\"compereUid\":64813268,\"effectAnchorId\":[64813268],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":27752790,\"ssid\":2828787569,\"templateType\":1},{\"compereList\":[1193059699],\"compereUid\":1193059699,\"effectAnchorId\":[1193059699],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":27752790,\"ssid\":2828787584,\"templateType\":1},{\"compereList\":[2198068829],\"compereUid\":2198068829,\"effectAnchorId\":[2198068829],\"guestList\":[],\"layerBabyType\":1,\"playMode\":7,\"sid\":27752790,\"ssid\":2828787557,\"templateType\":1}]");
        }
        clock.tag();
        actRedisGroupDao.hmGet("1", "hash_big_key_test", hashKey);
        clock.tag();
        System.out.println("redis test result:" + clock);
    }

    @Test
    public void makeRankTestData() {
        for (int i = 0; i < 10000; i++) {
            actRedisGroupDao.zAdd("4", "hdzt_ranking:2024112001:8:_:2024121117:_|1", Convert.toString(i), i);
        }
    }

    @Test
    public void queryRankTest() {
        Clock clock = new Clock();
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(2024112001L, 8L, 1L, "2024121117", 30000, Maps.newHashMap());
        String outPut = "redis test result:" + clock + ",ranks:" + ranks.size();
        System.out.println(outPut);
    }

}
