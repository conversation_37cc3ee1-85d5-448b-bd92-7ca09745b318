package com.yy.gameecology.service;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.service.HdztRankService;
import com.yy.thrift.hdztranking.BatchRankingItem;
import com.yy.thrift.hdztranking.QueryRankingRequest;
import com.yy.thrift.hdztranking.Rank;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties", "classpath:env/local/application-inner.properties"})
public class HdztRankingServiceTest {

    static {
        System.setProperty("group", "1");
    }

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Test
    public void test() {
        long startMills = System.currentTimeMillis();
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(2023051001L, 21L, 100L, "", 10, Collections.emptyMap());
        if (CollectionUtils.isEmpty(ranks)) {
            return;
        }
        Map<String, QueryRankingRequest> batchQueryRequestMap = new HashMap<>();
        for (Rank rank : ranks) {
            QueryRankingRequest request = new QueryRankingRequest();
            request.setActId(2023051001);
            request.setRankingId(2001);
            request.setPhaseId(100);
            request.setDateStr("");
            request.setRankingCount(3);
            request.setFindSrcMember(rank.getMember());
            batchQueryRequestMap.put(rank.getMember(), request);
        }
        Map<String, String> ext = new HashMap<>();

        Map<String, BatchRankingItem> batchRanks = hdztRankingThriftClient.queryBatchRanking(batchQueryRequestMap, ext);
        System.out.println("time use total:" + (System.currentTimeMillis() - startMills));
        System.out.println(batchRanks);
    }
}
