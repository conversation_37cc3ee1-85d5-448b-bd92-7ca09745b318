<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true" scan="true">
	<property name="PROGRESS_GROUP" value="${group:-}"/>
	<property name="PROGRESS_NAME" value="activity${PROGRESS_GROUP}.gameecology.yy.com"/>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="com.yy.gameecology.common.utils.ConsoleFilter"/>
		<encoder>
			<charset>UTF-8</charset>
			<pattern>
				<![CDATA[
					%date %level [%X{origin} %X{trace_id}] [%thread] %logger{0}:%line - %msg%n
				]]>
			</pattern>
		</encoder>
	</appender>

	<appender name="monitor"
		class="com.yy.ent.clients.bam.logback.AsyncLogCollectAppender">
		<srvname>java.gameecology.activity${PROGRESS_GROUP}</srvname>
		<filter class="com.yy.ent.clients.bam.logback.EntryConditionFilter"/>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		</filter>
	</appender>

	<root level="INFO">
		<appender-ref ref="monitor" />
		<appender-ref ref="STDOUT" />
	</root>

</configuration>
