<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.1.xsd
        http://www.springframework.org/schema/task
		http://www.springframework.org/schema/task/spring-task-3.1.xsd">

	<!-- Spring Task @Scheduled programming model, 要配置大一定，否则定时器可能运行不及时  -->
	<task:executor id="executor" pool-size="50" />
	<task:scheduler id="scheduler" pool-size="50" />
	<task:annotation-driven executor="executor" scheduler="scheduler" />

	<!-- add app shutdown hook -->
	<bean id="appShutdownHolder" class="com.yy.gameecology.common.utils.ShutdownHolder"/>

	<!-- spring bean 感知工厂 -->
	<bean id="springBeanAwareFactory" class="com.yy.gameecology.common.support.SpringBeanAwareFactory" />

	<context:component-scan base-package="com.yy.gameecology,com.yy.apphistory" />

	<!-- 鹰眼上报服务 -->
	<bean id="eagleReport" class="com.yy.gameecology.common.report.EagleReport" />

	<import resource="applicationContext-cache.xml"/>

</beans>

