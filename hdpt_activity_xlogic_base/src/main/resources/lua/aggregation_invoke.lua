local preInvokeTime = tonumber(redis.call('get', KEYS[1]) or 0)
local now = tonumber(ARGV[1] or 0)
local inteval = tonumber(ARGV[2] or 0)
local result = -1
-- 不用更新
if (preInvokeTime > now) then
    result = -1
    -- 立刻更新
elseif (now - preInvokeTime >= inteval) then
    redis.call('set', KEYS[1], now)
    redis.call('expire', KEYS[1], inteval)
    result = 0
    -- 延后更新
else
    redis.call('set', KEYS[1], preInvokeTime + inteval)
    redis.call('expire', KEYS[1], inteval)
    result = preInvokeTime + inteval - now
end

return result

