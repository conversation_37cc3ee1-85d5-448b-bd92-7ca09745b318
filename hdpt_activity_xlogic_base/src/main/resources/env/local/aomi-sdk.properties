# \u88ab\u76d1\u63a7\u8fdb\u7a0b\u540d\uff0c\u5efa\u8bae\u4ee5\u201c\u4e1a\u52a1\u540d.\u8fdb\u7a0b\u540d\u201d\u7684\u65b9\u5f0f\u8fdb\u884c\u547d\u540d
aomi.sdk.processname=yzbb.core

#\u662f\u5426\u542f\u7528sdk\u529f\u80fd\uff0c\u9ed8\u8ba4\u662ftrue
aomi.sdk.init=true

#\u662f\u5426\u6253\u5370\u4e0a\u62a5\u6570\u636e\uff0c\u9ed8\u8ba4\u662ffalse
aomi.sdk.report.print=true

# \u8fdb\u7a0b\u5bf9\u5916\u670d\u52a1\u7aef\u53e3\uff0c\u8bf7\u4fdd\u8bc1\u4e0e\u4f60\u771f\u6b63\u8fdb\u7a0b\u5bf9\u5916\u670d\u52a1\u7684\u76d1\u542c\u7aef\u53e3\u4fdd\u6301\u4e00\u81f4
aomi.sdk.portlist=19700

#\u81ea\u52a8\u83b7\u53d6\uff0c\u4e00\u822c\u4e0d\u7528\u8bbe\u7f6e\uff0c\u591a\u4e2a\u7528,\u53f7\u9694\u5f00
#aomi.sdk.iplist=xx.xx.xx.xx

#\u670d\u52a1\u8fdb\u7a0b\u6240\u5728\u57df\uff0c\u4e00\u822c\u7528\u4e8e\u76f4\u63a5\u5bf9\u63a5\u5ba2\u6237\u7aef\u7684\u670d\u52a1\uff08\u5982\uff1amobSrv\uff09
#aomi.sdk.domain=appid.60273

#aop\u4ee3\u7406\u53c2\u6570\uff0c\u4e00\u822c\u65e0\u987b\u914d\u7f6e
#aomi.sdk.agent.args

#\u81ea\u5b9a\u4e49\u4e0a\u62a5http url - \u6d4b\u8bd5\u73af\u5883
aomi.sdk.reportUrl=http://test.clt.aomi.yy.com/collector/reportModelData.action
