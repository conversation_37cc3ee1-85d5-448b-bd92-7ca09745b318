#web server port, \u4E34\u65F6\u7528\u4E8E\u6D4B\u8BD5\uFF0C \u6D4B\u8BD5\u5B8C\u6BD5\u540E\u8981\u5173\u95ED\uFF01
web.server.port=7038

thrift_gameecologybridge_server_port=10222
thrift_gameecologybridge_server_timeout=3600
thrift_gameecologybridge_server_registry=

#java service sdk, \u5DF2\u63D0\u524D\u7533\u8BF7\u597D\uFF0C\u9700\u8981\u65F6\u53EF\u76F4\u63A5\u4F7F\u7528 - added by guoliping / 2019-10-23
svcsdk.appId=15448
svcsdk.s2sRegKey=01382de5bccd8f0ad475465512dcc14199c30f5d7840c715
svcsdk.bTestEnv=false

#eagle eye report setting
eagle_eye_appname=gameecology_activity3
eagle_eye_srvname=activity3.gameecology.yy.com

# redis config
gameecology.redis.database=3

##################################kafka start################################################################

kafka.jiaoyou.wx.group=group_friend_combo_end_event_act_3
kafka.jiaoyou.sz.group=group_friend_combo_end_event_act_3

########½»ÓÑËÍÀñÊÂ¼þ kafka############
kafka.jiaoyou.props.wx.group=friend_props_event_gameecology_activity_3
kafka.jiaoyou.props.sz.group=friend_props_event_gameecology_activity_3

########½»ÓÑ¸ÇÕÂÊÂ¼þ kafka############
kafka.jiaoyou.seal.wx.group=friend_seal_event_gameecology_activity_3
kafka.jiaoyou.seal.sz.group=friend_seal_event_gameecology_activity_3

########½»ÓÑÖ÷³Ö¿ª²¥ÊÂ¼þ kafka############
kafka.jiaoyou.push_live.wx.group=friend_push_live_event_gameecology_activity_3
kafka.jiaoyou.push_live.sz.group=friend_push_live_event_gameecology_activity_3

########½»ÓÑÂÒ¶·½áÊøÊÂ¼þ kafka############
kafka.jiaoyou.fight_end.wx.group=friend_fight_end_event_gameecology_activity_3
kafka.jiaoyou.fight_end.sz.group=friend_fight_end_event_gameecology_activity_3

kafka.jiaoyou.compere-online.wx.group=friend_compere_online_hdzk_activity_3
kafka.jiaoyou.compere-online.sz.group=friend_compere_online_hdzk_activity_3

###########################################################################################
#hdzt kafka
kafka.hdzt-wx.consumer.bootstrap-servers=kafkawx017-core001.yy.com:8104,kafkawx017-core002.yy.com:8104,kafkawx017-core003.yy.com:8104
kafka.hdzt-wx.consumer.auto-offset-reset=latest
kafka.hdzt-wx.producer.bootstrap-servers=kafkawx017-core001.yy.com:8104,kafkawx017-core002.yy.com:8104,kafkawx017-core003.yy.com:8104
kafka.hdzt-wx.producer.acks=all

kafka.hdzt-sz.consumer.bootstrap-servers=hdzt-sz-kafka-prod-3.dbms-kafka.self.int.yy.com:8163,hdzt-sz-kafka-prod-4.dbms-kafka.self.int.yy.com:8163,hdzt-sz-kafka-prod-5.dbms-kafka.self.int.yy.com:8163
kafka.hdzt-sz.consumer.auto-offset-reset=latest
kafka.hdzt-sz.producer.bootstrap-servers=hdzt-sz-kafka-prod-3.dbms-kafka.self.int.yy.com:8163,hdzt-sz-kafka-prod-4.dbms-kafka.self.int.yy.com:8163,hdzt-sz-kafka-prod-5.dbms-kafka.self.int.yy.com:8163
kafka.hdzt-sz.producer.acks=all
###################################kafka end###############################################################
#udb sa
thrift.service-agent-new.url=thrift://${MY_HOST_IP}:12500;thrift://${MY_HOST_IP}:12500;thrift://${MY_HOST_IP}:12500

# kafka svc call back
yy.kafka.svc.gateway.wx.consumer.bootstrap-servers=kafkawx013-core001.yy.com:8112,kafkawx013-core002.yy.com:8112,kafkawx013-core003.yy.com:8112
yy.kafka.svc.gateway.wx.consumer.auto-offset-reset=latest
yy.kafka.svc.gateway.wx.consumer.topic=svc_platform_gateway_callback_geact_topic_3
yy.kafka.svc.gateway.wx.consumer.group-id=group_svc_gateway_callback_gameecology_activity_3

# mysql datasource for db gameecology
mysql.gameecology.jdbcUrl=************************************************************************************************************************************************************
mysql.gameecology.username=udb_ma_rw@gamebaby
mysql.gameecology.driver-class-name=com.mysql.jdbc.Driver
mysql.gameecology.maxPoolSize=100

s2s.name=hdzk_activity3
s2s.key=6608f148261a7f61b5015f3c3d4099dcb47637ee3ccb77afe241f67d0acc596e

s2s.hdzk.server.name=hdzk_server3
s2s.hdzk.server.token=yzbb_is_good