
package com.yy.gameecology.activity.client.thrift;

import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.BaseBridgeThriftClient;
import com.yy.thrift.gameecology_bridge.GameecologyBridgeService.Iface;
import org.apache.dubbo.config.annotation.Method;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class BridgeGamebabyActivityThriftClient extends BaseBridgeThriftClient {

    private final static Logger log = LoggerFactory.getLogger(BridgeGamebabyActivityThriftClient.class);

    @Reference(protocol = "attach_nythrift", owner = "${thrift_bridge_gamebabyactivity_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"},
            methods = {
                    @Method(name = "write", retries = 0),
                    @Method(name = "writeBinary", retries = 0),
            })
    protected Iface proxy;

    public BridgeGamebabyActivityThriftClient() {
        super(log, Const.GE_SOURCE.GAMEBABY_ACTIVITY);
    }

    @Override
    public Iface getProxy() {
        return proxy;
    }
}
