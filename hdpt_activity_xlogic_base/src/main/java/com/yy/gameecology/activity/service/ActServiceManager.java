package com.yy.gameecology.activity.service;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * desc:活动管理类，支持多个活动并行
 *
 * @createBy 曾文帜
 * @create 2020-10-14 15:02
 **/
@Component
public class ActServiceManager {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private static Map<Long, ActService> actServiceMap = Maps.newConcurrentMap();

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    public static void register(List<Long> actIds, ActService actService) {
        for (Long actId : actIds) {
            if (actServiceMap.containsKey(actId)) {
                throw new RuntimeException("1个活动不能有多个处理类,actId:" + actId);
            }
            actServiceMap.put(actId, actService);
        }
    }

    public static void registerNew(ActAbstractService actService) {
        if(actService.isMyGroupDuty()) {
            Long actId = actService.getActivityId();
            if(actId != null) {
                if (actServiceMap.containsKey(actId)) {
                    throw new RuntimeException("1个活动不能有多个处理类,actId:" + actId);
                }
                actServiceMap.put(actId, actService);
            }
        }
    }

    public ActService getActService(Long actId) {
        return actServiceMap.get(actId);
    }

    /**
     * 送礼事件
     *
     * @param sendGiftEvent 三模板送礼事件
     */
    public void invokeAllEffectActSendGiftEvent(SendGiftEvent sendGiftEvent) {
        Map<String, Boolean> invoked = Maps.newConcurrentMap();
        for (Long actId : actServiceMap.keySet()) {
            ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
            if (activityInfoVo == null || !actId.equals(activityInfoVo.getActId())) {
                log.warn("act info not found,actId:{}", actId);
                continue;
            }
            if (activityInfoVo.getCurrentTime() < activityInfoVo.getBeginTime()
                    || activityInfoVo.getCurrentTime() > activityInfoVo.getEndTime()) {
                log.warn("not in act time,actId:{}", actId);
                continue;
            }

            ActService actService = actServiceMap.get(actId);
            if (actService != null) {
                //用于控制同1个实现类只触发一次
                String invokeKey = actService.getClass().getTypeName();
                if (invoked.containsKey(invokeKey)) {
                    continue;
                }

                invoked.put(invokeKey, true);

                //触发各个活动实现类送礼事件
                actService.onSendGiftEvent(sendGiftEvent);
            }
        }
    }
}
