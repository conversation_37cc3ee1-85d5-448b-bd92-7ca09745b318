package com.yy.gameecology.activity.client.thrift;

import com.yy.gameecology.common.utils.DateUtil;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.BusiId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class WeidengServiceClient{

    private static final Logger log = LoggerFactory.getLogger(WeidengServiceClient.class);

    private static final int RETRY = 3;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;


    /**
     * 通过中台点尾灯
     *
     * @param busiId
     * @param uid
     * @param taskId
     * @param packageId
     * @return
     */
    public boolean lightUpWeidengByHdzt(BusiId busiId, long uid, long taskId, long packageId) {

        String seq = "weiding-" + UUID.randomUUID().toString();
        int retry = RETRY;
        while (retry-- > 0) {
            try {
                BatchWelfareResult result = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), busiId.getValue(), uid, taskId, 1, packageId, seq);
                if (result == null || result.getCode() != 0) {
                    log.error("lightUpWeidengByHdzt error,uid:{},taskId:{},packageId:{},result:{}", uid, taskId, packageId, result);
                }
                if (result != null) {
                    log.info("lightUpWeidengByHdzt with busiId:{}, uid:{}, taskId:{}, packageId:{}, seq:{}", busiId, uid, taskId, packageId, seq);
                    return true;
                }
                Thread.sleep(50);
            } catch (Exception e) {
                log.warn("lightUpWeideng exception:", e);
            }
        }
        return false;
    }

    /**
     * 通过中台点尾灯-适用于 packageId 是连续的尾灯
     *
     * @param busiId
     * @param uid
     * @param taskId
     * @param startPackageId 尾灯开始packageId
     * @param wdLevel
     * @return
     */
    public boolean lightUpWeidengByHdzt(BusiId busiId, long uid, long taskId, long startPackageId, int wdLevel) {
        return lightUpWeidengByHdzt(busiId, uid, taskId, startPackageId + wdLevel - 1);
    }
}
