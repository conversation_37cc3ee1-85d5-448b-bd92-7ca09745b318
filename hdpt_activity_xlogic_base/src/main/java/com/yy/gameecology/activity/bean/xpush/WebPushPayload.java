package com.yy.gameecology.activity.bean.xpush;

import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName
 * @description TODO
 * @date 2019/8/14 14:05
 */
@Data
public class WebPushPayload {
    /**
     * 推送标题
     */
    private MulLangField title;
    /**
     * 推送内容
     */
    private MulLangField content;
    /**
     * 图标URL（web推送使用,app推送不需要）
     */
    private String icon;
    /**
     * 跳转链接URL（web推送使用,app推送不需要）
     */
    private String link;
    /**
     * push模板（之前发网关的payload）
     */
    private Model model;

    /**
     * 文案库id
     */
    private String textId;

    /**
     * push 图标
     */
    private String img;


}
