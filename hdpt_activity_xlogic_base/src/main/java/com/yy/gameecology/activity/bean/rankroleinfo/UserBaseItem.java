package com.yy.gameecology.activity.bean.rankroleinfo;

import com.yy.gameecology.common.utils.Convert;

/**
 * @Author: CXZ
 * @Desciption: 用户基本信息
 * @Date: 2020/9/17 0:31
 * @Modified:
 */
public class UserBaseItem extends RoleItem {
    private Long uid;
    private String nick;
    private Long yyno;

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick;
    }

    public Long getYyno() {
        return yyno;
    }

    public void setYyno(Long yyno) {
        this.yyno = yyno;
    }

    @Override
    public void setKey(String key) {
        setUid(Convert.toLong(key));
    }

    @Override
    public String getKey() {
        return getUid()+"";
    }

    @Override
    public String getName() {
        return nick;
    }

    @Override
    public void setName(String name) {
        this.nick = name;
    }
}
