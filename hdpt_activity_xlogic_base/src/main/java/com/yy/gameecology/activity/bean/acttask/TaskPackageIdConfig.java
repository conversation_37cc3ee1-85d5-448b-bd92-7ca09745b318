package com.yy.gameecology.activity.bean.acttask;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-02-23 20:22
 **/
@Data
public class TaskPackageIdConfig {
    @ComponentAttrField(labelText = "奖池taskId")
    private Long taskId;

    @ComponentAttrField(labelText = "奖包packageId",remark = "如果是抽奖模型，一般留空，填大于0时代表必中的奖包id")
    private Long packageId;
}
