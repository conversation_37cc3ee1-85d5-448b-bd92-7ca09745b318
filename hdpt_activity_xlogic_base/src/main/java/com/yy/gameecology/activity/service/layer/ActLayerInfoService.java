package com.yy.gameecology.activity.service.layer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.actlayer.*;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.TaskUserInfoVo;
import com.yy.gameecology.activity.client.thrift.FtsRecommendDataThriftClient;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.client.yrpc.DanmakuActivityClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.activity.service.layer.itembuilder.LayerItemBuilder;
import com.yy.gameecology.activity.service.layer.itembuilder.LayerItemBuilderManager;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.NickExt;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.db.model.gameecology.ActLayerViewDefine;
import com.yy.gameecology.common.db.model.gameecology.ActResult;
import com.yy.gameecology.common.db.model.gameecology.ActRoleRankMap;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.component.attr.ActLayerConfigComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.DanmakuGameComponentAttr;
import com.yy.java.webdb.BatchUserInfoWithNickExt;
import com.yy.protocol.pb.danmu.Danmu;
import com.yy.thrift.hdztranking.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:活动浮层信息
 *
 * @createBy 曾文帜
 * @create 2020-07-28 21:28Z
 **/
@Service
public class ActLayerInfoService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    protected static final String NICK_EXT_KEY = "nickExtUsers";

    @Autowired
    protected CacheService cacheService;

    @Autowired
    protected ActRoleRankMapService actRoleRankMapService;

    @Autowired
    protected MemberInfoService memberInfoService;

    @Autowired
    protected CommonService commonService;


    @Autowired
    protected HdztRankingThriftClient hdztRankingThriftClient;


    @Autowired
    protected EnrollmentNewService enrollmentService;

    @Autowired
    protected WebdbThriftClient webdbThriftClient;

    @Autowired
    protected OnlineChannelService onlineChannelService;

    @Autowired
    protected UserInfoService userInfoService;

    @Autowired
    protected HdztTaskService hdztTaskService;


    @Autowired
    protected SignedService signedService;

    @Autowired
    protected ActTaskService actTaskService;

    @Autowired
    protected HdztRankConfigService hdztRankConfigService;

    @Autowired
    protected ActRedisGroupDao actRedisDao;

    @Autowired
    protected RedisConfigManager redisConfigManager;

    @Autowired
    protected ActLayerInfoExtService actLayerInfoExtService;

    @Autowired
    protected LayerItemBuilderManager layerItemBuilderManager;

    @Autowired
    protected ActResultService actResultService;


    @Autowired
    protected LayerConfigService layerConfigService;

    @Autowired
    protected UserTaskService userTaskService;


    @Autowired
    protected HdztPhaseConfigService hdztPhaseConfigService;


    private static final String NEED_SETTLE_STATUS = "need_settle_status";


    public static final String RESURRECTION_MEMBERS = "resurrection_members";


    @Cached(timeToLiveMillis = 4 * 1000)
    public LayerBroadcastInfo getSimpleLayerInfo(Long actId, Long sid, Long ssid) {
        Date now = commonService.getNow(actId);
        Clock clock = new Clock();

        LayerBroadcastInfo layerInfo = new LayerBroadcastInfo();
        Map<String, ActLayerViewDefine> actLayerViewDefineMap = cacheService.getActLayerViewDefine(actId);
        if (MapUtils.isEmpty(actLayerViewDefineMap)) {
            log.warn("ActLayerViewDefine getSimpleLayerInfo not config,actId:{}", actId);
            return layerInfo;
        }

        OnlineChannelInfo onlineChannelInfo = onlineChannelService.get(sid, ssid);
        if (onlineChannelInfo == null) {
            onlineChannelInfo = new OnlineChannelInfo();
            onlineChannelInfo.setSid(sid);
            onlineChannelInfo.setSsid(ssid);
        }
        layerInfo.setLayerBabyType(onlineChannelInfo.getLayerBabyType());

        ActivityInfoVo actInfo = hdztRankingThriftClient.queryActivityInfo(actId);
        if (actInfo == null || actInfo.getActId() == 0) {
            log.warn("buildLayerInfo getSimpleLayerInfo error,get act info failed,paraActId:{},actInfo:{}", actId, JSON.toJSONString(actInfo));
            return null;
        }
        Long busiId = webdbThriftClient.getBusiId(sid, ssid);

        //---tab数据构造生成
        for (String itemType : actLayerViewDefineMap.keySet()) {
            ActLayerViewDefine define = actLayerViewDefineMap.get(itemType);
            if (!inLayerTabShowTime(actId, now, define)) {
                continue;
            }
            //判断是否指定展示的模板业务类型（场景举例：分赛场的时候只有约战和宝贝参与）
            if (!define.containsBusiId(busiId)) {
                continue;
            }
            //获取tab构造器
            LayerItemBuilder layerItemBuilder = layerItemBuilderManager.getBuilder(itemType);
            List<String> memberIds = layerItemBuilder.getMemberIds(actInfo, busiId, define, onlineChannelInfo);
            //生成tab数据
            List<LayerMemberItem> layerMemberItems = layerItemBuilder.buildSimpleInfo(now, actInfo, busiId, define, memberIds);
            //填充tab数据
            layerItemBuilder.fillSimpleInfo(define, layerInfo, layerMemberItems);
        }

        return layerInfo;
    }

    @Cached(timeToLiveMillis = 4 * 1000)
    public LayerBroadcastInfo getLayerInfo(Long actId, Long sid, Long ssid, String from) {
        Date now = commonService.getNow(actId);
        return buildLayerInfo(actId, sid, ssid, now, from);
    }

    public Map<String, Object> queryLayerStatus(Long actId, Long sid, Long ssid) {
        Map<String, Object> result = Maps.newHashMap();
        Date now = commonService.getNow(actId);

        LayerBroadcastInfo info = buildLayerInfo(actId, sid, ssid, now, "web");
        int showStatus = showLayer(sid, ssid, info) ? 1 : 0;

        result.put("showStatus", showStatus);
        result.put("time", System.currentTimeMillis());
        result.put("grey", commonService.isGrey(actId));
        return result;
    }

    public boolean showLayer(long sid, long ssid, LayerBroadcastInfo layerBroadcastInfo) {
        if (layerBroadcastInfo == null) {
            return false;
        }
        //活动倒计时期间
        if (layerBroadcastInfo.getCurrentTime() >= layerBroadcastInfo.getActBeginShowTime()
                && layerBroadcastInfo.getCurrentTime() < layerBroadcastInfo.getActBeginTime()) {
            return true;
        }
        //活动展示时间已结束
        if (layerBroadcastInfo.getCurrentTime() > layerBroadcastInfo.getActEndShowTime()) {
            return false;
        }

        if (layerBroadcastInfo.getAnchorInfo() != null
                && layerBroadcastInfo.getAnchorInfo().stream().anyMatch(p -> !LayerViewStatus.containsNotShowStatus(p.getViewStatus()))) {
            return true;
        }
        if (layerBroadcastInfo.getChannelInfo() != null
                && !LayerViewStatus.containsNotShowStatus(layerBroadcastInfo.getChannelInfo().getViewStatus())) {
            return true;
        }
        if (layerBroadcastInfo.getSubChannelInfo() != null
                && !LayerViewStatus.containsNotShowStatus(layerBroadcastInfo.getSubChannelInfo().getViewStatus())) {
            return true;
        }
        if (layerBroadcastInfo.getTopNGroupInfo() != null
                && layerBroadcastInfo.getTopNGroupInfo().stream().anyMatch(p -> !LayerViewStatus.containsNotShowStatus(p.getViewStatus()))) {
            return true;
        }
        if (layerBroadcastInfo.getExtMemberItem() != null
                && layerBroadcastInfo.getExtMemberItem().stream().anyMatch(p -> !LayerViewStatus.containsNotShowStatus(p.getViewStatus()))) {
            return true;
        }
        return false;
    }

    public LayerBroadcastInfo buildLayerInfo(Long actId, Long sid, Long ssid, Date now, String from) {
        Clock clock = new Clock();

        LayerBroadcastInfo layerInfo = new LayerBroadcastInfo();
        Map<String, ActLayerViewDefine> actLayerViewDefineMap = cacheService.getActLayerViewDefine(actId);
        if (MapUtils.isEmpty(actLayerViewDefineMap)) {
            log.warn("ActLayerViewDefine buildLayerInfo not config,actId:{}", actId);
            return layerInfo;
        }
        if (layerConfigService.getLayerAttrConfig(actId, false) == null) {
            log.warn("ActLayerView compt not config,actId:{}", actId);
            return layerInfo;
        }
        if (logLayerInfo(actId, sid)) {
            log.info("buildLayerInfo begin with actId:{}, sid:{}, ssid:{}, now:{},cost:{}", actId, sid, ssid, now, clock.tag());
        }

        OnlineChannelInfo onlineChannelInfo = onlineChannelService.get(sid, ssid);
        if (onlineChannelInfo == null) {
            onlineChannelInfo = new OnlineChannelInfo();
            onlineChannelInfo.setSid(sid);
            onlineChannelInfo.setSsid(ssid);
        }
        layerInfo.setLayerBabyType(onlineChannelInfo.getLayerBabyType());

        if (logLayerInfo(actId, sid)) {
            log.info("buildLayerInfo with actId:{}, sid:{}, ssid:{}, onlineChannelInfo:{}", actId, sid, ssid, JSON.toJSONString(onlineChannelInfo));
        }
        ActivityInfoVo actInfo = hdztRankingThriftClient.queryActivityInfo(actId);
        if (actInfo == null || actInfo.getActId() == 0) {
            log.warn("buildLayerInfo error,get act info failed,paraActId:{},actInfo:{}", actId, JSON.toJSONString(actInfo));
            return null;
        }
        Long busiId = webdbThriftClient.getBusiId(sid, ssid);

        //活动基本信息
        layerInfo.setActId(actId);
        layerInfo.setCurrentTime(now.getTime());
        layerInfo.setActEndTime(actInfo.getEndTime());
        layerInfo.setActEndShowTime(actInfo.getEndTimeShow());
        layerInfo.setActBeginTime(actInfo.getBeginTime());
        layerInfo.setActBeginShowTime(actInfo.getBeginTimeShow());
        layerInfo.setActBusiId(actInfo.getBusiId());
        layerInfo.setSequence(System.currentTimeMillis());

        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(20);

        //---tab数据构造生成
        for (String itemType : actLayerViewDefineMap.keySet()) {
            ActLayerViewDefine define = actLayerViewDefineMap.get(itemType);
            if (!inLayerTabShowTime(actId, now, define)) {
                continue;
            }
            //判断是否指定展示的模板业务类型（场景举例：分赛场的时候只有约战和宝贝参与）
            if (!define.containsBusiId(busiId)) {
                continue;
            }
            //获取tab构造器
            LayerItemBuilder layerItemBuilder = layerItemBuilderManager.getBuilder(itemType);
            List<String> memberIds = layerItemBuilder.getMemberIds(actInfo, busiId, define, onlineChannelInfo);
            //生成tab数据
            List<LayerMemberItem> layerMemberItems = layerItemBuilder.build(now, actInfo, busiId, define, memberIds, ImmutableMap.of("playMode", onlineChannelInfo.getPlayMode()));

            //填充tab数据
            layerItemBuilder.fillLayerBroadcastInfo(define, layerInfo, layerMemberItems, memberIds, null);

            if (logLayerInfo(actId, sid)) {
                log.info("buildLayerInfo itemType info:{},cost:{}", itemType, clock.tag());
            }
        }

        //---外部扩展器加工广播信息
        layerInfo.getExt().put("sid", sid);
        layerInfo.getExt().put("ssid", ssid);
        layerInfo.getExt().put("from", from);

        layerInfo = actLayerInfoExtService.customBroadcastInTheEnd(actId, layerInfo);

        //外部扩展器给挂件增加扩展信息
        Map<String, Object> extInfo = actLayerInfoExtService.extendLayer(actId, sid, ssid, now, null);
        if (MapUtils.isNotEmpty(extInfo)) {
            layerInfo.getExt().putAll(extInfo);
        }

        if (logLayerInfo(actId, sid)) {
            log.info("buildLayerInfo layer info:{},cost:{}", JSON.toJSONString(layerInfo), clock.tag());
        }
        return layerInfo;
    }


    /**
     * 挂件是否展示结算中
     */
    public boolean inSettle(long actId, Date now) {
        //TODO 这里如果有必要，时间控制上可以优化为：
        //有阶段晋级来源的，阶段开始前9秒，展示结算中
        //阶段展示结束时间>阶段结束时间，展示top n的时候，在阶段结束后9秒也展示结算中
        if (!commonService.isActivityOneFlag(actId, NEED_SETTLE_STATUS, 1)) {
            return false;
        }

        // 在每日的 00:00:00 ~ 00:00:09 之间，无条件认为是结算中
        final int nine = 9;
        if (DateUtil.getHours(now) == 0 && DateUtil.getMinute(now) == 0 && DateUtil.getHourSeconds(now) <= nine) {
            return true;
        }

        return false;
    }

    protected void putActorQueryContributeItemExt(List<ActorQueryItem> items, String value) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        for (ActorQueryItem item : items) {
            putActorQueryContributeItemExt(item, value);
        }
    }

    protected void putActorQueryContributeItemExt(ActorQueryItem item, String value) {
        if (item == null) {
            return;
        }
        if (MapUtils.isEmpty(item.getExtData())) {
            return;
        }
        if (!item.getExtData().containsKey(ActorQueryItemExtName.SCORE_SOURCE)) {
            return;
        }
        if (item.getExtData().get(ActorQueryItemExtName.SCORE_SOURCE).equals(Convert.toString(RoleRankMapScoreSource.CONTRIBUTE.code))) {
            putActorQueryItemExt(item, "contributeMember", value);
        }
    }



    protected void putActorQueryItemExt(ActorQueryItem item, String key, String value) {
        if(item.getExtData() == null){
            item.setExtData(Maps.newHashMap());
        }
        item.getExtData().put(key,value);
    }

    //构造查询成员积分参数
    protected List<ActorQueryItem> genQueryActorInfoPara(ActivityInfoVo actInfo, ActLayerViewDefine viewDefine, Long busiId, List<String> memberIds, Long rankMapType, Date now) {
        List<ActorQueryItem> paras = Lists.newArrayList();
        for (String memberId : memberIds) {
            ActorQueryItem actorQueryItem = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, rankMapType, now);
            if (actorQueryItem != null) {
                paras.add(actorQueryItem);
            }
        }
        return paras;
    }


    protected ActorQueryItem genQueryActorInfoPara(ActivityInfoVo actInfo, ActLayerViewDefine viewDefine, Long busiId, String memberId, Long rankMapType, Date now) {
        return genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, rankMapType, now, false);
    }

    protected ActorQueryItem genQueryActorInfoPara(ActivityInfoVo actInfo, ActLayerViewDefine viewDefine, Long busiId, String memberId, Long rankMapType, Date now, boolean queryPrePhase) {
        Long actId = actInfo.getActId();
        Integer roleType = viewDefine.getRoleType();
        String itemKey = viewDefine.getItemTypeKey();
        Long roleId = enrollmentService.getFirstEnrolDestRoleId(actId, busiId, roleType, memberId);
        if (roleId == 0) {
            return null;
        }
        Long rankId = actRoleRankMapService.getActLayerRankId(actInfo.getActId(), itemKey, memberId, roleId, rankMapType, now);
        if (rankId == null || rankId == 0) {
            return null;
        }
        ActRoleRankMap actRoleRankMap = null;
        //查询上一阶段的积分
        if (queryPrePhase) {
            actRoleRankMap = actRoleRankMapService.getActRolePrePhaseRankMap(actInfo.getActId(), itemKey, roleId, rankMapType, now);
        } else {
            actRoleRankMap = actRoleRankMapService.getActRoleRankMap(actInfo.getActId(), itemKey, memberId, roleId, rankMapType, now);
        }
        if (actRoleRankMap == null) {
            return null;
        }
        ActorQueryItem item = new ActorQueryItem();
        item.setRankingId(actRoleRankMap.getRankId());
        item.setPhaseId(actRoleRankMap.getPhaseId());
        item.setActorId(memberId);
        item.setRoleType(roleType);
        item.setExtData(Maps.newHashMap());
        if (!StringUtil.isEmpty(actRoleRankMap.getDateKey())) {
            item.setDateStr(DateUtil.format(now, actRoleRankMap.getDateKey()));
        }

        String ext = actRoleRankMap.getExt();
        if (StringUtil.isNotBlank(ext)) {
            JSONObject extJson = JSON.parseObject(ext);
            for (Map.Entry<String, Object> entry : extJson.entrySet()) {
                item.getExtData().put(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
        if (actRoleRankMap.getScoreSource() != null && actRoleRankMap.getScoreSource() > 0) {
            item.getExtData().put(ActorQueryItemExtName.SCORE_SOURCE, Convert.toString(actRoleRankMap.getScoreSource()));
        }
        item.setWithStatus(true);
        return item;
    }


    /**
     * 获取pk信息
     *
     * @param roleRankMap 可选参数，如果不传自动生成
     */
    public PKInfo getPkInfo(long actId, ActLayerViewDefine viewDefine, long busiId, String memberId, Date now, ActRoleRankMap roleRankMap) {
        Integer roleType = viewDefine.getRoleType();
        String itemKey = viewDefine.getItemTypeKey();

        PKInfo pkInfo = new PKInfo();

        Long roleId = enrollmentService.getFirstEnrolDestRoleId(actId, busiId, roleType, memberId);
        if (roleRankMap == null) {
            roleRankMap = actRoleRankMapService.getActRoleRankMap(actId, itemKey, memberId, roleId, RankMapType.PK, now);
        }

        if (roleRankMap == null) {
            return null;
        }
        String dateKey = "";
        if (!StringUtil.isEmpty(roleRankMap.getDateKey())) {
            dateKey = DateUtil.format(now, roleRankMap.getDateKey());
        }
        PkInfo hdztPkInfo = hdztRankingThriftClient.queryPhasePkgroup(
                roleRankMap.getActId(),
                roleRankMap.getRankId(),
                roleRankMap.getPhaseId(),
                dateKey,
                dateKey,
                true, true, null);

        if (hdztPkInfo == null) {
            return null;
        }
        if (hdztPkInfo.getSettleStatus() == -1L) {
            pkInfo.setInSettle(true);
            return pkInfo;
        }

        pkInfo.setPkAward(hdztPkInfo.getPkAward());
        List<GroupMemberItem> curGroupMemberItems = null;
        if (CollectionUtils.isNotEmpty(hdztPkInfo.getPkGroupItems())) {
            for (PkGroupItem phasePkgroup : hdztPkInfo.getPkGroupItems()) {
                for (List<GroupMemberItem> pairPkItems : phasePkgroup.getMemberPkItems()) {
                    for (GroupMemberItem pkgroupItem : pairPkItems) {
                        if (memberId.equals(pkgroupItem.getMemberId())) {
                            curGroupMemberItems = pairPkItems;
                            break;
                        }
                    }
                }
            }
        }

        if (curGroupMemberItems == null) {
            return null;
        }

        GroupMemberItem pkgroupItemA = null;
        GroupMemberItem pkgroupItemB = null;
        for (GroupMemberItem pkgroupItem : curGroupMemberItems) {
            if (memberId.equals(pkgroupItem.getMemberId())) {
                pkgroupItemA = pkgroupItem;
            } else {
                pkgroupItemB = pkgroupItem;
            }
        }

        if (pkgroupItemA == null || pkgroupItemB == null) {
            return null;
        }

        List<PKItem> pkItems = Lists.newArrayList();
        PKItem PKItemA = new PKItem();
        PKItemA.setMemberId(pkgroupItemA.getMemberId());
        PKItemA.setPkValue(pkgroupItemA.getScore());
        PKItemA.setRank(pkgroupItemA.getRank());
        MemberInfo memberInfoA = memberInfoService.getMemberInfo(busiId, RoleType.findByValue(roleType), pkgroupItemA.getMemberId());
        if (memberInfoA != null && !StringUtil.isEmpty(memberInfoA.getName())) {
            PKItemA.setNickName(Base64.encodeBase64String(memberInfoA.getName().getBytes()));
            PKItemA.setAvatar(memberInfoA.getLogo());
        }
        EnrollmentInfo enrollmentInfoA = enrollmentService.getFirstEnrolMember(actId, 0L, roleType, pkgroupItemA.getMemberId());
        if (enrollmentInfoA != null) {
            PKItemA.setSignAsId(enrollmentInfoA.getSignAsid() + "");
        }
        pkItems.add(PKItemA);

        PKItem PKItemB = new PKItem();
        PKItemB.setMemberId(pkgroupItemB.getMemberId());
        PKItemB.setPkValue(pkgroupItemB.getScore());
        PKItemB.setRank(pkgroupItemB.getRank());
        MemberInfo memberInfoB = memberInfoService.getMemberInfo(busiId, RoleType.findByValue(roleType), PKItemB.getMemberId());
        if (memberInfoB != null && !StringUtil.isEmpty(memberInfoB.getName())) {
            PKItemB.setNickName(Base64.encodeBase64String(memberInfoB.getName().getBytes()));
            PKItemB.setAvatar(memberInfoB.getLogo());
            PKItemB.setSignAsId(memberInfoB.getAsid());
        }
        EnrollmentInfo enrollmentInfoB = enrollmentService.getFirstEnrolMember(actId, 0L, roleType, pkgroupItemB.getMemberId());
        if (enrollmentInfoB != null) {
            PKItemB.setSignAsId(enrollmentInfoB.getSignAsid() + "");
        }
        pkItems.add(PKItemB);

        pkInfo.setPkItems(pkItems);

        PhaseInfo rankingPhaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, roleRankMap.getPhaseId());
        if (rankingPhaseInfo != null) {
            long leftSeconds;

            //强制配置使用按阶段倒计时
            boolean configUsePhaseLeftSeconds = false;
            if (StringUtil.isNotBlank(roleRankMap.getExt())) {
                JSONObject mapExt = JSON.parseObject(roleRankMap.getExt());
                if (mapExt != null && Const.ONESTR.equals(mapExt.getString(RoleRankMapExtName.PK_USE_PHASE_LEFT_SECONDS))) {
                    configUsePhaseLeftSeconds = true;
                }
            }
            if (configUsePhaseLeftSeconds) {
                leftSeconds = (rankingPhaseInfo.getEndTime() - now.getTime()) / 1000;
            }
            //按天pk倒计时
            else if (DateUtil.PATTERN_TYPE2.equals(roleRankMap.getDateKey())) {
                leftSeconds = DateUtil.getTodayLeftSecondsExcludeLastSeconds(now);
                //有的按天pk结束的时候，不是一整天的
                long phaseLeftSeconds = (rankingPhaseInfo.getEndTime() - now.getTime()) / 1000;
                if (phaseLeftSeconds < leftSeconds) {
                    leftSeconds = phaseLeftSeconds;
                }
            }
            //按小时pk倒计时
            else if (DateUtil.PATTERN_YYYYMMDD.equals(roleRankMap.getDateKey())) {
                leftSeconds = DateUtil.getHourLeftSeconds(now);
            }
            //按阶段pk倒计时
            else {
                leftSeconds = (rankingPhaseInfo.getEndTime() - now.getTime()) / 1000;
            }
            pkInfo.setPkLeftSeconds(leftSeconds);
        }

        return pkInfo;
    }


    /**
     * 是否需要查询上一阶段信息
     * 通常复活赛才需要查询上一阶段信息来做特殊展示用
     * 复活赛展示: 阶段 extJson:phaseType==1002 &&  state == 2 && prePhaseState == 0 代表上一阶段已晋级，这一阶段不用参加复活赛
     */
    protected boolean isResurrection(String phaseExtJson) {
        if (StringUtil.isBlank(phaseExtJson)) {
            return false;
        }
        JSONValidator jsonValidator = JSONValidator.from(phaseExtJson);
        if (!jsonValidator.validate()) {
            return false;
        }
        try {
            JSONObject jsonObject = JSON.parseObject(phaseExtJson);
            return PhaseInfoExt.PHASE_TYPE_1002.equals(jsonObject.getInteger("phaseType"));
        } catch (Exception e) {
            log.warn("needQueryPrePhaseStatus error,e:{}", e.getMessage(), e);
            return false;
        }
    }


    //小时榜是否开始
    protected boolean inHourRank(RankingInfo rankingInfo, Date now) {
        if (StringUtil.isBlank(rankingInfo.getTimeKeyBegin()) && StringUtil.isBlank(rankingInfo.getTimeKeyEnd())) {
            return true;
        }

        try {
            if (StringUtil.isNotBlank(rankingInfo.getTimeKeyBegin())) {
                String beginTimeStr = DateUtil.format(now, DateUtil.PATTERN_TYPE5) + " " + rankingInfo.getTimeKeyBegin();
                Date dateBeginTime = DateUtil.getDate(beginTimeStr);
                if (now.before(dateBeginTime)) {
                    return false;
                }
            }
            if (StringUtil.isNotBlank(rankingInfo.getTimeKeyEnd())) {
                String endTimeStr = DateUtil.format(now, DateUtil.PATTERN_TYPE5) + " " + rankingInfo.getTimeKeyEnd();
                Date dateEndTime = DateUtil.getDate(endTimeStr);
                if (now.after(dateEndTime)) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("inHourRank error,ranking info:{},e:{}", JSON.toJSONString(rankingInfo), e.getMessage(), e);
            return false;
        }
    }

    //分组排名
    protected GroupInfo getGroupInfo(long actId, long destRankId, long destPhaseId, String memberId, String dateStr, Map<String, String> extMap) {
        GroupInfo groupInfo = new GroupInfo();
        MemberGroup hdztGroupInfo = hdztRankingThriftClient.queryQualificationGroup(actId, destRankId, destPhaseId, dateStr, true, true, null);
        if (hdztGroupInfo == null || CollectionUtils.isEmpty(hdztGroupInfo.getGroupItems())) {
            return null;
        }

        String groupName = null;
        GroupItem curGroup = null;
        GroupMemberItem curMemberItem = null;
        List<GroupMemberItem> curGroupMember = null;
        for (GroupItem groupItem : hdztGroupInfo.getGroupItems()) {
            for (GroupMemberItem groupMemberItem : groupItem.getMemberItems()) {
                if (memberId.equals(groupMemberItem.getMemberId())) {
                    curGroup = groupItem;
                    groupName = groupItem.getName();
                    curMemberItem = groupMemberItem;
                    curGroupMember = groupItem.getMemberItems();
                    break;
                }
            }
        }
        if (curMemberItem == null) {
            return null;
        }

        groupInfo.setGroupName(groupName);
        groupInfo.setMemberId(curMemberItem.getMemberId());
        groupInfo.setScore(curMemberItem.getScore());


        int rank = 0;
        long preScore = 0;
        //距离上一名分值
        long preOffsetScore = 0;
        boolean allScoreIsZero = true;

        final String pkValueStr = "pkValue";
        if (extMap != null && extMap.containsKey(pkValueStr)) {
            Map<String, String> pkValueQueryExt = Maps.newHashMap();
            //查询荣耀值
            pkValueQueryExt.put(RankExtParaKey.RANK_PK_VALUE, "");
            List<String> memberList = curGroupMember.stream().map(GroupMemberItem::getMemberId).collect(Collectors.toList());
            Map<String, Rank> membersRanking = hdztRankingThriftClient.queryPointedMembersRanking(actId, destRankId,
                    destPhaseId, dateStr, memberList, pkValueQueryExt);
            //这里lastScore在后面都没用的,用来记录下当前总pk荣耀值
            for (GroupMemberItem item : curGroupMember) {
                item.setLastScore(membersRanking.get(item.getMemberId()).getScore());
            }
            curGroupMember = curGroupMember.stream().sorted(Comparator.comparing(GroupMemberItem::getScore, Comparator.reverseOrder()).thenComparing(
                    GroupMemberItem::getLastScore, Comparator.reverseOrder())).collect(Collectors.toList());
        } else {
            curGroupMember = curGroupMember.stream().sorted(Comparator.comparing(GroupMemberItem::getRank)).collect(Collectors.toList());
        }

        for (int i = 0; i < curGroupMember.size(); i++) {
            GroupMemberItem curMember = curGroupMember.get(i);
            if (curMember.getScore() > 0) {
                allScoreIsZero = false;
            }
            //上一名分值
            if (i > 0) {
                preScore = curGroupMember.get(i - 1).getScore();
            }
            if (memberId.equals(curMember.getMemberId())) {
                rank = i + 1;
                long offset = preScore - curMember.getScore();
                if (offset > 0) {
                    preOffsetScore = offset;
                }
            }
        }

        groupInfo.setAllScoreIsZero(allScoreIsZero);
        groupInfo.setPassDesc(String.format("%s进%s", curGroupMember.size(), curGroup.getCount()));
        groupInfo.setRank(allScoreIsZero ? -1 : rank);
        groupInfo.setGroupOffsetScore(preOffsetScore);
        return groupInfo;
    }

    //积分赛日排名奖励积分
    protected int getDayRankAwardScore(Long actId, Long srcRankId, Long phaseId, Integer rank) {

        PkInfo hdztPkInfo = hdztRankingThriftClient
                .queryPhasePkgroup(actId, srcRankId, phaseId, "", "", false, false, null);

        if (hdztPkInfo == null) {
            return 0;
        }
        if (rank == null || rank <= 0) {
            return 0;
        }

        List<Integer> rankAward = hdztPkInfo.getRankAward();
        if (rank > rankAward.size()) {
            return 0;
        }
        int index = rank - 1;

        return rankAward.get(index);
    }


    public long getPhaseLeftSeconds(RankingInfo rankingInfo, PhaseInfo phaseInfo, Date now) {
        //日榜
        if (rankingInfo != null && TimeKeyHelper.isSplitByDay(rankingInfo.getTimeKey())) {
            return DateUtil.getTodayLeftSecondsExcludeLastSeconds(now);
        }
        if (rankingInfo != null && TimeKeyHelper.isSplitByHour(rankingInfo.getTimeKey())) {
            return DateUtil.getHourLeftSeconds(now);
        }

        if(rankingInfo != null && TimeKeyHelper.isSplitBy15Min(rankingInfo.getTimeKey()) ){
            return DateUtil.get15MinLeftSeconds(now);
        }

        if(rankingInfo != null && TimeKeyHelper.isSplitBy30Min(rankingInfo.getTimeKey()) ){
            return DateUtil.get30MinLeftSeconds(now);
        }

        return getPhaseLeftSeconds(phaseInfo, now);
    }

    protected long getPhaseLeftSeconds(PhaseInfo phaseInfo, Date now) {
        if (phaseInfo == null) {
            return -1;
        }
        long leftSeconds = (phaseInfo.getEndTime() - now.getTime()) / 1000;

        if (leftSeconds < 0) {
            leftSeconds = -1;
        }
        return leftSeconds;
    }


    protected String getPassDescContent(RankingInfo rankingInfo) {
        if (rankingInfo == null) {
            return "";
        }
        RankingPhaseInfo rankingPhaseInfo = rankingInfo.getCurrentPhase();
        if (rankingPhaseInfo == null) {
            return "";
        }

        //如果有定制文案配置，则优先定制配置
        long phaseId = rankingPhaseInfo.getPhaseId();
        ActLayerConfigComponentAttr attr = layerConfigService.getLayerAttrConfig(rankingInfo.getActId());
        if (attr.getRacePassDesc().containsKey(rankingInfo.getRankingId())
                && attr.getRacePassDesc().get(rankingInfo.getRankingId()).containsKey(phaseId)) {
            return attr.getRacePassDesc().get(rankingInfo.getRankingId()).get(phaseId);
        }

        //N进X，N==X的时候不展示,通常用于排位赛
        if (rankingPhaseInfo.getTotalCount() > 0 && rankingPhaseInfo.getTotalCount() == rankingPhaseInfo.getPassCount()) {
            return "";
        }

        if (rankingPhaseInfo.getTotalCount() == -1 && rankingPhaseInfo.getPassCount() == -1) {
            return "";
        }

        if (rankingPhaseInfo.getPassCount() == -1) {
            return "";
        }

        String n = rankingPhaseInfo.getTotalCount() == -1 ? "N" : rankingPhaseInfo.getTotalCount() + "";
        String m = rankingPhaseInfo.getPassCount() == -1 ? "N" : rankingPhaseInfo.getPassCount() + "";
        return String.format("%s进%s", n, m);
    }


    /**
     * 阶段top n ,榜单多个晋级方向是需要配置 -这个已经不能判断是最后一节阶段
     */
    protected int getLastPhaseTopN(RankingInfo rankingInfo, Long phaseId) {
        ActLayerConfigComponentAttr layerAttr = layerConfigService.getLayerAttrConfig(rankingInfo.getActId());
        Long rankIdKey = rankingInfo.getRankingId();
        if (layerAttr.getLastPhaseTopN().containsKey(rankIdKey)
                && layerAttr.getLastPhaseTopN().get(rankIdKey).containsKey(phaseId)) {
            return layerAttr.getLastPhaseTopN().get(rankIdKey).get(phaseId);
        }
        return 0;
    }

    /**
     * 获取活动结束后top n的title,如果榜单扩展属性没有配置则用rankName
     */
    protected String getLastPhaseTopTitle(RankingInfo rankingInfo, long phaseId, long rank) {
        ActLayerConfigComponentAttr layerAttr = layerConfigService.getLayerAttrConfig(rankingInfo.getActId());
        String title = "";
        if (MapUtils.isEmpty(rankingInfo.getExtData())) {
            title = rankingInfo.getRankingName();
        }
        Long rankIdKey = rankingInfo.getRankingId();
        //优先返回按照名次配置
        if (layerAttr.getLastPhaseRankTopTitle().containsKey(rankIdKey)
                && layerAttr.getLastPhaseRankTopTitle().get(rankIdKey).containsKey(phaseId)
                && layerAttr.getLastPhaseRankTopTitle().get(rankIdKey).get(phaseId).containsKey(rank)) {
            title = layerAttr.getLastPhaseRankTopTitle().get(rankIdKey).get(phaseId).get(rank);
        }
        //再返回按照非名次配置
        else if (layerAttr.getLastPhaseTopTitle().containsKey(rankIdKey)
                && layerAttr.getLastPhaseTopTitle().get(rankIdKey).containsKey(phaseId)) {
            title = layerAttr.getLastPhaseTopTitle().get(rankIdKey).get(phaseId);
        }

        return String.format(title, rank);
    }

    /**
     * 应对配置非最后一阶段获得的title 展示
     */
    protected ActResult getNotLastPhaseTitle(long now, String groupCode, long actId, long roleType, String memberId) {

        ActResult actResult = actResultService.queryInShowTimeLayerResultCache(actId, groupCode, roleType, memberId);
        if (actResult == null || StringUtil.isEmpty(actResult.getExtData())) {
            return actResult;
        }
        try {

            JSONObject jsonObject = JSON.parseObject(actResult.getExtData());
            Date startTime = jsonObject.getDate("layerStartShowTime");
            Date endTime = jsonObject.getDate("layerEndShowTime");
            if (startTime != null && endTime != null && now >= startTime.getTime() && now <= endTime.getTime()) {
                return actResult;
            }
            return null;
        } catch (Exception e) {
            log.error("getNotLastPhaseTitle error,actId:{},roleType:{},memberId:{},e:{}", actId, roleType, memberId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 如果配置了tab展示时间，则会加上判断
     * 使用场景：不同赛道tab开始展示时间不一样，无法统一用活动时间，未参赛，定位不了成员所在阶段，获取不到阶段时间判断
     */
    private boolean inLayerTabShowTime(long actId, Date now, ActLayerViewDefine define) {
        long nowTime = now.getTime();
        if (define.getStartShowTime() != null && nowTime < define.getStartShowTime().getTime()) {
            return false;
        }
        if (define.getEndShowTime() != null && nowTime > define.getEndShowTime().getTime()) {
            return false;
        }

        return true;
    }


    /**
     * 是否抽签中
     */
    protected boolean isInDrawLots(long nowMill, PhaseInfo phaseInfo) {
        if (phaseInfo == null || StringUtil.isEmpty(phaseInfo.getExtJson())) {
            return false;
        }
        try {
            JSONObject jsonObject = JSON.parseObject(phaseInfo.getExtJson());
            Date startTime = jsonObject.getDate(PhaseInfoExt.DRAW_LOTS_START_TIME);
            Date endTime = jsonObject.getDate(PhaseInfoExt.DRAW_LOTS_END_TIME);
            if (startTime != null && endTime != null && nowMill >= startTime.getTime() && nowMill <= endTime.getTime()) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("isInDrawLots error,phaseInfo:{},e:{}", JSON.toJSONString(phaseInfo), e.getMessage(), e);
            return false;
        }
    }

    public String getPrePhaseTopNTitle(Date now, long actId, long roleType, ActorInfoItem item, ActLayerConfigComponentAttr layerAttr) {
        if (item == null) {
            return StringUtil.EMPTY;
        }
        Long rankIdKey = item.getRankingId();
        Long phaseIdKey = item.getRankingId();
        if (!layerAttr.getPrePhaseTopNTitle().containsKey(rankIdKey)) {
            return StringUtil.EMPTY;
        }
        if (!layerAttr.getPrePhaseTopNTitle().get(rankIdKey).containsKey(phaseIdKey)) {
            return StringUtil.EMPTY;
        }

        //{rankId:{phaseId:title}}
        ShowTopTitleConfig config = layerAttr.getPrePhaseTopNTitle().get(rankIdKey).get(phaseIdKey);
        if (now.after(config.getEndTime()) || now.before(config.getStartTime())) {
            return StringUtil.EMPTY;
        }

        String dateCode;
        //一天中前n秒才展示
        if (config.getDataModel() == 0) {
            dateCode = DateUtil.format(DateUtil.add(now, -1), DateUtil.PATTERN_TYPE2);
        } else {
            //取当天,如最后一个小时展示当前的topN
            dateCode = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
            String startShowTime = config.getStartShowTime();
            String endShowTime = config.getEndShowTime();
            Date dateBegin = DateUtil.getDate(dateCode + startShowTime, DateUtil.PATTERN_TYPE1);
            Date dateEnd = DateUtil.getDate(dateCode + endShowTime, DateUtil.PATTERN_TYPE1);
            if (now.before(dateBegin) || now.after(dateEnd)) {
                return StringUtil.EMPTY;
            }
        }

        //String dateCode = DateUtil.format(DateUtil.add(now, -1), DateUtil.PATTERN_TYPE2);
        ActorQueryItem queryItem = new ActorQueryItem();
        queryItem.setRankingId(item.getRankingId());
        queryItem.setPhaseId(item.getPhaseId());
        queryItem.setActorId(item.getActorId());
        queryItem.setRoleType(roleType);
        queryItem.setDateStr(dateCode);
        queryItem.setWithStatus(true);
        ActorInfoItem preDay = hdztRankingThriftClient.queryActorRankingInfo(actId, queryItem);
        if (preDay != null && preDay.getRank() > 0 && preDay.getRank() <= config.getTopN()) {
            return String.format(config.getTitle(), preDay.getRank());
        }

        return null;
    }

    /**
     * 输出频道广播日志
     */
    protected boolean logLayerInfo(long actId, long sid) {
        ActLayerConfigComponentAttr attr = layerConfigService.getLayerAttrConfig(actId);
        return attr.getBroDebugSid().contains(sid);
    }

    /**
     * 主播挂件信息
     * @param viewDefine
     * @param actInfo
     * @param busiId
     * @param roleType
     * @param anchorUids
     * @param now
     * @param playMode
     * @param mainRankMember 贡献榜的主榜成员，如果没有传空
     * @return
     */
    protected List<LayerMemberItem> genAnchorInfos(ActLayerViewDefine viewDefine, ActivityInfoVo actInfo, Long busiId, int roleType, List<Long> anchorUids, Date now, Long playMode
            , String mainRankMember) {
        long actId = actInfo.getActId();
        List<LayerMemberItem> anchors = Lists.newArrayList();

        //---构造查询成员积分参数
        List<String> memberIds = anchorUids.stream().map(x -> x + "").collect(Collectors.toList());
        List<ActorQueryItem> actorStatusPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberIds, RankMapType.NORMAL, now);

        //如果这个是空的,尝试添加总榜的
        if (CollectionUtils.isEmpty(actorStatusPara)) {
            actorStatusPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberIds, RankMapType.TOTAL, now);
        }
        //item type key是contribute，并且act_role_rank_map.source==2,查询贡献榜
        if (LayerItemTypeKey.CON_SET.contains(viewDefine.getItemTypeKey())) {
            putActorQueryContributeItemExt(actorStatusPara, mainRankMember);
        }
        //-----查出成员积分
        Map<String, ActorInfoItem> actorInfoItemMap = hdztRankingThriftClient.queryActorRankingInfoMap(actId, actorStatusPara);
        //---用户信息

        Map<Long, UserInfoVo> userInfoMap = null;

        Map<String, Map<String, MultiNickItem>> multiNickUsers =  new HashMap<>(anchorUids.size());
        // 设置多昵称条件
        if (nickExt(viewDefine)) {
            userInfoMap = userInfoService.getUserInfoWithNickExt(anchorUids, multiNickUsers, false, Template.all.getCode());
        }

        if (userInfoMap == null) {
            userInfoMap = userInfoService.getUserInfo(anchorUids, getTemplate(actId, roleType));
        }

        for (int i = 0; i < anchorUids.size(); i++) {
            Long uid = anchorUids.get(i);
            ActorInfoItem actorInfoItem = actorInfoItemMap.get(uid + "");
            UserInfoVo userInfoVo = userInfoMap.get(uid);
            int sort = i;
            //23是多主持团战，所以都是0主持
            final int playMode23 = 23;
            if (playMode != null && playMode.intValue() == playMode23) {
                sort = 0;
            }
            LayerMemberItem layerMemberItem = genAnchorInfo(viewDefine, actInfo, uid + "", mainRankMember, roleType, actorInfoItem, userInfoVo, busiId, sort, now);
            //扩展信息
            Map<String, Object> ext = layerMemberItem.getExt();
            if (ext == null) {
                ext = new HashMap<>(5);
                layerMemberItem.setExt(ext);
            }
            Map<String, Object> itemExtInfo = actLayerInfoExtService.extendLayerMemberItem(actId, layerMemberItem);
            if (MapUtils.isNotEmpty(itemExtInfo)) {
                ext.putAll(itemExtInfo);
            }

            if (MapUtils.isNotEmpty(multiNickUsers)) {
                if(multiNickUsers.containsKey(String.valueOf(uid))){
                    Map<String, Map<String, MultiNickItem>> itemMultiNickUsers = Map.of(String.valueOf(uid),multiNickUsers.get(String.valueOf(uid)));
                    ext.put(NICK_EXT_KEY, itemMultiNickUsers);
                }

            }

            anchors.add(layerMemberItem);
        }

        return anchors;
    }

    /**
     * 决定从那个数据源获取用户信息
     **/
    private Template getTemplate(long actId, int roleType) {
        ActLayerConfigComponentAttr attr = layerConfigService.getLayerAttrConfig(actId);
        Integer source = attr.getUserInfoSourceMap().get(roleType);
        if (source == null) {
            return Template.unknown;
        }
        return Template.getTemplate(Convert.toInt(source, 9999));
    }

    protected boolean nickExt(ActLayerViewDefine viewDefine) {
        if (viewDefine == null) {
            return false;
        }

        if (!StringUtils.startsWith(viewDefine.getExtJson(), StringUtil.OPEN_BRACE)) {
            return false;
        }

        JSONObject extJson = JSON.parseObject(viewDefine.getExtJson());
        return extJson.getBooleanValue("nickExt");
    }

    private LayerMemberItem genAnchorInfo(ActLayerViewDefine viewDefine,
                                          ActivityInfoVo actInfo,
                                          String memberId,
                                          String mainRankMemberId,
                                          Integer roleType,
                                          ActorInfoItem actorInfoItem,
                                          UserInfoVo userinfo,
                                          Long busiId,
                                          int sort,
                                          Date now) {


        long actId = actInfo.getActId();
        long uid = Convert.toLong(memberId, 0);
        ActLayerConfigComponentAttr layerAttr = layerConfigService.getLayerAttrConfig(actId);
        EnrollmentInfo enrollmentInfo = enrollmentService.tryGetFirstEnrolMemberCache(actInfo.getActId(), busiId, roleType, memberId);

        LayerMemberItem itemRes = new LayerMemberItem();
        itemRes.setItemType(viewDefine.getItemTypeKey());
        //主持/宝贝主播信息
        itemRes.setSort(sort);
        itemRes.setMemberId(memberId);
        if (userinfo != null) {
            itemRes.setNickName(Base64Utils.encodeToString(userinfo.getNick().getBytes()));
            itemRes.setLogo(userinfo.getAvatarUrl());
        }
        itemRes.setSignSid(enrollmentInfo == null ? 0 : enrollmentInfo.getSignSid());
        itemRes.setAsid(enrollmentInfo == null ? "0" : enrollmentInfo.getSignAsid() + "");
        itemRes.setRoleId(enrollmentInfo == null ? 0 : enrollmentInfo.getDestRoleId());
        itemRes.setRoleType(roleType);
        //交友的帽子，签约了。不报名，但是能做任务，所以要加多个签约状态 ;
        if (busiId == BusiId.MAKE_FRIEND.getValue()) {
            ChannelInfoVo signInfo = signedService.getSignedInfo(Convert.toLong(memberId, 0), Template.makefriend.getCode());
            itemRes.setSignStatus(signInfo != null && Convert.toLong(signInfo.getSid(), 0) > 0 ? 1 : 0);
        } else {
            itemRes.setSignStatus(1);
        }

        //宝贝模板,非签约主播不展示挂件主播tab
        if (busiId == BusiId.GAME_BABY.getValue() && !Objects.equals(viewDefine.getItemTypeKey(), LayerItemTypeKey.ANCHOR_5)) {
            ChannelInfoVo signInfo = signedService.getSignedInfo(Convert.toLong(memberId, 0), Template.gamebaby.getCode());
            if (signInfo == null || Convert.toLong(signInfo.getSid(), 0) <= 0) {
                itemRes.setState(ActorInfoStatus.NOT_IN_PHASE);
                itemRes.setSignStatus(-1);
                return itemRes;
            }
        }

        //任务信息
        long taskRankId = layerAttr.getAnchorTaskRankId().getOrDefault(busiId, -1L);
        if (taskRankId > 0) {
            long phaseId = layerAttr.getAnchorTaskPhaseId().getOrDefault(busiId, -1L);
            long timeKey = layerAttr.getAnchorTaskTimeKey().getOrDefault(busiId, TimeKeyHelper.TIME_KEY_NO);
            String dateStr = TimeKeyHelper.getTimeCode(timeKey, now);
            TaskUserInfoVo taskUserInfoVo = hdztTaskService.getUserTaskInfo(uid, actInfo.getActId(), taskRankId, phaseId, dateStr);
            if (taskUserInfoVo != null && !org.springframework.util.CollectionUtils.isEmpty(taskUserInfoVo.getMissions())) {
                itemRes.setMissions(taskUserInfoVo.getMissions());
                itemRes.getViewSupport().put(LayerViewSupportName.MISSION_COMPLETE, userTaskService.isCompleteMission(taskUserInfoVo.getMissions()));
            }
        }

        ActResult actResult = getNotLastPhaseTitle(now.getTime(), viewDefine.getItemTypeKey(), actId, roleType, memberId);
        if (actResult != null) {
            itemRes.setShowHonorTitle(true);
            itemRes.setLastPhaseTopTitle(actResult.getTitle());
        }

        //日榜，次日前5分钟，展示top n
        String prePhaseTopNTitle = getPrePhaseTopNTitle(now, actId, roleType, actorInfoItem, layerAttr);
        if (StringUtil.isNotBlank(prePhaseTopNTitle)) {
            itemRes.setShowHonorTitle(true);
            itemRes.setLastPhaseTopTitle(prePhaseTopNTitle);
        }


        //结算状态
        if (inSettle(actInfo.getActId(), now)) {
            itemRes.setSettleStatus(1);
            return itemRes;
        }

        if (actorInfoItem == null || StringUtil.ZERO.equals(actorInfoItem.getActorId())) {
            if (enrollmentInfo == null) {
                itemRes.setState(ActorInfoStatus.NOT_IN);
            } else {
                //不在赛程内
                itemRes.setState(ActorInfoStatus.NOT_IN_PHASE);
                if (SysEvHelper.isDev()) {
                    log.warn("not_in_phase,actorInfoItem:{},enrollmentInfo:{}", JSON.toJSONString(actorInfoItem), JSON.toJSONString(enrollmentInfo));
                }
            }
            return itemRes;
        }

        long rankId = actorInfoItem.getRankingId();
        itemRes.setRank(Convert.toInt(actorInfoItem.getRank(), 0));
        //主播分组名称 例如 女主持 男主持 天团 约战主播 宝贝主播
        String roleName = enrollmentService.getFirstEnrolDestRoleName(actInfo.getActId(), busiId, roleType, memberId);
        itemRes.setRoleType(roleType);
        itemRes.setRoleName(roleName);
        itemRes.setCurRankId(actorInfoItem.getRankingId());
        itemRes.setCurPhaseId(actorInfoItem.getPhaseId());
        PhaseInfo curPhaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, actorInfoItem.getPhaseId());
        itemRes.setCurPhaseInfo(curPhaseInfo);


        //排名、总积分、距离上一名
        itemRes.setScore(actorInfoItem.getScore());
        itemRes.setOffsetScore(actorInfoItem.getRank() != 1 ? actorInfoItem.getPreScore() - actorInfoItem.getScore() : 0);
        //actorInfoItem.getStatus() -1非参赛角色（没在分组名单） 0-正常状态（在晋级线以上） 1-代表有危险（在晋级线以下） 2-被淘汰 500-待结算中
        itemRes.setState(actorInfoItem.getStatus());
        //晋级信息
        final RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, rankId);
        String passDesc = getPassDescContent(rankingInfo);
        itemRes.setPassDesc(passDesc);
        itemRes.setCurRankName(rankingInfo.getRankingName());
        itemRes.setCurRankNameShow(rankingInfo.getRankingNameShow());
        long leftSeconds = getPhaseLeftSeconds(rankingInfo, curPhaseInfo, now);
        itemRes.setLeftSeconds(leftSeconds);
        itemRes.setCurRankExtJson(rankingInfo.getRankingExtjson());

        //pk信息
        PKInfo pkInfo = getPkInfo(actId, viewDefine, roleType, memberId, now, null);
        //结算中
        if (pkInfo != null && pkInfo.isInSettle()) {
            itemRes.setSettleStatus(1);
            return itemRes;
        }
        if (pkInfo != null) {
            itemRes.setPkInfo(pkInfo);
        }

        //分组排名 GroupInfo
        ActorQueryItem queryGroupPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.GROUP, now);
        if (queryGroupPara != null) {
            GroupInfo groupInfo = getGroupInfo(actId, queryGroupPara.getRankingId(), queryGroupPara.getPhaseId(), memberId, queryGroupPara.getDateStr(), queryGroupPara.getExtData());
            if (groupInfo != null) {
                itemRes.setGroupName(groupInfo.getGroupName());
                itemRes.setGroupRank(groupInfo.getRank());
                itemRes.setGroupOffsetScore(groupInfo.getGroupOffsetScore());
                if (isSetGroupPassDesc(layerAttr, rankingInfo, actorInfoItem.getPhaseId())) {
                    itemRes.setPassDesc(groupInfo.getPassDesc());
                }
                itemRes.setRank(groupInfo.getRank());
                //1、 晋级结算到新的阶段，当榜单第一名分数为0，所有人都是正常状态，排名为-- ;
                //2、榜单第一名出现分数后，按照实际排名(有的有可能是0分,但是继承了上一阶段的排名)展示排名和危险状态
                if (groupInfo.isAllScoreIsZero()) {
                    itemRes.setGroupRank(-1);
                    if (itemRes.getState() == 1) {
                        itemRes.setState(0);
                    }
                }

                //存在循环pk且需要用积分加荣耀值排名,不展示危标签
                final String pkValueStr = "pkValue";
                if (queryGroupPara.getExtData().containsKey(pkValueStr)) {
                    itemRes.setState(0);
                }
                //前端展示需要，榜单名称加上分组名称
                //itemRes.setCurRankName(itemRes.getCurRankName() + groupInfo.getGroupName());
                itemRes.setCurRankName(groupInfo.getGroupName());
                itemRes.setCurRankNameShow(groupInfo.getGroupName());
            }
        }

        // 日排行日荣耀值
        ActorQueryItem queryDayActorPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.DAY, now);
        if (queryDayActorPara != null) {
            ActorInfoItem dayActorItem = hdztRankingThriftClient.queryActorRankingInfo(actId, queryDayActorPara);
            if (dayActorItem != null) {
                itemRes.setDayRank(Convert.toInt(dayActorItem.getRank(), 0));
                itemRes.setDayScore(dayActorItem.getScore());
            }
        }

        //总榜荣耀值和排名
        ActorQueryItem queryTotalActorPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.TOTAL, now);
        if (queryTotalActorPara != null) {
            if (LayerItemTypeKey.CON_SET.contains(viewDefine.getItemTypeKey())) {
                putActorQueryContributeItemExt(queryTotalActorPara, mainRankMemberId);
            }
            ActorInfoItem totalActorItem = hdztRankingThriftClient.queryActorRankingInfo(actId, queryTotalActorPara);
            if (totalActorItem != null) {
                itemRes.setTotalRank(Convert.toInt(totalActorItem.getRank(), 0));
                itemRes.setTotalScore(totalActorItem.getScore());
                long totalRankOffsetScore = totalActorItem.getRank() != 1 ? totalActorItem.getPreScore() - totalActorItem.getScore() : 0;
                itemRes.getExt().put("totalRankOffsetScore", totalRankOffsetScore);

                PhaseInfo rankingPhaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, totalActorItem.getPhaseId());
                long totalRankLeftSeconds = getPhaseLeftSeconds(rankingPhaseInfo, now);
                itemRes.getExt().put("totalRankLeftSeconds", totalRankLeftSeconds);
            }
        }

        //获取日排名奖励积分
        ActorQueryItem queryAwardPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.AWARD, now);
        if (queryAwardPara != null) {
            int addScore = getDayRankAwardScore(actId, queryAwardPara.getRankingId(), queryAwardPara.getPhaseId(), itemRes.getDayRank());
            itemRes.setAddScore(addScore);
        }


        //复活赛需要查询上一阶段信息
        if (curPhaseInfo != null && isResurrection(curPhaseInfo.getExtJson())) {
            ActorQueryItem prePara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.NORMAL, now, true);
            ActorInfoItem preActorItem = hdztRankingThriftClient.queryActorRankingInfo(actId, prePara);
            if (preActorItem != null) {
                itemRes.setPrePhaseState(preActorItem.getStatus());
            }
        }


        //需要参赛资格，尝试读取是否是在淘汰赛制中，并填充淘汰赛制信息
        if (hdztRankConfigService.isRaceQualification(rankingInfo)) {
            fillNotInRaceMemberInfo(actId, viewDefine, memberId, itemRes, now);
        }

        if (!itemRes.isShowHonorTitle()) {
            String topTitle = getLastPhaseTopTitle(rankingInfo, actorInfoItem.getPhaseId(), itemRes.getLastPhaseRank());
            itemRes.setLastPhaseTopTitle(topTitle);
        }

        //最后一个阶段晋级结算展示top n (和最后一阶段n进x的n是同一个标识)
        int lastPhaseTopN = curPhaseInfo != null ? getLastPhaseTopN(rankingInfo, curPhaseInfo.getPhaseId()) : 0;
        itemRes.setLastPhaseTopN(lastPhaseTopN);

        return itemRes;
    }


    private boolean isSetGroupPassDesc(ActLayerConfigComponentAttr attr, RankingInfo rankingInfo, long phaseId) {
        if (rankingInfo == null) {
            return false;
        }
        long rankId = rankingInfo.getRankingId();
        if (!attr.getUseGroupPassDesc().containsKey(rankId)) {
            return false;
        }

        return attr.getUseGroupPassDesc().get(rankId).contains(phaseId);
    }


    private boolean fillNotInRaceMemberInfo(long actId, ActLayerViewDefine viewDefine, String memberId, LayerMemberItem itemRes, Date now) {
        long roleType = viewDefine.getRoleType();
        //榜单名称、分数、排名、状态、
        long raceRankId = hdztRankingThriftClient.queryNotInRaceRankId(actId, memberId);
        if (raceRankId <= 0) {
            return false;

        }
        //未晋级赛制，赛道切换，分组会变，目前无映射关系，roleId无法确认，配置成0
        ActRoleRankMap actRoleRankMap = actRoleRankMapService.getActRoleRankMap(actId, viewDefine.getItemTypeKey(), HdztRoleId.UNKNOW, raceRankId, RankMapType.NOT_IN_RACE, now);
        if (actRoleRankMap == null) {
            return false;
        }

        ActorQueryItem item = new ActorQueryItem();
        item.setRankingId(actRoleRankMap.getRankId());
        item.setPhaseId(actRoleRankMap.getPhaseId());
        item.setActorId(memberId);
        item.setRoleType(roleType);
        item.setQueryPhaseRank(1);
        ActorInfoItem actorInfo = hdztRankingThriftClient.queryActorRankingInfo(actId, item);
        if (actorInfo == null) {
            return false;
        }

        itemRes.setScore(actorInfo.getScore());
        itemRes.setRank(Convert.toInt(actorInfo.getRank(), 0));
        //淘汰赛制榜单没有淘汰危险晋级的说法，固定是0正常状态
        itemRes.setState(0);
        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, actRoleRankMap.getRankId(), actRoleRankMap.getPhaseId());
        if (rankingInfo != null) {
            itemRes.setCurRankName(rankingInfo.getRankingName());
            itemRes.setCurRankNameShow(rankingInfo.getRankingNameShow());
            itemRes.setCurRankId(rankingInfo.getRankingId());
            //未晋级榜单特殊标识（瓜分奖池榜单特殊标识）：curRankExtJson:prizePool==1
            itemRes.setCurRankExtJson(rankingInfo.getRankingExtjson());
            itemRes.setCurPhaseId(actRoleRankMap.getPhaseId());
            //日荣耀值达可参与复活赛特殊标识：PhaseInfo:canJump2PhaseInfo===1
            if (rankingInfo.getCurrentPhase() != null) {
                PhaseInfo phaseInfo = PhaseInfo.toPhaseInfo(actId, rankingInfo.getCurrentPhase());
                itemRes.setCurPhaseInfo(phaseInfo);
                itemRes.setLeftSeconds(getPhaseLeftSeconds(phaseInfo, now));
            }
            itemRes.setPassDesc("");


            //看是否已具有复活资格
            String key = Const.addActivityPrefix(actId, RESURRECTION_MEMBERS);
            String groupCode = redisConfigManager.getGroupCode(actId);
            Object resurrect = actRedisDao.getRedisTemplate(groupCode).opsForHash().get(key, memberId);
            if (resurrect != null) {
                //已经具备复活资格
                itemRes.setState(5);
            }
        }

        return true;
    }


}

