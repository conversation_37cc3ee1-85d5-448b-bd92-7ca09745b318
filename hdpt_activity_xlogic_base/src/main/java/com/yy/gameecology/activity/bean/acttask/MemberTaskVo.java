package com.yy.gameecology.activity.bean.acttask;

import com.yy.gameecology.activity.bean.rank.UserRankItem;

import java.util.List;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-10-26 14:44
 **/
public class MemberTaskVo {

    /**
     * 当前时间-毫秒
     */
    private long curTime;

    /**
     * 开始时间-毫秒
     */
    private long startTime;

    private Long taskId;

    /**
     *成员id
     */
    private String memberId;

    /**
     * 公会短号
     */
    private String asid;

    /**
     *任务名称（例如 任务一、任务二、任务三）
     */
    private String title;

    /**
     *主播/公会/厅  名称
     */
    private String name;

    /**
     *子任务
     */
    private List<MemberTaskItemVo> taskItems;

    /**
     *说明规则
     */
    private String awardText;

    /**
     *倒计时，单位 秒
     */
    private long leftSeconds;

    /**
     *贡献榜
     */
    private List<UserRankItem> ranks;

    private Integer sort;

    /**
     * 角色类型 200-主播 400-公会
     */
    private Integer roleType;

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<MemberTaskItemVo> getTaskItems() {
        return taskItems;
    }

    public void setTaskItems(List<MemberTaskItemVo> taskItems) {
        this.taskItems = taskItems;
    }

    public String getAwardText() {
        return awardText;
    }

    public void setAwardText(String awardText) {
        this.awardText = awardText;
    }

    public long getLeftSeconds() {
        return leftSeconds;
    }

    public void setLeftSeconds(long leftSeconds) {
        this.leftSeconds = leftSeconds;
    }

    public List<UserRankItem> getRanks() {
        return ranks;
    }

    public void setRanks(List<UserRankItem> ranks) {
        this.ranks = ranks;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getAsid() {
        return asid;
    }

    public void setAsid(String asid) {
        this.asid = asid;
    }

    public Integer getRoleType() {
        return roleType;
    }

    public void setRoleType(Integer roleType) {
        this.roleType = roleType;
    }

    public long getCurTime() {
        return curTime;
    }

    public void setCurTime(long curTime) {
        this.curTime = curTime;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }
}
