package com.yy.gameecology.activity.service.wzry;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.client.thrift.SaiBaoClient;
import com.yy.gameecology.activity.client.thrift.TurnoverAccountServiceClient;
import com.yy.gameecology.activity.client.thrift.TurnoverProductServiceClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.activity.dao.mysql.WzryDao;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.consts.wzry.WzryGameRecordState;
import com.yy.gameecology.common.consts.wzry.WzryGameState;
import com.yy.gameecology.common.consts.wzry.WzryGameVoState;
import com.yy.gameecology.common.consts.wzry.WzryLimitTeam;
import com.yy.gameecology.common.db.model.gameecology.wzry.*;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.IdGeneratorHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.bean.wzry.WzryAwardRollVo;
import com.yy.gameecology.hdzj.bean.wzry.WzryGameVo;
import com.yy.gameecology.hdzj.bean.wzry.WzryMyGameRecordVo;
import com.yy.gameecology.hdzj.bean.wzry.WzryMyGameVo;
import com.yy.gameecology.hdzj.element.component.attr.WzryGameComponentAttr;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-12 10:44
 **/
@Service
public class WzryGameService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private Locker locker;

    @Autowired
    private GameecologyDao gameecologyDao;

    @Autowired
    private WzryDao wzryDao;

    @Lazy
    @Autowired
    private WzryGameService wzryGameService;


    @Autowired
    private ZhuiwanRiskClient zhuiwanRiskClient;

    @Autowired
    private TurnoverProductServiceClient turnoverProductServiceClient;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ActInfoService actInfoService;

    @Autowired
    private SaiBaoClient saiBaoClient;

    @Autowired
    private TurnoverAccountServiceClient turnoverAccountServiceClient;


    public List<WzryGameVo> buildWzryLast2DayGameVo(WzryGameComponentAttr attr, Date now,
                                                    List<WzryGameView> views
            , Map<Long, WzryGameViewTeam> joinGameRecord
            , Map<String, Long> userSub) {

        List<WzryGameVo> result = Lists.newArrayList();

        //已预约记录
        for (WzryGameView view : views) {
            WzryGameVo vo = new WzryGameVo();
            int remain = 0;
            //5V5要显示剩余多少人开赛
            if (view.getBattleMode().equals(BattleMode.GAME_5V5)) {
                if (view.getEnters() < attr.getCreate5V5GameViewAmount() * BattleMode.GAME_5V5_ENTER_LIMIT) {
                    remain = BattleMode.GAME_5V5_ENTER_LIMIT - (view.getEnters() % BattleMode.GAME_5V5_ENTER_LIMIT);
                }
            }
            vo.setRemaining(remain);
            if (view.getBattleMode().equals(BattleMode.GAME_1V1)) {
                vo.setGameIcon(attr.getGameIcon1V1());
            } else {
                vo.setGameIcon(attr.getGameIcon5V5());
            }
            vo.setGameViewCode(view.getGameViewCode());
            vo.setGameName(view.getGameViewName());
            vo.setBattleMode(view.getBattleMode());
            vo.setStartTime(DateUtil.format(view.getStartTime()));
            int state = WzryGameVoState.NOT_OPEN;
            //赛事未开启，已预约
            if (userSub.containsKey(view.getGameViewCode())) {
                state = WzryGameVoState.NOT_OPEN_SUBSCRIBE;
            }
            //报名中
            if (now.after(view.getSignUpStartTime())) {
                state = WzryGameVoState.SIGN_IN;
            }

            //已报名
            if (joinGameRecord.containsKey(view.getId())) {
                state = WzryGameVoState.SIGNED;
            }
            //5V5，参数人数固定
            else if (view.getBattleMode().equals(BattleMode.GAME_5V5) && remain == 0) {
                state = WzryGameVoState.FULL;
            }
            //1v1用实际开的赛事来判断是否满人
            else if (view.getBattleMode().equals(BattleMode.GAME_1V1) && view.getEnters() >= attr.getCreate1V1GameViewAmount() * BattleMode.GAME_1V1_ENTER_LIMIT) {
                state = WzryGameVoState.FULL;
            }

            //门票价格
            vo.setPrice(calGamePrice(view.getBattleMode(), attr));

            vo.setState(state);

            result.add(vo);
        }

        return result;

    }

    public Map<Long, WzryGameViewTeam> queryUserJoinGameRecord(long actId, List<WzryGameView> sourceGame, long uid) {
        if (CollectionUtils.isEmpty(sourceGame)) {
            return Maps.newHashMap();
        }
        //已报名记录
        List<Long> gameViewId = sourceGame.stream().map(WzryGameView::getId).toList();
        WzryGameViewTeam gameViewTeamWhere = new WzryGameViewTeam();
        gameViewTeamWhere.setActId(actId);
        gameViewTeamWhere.setUid(uid);
        String gameViewTeamAfterWhere = String.format(" and game_view_id in (%s)", StringUtils.join(gameViewId, ","));
        List<WzryGameViewTeam> teams = gameecologyDao.select(WzryGameViewTeam.class, gameViewTeamWhere, gameViewTeamAfterWhere);
        return teams.stream().collect(Collectors.toMap(WzryGameViewTeam::getGameViewId, p -> p));
    }


    /**
     * 我参加的未结算完的赛事，用于快速进入赛事
     *
     * @param startTimeEnd 比赛开始时间截止时间点
     */
    public List<WzryMyGameVo> getWzryMyGameVo(long actId, WzryGameComponentAttr attr, Date startTimeStart, Date startTimeEnd, long uid) {
        List<WzryMyGameVo> result = Lists.newArrayList();

        List<WzryGameView> wzryGameViews = wzryDao.queryMyJoinGameView(actId, uid, startTimeStart, startTimeEnd);
        if (CollectionUtils.isEmpty(wzryGameViews)) {
            return result;
        }

        var gameViewCodes = wzryGameViews.stream().map(WzryGameView::getGameViewCode).collect(Collectors.toList());
        //实际参与的参赛 1v1，系统未完成分配成员前会没数据
        Map<String, WzryGame> wzryGameMap = wzryDao.queryMyJoinGame(actId, uid, gameViewCodes);
        List<Long> gameIds = wzryGameMap.values().stream().map(WzryGame::getId).collect(Collectors.toList());
        List<WzryGameChannel> gameChannels = wzryDao.queryGameChannel(actId, gameIds);
        Map<Long, Integer> userTeamMap = wzryDao.queryUserTeam(actId, gameIds, uid);

        long now = commonService.getNow(actId).getTime();

        for (WzryGameView view : wzryGameViews) {
            WzryMyGameVo vo = new WzryMyGameVo();
            if (wzryGameMap.containsKey(view.getGameViewCode())) {

                WzryGame game = wzryGameMap.get(view.getGameViewCode());
                vo.setGameCode(game.getGameCode());
                vo.setChildid(game.getChildid());
                //赛事已关闭，不展示
                if (!WzryGameState.CAN_SHOW_MY_GAME_STATES.contains(game.getState())) {
                    continue;
                }

                if (userTeamMap.containsKey(game.getId())) {
                    int userTeam = userTeamMap.get(game.getId());
                    Optional<WzryGameChannel> gameChannel = gameChannels.stream()
                            .filter(p -> p.getGameId().equals(game.getId()) && p.getLimitTeam().equals(userTeam)).findFirst();
                    if (gameChannel.isPresent()) {
                        vo.setSid(gameChannel.get().getSid());
                        vo.setSsid(gameChannel.get().getSsid());
                    }
                }
            }
            vo.setBattleMode(view.getBattleMode());
            vo.setStartTime(DateUtil.format(view.getStartTime()));

            //5v5报名后即可进入房间
            int state5V5 = StringUtils.isNotBlank(vo.getChildid()) ? 1 : 0;
            int state1V1 = now >= view.getStartTime().getTime() && StringUtils.isNotBlank(vo.getChildid()) ? 1 : 0;
            int state = BattleMode.GAME_5V5 == view.getBattleMode() ? state5V5 : state1V1;
            //赛事开启状态
            vo.setState(state);

            if (view.getBattleMode().equals(BattleMode.GAME_1V1)) {
                vo.setGameIcon(attr.getGameIcon1V1());
            } else {
                vo.setGameIcon(attr.getGameIcon5V5());
            }

            result.add(vo);
        }

        return result;
    }

    public void joinGame(WzryGameComponentAttr attr, long uid, String app, String ip, String hdid, int clientType, String gameViewCode) {
        long actId = attr.getActId();

        log.info("joinGame begin,actId:{},uid:{},app:{},hdid:{},clientType:{},gameView:{}", actId, uid, app, hdid, clientType, gameViewCode);
        WzryGameView gameView = wzryDao.queryWzryGameView(actId, gameViewCode);
        //---加入赛事前置判断
        checkJoinGame(attr, gameView, uid, app, ip, hdid, clientType);

        Clock clock = new Clock();
        //----赛事锁,执行和写相关操作。这里要锁住，否则5V5队伍分配会有严重并发问题！！！
        String lockName = "joinGame:" + gameViewCode;
        Secret secret = locker.lock(lockName, 6, "", 30,100);
        if (secret == null) {
            throw new SuperException("网络超时，请重试", SuperException.E_DATA_ERROR);
        }
        try {
            wzryGameService.saveJoinGameData(attr, gameView, uid, ip, gameViewCode);

        } catch (Exception e) {
            log.error("save wzrz game error,e:{}", e.getMessage(), e);
            throw e;

        } finally {
            locker.unlock(lockName, secret);
            log.info("joinGame done,gameViewCode:{},uid:{},clock:{}", gameViewCode, uid, clock.tag());
        }
    }

    //TODO 引入宝贝的异常回滚状态机:回滚营收扣费
    @Transactional(rollbackFor = Exception.class)
    public void saveJoinGameData(WzryGameComponentAttr attr, WzryGameView gameView, long uid, String ip, String gameViewCode) {
        long actId = attr.getActId();
        //门票余额扣减、增加业务参赛数据、参赛库存最后一道卡判断
        WzryGameViewTeam addViemTeam = new WzryGameViewTeam();
        addViemTeam.setId(IdGeneratorHelper.genId(WzryGameViewTeam.TABLE_NAME, new Date()));
        addViemTeam.setActId(actId);
        addViemTeam.setGameViewId(gameView.getId());
        addViemTeam.setUid(uid);
        addViemTeam.setCreateTime(new Date());
        addViemTeam.setAllocationTeam(gameView.getBattleMode() == BattleMode.GAME_5V5 ? 1 : 0);
        gameecologyDao.insert(WzryGameViewTeam.class, addViemTeam);

        int addGameViewEntersResult = wzryDao.updateGameViewEnters(gameView.getId(), getMaxJoinGameEnters(gameView.getBattleMode(), attr));
        if (addGameViewEntersResult <= 0) {
            throw new RuntimeException("人数超出限制");
        }

        //5v5，用户参赛后可以提前进入语音房房间，需要实时分配具体赛事
        if (gameView.getBattleMode() == BattleMode.GAME_5V5) {
            WzryGame game = wzryDao.joinAllocation5V5Game(actId, gameViewCode);
            if (game == null) {
                throw new RuntimeException("人数超出限制");
            }

            int alreadyEnters = game.getEnters();
            WzryGameTeam addGameTeam = new WzryGameTeam();
            addGameTeam.setId(IdGeneratorHelper.genId(WzryGameTeam.TABLE_NAME, new Date()));
            addGameTeam.setActId(actId);
            addGameTeam.setGameId(game.getId());
            addGameTeam.setUid(uid);
            addGameTeam.setCreateTime(new Date());
            //参赛队伍分配：上层要控制好赛事粒度参赛的串行，否则这里会有严重的并发问题！！！！！

            //该玩家所属阵营，1表示“A战队”，2表示“B战队”
            addGameTeam.setLimitTeam(WzryLimitTeam.cal5V5LimitTeam(alreadyEnters));
            //座位号，1~5
            addGameTeam.setSeatId((alreadyEnters / 2) + 1);
            addGameTeam.setInGame(0);
            gameecologyDao.insert(WzryGameTeam.class, addGameTeam);

            int addGameEntersResult = wzryDao.updateGameEnters(game.getId(), getMaxJoinGameEnters(gameView.getBattleMode(), attr));
            if (addGameEntersResult <= 0) {
                throw new RuntimeException("人数超出限制");
            }
        }
        //1v1初始化人员，由系统打乱分配到各场赛事 分配具体赛事
        else if (gameView.getBattleMode() == BattleMode.GAME_1V1) {
            log.info("1v1 sys join game");

        } else {
            throw new RuntimeException("not support battle mode");
        }

        //扣费
        int price = calGamePrice(gameView.getBattleMode(), attr);
        String seq = buildTurnoverJoinGameConsumeSeq(gameView.getId(), uid);
        String desc = "参赛抵扣门票";
        Map<String, Object> ext = ImmutableMap.of("cost", price);
        var result = turnoverProductServiceClient.consumeProductNew(uid, attr.getPiaoProductId(), attr.getPiaoProductType(), price, attr.getTurnoverAppId(), seq, desc, JSON.toJSONString(ext), ip);
        //TODO -405 SEQ 重复
        if (result == null || result.getCode() != 1) {
            log.error("turnover consumeProductNew error,result:{}", result);
            throw new RuntimeException("consume error seq:" + seq);
        }
    }

    public int calGamePrice(int battleMode, WzryGameComponentAttr attr) {
        return battleMode == BattleMode.GAME_1V1 ? attr.getPrice1V1() : attr.getPrice5V5();
    }

    public String buildTurnoverJoinGameConsumeSeq(Long gameViewId, long uid) {
        return String.format("joingame:%s:%s", gameViewId, uid);
    }


    private void checkJoinGame(WzryGameComponentAttr attr, WzryGameView gameView, long uid, String app, String ip, String hdid, int clientType) {
        long actId = attr.getActId();
        String riskStrategyKey = attr.getRiskStrategyKey();
        Date now = commonService.getNow(actId);

        //活动时间判断
        if (!actInfoService.inActShowTime(actId)) {
            throw new SuperException("非活动时间", SuperException.E_DATA_ERROR);
        }

        //风控判断
        if (StringUtil.isNotBlank(riskStrategyKey) && zhuiwanRiskClient.hitRiskOfApp(uid, hdid, ip, app, "", clientType, riskStrategyKey)) {
            log.warn("joinGame hitRiskOfApp,actId:{},uid:{}", actId, uid);
            throw new SuperException("账号存在异常，参赛失败", SuperException.E_DATA_ERROR);
        }

        //业务数据前置判断：赛事时间有效性判断、赛事状态判断、 赛事成员是否已满判断、重复参赛判断
        if (gameView == null) {
            throw new SuperException("赛事异常，参赛失败", SuperException.E_DATA_ERROR);
        }
        if (now.after(gameView.getSignUpEndTime())) {
            throw new SuperException("报名比赛时间已截止", SuperException.TIME_END);
        }

        WzryGameViewTeam teamWhere = new WzryGameViewTeam();
        teamWhere.setActId(actId);
        teamWhere.setUid(uid);
        teamWhere.setGameViewId(gameView.getId());
        if (gameecologyDao.count(WzryGameViewTeam.class, teamWhere) > 0) {
            throw new SuperException("已报名成功，请勿重复参赛", SuperException.E_DATA_ERROR);
        }

        //参赛人数预判断，减少参赛失败率
        int getMaxJoinGameEnters = getMaxJoinGameEnters(gameView.getBattleMode(), attr);
        if (gameView.getEnters() >= getMaxJoinGameEnters) {
            throw new SuperException("参赛人数已满", SuperException.E_DATA_ERROR);
        }


        //营收外部判断：门票余额判断
        long balance = turnoverAccountServiceClient.getUserAccountFilterByUidSlave(uid, attr.getTurnoverAppId(), attr.getPiaoCurrentType());
        if (balance < calGamePrice(gameView.getBattleMode(), attr)) {
            throw new SuperException("门票余额不足", SuperException.E_DATA_ERROR);
        }

    }

    public int getMaxJoinGameEnters(int battleMode, WzryGameComponentAttr attr) {
        if (battleMode == BattleMode.GAME_5V5) {
            return attr.getCreate5V5GameViewAmount() * BattleMode.GAME_5V5_ENTER_LIMIT;
        } else if (battleMode == BattleMode.GAME_1V1) {
            return attr.getCreate1V1GameViewAmount() * BattleMode.GAME_1V1_ENTER_LIMIT;
        }
        throw new RuntimeException("getMaxJoinGameEnters not support,battleMode:" + battleMode);
    }

    public String getJumpGame(long actId, String gameCode, long uid) {
        WzryGame where = new WzryGame();
        where.setGameCode(gameCode);
        where.setActId(actId);
        WzryGame wzryGame = gameecologyDao.selectOne(WzryGame.class, where, "");
        if (wzryGame == null) {
            throw new SuperException("赛事未创建", SuperException.E_DATA_ERROR);
        }

        if (StringUtil.isEmpty(wzryGame.getChildid())) {
            throw new SuperException("赛事未开启", SuperException.E_DATA_ERROR);
        }
        if (WzryGameState.CLOSE_STATES.contains(wzryGame.getState())) {
            throw new SuperException("赛事已结束", SuperException.E_DATA_ERROR);
        }

        Date now = commonService.getNow(actId);
        if (now.before(wzryGame.getStartTime())) {
            throw new SuperException("赛事未开启", SuperException.E_DATA_ERROR);
        }

        //参赛资格判断
        WzryGameTeam wzryGameTeamWhere = new WzryGameTeam();
        wzryGameTeamWhere.setGameId(wzryGame.getId());
        wzryGameTeamWhere.setUid(uid);
        WzryGameTeam wzryGameTeam = gameecologyDao.selectOne(WzryGameTeam.class, wzryGameTeamWhere, "");
        if (wzryGameTeam == null) {
            throw new SuperException("您未报名本场比赛", SuperException.E_DATA_ERROR);
        }
        //拼接赛事入口
        var joinResult = saiBaoClient.joinRoom(wzryGame.getChildid(), uid, wzryGameTeam.getSeatId(), wzryGameTeam.getLimitTeam());
        if (StringUtil.isEmpty(joinResult.getRoomUrl()) || StringUtil.isEmpty(joinResult.getRoomParameters())) {
            log.error("sao bao join room error,actId:{},game code:{},uid:{}", actId, gameCode, uid);
            throw new SuperException("进入房间异常", SuperException.E_DATA_ERROR);
        }
        return joinResult.getRoomUrl() + wzryGame.getChildid() + "?" + joinResult.getRoomParameters();
    }


    public WzryMyGameVo queryWzryMyGameVo(long actId, String gameCode, long sid, long ssid, long uid) {
        WzryGame wzryGame = findWzryGame(actId, gameCode, sid, ssid);
        if (wzryGame == null) {
            throw new SuperException("赛事不存在", SuperException.E_DATA_ERROR);
        }

        WzryMyGameVo vo = new WzryMyGameVo();
        vo.setGameCode(wzryGame.getGameCode());
        vo.setBattleMode(wzryGame.getBattleMode());
        vo.setStartTime(DateUtil.format(wzryGame.getStartTime()));
        vo.setSid(0L);
        vo.setSsid(0L);
        vo.setState(wzryGame.getState() == WzryGameState.CLOSE ? WzryGameState.CLOSE : 0);
        return vo;
    }

    private WzryGame findWzryGame(long actId, String gameCode, long sid, long ssid) {
        if (StringUtil.isNotBlank(gameCode)) {
            return wzryDao.queryWzryGame(actId, gameCode);
        }

        WzryGameChannel wzryGameChannelWhere = new WzryGameChannel();
        wzryGameChannelWhere.setActId(actId);
        wzryGameChannelWhere.setSid(sid);
        wzryGameChannelWhere.setSsid(ssid);

        List<WzryGameChannel> wzryGameChannels = gameecologyDao.select(WzryGameChannel.class, wzryGameChannelWhere, "");
        List<Long> gameIds = wzryGameChannels.stream().map(WzryGameChannel::getGameId).distinct().toList();
        if (CollectionUtils.isEmpty(gameIds)) {
            return null;
        }
        return wzryDao.queryLastStartTimeWzryGame(actId, gameIds);
    }

    public List<WzryMyGameRecordVo> getWzryMyGameCloseGame(WzryGameComponentAttr attr, Long actId, long uid, int page, int pageSize) {
        List<WzryGame> wzryGames = wzryDao.getWzryMyGameCloseGame(actId, uid, page, pageSize);
        if (CollectionUtils.isEmpty(wzryGames)) {
            return Lists.newArrayList();
        }
        List<Long> gameIds = wzryGames.stream().map(WzryGame::getId).toList();
        Map<Long, Integer> userTeams = wzryDao.queryUserTeam(actId, gameIds, uid);

        List<WzryMyGameRecordVo> vos = Lists.newArrayList();
        for (WzryGame game : wzryGames) {
            WzryMyGameRecordVo vo = new WzryMyGameRecordVo();
            vo.setGameId(game.getId());
            if (game.getBattleMode().equals(BattleMode.GAME_1V1)) {
                vo.setGameIcon(attr.getGameIcon1V1());
            } else {
                vo.setGameIcon(attr.getGameIcon5V5());
            }
            vo.setGameName(game.getGameName());
            vo.setBattleMode(game.getBattleMode());
            vo.setStartTime(DateUtil.format(game.getStartTime()));
            if (game.getState() != WzryGameState.CLOSE) {
                vo.setState(WzryGameRecordState.ABNORMAL);
            } else {
                Integer userTeamInfo = userTeams.get(game.getId());
                //胜利判断
                if (game.getWinnerTeam().equals(userTeamInfo)) {
                    vo.setState(WzryGameRecordState.WIN);
                    vo.setAward(game.getAward());
                } else {
                    vo.setState(WzryGameRecordState.FAILED);
                }
            }

            vos.add(vo);
        }

        return vos;
    }


    /**
     * 王者荣耀赏金获得滚屏信息
     */
    @Cached(timeToLiveMillis = CacheTimeout.AWARD_ROLL)
    public List<WzryAwardRollVo> getWzryAwardRollVos(long actId, int batchSize) {
        List<WzryAwardRollVo> result = Lists.newArrayList();

        WzryGame where = new WzryGame();
        where.setActId(actId);
        where.setState(WzryGameState.CLOSE);
        String afterWhere = " order by start_time desc limit 15";
        List<WzryGame> closeGames = gameecologyDao.select(WzryGame.class, where, afterWhere);

        if (CollectionUtils.isEmpty(closeGames)) {
            return result;
        }

        List<Long> gameIds = closeGames.stream().map(WzryGame::getId).toList();
        WzryGameTeam gameTeamWhere = new WzryGameTeam();
        gameTeamWhere.setActId(actId);
        String teamAfterWhere = String.format(" and game_id in (%s) order by create_time desc ", StringUtils.join(gameIds, ","));
        List<WzryGameTeam> gameTeams = gameecologyDao.select(WzryGameTeam.class, gameTeamWhere, teamAfterWhere);

        List<Long> uids = gameTeams.stream().map(WzryGameTeam::getUid).toList();
        Map<Long, UserBaseInfo> userBaseInfoMap = commonService.batchGetUserInfos(uids, false);
        Map<Long, WzryGame> gameMap = closeGames.stream().collect(Collectors.toMap(WzryGame::getId, p -> p));
        int alreadyAdd = 0;
        for (WzryGameTeam team : gameTeams) {
            WzryGame wzryGame = gameMap.get(team.getGameId());
            boolean inWinTeam = wzryGame.getWinnerTeam().equals(team.getLimitTeam());
            if (inWinTeam && wzryGame.getAward() > 0 && userBaseInfoMap.containsKey(team.getUid())) {
                alreadyAdd++;

                WzryAwardRollVo vo = new WzryAwardRollVo();
                vo.setAward(Convert.toString(wzryGame.getAward()));
                UserBaseInfo userBaseInfo = userBaseInfoMap.get(team.getUid());
                vo.setNickName(StringUtil.left(userBaseInfo.getNick(), 1) + "***");
                result.add(vo);
            }
            if (alreadyAdd >= batchSize) {
                break;
            }
        }

        return result;
    }


    @Cached(timeToLiveMillis = 5 * 1000)
    public boolean existRoomPoll(long sid, long ssid) {
        return wzryDao.existRoomPoll(sid, ssid);
    }

    @Cached(timeToLiveMillis = 5 * 1000)
    public boolean existRoomChannel(long actId, long sid, long ssid) {
        return wzryDao.existRoomChannel(actId, sid, ssid);
    }
}
