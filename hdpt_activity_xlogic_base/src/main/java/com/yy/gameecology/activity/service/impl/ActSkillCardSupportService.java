package com.yy.gameecology.activity.service.impl;

import com.yy.gameecology.activity.rolebuilder.RoleBuilder;
import com.yy.gameecology.activity.rolebuilder.impl.*;
import com.yy.gameecology.activity.service.ActSupportService;
import com.yy.gameecology.common.bean.Template;
import com.yy.thrift.act_ext_support.MemberItemInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/10/19 14:35
 **/
@Component
public class ActSkillCardSupportService implements ActSupportService {
    private static Logger logger = LoggerFactory.getLogger(ActSkillCardSupportService.class);

    @Override
    public Map<String, Map<String, MemberItemInfo>> queryMember(long actId, long rankId, Map<String, List<String>> memberIdMap) {
        return null;
    }

    @Override
    public RoleBuilder getUserRankBuilder() {
        return userRankBuilder;
    }

    @Override
    public RoleBuilder getBabyRankBuilder() {
        return babyRankBuilder;
    }

    @Override
    public RoleBuilder getHostRankBuilder() {
        return babyRankBuilder;
    }

    @Override
    public RoleBuilder getGuildRankBuilder() {
        return guildRankBuilder;
    }

    @Override
    public RoleBuilder getSubGuildRankBuilder() {
        return subGuidRankBuilder;
    }

    @Override
    public RoleBuilder getFamilyRankBuilder() {
        return familyRankBuilder;
    }

    @Override
    public RoleBuilder getRoomRankBuilder() {
        return roomRankBuilder;
    }

    @Override
    public RoleBuilder getTeamRankBuilder() {
        return null;
    }

    private RoleBuilder userRankBuilder = new UserRoleBuilder();
    private RoleBuilder babyRankBuilder = new BabyRoleBuilder(Template.peiwan);
    private RoleBuilder guildRankBuilder = new GuildRoleBuilder();
    private RoleBuilder subGuidRankBuilder = new SubGuildRoleBuilder();
    private RoleBuilder familyRankBuilder = new FamilyRoleBuilder();

    private RoleBuilder roomRankBuilder = new RoomRoleBuilder();
}
