package com.yy.gameecology.activity.worker.server.yrpc;

import com.yy.gameecology.activity.config.svcsdk.SvcSDKCallback;
import com.yy.gameecology.common.consts.Const;
import com.yy.protocol.pb.svcsdkproxy.SvcSdkGateway;
import com.yy.protocol.pb.svcsdkproxy.SvcSdkProxyCallbackProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;

@Slf4j
@ConditionalOnExpression(Const.EXPRESSION_NOT_LOCAL + " and " + Const.EXPRESSION_NOT_HISTORY)
@Service(protocol = "yrpc", registry = "yrpc-reg")
public class SvcSdkProxyCallbackProviderImpl implements SvcSdkProxyCallbackProvider {

    @Autowired
    private SvcSDKCallback svcSDKCallback;

    @Override
    public SvcSdkGateway.SvcSdkGatewayCallbackRsp callback(SvcSdkGateway.SvcSdkGatewayCallbackReq req) {
        svcSDKCallback.onRecvProxyMsg(req.getMsg().getSuid(), req.getMsg().getUid(), req.getMsg().getSid(), req.getMsg().getSsid(), req.getMsg().getData().toByteArray(), req.getMsg().getExtMap());
        log.info("SvcSdkProxyCallbackProviderImpl is completed uid:{}", req.getMsg().getUid());
        return SvcSdkGateway.SvcSdkGatewayCallbackRsp.newBuilder().setMsgId(req.getMsgId()).setCode(0).setMessage("success").build();
    }
}
