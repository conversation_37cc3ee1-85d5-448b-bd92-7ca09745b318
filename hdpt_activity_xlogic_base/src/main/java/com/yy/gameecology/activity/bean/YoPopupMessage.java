package com.yy.gameecology.activity.bean;

import com.yy.gameecology.common.enums.YoPopupMessagePlatform;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.util.Map;

/**
 * push消息
 */
@Getter
@Builder
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@ToString
public class YoPopupMessage {

    @NonFinal
    @Default
    int platform = YoPopupMessagePlatform.ANDROID.ordinal();

    String app;
    /**
     * icon
     */
    String icon;

    /**
     * 标题（纯文字）
     */
    String title;

    /**
     * 内容，支持：
     * <b></b>
     * <font color="#998989"></font>
     */
    String content;

    /**
     * 游戏内toast内容
     */
    String innerContent;

    /**
     * 跳转链接
     */
    String link;

    /**
     * 背景图，可为空，为空时纯白底
     */
    String background;

    /**
     * 拓展字段
     */
    Map<String, String> extend;
}
