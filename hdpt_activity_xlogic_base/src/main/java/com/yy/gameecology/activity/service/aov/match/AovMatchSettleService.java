package com.yy.gameecology.activity.service.aov.match;

import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.aov.AovConst;
import com.yy.gameecology.common.db.mapper.aov.AovMatchNodeExtMapper;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseMapper;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseRoundExtMapper;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseTeamMapper;
import com.yy.gameecology.common.db.model.gameecology.aov.AovMatchNode;
import com.yy.gameecology.common.db.model.gameecology.aov.AovPhase;
import com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseRound;
import com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseTeam;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.hdzj.element.component.attr.AovMatchComponentAttr;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class AovMatchSettleService {

    @Autowired
    private Locker locker;

    @Resource
    private AovPhaseMapper aovPhaseMapper;

    @Resource
    private AovPhaseTeamMapper aovPhaseTeamMapper;

    @Resource
    private AovPhaseRoundExtMapper aovPhaseRoundExtMapper;

    @Resource
    private AovMatchNodeExtMapper aovMatchNodeExtMapper;

    @Autowired
    private AovMatchTransService aovMatchTransService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    /**
     * 报名成功后，初始化node
     * @param attr
     * @param now
     */
    public void initMatchNodes(AovMatchComponentAttr attr, Date now) {
        AovPhase phase = aovPhaseMapper.selectProcessingPhase(now, attr.getActId(), AovConst.PhaseState.SUCC);
        if (phase == null) {
            return;
        }

        if (now.before(phase.getSignupEndTime())) {
            return;
        }

        List<AovPhaseTeam> teams = aovPhaseTeamMapper.selectValidTeamIds(phase.getId());
        if (CollectionUtils.isEmpty(teams)) {
            return;
        }

        teams.sort((t1, t2) -> Math.toIntExact((t2.getTeamScore() / t2.getMemberCnt()) - (t1.getTeamScore() / t1.getMemberCnt())));

        List<AovPhaseRound> rounds = aovPhaseRoundExtMapper.selectRoundsByPhaseId(phase.getId(), AovConst.RoundState.INIT);
        if (CollectionUtils.isEmpty(rounds)) {
            return;
        }

        aovMatchTransService.saveMatchNodes(phase, rounds, teams);
    }

    /**
     * 调整时间结束后，将状态改为比赛状态
     * @param attr
     * @param now
     */
    public void closeAdjustingNodes(AovMatchComponentAttr attr, Date now) {
        AovPhase phase = aovPhaseMapper.selectProcessingPhase(now, attr.getActId(), AovConst.PhaseState.INITIALIZED);
        if (phase == null) {
            return;
        }

        if (now.before(phase.getAdjustEndTime())) {
            return;
        }

        List<AovPhaseRound> rounds = aovPhaseRoundExtMapper.selectRoundsByPhaseId(phase.getId(), AovConst.RoundState.ADJUSTING);
        if (CollectionUtils.isEmpty(rounds)) {
            return;
        }

        for (AovPhaseRound round : rounds) {
            final String lockName = "close_adjusting_round:" + round.getId();
            Secret secret = locker.lock(lockName, 60);
            if (locker == null) {
                continue;
            }

            try {
                List<AovMatchNode> nodes = aovMatchNodeExtMapper.selectNodeByRoundId(phase.getId(), round.getId(), AovConst.MatchNodeState.ADJUSTING);
                boolean valid = nodes.stream().allMatch(node -> node.getTeamId() > 0 || node.getTeamId() == -1);
                if (!valid) {
                    //TODO: alert
                    log.error("adjusting node invalid");
                    continue;
                }

                int expectedNodeCount = 1 << (round.getRoundNum() - 1);
                if (nodes.size() != expectedNodeCount) {
                    log.warn("closeAdjustingNodes roundNum:{} expected node count:{}, actual:{}", round.getRoundNum(), expectedNodeCount, nodes.size());
                    continue;
                }
                aovMatchTransService.saveCloseAdjustingRound(attr, round, nodes, now);
            } finally {
                locker.unlock(lockName, secret);
            }
        }
    }

    /**
     * 晋级后或者结束adjusting后，创建 game
     * @param attr
     * @param now
     */
    public void createRoundGames(AovMatchComponentAttr attr, Date now) {
        AovPhase phase = aovPhaseMapper.selectProcessingPhase(now, attr.getActId(), AovConst.PhaseState.INITIALIZED);
        if (phase == null) {
            return;
        }

        if (now.before(phase.getAdjustEndTime())) {
            return;
        }

        List<AovPhaseRound> rounds = aovPhaseRoundExtMapper.selectRoundsByPhaseId(phase.getId(), AovConst.RoundState.GAME_CREATABLE);
        if (CollectionUtils.isEmpty(rounds)) {
            return;
        }

        for (AovPhaseRound round : rounds) {
            final String lockName = "create_round_games:" + round.getId();
            Secret secret = locker.lock(lockName, 60);
            if (locker == null) {
                continue;
            }

            try {
                // 自动创建比赛
                if (round.getCreateGame() == 1) {
                    List<AovMatchNode> nodes = aovMatchNodeExtMapper.selectNodeByRoundId(phase.getId(), round.getId(), AovConst.MatchNodeState.GAME_CREATABLE);
                    int expectedNodeCount = 1 << (round.getRoundNum() - 1);
                    if (nodes.size() != expectedNodeCount) {
                        log.error("createRoundGames roundNum:{} expected node count:{}, actual:{}", round.getRoundNum(), expectedNodeCount, nodes.size());
                        continue;
                    }
                    aovMatchTransService.saveCreateAutoGames(attr, round, nodes, now);

                    continue;
                }

                // 手动创建比赛
                List<AovMatchNode> nodes = aovMatchNodeExtMapper.selectNodeByRoundId(phase.getId(), round.getId(), AovConst.MatchNodeState.GAME_CREATABLE);
                if (CollectionUtils.isEmpty(nodes)) {
                    int rs = aovPhaseRoundExtMapper.updateRoundState(round.getId(), AovConst.RoundState.GAME_CREATABLE, AovConst.RoundState.GAME_CREATED);
                    log.info("createRoundGames found game creatable nodes is empty update round state with roundId:{} rs:{}", round.getId(), rs);
                    continue;
                }

                aovMatchTransService.saveCreateManualGames(attr, round, nodes, now);
            } finally {
                locker.unlock(lockName, secret);
            }
        }
    }

    /**
     * 晋级round
     * @param attr
     * @param now
     */
    public void settleRounds(AovMatchComponentAttr attr, Date now) {
        Long phaseId = aovPhaseMapper.selectProcessingPhaseId(attr.getActId(), now, AovConst.PhaseState.INITIALIZED);
        if (phaseId == null || phaseId < 0) {
            return;
        }

        List<AovPhaseRound> rounds = aovPhaseRoundExtMapper.selectRoundsByPhaseId(phaseId, AovConst.RoundState.GAME_CREATED);
        if (CollectionUtils.isEmpty(rounds)) {
            return;
        }

        for (AovPhaseRound round : rounds) {
            settleRound(attr, round, now);
        }
    }

    public void settleRound(AovMatchComponentAttr attr, AovPhaseRound round, Date now) {
        // 上锁
        final String lockName = "aov_settle_round:" + round.getId();
        Secret secret = locker.lock(lockName, 60);
        if (secret == null) {
            return;
        }

        try {
            int settleReserveMin = attr.getSettleReserveMin();
            Date settleDeadTime = DateUtils.addMinutes(round.getEndTime(), -settleReserveMin);
            if (now.after(settleDeadTime)) {
                log.error("round is after settle dead time roundNum:{} endTime:{}", round.getRoundNum(), round.getEndTime());
                String msg = commonService.buildActRuliuMsg(attr.getActId(), true, "轮次结束异常", "轮次【" + round.getRoundNum() + "】距离结束时间少于" + settleReserveMin + "尚未完成结算");
                baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_ACT_MOBILE_USER_GROWTH, msg, Collections.emptyList());
            }

            AovPhaseRound nextRound = aovPhaseRoundExtMapper.selectRound(round.getPhaseId(), round.getRoundNum() - 1);

            int requiredNodeCount = 1 << (round.getRoundNum() - 1);
            List<AovMatchNode> matchNodes = aovMatchNodeExtMapper.selectNodeByRoundId(round.getPhaseId(), round.getId(), AovConst.MatchNodeState.RESULTED);
            if (matchNodes.size() < requiredNodeCount) {
                return;
            }

            Map<Integer, AovMatchNode> nodeMap = new HashMap<>(matchNodes.size());
            for (AovMatchNode node : matchNodes) {
                nodeMap.put(node.getNodeIndex(), node);
            }

            if (MapUtils.isEmpty(nodeMap)) {
                return;
            }

            aovMatchTransService.settleRound(attr, round, nextRound, nodeMap, now);
        } finally {
            locker.unlock(lockName, secret);
        }
    }
}
