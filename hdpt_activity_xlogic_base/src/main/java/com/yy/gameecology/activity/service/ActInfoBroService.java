package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.commons.ActAttrCont;
import com.yy.protocol.pb.GameecologyActivity.Act202008_ActInfo;
import com.yy.protocol.pb.GameecologyActivity.GameEcologyMsg;
import com.yy.protocol.pb.GameecologyActivity.PacketType;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztranking.BusiId;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * desc:活动配置信息广播服务
 *
 * @createBy 曾文帜
 * @create 2020-08-10 20:52
 **/
@Service
public class ActInfoBroService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private CommonService commonService;


    @Autowired
    private SvcSDKService svcSDKService;

    @Autowired
    private WhiteListService whiteListService;

    // 陪玩 活动配置广播
    private void broPeiWanActInfo(ActivityInfoVo activityInfoVo) {

        long actId = activityInfoVo.getActId();
        List<ChannelInfo> channelInfos = whiteListService.getPwAllChannel(actId);
        log.info("begin bro,actId:{},size:{}", actId, channelInfos.size());
        broActInfo(actId, channelInfos);
    }

    private void broSkillCardActInfo(ActivityInfoVo activityInfoVo) {
        long actId = activityInfoVo.getActId();
        List<ChannelInfo> channelInfos = commonService.getSkillCardAllChannel();
        log.info("begin broSkillCardActInfo,actId:{},size:{}", actId, channelInfos.size());
        broActInfo(actId, channelInfos);
    }

    private void broExtChannelActInfo(ActivityInfoVo activityInfoVo){
        long actId = activityInfoVo.getActId();
        List<ChannelInfo> channelInfos = whiteListService.getAllCustomerChannel(actId);
        log.info("begin broExtChannelActInfo,actId:{},size:{}", actId, channelInfos.size());
        broActInfo(actId, channelInfos);
    }

    // 约战 宝贝 交友 陪玩 活动配置广播
    public void broadcastActInfo(long actId) {
        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
        if (activityInfoVo == null || activityInfoVo.getActId() == 0) {
            log.warn("actInfo not found,actId:{}", actId);
            return;
        }
        //展示时间开始前15分钟就开始广播
        Long currentTime = activityInfoVo.getCurrentTime();
        final long fifteenMins = 15 * DateUtils.MILLIS_PER_MINUTE;
        if (currentTime < (activityInfoVo.getBeginTimeShow() - fifteenMins)) {
            log.warn("currentTime:{} not in begin time show for actId:{}", currentTime, actId);
            return;
        }

        final long twentyFiveHours = 25 * DateUtils.MILLIS_PER_HOUR;
        //90000_000
        if (currentTime - activityInfoVo.getEndTime() > twentyFiveHours) {
            log.warn("currentTime:{} not in end time show for actId:{}", currentTime, actId);
            return;
        }

        Act202008_ActInfo.Builder actInfo = Act202008_ActInfo.newBuilder()
                .setActId(activityInfoVo.getActId())
                .setBeginTime(activityInfoVo.getBeginTime() / 1000)
                .setEndTime(activityInfoVo.getEndTime() / 1000)
                .setStartShowTime(activityInfoVo.getBeginTimeShow() / 1000)
                .setEndShowTime(activityInfoVo.getEndTimeShow() / 1000)
                .setCurrentTime(currentTime / 1000);

        GameEcologyMsg msg = GameEcologyMsg.newBuilder()
                .setUri(PacketType.kAct202008_ActInfo_VALUE)
                .setAct202008ActInfo(actInfo)
                .build();

        List<Integer> broBusiIds = getBroBusiIds(activityInfoVo);

        for (Integer busiId : broBusiIds) {
            if (busiId == BusiId.MAKE_FRIEND.getValue()) {
                svcSDKService.broadcastTemplate(Template.Jiaoyou, msg);
            } else if (busiId == BusiId.YUE_ZHAN.getValue()) {
                svcSDKService.broadcastTemplate(Template.Yuezhan, msg);
            } else if (busiId == BusiId.GAME_BABY.getValue()) {
                svcSDKService.broadcastTemplate(Template.Gamebaby, msg);
            } else if (busiId == BusiId.PEI_WAN.getValue()) {
                broPeiWanActInfo(activityInfoVo);
            } else if (busiId == BusiId.SKILL_CARD.getValue()) {
                broSkillCardActInfo(activityInfoVo);
            }
        }

        //额外扩展频道
        broExtChannelActInfo(activityInfoVo);

        log.info("broadcastActInfo done@currentTime:{}, actId:{}, broBusiIds:{}", currentTime, actId, JSON.toJSONString(broBusiIds));
    }

    /**
     * 获取活动广播的busiId
     *
     * @param activityInfoVo
     * @return
     */
    public List<Integer> getBroBusiIds(ActivityInfoVo activityInfoVo) {
        Long actId = activityInfoVo.getActId();
        Long busiId = activityInfoVo.getBusiId();
        String broBusiIds = commonService.getActAttr(actId, ActAttrCont.BRO_BUSI_IDS);
        if (StringUtils.isNotBlank(broBusiIds)) {
            return Stream.of(broBusiIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        }

        if (BusiId.GAME_ECOLOGY.getValue() == busiId.intValue()) {
            return Lists.newArrayList(BusiId.YUE_ZHAN.getValue(), BusiId.MAKE_FRIEND.getValue(), BusiId.GAME_BABY.getValue());
        } else {
            return Collections.singletonList(busiId.intValue());
        }
    }


    public void broActInfo(long actId, List<ChannelInfo> channelInfos) {
        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
        if (activityInfoVo == null) {
            log.warn("broActInfo return,act is null,actId:{}", actId);
            return;
        }
        if (CollectionUtils.isEmpty(channelInfos)) {
            log.warn("broActInfo return,channelInfos is null,actId:{}", actId);
            return;
        }


        //广播延迟1天停止广播
        if (activityInfoVo.getCurrentTime() - activityInfoVo.getEndTimeShow() > DateUtils.MILLIS_PER_DAY) {
            log.info("act is end:{}", actId);
            return;
        }

        Act202008_ActInfo.Builder actInfo = Act202008_ActInfo.newBuilder()
                .setActId(activityInfoVo.getActId())
                .setBeginTime(activityInfoVo.getBeginTime() / 1000)
                .setEndTime(activityInfoVo.getEndTime() / 1000)
                .setStartShowTime(activityInfoVo.getBeginTimeShow() / 1000)
                .setEndShowTime(activityInfoVo.getEndTimeShow() / 1000)
                .setCurrentTime(activityInfoVo.getCurrentTime() / 1000);

        GameEcologyMsg msg = GameEcologyMsg.newBuilder()
                .setUri(PacketType.kAct202008_ActInfo_VALUE)
                .setAct202008ActInfo(actInfo)
                .build();


        for (ChannelInfo channelInfo : channelInfos) {
            if (channelInfo.getSsid() == 0) {
                svcSDKService.broadcastTop(channelInfo.getSid(), msg);
            } else {
                svcSDKService.broadcastSub(channelInfo.getSid(), channelInfo.getSsid(), msg);
            }
        }

        log.info("bro done,actId:{},channel size:{}", actId, channelInfos.size());
    }
}
