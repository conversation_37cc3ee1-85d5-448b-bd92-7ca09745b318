package com.yy.gameecology.activity.bean.mq.hdzk;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-07 17:20
 **/
@Data
public class PepcGameEndEvent extends HdzkBaseEvent {

    public PepcGameEndEvent() {
        super(HdzkBaseEvent.PEPC_GAME_END);
    }

    /**
     * 活动id
     */
    private long actId;
    /**
     * 赛事id
     */
    private long gameId;

    /**
     * game_view冗余字段：1-四排，2-单排
     */
    private int joinFunc;
    /**
     * 赛事状态
     */
    private int state;

    /**
     * 比赛开始时间(毫秒)
     */
    private long startTime;

    /**
     *进入游戏用户
     */
    private List<Long> inGameUid = Lists.newArrayList();

    /**
     *进入h5房间用户
     */
    private List<Long> jumpRoomUid = Lists.newArrayList();
}
