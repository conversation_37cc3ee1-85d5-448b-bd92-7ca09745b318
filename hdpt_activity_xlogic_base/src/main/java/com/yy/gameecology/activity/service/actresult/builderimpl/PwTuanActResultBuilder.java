package com.yy.gameecology.activity.service.actresult.builderimpl;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.service.actresult.ActResultMemberLoader;
import com.yy.gameecology.common.consts.HdztRoleId;
import com.yy.thrift.act_ext_support.MemberItemInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-11-17 16:12
 **/
@Component
public class PwTuanActResultBuilder extends BuilderBase implements ActResultMemberLoader {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    @Override
    public Map<String, Map<String, MemberInfo>> loadMemberInfo(long actId, long type, long roleType, List<String> memberIds) {
        Map<String, List<String>> memberIdMap = Maps.newHashMap();
        memberIdMap.put(HdztRoleId.PW_TUAN + "", memberIds);
        Map<String, Map<String, MemberItemInfo>> pwChannelMap = actPwSupportService.queryMember(0, 0, memberIdMap);
        Map<String, MemberItemInfo> pwTuanInfo = pwChannelMap.getOrDefault(HdztRoleId.PW_TUAN + "", Maps.newHashMap());

        Map<String, MemberInfo> memberInfoMap = Maps.newHashMap();
        for (String memberId : pwTuanInfo.keySet()) {
            MemberItemInfo memberItemInfo = pwTuanInfo.get(memberId);

            MemberInfo memberInfo = new MemberInfo();
            memberInfo.setName(memberItemInfo.getBaseFieldMemberName());
            memberInfo.setLogo(memberItemInfo.getBaseFieldMemberUrl());
            memberInfo.setAsid(memberItemInfo.getExt().getOrDefault("asid", ""));
            memberInfoMap.put(memberId, memberInfo);
        }

        return ImmutableMap.of(builderKey(type, roleType), memberInfoMap);
    }
}
