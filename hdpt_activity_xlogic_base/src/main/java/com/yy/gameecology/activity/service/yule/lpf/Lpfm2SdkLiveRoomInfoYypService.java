package com.yy.gameecology.activity.service.yule.lpf;

import com.yy.gameecology.activity.service.yule.lpf.domain.pb.lpfm2YYP.GetPublishInfoByUidsReq;
import com.yy.gameecology.activity.service.yule.lpf.domain.pb.lpfm2YYP.GetPublishInfoByUidsRsp;
import org.apache.dubbo.common.annotation.Yrpc;

public interface Lpfm2SdkLiveRoomInfoYypService {

    @Yrpc(functionName = "GetPublishInfoByUids", reqUri = 52722, resUri = 52722)
    public GetPublishInfoByUidsRsp getPublishInfoByUids(GetPublishInfoByUidsReq req);
}
