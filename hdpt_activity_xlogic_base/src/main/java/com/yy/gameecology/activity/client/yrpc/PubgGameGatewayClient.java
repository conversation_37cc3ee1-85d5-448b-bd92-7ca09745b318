package com.yy.gameecology.activity.client.yrpc;

import com.google.common.collect.Lists;
import com.yy.gameecology.common.consts.pubg.PubgConst;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.zhuiya.game.gateway.gen.pb.GameGateway;
import com.yy.zhuiya.game.gateway.gen.pb.GameGatewayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class PubgGameGatewayClient {

    @Reference(protocol = "yrpc", owner = "game_gateway_yrpc", registry = "yrpc-reg")
    private GameGatewayService gameGatewayService;

    /**
     * 创建比赛
     * @param gameId
     * @param gameName
     * @param signUpStartTime
     * @param signUpEndTime
     * @param startTime
     * @param endTime
     * @param userType
     * @param moduleId
     * @param joinFunc
     * @return 返回创建成功的比赛信息，返回null创建失败
     */
    public GameGateway.CreatePubgMatchVO createPubgGame(long gameId, String gameName, Date signUpStartTime, Date signUpEndTime, Date startTime, Date endTime, int userType, int moduleId, int joinFunc) {
        GameGateway.TimeRange join = GameGateway.TimeRange.newBuilder()
                .setStartTime(signUpStartTime.getTime())
                .setEndTime(signUpEndTime.getTime())
                .build();

        GameGateway.TimeRange match = GameGateway.TimeRange.newBuilder()
                .setStartTime(startTime.getTime())
                .setEndTime(endTime.getTime())
                .build();

        GameGateway.PubgModule module = GameGateway.PubgModule.newBuilder()
                .setMap(GameGateway.PubgMap.forNumber(moduleId))
                .build();

        GameGateway.PubgRound round = GameGateway.PubgRound.newBuilder()
                .setRoundId(1)
                .setStartTime(startTime.getTime())
                .build();

        GameGateway.PubgRule pubgRule = GameGateway.PubgRule.newBuilder()
                .setJoinFunc(GameGateway.JoinFunc.forNumber(joinFunc))
                .setBoNum(1)
                .addModules(module)
                .addRounds(round)
                .setCheckCert(false)
                .build();

        GameGateway.MatchRule matchRule = GameGateway.MatchRule.newBuilder()
                .setPassportLimit(GameGateway.PassportType.forNumber(userType))
                .setPubgRule(pubgRule)
                .setSignTeamMinMem(1)
                .build();

        GameGateway.MatchVO matchVO = GameGateway.MatchVO.newBuilder()
                .setMatchName(gameName)
                .setJoin(join)
                .setMatch(match)
                .setRule(matchRule)
                .build();

        GameGateway.CreatePubgMatchReq req = GameGateway.CreatePubgMatchReq.newBuilder()
                .setMatch(matchVO)
                .build();

        try {
            GameGateway.CreatePubgMatchRsp rsp = gameGatewayService.createPubgMatch(req);
            if (rsp != null && rsp.getCode() == 0) {
                return rsp.getData();
            }

            if (rsp != null) {
                log.error("call createPubgMatch fail gameId:{}, code:{} msg:{}", gameId, rsp.getCode(), rsp.getMsg());
            }
        } catch (Exception e) {
            log.error("call createPubgMatch fail gameId:{},e:{}", gameId, e.getMessage(), e);
        }

        return null;
    }

    /**
     *
     * @param teamName 腾讯限制，最大为8个字符（中英文任意字符都一样）
     */
    public GameGateway.CreateTeamRsp.CreateTeamVO createTeam(long matchId, String teamName, long uid) {
        teamName = StringUtil.left(teamName, PubgConst.MAX_TEAM_NAME_LEN);
        GameGateway.CreateTeamReq.TeamVO teamVO = GameGateway.CreateTeamReq.TeamVO.newBuilder()
                .setName(teamName)
                .setType(1)
                .setAuditSwitch(1)
                .build();

        GameGateway.CreateTeamReq req = GameGateway.CreateTeamReq.newBuilder()
                .setTeam(teamVO)
                .setMatchId(matchId)
                .setUid(uid)
                .build();

        try {
            GameGateway.CreateTeamRsp rsp = gameGatewayService.createTeam(req);
            if (rsp != null && rsp.getCode() == 0) {
                return rsp.getData();
            }

            if (rsp != null) {
                log.error("call createTeam fail,teamName:{},matchId:{},uid:{}, code:{} msg:{}", teamName, matchId, uid, rsp.getCode(), rsp.getMsg());
            }
        } catch (Exception e) {
            log.error("call createTeam exception,teamName:{},matchId:{},uid:{},e:{}", teamName, matchId, uid, e.getMessage(), e);
        }

        return null;
    }

    public boolean joinTeam(long matchId, String teamId, long uid) {
        GameGateway.JoinTeamReq req = GameGateway.JoinTeamReq.newBuilder()
                .setTeamId(teamId)
                .setMatchId(matchId)
                .setUid(uid)
                .build();

        try {
            GameGateway.JoinTeamRsp rsp = gameGatewayService.joinTeam(req);
            if (rsp != null && rsp.getCode() == 0) {
                log.info("call joinTeam dome,matchId:{},teamId:{},uid:{}", matchId, teamId, uid);
                return true;
            }

            if (rsp != null) {
                log.error("call joinTeam fail,matchId:{},teamId:{},uid:{}, code:{} msg:{}", matchId, teamId, uid, rsp.getCode(), rsp.getMsg());
            }
        } catch (Exception e) {
            log.error("call joinTeam exception:", e);
        }

        return false;
    }

    public boolean teamSignUp(long sysTeamId, long matchId, String teamId) {
        log.info("teamSignUp begin,sysTeamId:{},matchId:{},teamId:{}", sysTeamId, matchId, teamId);
        if (matchId == 0) {
            log.info("teamSignUp zero,sysTeamId:{},matchId:{},teamId:{}", sysTeamId, matchId, teamId);
            return false;
        }
        GameGateway.TeamSignupReq req = GameGateway.TeamSignupReq.newBuilder()
                .setMatchId(matchId)
                .setTeamId(teamId)
                .build();

        try {
            GameGateway.TeamSignupRsp rsp = gameGatewayService.teamSignup(req);
            if (rsp != null && rsp.getCode() == 0) {
                return true;
            }

            if (rsp != null) {
                log.error("call teamSignup fail,matchId:{},teamId:{}, code:{} msg:{}", matchId, teamId, rsp.getCode(), rsp.getMsg());
            }
        } catch (Exception e) {
            log.error("call teamSignup exception,matchId:{},teamId:{},e:{}", matchId, teamId, e.getMessage(), e);
        }

        return false;
    }

    public boolean soloSignup(long matchId, long uid) {
        GameGateway.SoloSignupReq req = GameGateway.SoloSignupReq.newBuilder()
                .setMatchId(matchId)
                .setUid(uid)
                .build();

        try {
            GameGateway.SoloSignupRsp rsp = gameGatewayService.soloSignup(req);
            if (rsp != null && rsp.getCode() == 0) {
                return true;
            }

            if (rsp != null) {
                log.error("call soloSignup fail uid:{},matchId:{} code:{} msg:{}", uid, matchId, rsp.getCode(), rsp.getMsg());
            }
        } catch (Exception e) {
            log.error("call soloSignup exception uid:{},matchId:{},e:{}", uid, matchId, e.getMessage(),e);
        }

        return false;
    }

    public GameGateway.QueryMatchDataRsp.QueryMatchDataVO queryMatchData(long matchId, int dataType, int start, int num) {
        GameGateway.QueryMatchDataReq req = GameGateway.QueryMatchDataReq.newBuilder()
                .setMatchId(matchId)
                .setDataType(dataType)
                .setStart(start)
                .setNum(num)
                .build();

        try {
            GameGateway.QueryMatchDataRsp rsp = gameGatewayService.queryMatchData(req);
            if (rsp != null && rsp.getCode() == 0) {
                return rsp.getData();
            }

            if (rsp != null) {
                log.error("call queryMatchData fail,matchId:{}, code:{} msg:{}", matchId, rsp.getCode(), rsp.getMsg());
            }
        } catch (Exception e) {
            log.error("call queryMatchData exception:", e);
        }

        return null;
    }

    public GameGateway.EnterMatchRsp.EnterMatchVO enterMatch(long siteId, long parentId, long matchId, long uid) {
        GameGateway.EnterMatchReq req = GameGateway.EnterMatchReq.newBuilder()
                .setSiteId(siteId)
                .setParentId(parentId)
                .setMatchId(matchId)
                .setUid(uid)
                .build();

        try {
            GameGateway.EnterMatchRsp rsp = gameGatewayService.enterMatch(req);
            if (rsp != null && rsp.getCode() == 0) {
                return rsp.getData();
            }

            if (rsp != null) {
                log.error("call enterMatch fail,siteId:{},parentId:{},matchId:{},uid:{}, code:{} msg:{}", siteId, parentId, matchId, uid, rsp.getCode(), rsp.getMsg());
            }
        } catch (Exception e) {
            log.error("call enterMatch exception,,siteId:{},parentId:{},matchId:{},uid:{}，e:{}", siteId, parentId, matchId, uid, e.getMessage(), e);
        }

        return null;
    }

    public GameGateway.GetPassportBindRsp.PassportBindVO getBindInfo(long uid) {
        return getBindInfo(uid, GameGateway.Game.GAME_PUBG_MOBILE);
    }

    public GameGateway.GetPassportBindRsp.PassportBindVO getBindInfo(long uid, GameGateway.Game game) {
        if (game == null) {
            game = GameGateway.Game.GAME_PUBG_MOBILE;
        }
        GameGateway.GetPassportBindReq req = GameGateway.GetPassportBindReq.newBuilder()
                .setGame(game)
                .setUid(uid)
                .build();

        try {
            GameGateway.GetPassportBindRsp rsp = gameGatewayService.getPassportBindInfo(req);
            if (rsp != null && rsp.getCode() == 0) {
                return rsp.getData();
            }

            if (rsp != null) {
                log.error("call getPassportBindInfo fail,uid:{}, code:{} msg:{}",uid, rsp.getCode(), rsp.getMsg());
            }
        } catch (Exception e) {
            log.error("call getPassportBindInfo fail,uid:{},e:{}", uid, e.getMessage(), e);
        }

        return null;
    }

    public GameGateway.QueryMatchBattleDetailRsp.BattleItemVO queryMatchBattleDetail(long matchId) {
        GameGateway.QueryMatchBattleDetailReq req = GameGateway.QueryMatchBattleDetailReq.newBuilder()
                .setMatchId(matchId)
                .setStart(0)
                .setNum(10)
                .setRoundId(0)
                .build();

        try {
            GameGateway.QueryMatchBattleDetailRsp rsp = gameGatewayService.queryMatchBattleDetail(req);
            if (rsp != null && rsp.getCode() == 0) {
                return CollectionUtils.isEmpty(rsp.getBattleListList()) ? null : rsp.getBattleList(0);
            }

            if (rsp != null) {
                log.error("call queryMatchBattleDetail fail,matchId:{}, code:{} msg:{}", matchId, rsp.getCode(), rsp.getMsg());
            }
        } catch (Exception e) {
            log.error("call queryMatchBattleDetail exception,matchId:{},e:{}", matchId, e.getMessage(), e);
        }

        return null;
    }

    public List<GameGateway.QueryLiveInfoRsp.LiveInfoList> queryLiveInfo(long childId) {

        GameGateway.QueryLiveInfoReq req = GameGateway.QueryLiveInfoReq.newBuilder()
                .addAllEventType(PubgConst.LiveEventType.HAS_CONTENT)
                //  "start":0,//分页参数
                //  "num":20, // 最大20 默认10
                .setStart(0)
                .setNum(20)
                .setChildId(childId)
                ///默认11    解说风格  11-普通AI解说直播  0-纯净流
                .setObStyle("11").build();
        try {
            var rsp = gameGatewayService.queryLiveInfo(req);
            if (rsp != null && rsp.getCode() == 0) {
                return rsp.getLiveInfoListList();
            }

            //空数据
            if (rsp != null && rsp.getCode() == 12004) {
                log.info("call queryLiveInfo empty code:{}", rsp.getCode());
                return null;
            }

            if (rsp != null) {
                log.error("queryLiveInfo exception,childId:{},rsp:{}", childId, rsp);
            }
        } catch (Exception e) {
            log.error("queryLiveInfo exception,childId:{},e:{}", e.getMessage(), e);
        }
        return null;
    }
}
