package com.yy.gameecology.activity.worker.server;

import com.yy.gameecology.activity.service.GameecologyBridgeServiceImpl;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;


/**
 * 对外提供了一定的服务，要等活动结束，先只让分组1注册，后面要取消这个服务或者修改s2s name
 * 预发布环境 & history 环境 不发布
 */
@ConditionalOnExpression(value = "#{${isYuFaFlag:0} == 0 && ${history:0} == 0 && ${s2s.hdzt.server.export:1} == 1}")
@Service(protocol = "attach_nythrift", registry = "thrift-reg")
public class GameecologyBridgeThriftServer extends GameecologyBridgeServiceImpl {
}
