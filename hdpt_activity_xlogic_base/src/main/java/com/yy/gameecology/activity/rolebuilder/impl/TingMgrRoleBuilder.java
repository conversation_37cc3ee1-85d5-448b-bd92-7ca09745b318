package com.yy.gameecology.activity.rolebuilder.impl;


import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.rankroleinfo.TingMgrRoleItem;
import com.yy.gameecology.activity.bean.rankroleinfo.UserBaseItem;
import com.yy.gameecology.activity.service.EnrollmentService;
import com.yy.gameecology.activity.service.SignedService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SpringBeanAwareFactory;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.RoleType;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/9/17 0:31
 * @Modified:
 */
public class TingMgrRoleBuilder extends UserRoleBaseBuilder<TingMgrRoleItem> {

//    private EnrollmentService enrollmentService = SpringBeanAwareFactory.getBean(EnrollmentService.class);
//
//    private SignedService signedService = SpringBeanAwareFactory.getBean(SignedService.class);

    private static final TingMgrRoleItem DEFAULT_OBJECT = new TingMgrRoleItem();

    static {
        DEFAULT_OBJECT.setKey("0");
        DEFAULT_OBJECT.setName("神秘厅管");
        DEFAULT_OBJECT.setAvatarInfo(Const.IMAGE.DEFAULT_USER_LOGO);

    }

    @Override
    public TingMgrRoleItem getDefaultObject() {
        return DEFAULT_OBJECT;
    }

    @Override
    public TingMgrRoleItem createBankObject() {
        return new TingMgrRoleItem();
    }

    @Override
    public Map<String, TingMgrRoleItem> addExtraInfo(long actId, long roleId, long rankId, long uid, Map<String, TingMgrRoleItem> roleItemMap) {

        List<String> noSignBabyUidStrs = roleItemMap.values().stream()
                .filter(roleItem -> roleItem.getContractSid() == null)
                .map(UserBaseItem::getUid).map(String::valueOf).collect(Collectors.toList());

        //填充签约信息--报名信息中获取
        EnrollmentService enrollmentService = SpringBeanAwareFactory.getBean(EnrollmentService.class);
        Map<String, EnrollmentInfo> enrollmentInfoMap = enrollmentService.getFirstEnrollmentInfoMap(actId, noSignBabyUidStrs, RoleType.TING_MGR);

        for (Map.Entry<String, EnrollmentInfo> entry : enrollmentInfoMap.entrySet()) {
            EnrollmentInfo enrollmentInfo = entry.getValue();
            TingMgrRoleItem tingMgrRoleItem = roleItemMap.get(entry.getKey());

            tingMgrRoleItem.setContractSid(enrollmentInfo.getSignSid());
            tingMgrRoleItem.setContractAsid(enrollmentInfo.getSignAsid());
        }

        List<Long> noSigntingMgrUids = roleItemMap.values().stream()
                .filter(roleItem -> roleItem.getContractSid() == null)
                .map(UserBaseItem::getUid).collect(Collectors.toList());

        //填充签约信息--报名业务端中获取
        if (!noSigntingMgrUids.isEmpty()) {
            SignedService signedService = SpringBeanAwareFactory.getBean(SignedService.class);
            Map<Long, ChannelInfoVo> channelInfoVoMap = signedService.getTingMgrSignedInfoMap(noSigntingMgrUids);
            for (Long babyUid : noSigntingMgrUids) {
                ChannelInfoVo channelInfoVo = channelInfoVoMap.get(babyUid);
                if (channelInfoVo != null) {
                    TingMgrRoleItem tingMgrRoleItem = roleItemMap.get(babyUid + "");
                    tingMgrRoleItem.setContractSid(channelInfoVo.getSid());
                    tingMgrRoleItem.setContractAsid(channelInfoVo.getAsId());
                }
            }
        }

        return roleItemMap;

    }

}
