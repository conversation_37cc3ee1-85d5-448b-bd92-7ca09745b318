package com.yy.gameecology.activity.service.layer.itembuilder;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.service.layer.ActLayerInfoExtService;
import com.yy.gameecology.common.consts.ActorInfoStatus;
import com.yy.gameecology.common.consts.LayerItemTypeKey;
import com.yy.gameecology.common.consts.LayerViewStatus;
import com.yy.gameecology.common.consts.RoleTypeSource;
import com.yy.gameecology.common.db.model.gameecology.ActLayerViewDefine;
import com.yy.gameecology.common.utils.Convert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CmptItemBuilder implements LayerItemBuilder {

    @Autowired
    private ActLayerInfoExtService actLayerInfoExtService;

    @Override
    public Set<String> getItemKeys() {
        return Set.of(LayerItemTypeKey.CMPT_ITEM, LayerItemTypeKey.CMPT_ITEM2);
    }

    @Override
    public List<String> getMemberIds(ActivityInfoVo actInfo, Long busiId, ActLayerViewDefine viewDefine, OnlineChannelInfo onlineChannel) {
        if (viewDefine.getRoleType() == RoleTypeSource.BABY) {
            return onlineChannel.getEffectAnchorId().stream().map(Convert::toString).collect(Collectors.toList());
        }
        return Collections.singletonList(onlineChannel.getSid() + "_" + onlineChannel.getSsid());
    }

    @Override
    public List<LayerMemberItem> build(Date now, ActivityInfoVo actInfo, Long busiId, ActLayerViewDefine viewDefine, List<String> memberIds, Map<String, Object> ext) {
        List<LayerMemberItem> result = new ArrayList<>(memberIds.size());
        for (int i = 0; i < memberIds.size(); i++) {
            String memberId = memberIds.get(i);
            LayerMemberItem memberItem = initMemberItem(memberId, viewDefine);
            memberItem.setState(ActorInfoStatus.NORMAL);
            memberItem.setViewStatus(LayerViewStatus.NORMAL_100);
            memberItem.setSort(i);
            //扩展信息
            Map<String, Object> itemExtInfo = actLayerInfoExtService.extendLayerMemberItem(actInfo.getActId(), memberItem);
            if (MapUtils.isNotEmpty(itemExtInfo)) {
                memberItem.getExt().putAll(itemExtInfo);
            }

            if (viewDefine.getRoleType() == RoleTypeSource.BABY) {
                memberItem.setSignStatus(1);
            }

            result.add(memberItem);
        }

        return result;
    }

    @Override
    public void fillLayerBroadcastInfo(ActLayerViewDefine viewDefine, LayerBroadcastInfo target, List<LayerMemberItem> source, List<String> memberIds, Map<String, Object> ext) {
        target.getExtMemberItem().addAll(source);
    }

    private LayerMemberItem initMemberItem(String memberId, ActLayerViewDefine viewDefine) {
        LayerMemberItem itemRes = new LayerMemberItem();
        itemRes.setMemberId(memberId);
        itemRes.setItemType(viewDefine.getItemTypeKey());
        itemRes.setRoleType(viewDefine.getRoleType());

        return itemRes;
    }
}
