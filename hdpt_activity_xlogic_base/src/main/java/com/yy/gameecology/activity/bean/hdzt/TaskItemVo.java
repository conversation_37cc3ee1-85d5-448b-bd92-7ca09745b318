package com.yy.gameecology.activity.bean.hdzt;

import com.yy.gameecology.hdzj.bean.AwardRecordInfo;
import com.yy.gameecology.hdzj.bean.AwardRoll;
import lombok.Data;

import java.util.List;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-30 11:35
 **/
@Data
public class TaskItemVo {
    private long taskId;
    private String taskName;
    private long taskLevel;
    private String taskLevelUrl;

    private String nick;
    private String avatarUrl;
    private String remark;

    private List<AwardRoll> awards;

}
