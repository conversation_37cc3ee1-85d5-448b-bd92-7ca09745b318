package com.yy.gameecology.activity.bean.mq;


/**
 * desc:营收充值支付结果回调
 *
 * @createBy 曾文帜
 * @create 2021-09-06 20:53
 **/
public class ChargeResponseEvent {

    /**
     *唯一id,订单号,和发起支付的orderId一致
     */
    private Long id;


    //0.未处理 1.成功 2.失败 3.已跳转 到 第三方支付平台，等待用户充值 4.回调数据异常
    //5.订单取消 6.订单重复 7.渠道关闭 8.余额不足 9.风险控制 10.充值优惠券无效
    //11.充值调用超时 12.订单已退款  13.订单已发起退款，处理中 14.退款失败
    //15.来自苹果沙盒的凭证,请申请添加白名单
    private Integer status;

    /**
     *额外信息
     */
    private String message;


    /**
     *外部订单号
     */
    private Long externOrderId;

    /**
     *订单生成时间
     */
    private Long createTime;

    /**
     *订单完成时间
     */
    private Long dealTime;

    /**
     *扩展json信息
     */
    private String expand;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }



    public Long getExternOrderId() {
        return externOrderId;
    }

    public void setExternOrderId(Long externOrderId) {
        this.externOrderId = externOrderId;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getDealTime() {
        return dealTime;
    }

    public void setDealTime(Long dealTime) {
        this.dealTime = dealTime;
    }

    public String getExpand() {
        return expand;
    }

    public void setExpand(String expand) {
        this.expand = expand;
    }
}
