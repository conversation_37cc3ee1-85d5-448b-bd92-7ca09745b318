package com.yy.gameecology.activity.bean.hdzt;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;

/**
 * desc:和中台奖包配置对应
 *
 * @createBy 曾文帜
 * @create 2021-08-10 20:35
 **/
public class HdztAwardConfig {

    /**
     * 1先记录领奖资格再发放
     */
    @ComponentAttrField(labelText = "奖励类型", remark = "1:先记录领奖资格再发放")
    private long awardType;

    /**
     * 领取的时候，是否有按天发放数量限制，0代表没有,>0代表每天总限量
     */
    @ComponentAttrField(labelText = "每天总限量", remark = "0代表没有,>0代表每天总限量")
    private long dayLimit;

    /**
     * 奖励名称
     **/
    @ComponentAttrField(labelText = "奖励名称")
    private String name;

    /**
     * 奖池id
     **/
    @ComponentAttrField(labelText = "奖池id")
    private long taskId;

    /**
     * 奖包id
     **/
    @ComponentAttrField(labelText = "奖包id")
    private long packageId;

    /**
     * 奖励数量
     **/
    @ComponentAttrField(labelText = "奖励数量")
    private int amount = 1;

    public long getTaskId() {
        return taskId;
    }

    public void setTaskId(long taskId) {
        this.taskId = taskId;
    }

    public long getPackageId() {
        return packageId;
    }

    public void setPackageId(long packageId) {
        this.packageId = packageId;
    }

    public long getDayLimit() {
        return dayLimit;
    }

    public void setDayLimit(long dayLimit) {
        this.dayLimit = dayLimit;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getAmount() {
        return amount;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }

    public long getAwardType() {
        return awardType;
    }

    public void setAwardType(long awardType) {
        this.awardType = awardType;
    }
}
