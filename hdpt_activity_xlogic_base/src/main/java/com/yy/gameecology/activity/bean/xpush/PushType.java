package com.yy.gameecology.activity.bean.xpush;

/**
 * <AUTHOR>
 * @ClassName
 * @description TODO
 * @date 2019/8/14 14:44
 */
public enum PushType {
    /**
     * 按UID
     */
    BY_UID(1),
    /**
     * 按登录类型推送
     */
    BY_LOGIN(2),
    /**
     * 按URL反查推送
     */
    BY_URL(3),
    /**
     * 所有用户（强推，包含关闭接收推送用户）
     */
    BY_ALL_FORCE(4),
    /**
     * 所有用户（用户配置，不包含关闭接收推送用户）
     */
    BY_ALL_CONFIG(5),
    /**
     * 按TAG查询设备推送
     */
    BY_TAG_FORCE(6);

    private int type;

    PushType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }
    }
