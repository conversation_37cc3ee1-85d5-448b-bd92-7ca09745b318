package com.yy.gameecology.activity.client.yrpc;

import com.yy.protocol.pb.zhuiwan.signin.SignIn;
import lombok.experimental.Delegate;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

/**
 * 追玩签到服务
 *
 * @Author: CXZ
 * @Desciption:
 * @Date: 2024/01/17 17:22
 * @Modified:
 */
@Component
public class ZhuiyaSignInClient implements ZhuiyaSignInService {
    @Reference(protocol = "yrpc", owner = "zhuiya_sign_yrpc", registry = {"yrpc-reg"}, lazy = true)
    private ZhuiyaSignInService zhuiyaSignInService;

    @Reference(protocol = "yrpc", owner = "zhuiya_sign_yrpc", registry = {"yrpc-reg"}, lazy = true, retries = 2, cluster = "failover")
    private ZhuiyaSignInService zhuiyaSignInReadService;

    @Override
    public SignIn.SignInResp signIn(SignIn.SignInReq req) {
        return zhuiyaSignInService.signIn(req);
    }

    @Override
    public SignIn.SignInDetailsResp getSignInDetails(SignIn.SignInDetailsReq req) {
        return zhuiyaSignInReadService.getSignInDetails(req);
    }
}
