package com.yy.gameecology.activity.bean.event;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-01-09 14:59
 **/
@Data
public class PublicScreenNotifyEvent {
    /**
     * seq
     */
    private String seq;

    /**
     * 时间戳(秒)
     */
    private long timestamp;
    /**
     * 公屏消息
     * {uid:n} - 多昵称标识，会被替换成昵称
     * 例:恭喜<font color='IM1' color_pc='#fff' action=''>{99999:n} </font>获得 <font color='IM1' color_pc='#fff' action=''>xxx勋章</font>
     */
    private String message;
    /**
     * 1-当前用户 2-当前频道，3-当前顶级频道，4-全家族，5= 聊天室全服，6-全服
     */
    private int notifyType;
    /**
     * 广播对象
     */
    private Long uid;
    private Long sid;
    private Long ssid;
    private Long familyId;

    /**
     * app黑名单,在名单内的app不展示,zhuiwan/dreamer,yomi,pcyy,yymobile,baidu,haokan,tieba,quanmin,yokh
     */
    private List<String> notShowApps = Lists.newArrayList();

    /**
     * 灰度顶级频道，当有灰度顶级频道的时候，只会广播在顶级内的频道
     */
    private List<Long> graySidList = Lists.newArrayList();

}
