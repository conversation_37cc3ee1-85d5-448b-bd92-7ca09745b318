package com.yy.gameecology.activity.worker.subscriber;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.mq.HdzkAovGameEvent;
import com.yy.gameecology.activity.bean.mq.HdzkAovRoundSettledEvent;
import com.yy.gameecology.activity.bean.mq.HdzkWzryGameEvent;
import com.yy.gameecology.activity.bean.mq.hdzk.*;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.HdzjEventDispatcher;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 代替原来的 HdztRabbitConsumer，处理hdzt发出的kafka消息
 *
 * <AUTHOR>
 * @date 2021年12月22日 上午11:53:26
 */
@ConditionalOnExpression(Const.EXPRESSION_NOT_HISTORY)
@Component
public class HdzkKafkaConsumer {

    private Logger log = LoggerFactory.getLogger(HdzkKafkaConsumer.class);


    @Autowired
    private HdzjEventDispatcher hdzjEventDispatcher;


    @KafkaListener(containerFactory = "hdztWxContainerFactory", id = "hdzt_wx_kafka_wzry_events",
            topics = "${kafka.hdzk.wzry.game.events.topic}",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void onWzryGameEventsWx(ConsumerRecord<String, String> consumerRecord) {
        wzryGameMessage("wx", consumerRecord.value());
    }

    @KafkaListener(containerFactory = "hdztSzContainerFactory", id = "hdzt_sz_kafka_wzry_events",
            topics = "${kafka.hdzk.wzry.game.events.topic}",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void onWzryGameEventsEventsSz(ConsumerRecord<String, String> consumerRecord) {
        wzryGameMessage("sz", consumerRecord.value());
    }

    public void wzryGameMessage(String from, String value) {
        log.info("wzryGameMessage from:{}, msg:{}", from, value);
        try {
            HdzkWzryGameEvent event = JSON.parseObject(value, HdzkWzryGameEvent.class);
            List<Pair<RoleType, String>> list = event.getUser().keySet().stream()
                    .map(user -> Pair.of(RoleType.USER, String.valueOf(user))).toList();
            hdzjEventDispatcher.notify(event.getActId(), list, event, event.getSeq());
        } catch (Exception e) {
            log.error("wzryGameMessage error,e:{},value:{}", e.getMessage(), value, e);
        }
    }


    @KafkaListener(containerFactory = "hdptWxKafkaContainerFactory", id = "hdpt_wx_kafka_common_events",
            topics = "${kafka.hdzk.common.events.topic}",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void onhdptCommonEventWx(ConsumerRecord<String, String> consumerRecord) {
        hdptCommonEventMessage("wx", consumerRecord.value());
    }

    @KafkaListener(containerFactory = "hdptWxXdcKafkaContainerFactory", id = "hdpt_sz_kafka_common_events",
            topics = "${kafka.hdzk.common.events.topic}",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void onhdptCommonEventSz(ConsumerRecord<String, String> consumerRecord) {
        hdptCommonEventMessage("sz", consumerRecord.value());
    }

    public void hdptCommonEventMessage(String from, String payload) {
        log.info("hdptCommonEventMessage from:{}, msg:{}", from, payload);
        try {
            int pos = payload.indexOf("|");
            if (pos == -1) {
                log.error("onMessage fail@from:{}, invalid message format, pos:{}, payload:{}", from, pos, payload);
                return;
            }


            log.info("onMessage info @from:{}, message format, pos:{}, payload:{}", from, pos, payload);
            long uri = Convert.toLong(payload.substring(0, pos), -1);
            String data = payload.substring(pos + 1);
            //事件分发
            dispatcher(from, uri, data);

        } catch (Throwable t) {
            log.error("onMessage exception@from:{}, err:{}, event:{}", from, t.getMessage(), payload, t);
        }
    }

    private void dispatcher(String from, Long uri, String data) throws Exception {

        Class zclass = null;
        if (uri == HdzkBaseEvent.DAY_TASK_COMPONENT_TASK_POLL_OUT) {
            zclass = DayTaskPollOutEvent.class;

        } else if (uri == HdzkBaseEvent.DAY_TASK_COMPONENT_COMPLETE_TASK) {
            zclass = DayTaskCompleteEvent.class;

        } else if (uri == HdzkBaseEvent.AOV_GAME_EVENT_URI) {
            zclass = HdzkAovGameEvent.class;
        } else if (uri == HdzkBaseEvent.AOV_ROUND_SETTLED_EVENT_URI) {
            zclass = HdzkAovRoundSettledEvent.class;
        } else if (uri == HdzkBaseEvent.PEPC_PHASE_SETTLE) {
            zclass = PepcPhaseSettleEvent.class;
        } else if (uri == HdzkBaseEvent.PEPC_GAME_END) {
            zclass = PepcGameEndEvent.class;
        }
        else {
            throw new Exception("事件uri无法识别，uri={}" + uri);
        }

        HdzkBaseEvent event = (HdzkBaseEvent) (JSON.parseObject(data, zclass));
        hdzjEventDispatcher.notify(event.getActId(), event, event.getSeq());
    }

}
