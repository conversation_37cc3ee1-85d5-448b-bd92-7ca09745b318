package com.yy.gameecology.activity.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 需要资源回收
 * 用于定制的初始化,定时器回收
 * 配合 <code>Scheduled</code>和<code>PostConstruct</code>一起使用
 * 组件有些逻辑是通过定时任务触发的,如果组件的通用性不高,建议也打上改注解,待活动结束后回收
 *
 * <AUTHOR>
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface NeedRecycle {
    /**
     * 结束时间(真实物理时间),格式 yyyy-MM-dd HH:mm:ss
     * 会依据该结束时间,通知用户进行资源回收
     **/
    String endTime() default "";

    /**
     * 针对长期的定时器,该值设置为true,表示不需要进行回收
     **/
    boolean notRecycle() default false;

    /**
     * 定时器的开发人员
     **/
    String author();
}
