package com.yy.gameecology.activity.dao.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.commons.RedisSupport;
import com.yy.gameecology.common.utils.Convert;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.TimeUnit;

public abstract class RedisBaseDao {

    protected StringRedisTemplate redisTemplate;

    /**
     * 获取redis服务器的时间（毫秒数）
     */
    public long time() {
        List<Object> list = redisTemplate.executePipelined((RedisConnection connection) -> {
            connection.time();
            return null;
        });
        return (long) list.get(0);
    }

    public <T> List<Object> zBatchIncrDouble(List<String> keys, List<T> vals, List<Double> incs) {
        return redisTemplate.executePipelined((RedisConnection connection) -> {
            for (int index = 0; index < keys.size(); index++) {
                connection.zIncrBy(keys.get(index).getBytes(), incs.get(index), vals.get(index).toString().getBytes());
            }
            return null;
        });
    }

    public <T> List<Object> zBatchIncr(List<String> keys, List<T> vals, List<Long> incs) {
        return redisTemplate.executePipelined((RedisConnection connection) -> {
            for (int index = 0; index < keys.size(); index++) {
                connection.zIncrBy(keys.get(index).getBytes(), incs.get(index), vals.get(index).toString().getBytes());
            }
            return null;
        });
    }



    public String get(final String key) {
        return redisTemplate.opsForValue().get(key);
    }

    public long incrValue(final String key, final long step) {
        return redisTemplate.opsForValue().increment(key, step);
    }

    public void set(final String key, final String value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public void set(final String key, final String value, long expireSec) {
        redisTemplate.opsForValue().set(key, value, expireSec, TimeUnit.SECONDS);
    }

    public boolean setNX(final String key, String val) {
        Boolean rs = redisTemplate.opsForValue().setIfAbsent(key, val);
        return rs != null && rs;
    }

    public boolean setNX(final String key, String val, final long sec) {
        Boolean succ = redisTemplate.execute((RedisCallback<Boolean>) connection -> {
            Boolean setNX = connection.setNX(key.getBytes(), val.getBytes());
            if (Boolean.TRUE.equals(setNX)) {
                connection.expire(key.getBytes(), sec);
            }
            return setNX;
        });
        return succ != null && succ;
    }

    public boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    public void del(final String key) {
        redisTemplate.delete(key);
    }

    public void delKeys(Set<String> keys) {
        redisTemplate.delete(keys);
    }

    public String hget(final String key, final String hashKey) {
        Object obj = redisTemplate.opsForHash().get(key, hashKey);
        if (obj == null) {
            return null;
        } else {
            return obj.toString();
        }
    }

    public Map<Object, Object> hGetAll(final String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    public List<Object> hmGet(final String key, List<Object> hashKeys) {
        return redisTemplate.opsForHash().multiGet(key, hashKeys);
    }

    public void hset(final String key, final String hashKey, final String value) {
        redisTemplate.opsForHash().put(key, hashKey, value);
    }
    public List<Object> hset( final String key, final String hashKey, final String value,
                                 long expireSecond) {
        List<Object> list = redisTemplate.execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                connection.hSet(key.getBytes(), hashKey.getBytes(), value.getBytes());
                if (expireSecond > 0) {
                    connection.expire(key.getBytes(), expireSecond);
                }
                return connection.closePipeline();
            }
        });
        return list;
    }

    public long hIncrByKey(final String key, final String hashKey, final long value) {
        return redisTemplate.boundHashOps(key).increment(hashKey, value);
    }

    public boolean hsetnx(final String key, final String hashKey, final String value) {
        return redisTemplate.boundHashOps(key).putIfAbsent(hashKey, value);
    }

    public List<Object> hdelKey(final String key, final List<String> hashKey) {
        List<Object> list = redisTemplate.execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                for (int i = 0; i < hashKey.size(); ++i) {
                    connection.hDel(key.getBytes(), hashKey.get(i).getBytes());
                }
                return connection.closePipeline();
            }
        });
        return list;
    }

    public void hdel(final String key, String hashKey) {
        redisTemplate.opsForHash().delete(key, hashKey);
    }



    public Double zScore(String key, String obj) {
        return redisTemplate.opsForZSet().score(key, obj);
    }

    public long zscore(String key, String row){
        return Convert.toLong(redisTemplate.opsForZSet().score(key, row));
    }

    /**
     * 只支持分数为正整数的zset
     * @param key
     * @param obj
     * @return
     */
    public List<Long> zScoreAndRank(String key, String obj) {
        List<Object> list = redisTemplate.execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                connection.zScore(key.getBytes(), obj.getBytes());
                connection.zRevRank(key.getBytes(), obj.getBytes());
                return connection.closePipeline();
            }
        });

        // 表示 zset key 或 member 不存在
        if(list.get(0) == null) {
            return Arrays.asList(0L, 0L);
        } else {
            long score = Convert.toLong(list.get(0));
            long rank = Convert.toLong(list.get(1)) + 1;
            return Arrays.asList(score, rank);
        }
    }

    public Long zRevRank(String key, String obj) {
        return redisTemplate.opsForZSet().reverseRank(key, obj);
    }

    public boolean zAdd(String key, String obj, double score) {
        return redisTemplate.opsForZSet().add(key, obj, score);
    }

    public long zIncr(String key, String obj, long score) {
        return redisTemplate.opsForZSet().incrementScore(key, obj, score).longValue();
    }

    public Set<TypedTuple<String>> zrevRange(String key, long num) {
        return redisTemplate.opsForZSet().reverseRangeWithScores(key, 0, num - 1);
    }

    public Set<TypedTuple<String>> zrevRange(String key, long start, long end) {
        return redisTemplate.opsForZSet().reverseRangeWithScores(key, start, end);
    }

    public Set<String> zrevRangeByScore(String key, long min, long max) {
        return redisTemplate.opsForZSet().reverseRangeByScore(key, min, max);
    }

    public Set<TypedTuple<String>> zrevRangeByScoreWithScore(String key, long min, long max) {
        return redisTemplate.opsForZSet().reverseRangeByScoreWithScores(key, min, max);
    }

    public Set<String> zrevRangeNoScores(String key, long num) {
        return redisTemplate.opsForZSet().reverseRange(key, 0, num - 1);
    }

    public Set<TypedTuple<String>> zrange(String key, long num) {
        return redisTemplate.opsForZSet().rangeWithScores(key, 0, num - 1);
    }

    public long zcard(String key) {
        return redisTemplate.opsForZSet().size(key);
    }

    public long zcount(String key, double min, double max) {
        return redisTemplate.opsForZSet().count(key, min, max);
    }

    public void zDel( final String key, final String obj) {
        redisTemplate.opsForZSet().remove(key, obj);
    }

    public Long getExpire(String key) {
        return redisTemplate.getExpire(key);
    }

    public boolean sIsMember(String key, String member) {
        return redisTemplate.opsForSet().isMember(key, member);
    }

    public <T> T executeLua(String scriptLuaName, Class<T> returnClz, List<String> keys, List<String> argv) {
        DefaultRedisScript<T> script = new DefaultRedisScript<>();
        script.setScriptText(RedisSupport.getScript(scriptLuaName));
        script.setResultType(returnClz);
        return redisTemplate.execute(script, keys, argv.toArray());
    }

    public boolean hExists(String key, String row) {
        return redisTemplate.opsForHash().hasKey(key, row);
    }

    public Set<String> sMembers(String key) {
        return redisTemplate.opsForSet().members(key);
    }

    public long sAdd(String key, String member) {
        return redisTemplate.opsForSet().add(key, member) ;
    }
    public Long sRem(String key, String... values) {
        return redisTemplate.opsForSet().remove(key, values);
    }




    /**
     * FIXME: 本代码支持到 2034/11/19 01:27:27
     * @return
     */
    public String getTime() {
        return "0." + (Integer.MAX_VALUE - System.currentTimeMillis() / 1000);
    }

    /**
     *
     * @param pattern
     * @return
     */
    public Set<String> keys( String pattern) {
        return redisTemplate.keys(pattern);
    }


    public boolean setExpire(String key, long seconds) {
        return redisTemplate.expire(key, seconds, TimeUnit.SECONDS);
    }




}
