package com.yy.gameecology.activity.service.wzry;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.client.thrift.SaiBaoClient;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.activity.dao.mysql.WzryDao;
import com.yy.gameecology.common.consts.BattleMode;
import com.yy.gameecology.common.consts.wzry.WzryGameState;
import com.yy.gameecology.common.consts.wzry.WzryGameViewState;
import com.yy.gameecology.common.consts.wzry.WzryLimitTeam;
import com.yy.gameecology.common.db.model.gameecology.wzry.*;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.IdGeneratorHelper;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.component.attr.WzryGameComponentAttr;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-25 18:09
 **/
@Service
public class WzryGameInitService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private Locker locker;

    /**
     * 每场赛事开赛前15分钟~开赛前1分钟
     */
    private static final int SIGN_UP_OFFSET_START = 15;

    /**
     * 每场赛事开赛前15分钟~开赛前1分钟
     */
    private static final int SIGN_UP_OFFSET_END = 1;


    @Autowired
    private GameecologyDao gameecologyDao;

    @Autowired
    private WzryDao wzryDao;

    @Autowired
    private SaiBaoClient saiBaoClient;

    @Lazy
    @Autowired
    private WzryGameInitService mySelf;


    /**
     * 上层用daycode去重，低频操作，1天数据1个事务没问题
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoCreateGame(WzryGameComponentAttr attr, String dayCode) {
        autoCreate1V1Game(attr, dayCode);
        autoCreate5V5Game(attr, dayCode);
    }


    public void autoCreate1V1Game(WzryGameComponentAttr attr, String dayCode) {
        long actId = attr.getActId();
        //---创建1V1赛事
        String startDateStr = DateUtil.format(DateUtil.getDate(dayCode, DateUtil.PATTERN_TYPE2), DateUtil.PATTERN_TYPE5) + " " + attr.getCreate1V1GameStartTime();
        Date startTime = DateUtil.getDate(startDateStr);
        for (int i = 0; i < attr.getCreate1V1GameDayAmount(); i++) {
            //时间作为key，保证创建赛事的幂等，如果1个时间点创建不止1场赛事view，这里的逻辑需要变更
            String gameViewCode = DateUtil.format(startTime, DateUtil.PATTERN_TYPE1) + "_1V1";
            Date signStart = DateUtil.addMinutes(startTime, -SIGN_UP_OFFSET_START);
            Date signEnd = DateUtil.addMinutes(startTime, -SIGN_UP_OFFSET_END);
            createGameView(actId, gameViewCode, "王者赏金赛", BattleMode.GAME_1V1, signStart, signEnd, startTime);
            //1V1的，1场赛事视图背后可对应多场赛事
            for (int j = 0; j < attr.getCreate1V1GameViewAmount(); j++) {
                String gameCode = gameViewCode + "_" + j;
                createGame(actId, gameViewCode, gameCode, "王者赏金赛", attr.getAward1V1(), BattleMode.GAME_1V1, signStart, signEnd, startTime);
            }

            startTime = DateUtil.addMinutes(startTime, attr.getCreate1V1GameTimeSpanMin());
        }
    }


    /**
     * 上层用daycode去重，低频操作，1天数据1个事务没问题
     */
    public void autoCreate5V5Game(WzryGameComponentAttr attr, String dayCode) {
        long actId = attr.getActId();
        //---创建1V1赛事
        String startDateStr = DateUtil.format(DateUtil.getDate(dayCode, DateUtil.PATTERN_TYPE2), DateUtil.PATTERN_TYPE5) + " " + attr.getCreate5V5GameStartTime();
        Date startTime = DateUtil.getDate(startDateStr);
        for (int i = 0; i < attr.getCreate5V5GameDayAmount(); i++) {
            //时间作为key，保证创建赛事的幂等，如果1个时间点创建不止1场赛事view，这里的逻辑需要变更
            String gameViewCode = DateUtil.format(startTime, DateUtil.PATTERN_TYPE1) + "_5V5";
            Date signStart = DateUtil.addMinutes(startTime, -SIGN_UP_OFFSET_START);
            Date signEnd = DateUtil.addMinutes(startTime, -SIGN_UP_OFFSET_END);
            createGameView(actId, gameViewCode, "王者赏金赛", BattleMode.GAME_5V5, signStart, signEnd, startTime);
            for (int j = 0; j < attr.getCreate5V5GameViewAmount(); j++) {
                String gameCode = gameViewCode + "_" + j;
                createGame(actId, gameViewCode, gameCode, "王者赏金赛", attr.getAward5V5(), BattleMode.GAME_5V5, signStart, signEnd, startTime);
            }


            startTime = DateUtil.addMinutes(startTime, attr.getCreate5V5GameTimeSpanMin());
        }
    }

    public void createGameView(long actId, String gameViewCode, String gameViewName, int battleMode, Date signUpStateTime, Date signUpEndTime, Date startTime) {
        WzryGameView exist = new WzryGameView();
        exist.setActId(actId);
        exist.setGameViewCode(gameViewCode);
        if (gameecologyDao.count(WzryGameView.class, exist) > 0) {
            log.error("createGameView skip,gameViewCode exist,actId:{},gameViewCode:{}", actId, gameViewCode);
            return;
        }
        WzryGameView wzryGameView = new WzryGameView();
        wzryGameView.setId(IdGeneratorHelper.genId(WzryGameTeam.TABLE_NAME, new Date()));
        wzryGameView.setActId(actId);
        wzryGameView.setGameViewCode(gameViewCode);
        wzryGameView.setGameViewName(gameViewName);
        wzryGameView.setBattleMode(battleMode);
        wzryGameView.setSignUpStartTime(signUpStateTime);
        wzryGameView.setSignUpEndTime(signUpEndTime);
        wzryGameView.setEnters(0);
        wzryGameView.setState(WzryGameViewState.INIT);
        wzryGameView.setStartTime(startTime);
        wzryGameView.setCreateTime(new Date());
        gameecologyDao.insert(WzryGameView.class, wzryGameView);
    }

    public void createGame(long actId, String gameViewCode, String gameCode, String gameName, long award, int battleMode, Date signUpStateTime, Date signUpEndTime, Date startTime) {
        WzryGame exist = new WzryGame();
        exist.setActId(actId);
        exist.setGameCode(gameCode);
        if (gameecologyDao.count(WzryGame.class, exist) > 0) {
            log.error("createGame skip,gameViewCode exist,actId:{},gameViewCode:{}", actId, gameCode);
            return;
        }
        WzryGame wzryGame = new WzryGame();

        wzryGame.setId(IdGeneratorHelper.genId(WzryGame.TABLE_NAME, new Date()));
        wzryGame.setActId(actId);
        wzryGame.setGameViewCode(gameViewCode);
        wzryGame.setGameCode(gameCode);
        wzryGame.setGameName(gameName);
        wzryGame.setBattleMode(battleMode);
        //王者赛宝赛事ID 怕生成id递增被人遍历，不能提前太长时间生成，由专门程序来初始化
        wzryGame.setChildid("");
        wzryGame.setSignUpStartTime(signUpStateTime);
        wzryGame.setSignUpEndTime(signUpEndTime);
        wzryGame.setStartTime(startTime);
        wzryGame.setEnters(0);
        wzryGame.setWinnerTeam(0);
        wzryGame.setAward(award);
        wzryGame.setState(battleMode == BattleMode.GAME_5V5 ? WzryGameState.INIT_TEAM : WzryGameState.INIT);
        wzryGame.setLastCheckTime(new Date());
        wzryGame.setCreateTime(new Date());
        gameecologyDao.insert(WzryGame.class, wzryGame);

        //-5V5 分配语音房房间号
        if (battleMode == BattleMode.GAME_5V5) {
            WzryRoomPool roomPool1 = allocationRoom();
            WzryRoomPool roomPool2 = allocationRoom();

            WzryGameChannel wzryGameChannel1 = new WzryGameChannel();
            wzryGameChannel1.setId(IdGeneratorHelper.genId(WzryGameChannel.TABLE_NAME, new Date()));
            wzryGameChannel1.setActId(actId);
            wzryGameChannel1.setGameId(wzryGame.getId());
            wzryGameChannel1.setLimitTeam(WzryLimitTeam.A);
            wzryGameChannel1.setSid(roomPool1.getSid());
            wzryGameChannel1.setSsid(roomPool1.getSsid());
            wzryGameChannel1.setCreateTime(new Date());

            WzryGameChannel wzryGameChannel2 = new WzryGameChannel();
            wzryGameChannel2.setId(IdGeneratorHelper.genId(WzryGameChannel.TABLE_NAME, new Date()));
            wzryGameChannel2.setActId(actId);
            wzryGameChannel2.setGameId(wzryGame.getId());
            wzryGameChannel2.setLimitTeam(WzryLimitTeam.B);
            wzryGameChannel2.setSid(roomPool2.getSid());
            wzryGameChannel2.setSsid(roomPool2.getSsid());
            wzryGameChannel2.setCreateTime(new Date());

            gameecologyDao.batchInsert(WzryGameChannel.class, Lists.newArrayList(wzryGameChannel1, wzryGameChannel2), WzryGameChannel.TABLE_NAME);

        }
    }

    public WzryRoomPool allocationRoom() {
        Secret secret = locker.lock("allocationRoom");
        WzryRoomPool result = null;
        try {
            WzryRoomPool where = new WzryRoomPool();
            where.setState(1);
            result = gameecologyDao.selectOne(WzryRoomPool.class, where, " order by last_use_time asc limit 1");
            if (result != null) {
                WzryRoomPool updateWhere = new WzryRoomPool();
                updateWhere.setId(result.getId());

                WzryRoomPool to = new WzryRoomPool();
                to.setLastUseTime(new Date());
                gameecologyDao.update(WzryRoomPool.class, updateWhere, to);
            }
        } catch (Exception e) {
            log.error("allocationRoom error,e:{}", e.getMessage(), e);

        } finally {
            locker.unlock("allocationRoom", secret);
        }

        return result;
    }

    /**
     * 报名前10分钟初始化王者赛宝 赛事
     * 不能提前太多批量创建赛宝赛事，因为id是递增，怕有被用户恶意进入房间
     */
    public void autoInitSbGame(long actId, Date signStartTimeEndTime, int size) {
        List<WzryGame> games = wzryDao.queryNeedInitSbGame(actId, signStartTimeEndTime, size);
        if (CollectionUtils.isEmpty(games)) {
            log.info("queryNeedInitSbGame empty,actId:{},signStartTimeEndTime:{},size:{}", actId, DateUtil.format(signStartTimeEndTime), size);
            return;
        }
        for (WzryGame game : games) {
            SysEvHelper.waiting(100);
            //创建赛事 更新赛事id
            WzryGame updateWhere = new WzryGame();
            updateWhere.setId(game.getId());
            WzryGame to = new WzryGame();

            var sbGame = saiBaoClient.createRoom(game.getGameName(), game.getBattleMode(), 1, false);
            String childid = sbGame.getRoomId();
            if (StringUtil.isEmpty(childid)) {
                log.error("create sao bao game error,game:{}", JSON.toJSONString(game));
                throw new RuntimeException("create sao bao game error");
            }
            to.setChildid(childid);

            int res = gameecologyDao.update(WzryGame.class, updateWhere, to);
            if (res != 1) {
                log.error("autoInitSbGame update game failed,gameId:{},childid:{}", game.getId(), childid);
            } else {
                log.info("autoInitSbGame update game done,gameId:{},childid:{}", game.getId(), childid);
            }
        }
    }


    public void autoAllocationGameTeam(long actId, Date now, int batch) {
        List<WzryGameView> wzryGameViews = wzryDao.queryNeedAllocationTeamGameView(actId, now, batch);
        if (CollectionUtils.isEmpty(wzryGameViews)) {
            log.info("autoAllocationGameTeam size 0 ,actId:{},now:{},batch:{}", actId, now, batch);
            return;
        }
        log.info("autoAllocationGameTeam size:{} ,actId:{},now:{},batch:{}", actId, wzryGameViews.size(), now, batch);
        for (WzryGameView view : wzryGameViews) {
            try {
                autoAllocationOneGameTeam(actId, now, view);
            } catch (Exception e) {
                log.error("autoAllocationOneGameTeam error, actId:{},gameViewCode:{},e:{}", actId, view.getGameViewCode(), e.getMessage(), e);
            }
        }
    }

    public void autoAllocationOneGameTeam(long actId, Date now, WzryGameView view) {
        log.info("autoAllocationGameTeam begin,view:{}", JSON.toJSONString(view));
        List<WzryGame> wzryGames = wzryDao.queryWzryGameByGameViewCode(actId, WzryGameState.INIT, view.getGameViewCode());
        List<WzryGameViewTeam> gameViewTeams = wzryDao.queryWzryGameViewTeam(actId, view.getId());
        //过滤未分配
        gameViewTeams = gameViewTeams.stream().filter(p -> Convert.toInt(p.getAllocationTeam(), 0) == 0).collect(Collectors.toList());
        //打乱
        Collections.shuffle(gameViewTeams);

        for (WzryGame wzryGame : wzryGames) {
            mySelf.init1V1GameTeam(actId, now, view, wzryGame, gameViewTeams);
        }
        wzryDao.updateWzryGameViewStatus(actId, view.getGameViewCode(), WzryGameViewState.INIT_TEAM);
        log.info("autoAllocationGameTeam done,view:{}", JSON.toJSONString(view));
    }

    @Transactional(rollbackFor = Exception.class)
    public void init1V1GameTeam(long actId, Date now, WzryGameView view, WzryGame wzryGame, List<WzryGameViewTeam> gameViewTeams) {
        for (int i = 0; i < BattleMode.GAME_1V1_ENTER_LIMIT; i++) {
            if (gameViewTeams.size() > 0) {
                //拿一个人,并移除掉
                WzryGameViewTeam wzryGameViewTeam = gameViewTeams.get(0);
                gameViewTeams.remove(wzryGameViewTeam);

                WzryGameTeam addGameTeam = new WzryGameTeam();
                addGameTeam.setId(IdGeneratorHelper.genId(WzryGameTeam.TABLE_NAME, new Date()));
                addGameTeam.setActId(actId);
                addGameTeam.setGameId(wzryGame.getId());
                addGameTeam.setUid(wzryGameViewTeam.getUid());
                addGameTeam.setCreateTime(new Date());
                //参赛队伍分配：上层要控制好赛事粒度参赛的串行，否则这里会有严重的并发问题！！！！！

                //该玩家所属阵营，1表示“A战队”，2表示“B战队”
                addGameTeam.setLimitTeam(i == 0 ? WzryLimitTeam.A : WzryLimitTeam.B);
                //座位号，1~5
                addGameTeam.setSeatId(1);
                addGameTeam.setInGame(0);
                gameecologyDao.insert(WzryGameTeam.class, addGameTeam);

                int updateResult = wzryDao.updateGameEnters(wzryGame.getId(), BattleMode.GAME_1V1_ENTER_LIMIT);
                if (updateResult <= 0) {
                    throw new RuntimeException("人数超出限制");
                }

                //写入已分配
                wzryDao.updateAllocationTeam(actId, view.getId(), wzryGameViewTeam.getUid());
            }
        }
        wzryDao.updateWzryGameStatus(actId, now, wzryGame.getId(), WzryGameState.INIT_TEAM, null);
    }
}
