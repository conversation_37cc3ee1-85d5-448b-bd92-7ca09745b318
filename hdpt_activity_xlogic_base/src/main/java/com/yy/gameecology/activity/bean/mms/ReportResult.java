package com.yy.gameecology.activity.bean.mms;

public enum ReportResult {
    /**
     * SUCCESS
     */
    SUCCESS(0,"成功"),
    /**
     * REQUEST_FAIL
     */
    REQUEST_FAIL(-1,"请求失败"),
    /**
     * REPORT_FAIL
     */
    REPORT_FAIL(-2,"上报失败"),

    VIOLATION(-3,"违规"),

    PARAM_ERROR(-4, "参数错误");

    String name;
    int code;

    ReportResult(int code , String name) {
        this.name = name;
        this.code = code;
    }

    public static ReportResult fromValue(int value){
        for(ReportResult e : ReportResult.values()){
            if(e.code == value){
                return e;
            }
        }
        return null;
    }
}

