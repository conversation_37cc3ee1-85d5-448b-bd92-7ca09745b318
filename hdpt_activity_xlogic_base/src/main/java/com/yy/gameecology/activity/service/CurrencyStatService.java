package com.yy.gameecology.activity.service;

import cn.hutool.core.util.StrUtil;
import com.yy.gameecology.activity.client.yrpc.CurrencyClient;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.protocol.pb.currency.CurrencyProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class CurrencyStatService {

    @Autowired
    private CurrencyClient currencyClient;

    @Autowired
    private SignedService signedService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    public void sendInfoflowMsg(long actId, long busiId, int size) {
        String cid = commonService.getActAttr(actId, "currency_stat_cid");
        if (StringUtils.isEmpty(cid)) {
            return;
        }

        List<CurrencyProto.UserCurrency> currencies = currencyClient.getUserCurrencyStat(1, cid, 10);
        StringBuilder builder = new StringBuilder();
        builder.append("sid -- uid -- 数量 -- 昵称").append('\n');
        if (CollectionUtils.isNotEmpty(currencies)) {
            List<Long> uids = currencies.stream().map(CurrencyProto.UserCurrency::getUid).toList();
            Map<Long, UserBaseInfo> userInfoMap = commonService.batchGetUserInfos(uids, false);
            for (var currency: currencies) {
                long uid = currency.getUid();
                long sid = signedService.getSignedSidByBusiId(uid, busiId);
                builder.append(StrUtil.padAfter(String.valueOf(sid), 40, ' ')).append(StringUtil.VERTICAL_BAR);
                builder.append(StrUtil.padAfter(String.valueOf(uid), 40, ' ')).append(StringUtil.VERTICAL_BAR);
                builder.append(StrUtil.padAfter(String.valueOf(currency.getAmount()), 20, ' ')).append(StringUtil.VERTICAL_BAR);
                String nick = "unknown";
                UserBaseInfo userInfo = userInfoMap.get(uid);
                if (userInfo != null) {
                    nick = userInfo.getNick();
                }

                builder.append(nick);
                builder.append('\n');
            }
        }

        String business = switch ((int) busiId) {
            case 500 -> "交友";
            case 810 -> "聊天室";
            default -> "未知";
        };
        String title = String.format("【剩余“完事如意”数量TOP%d】（%s）", size, business);

        String msg = builder.toString();

        String message = commonService.buildActRuliuMsg(actId, false, title, msg);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, message, Collections.emptyList());
    }
}
