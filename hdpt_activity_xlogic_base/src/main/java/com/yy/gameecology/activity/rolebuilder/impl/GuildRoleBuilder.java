package com.yy.gameecology.activity.rolebuilder.impl;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.rankroleinfo.GuildRoleItem;
import com.yy.gameecology.activity.rolebuilder.RoleBuilder;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SpringBeanAwareFactory;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.WebdbChannelInfo;
import com.yy.thrift.act_ext_support.MemberItemInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/9/17 0:31
 * @Modified:
 */
public class GuildRoleBuilder implements RoleBuilder<GuildRoleItem> {

//    private WebdbThriftClient webdbThriftClient = SpringBeanAwareFactory.getBean(WebdbThriftClient.class);

    private static final GuildRoleItem DEFAULT_OBJECT = new GuildRoleItem();
    static  {
        DEFAULT_OBJECT.setKey("0");
        DEFAULT_OBJECT.setName("神秘工会");
        DEFAULT_OBJECT.setAvatarInfo(Const.IMAGE.DEFAULT_CHANNEL_LOGO);
    }
    @Override
    public GuildRoleItem getDefaultObject() {
        return DEFAULT_OBJECT;
    }

    @Override
    public GuildRoleItem createBankObject() {
        return new GuildRoleItem();
    }

    /**
     * 从yy获取信息
     *
     * @param members
     * @return
     */
    @Override
    public Map<String, GuildRoleItem> buildRankByYy(Set<String> members) {
        if (CollectionUtils.isEmpty(members)){
            return Maps.newHashMap();
        }

        WebdbThriftClient webdbThriftClient = SpringBeanAwareFactory.getBean(WebdbThriftClient.class);
        List<Long> sids = members.stream().map(Long::parseLong).collect(Collectors.toList());
        Map<Long, WebdbChannelInfo> channelInfos = webdbThriftClient.batchGetChannelInfo(sids);
        GuildRoleItem defaultObject = getDefaultObject();
        return sids.stream().map(sid -> {
            WebdbChannelInfo channelInfo = channelInfos.get(sid);
            GuildRoleItem roleItem = new GuildRoleItem();
            roleItem.setSid(sid);
            roleItem.setName(channelInfo == null ? defaultObject.getName() : channelInfo.getName());
            roleItem.setAvatarInfo(channelInfo == null ? defaultObject.getAvatarInfo() : WebdbUtils.getLogo(channelInfo));
            long asid = Optional.ofNullable(channelInfo).map(WebdbChannelInfo::getAsid).filter(StringUtils::isNumeric).map(Long::parseLong).orElse(sid);
            roleItem.setAsid(asid);
            return roleItem;
        }).collect(Collectors.toMap(roleItem->roleItem.getSid().toString(), Function.identity()));
    }

    @Override
    public GuildRoleItem buildRankItemByEx(MemberItemInfo memberItemInfo, GuildRoleItem roleItem) {
        if (memberItemInfo.getExt() != null) {
            Map<String, String> ext = memberItemInfo.getExt();
            roleItem.setAsid(Convert.toLong(ext.get("asid"), roleItem.getSid()));
        }
        return roleItem;
    }



}
