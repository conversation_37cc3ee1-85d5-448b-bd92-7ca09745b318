package com.yy.gameecology.activity.bean;

import com.yy.gameecology.common.utils.StringUtil;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * desc:更新任务结果
 *
 * <AUTHOR>
 * @date 2023-08-24 14:40
 **/
@Data
public class UpdateTaskResult {

    /**
     * 0==正常更新任务   1==seq重复，重放了
     */
    public long code;
    /**
     * 本次送礼触发的 闯关轮数
     */
    public long roundComplete;

    /**
     * 开始站，从1开始
     */
    public long startTaskIndex;

    /**
     * startTaskIndex = currTaskIndex 表示站无变化
     * 当前完成站，从1开始，1表示还没有过站。如果无需重置任务，则当前完成站最大值为 总站+1，代表全部完成了
     */
    public long currTaskIndex;

    /**
     * 当前增加的分数
     */
    public long itemScore;

    /**
     * 增加后的分数
     */
    public long afterScore;

    /**
     * key: memberid@roletype ; value:贡献成员分数key
     */
    public Map<String, List<String>> contributeKey;


//    private List<List<String>> loginfo;

}
