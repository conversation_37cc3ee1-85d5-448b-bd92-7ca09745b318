package com.yy.gameecology.activity.client.thrift;

import com.google.common.collect.Maps;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.consts.CacheTimeout;
import com.yy.gameecology.common.utils.MyListUtils;
import com.yy.thrift.turnover.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/10/12 11:43
 **/
@Service
@Slf4j
public class TurnoverFamilyThriftClient {

    @Reference(protocol = "nythrift_compact", owner = "${turnoverContract_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"})
    private TFamilyService.Iface proxy = null;

    @Reference(protocol = "nythrift_compact", owner = "${turnoverContract_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"}
            , retries = 2 , cluster = "failover")
    private TFamilyService.Iface readProxy = null;

    public TFamilyService.Iface getProxy() {
        return proxy;
    }

    public TFamilyService.Iface getReadProxy() {
        return readProxy;
    }

    /**
     * 查询主榜的签约家族id
     **/
    public long queryContractFamilyId(long uid) {
        try {
            TFamilyContract familyContract = getReadProxy().queryContractByLiveUid(uid);
            if (familyContract != null && familyContract.getLiveUid() == uid) {
                return familyContract.getFamilyId();
            }
        } catch (Exception ex) {
            log.error("queryContractByLiveUid error,uid={}", uid, ex);
        }

        return 0;
    }

    public long querySsidFamilyId(long ssid) {
        try {
            TFamilySsid tFamilySsid = getReadProxy().queryFamilySsidBySsid(ssid);
            if (tFamilySsid != null && tFamilySsid.getSsid() == ssid) {
                return tFamilySsid.getFamilyId();
            }
        } catch (Exception ex) {
            log.error("querySsidFamilyId error,ssid={}", ssid, ex);
        }

        return 0;
    }

    /**
     * 批量查询主持的签约家族id
     **/
    public Map<Long, Long> batchQueryContractFamilyIds(List<Long> uidList,boolean throwError) {
        Map<Long, Long> map = Maps.newHashMap();
        try {
            Map<Long, TFamilyContract> familyContractMap = getReadProxy().batchQueryContractByLiveUids(uidList);
            for (TFamilyContract value : familyContractMap.values()) {
                map.put(value.getLiveUid(), value.getFamilyId());
            }
        } catch (Exception ex) {
            log.error("batchQueryContractFamilyIds error,uidList={},e:{}", uidList, ex.getMessage(), ex);
            if (throwError) {
                throw new RuntimeException(ex);
            }
        }

        return map;
    }

    /**
     * 批量查询主持的签约家族id
     **/
    public Map<Long, Long> batchQueryContractFamilyIds(List<Long> uidList) {
        return batchQueryContractFamilyIds(uidList, false);
    }

    /**
     * 自动分批，批量查询主持的签约家族id
     **/
    public Map<Long, Long> batchQueryContractFamilyIds(List<Long> uidList, int batchSize) {
        Map<Long, Long> result = Maps.newHashMap();
        List<List<Long>> uidBatch = MyListUtils.subList(uidList, batchSize);
        for (List<Long> uidBatchItem : uidBatch) {
            Map<Long, Long> itemMap = batchQueryContractFamilyIds(uidBatchItem, true);
            if (MapUtils.isNotEmpty(itemMap)) {
                result.putAll(itemMap);
            }
        }

        return result;
    }


    public Map<Long, TFamily> batchGetFamily(List<Long> familyIds) {
        if (CollectionUtils.isEmpty(familyIds)) {
            return Maps.newHashMap();
        }
        try {
            return getReadProxy().queryFamily(familyIds);
        } catch (Exception ex) {
            log.error("batchGetFamily error,familyIds={}", familyIds, ex);
            return Maps.newHashMap();
        }
    }

    @Cached(timeToLiveMillis = CacheTimeout.PW_TUAN)
    public List<TFamilySsid> listFamilySsid(long familyId) {
        try {
            TFamilySsidPageResult pageResult = getReadProxy().queryFamilySsidByFamilyId(familyId, 1, 50);

            return pageResult.contents;
        } catch (Exception ex) {
            log.error("listFamilySsid error,familyId={}", familyId, ex);
        }

        return new ArrayList<>();
    }
}
