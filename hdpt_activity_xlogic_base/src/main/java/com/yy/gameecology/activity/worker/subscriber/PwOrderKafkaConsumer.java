package com.yy.gameecology.activity.worker.subscriber;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.mq.PwOrderEvent;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.layer.BroActLayerService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.MqConst;
import com.yy.gameecology.hdzj.HdzjEventDispatcher;
import com.yy.thrift.hdztranking.RoleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/7/10 9:48
 **/
@ConditionalOnExpression(Const.EXPRESSION_NOT_HISTORY)
@Component
@Slf4j
public class PwOrderKafkaConsumer {
    @Autowired
    private HdzjEventDispatcher hdzjEventDispatcher;
    @Autowired
    private BroActLayerService broActLayerService;

    @Autowired
    private CommonService commonService;


    @KafkaListener(containerFactory = "hdptWxKafkaContainerFactory", id = "pw_order_wx_kafka",
            topics = MqConst.PW_ORDER_COMPLETE_TOPIC,
            groupId = "${kafka.hdpt.wx.group}")
    public void onPwOrderEventFromWx(ConsumerRecord<String, String> record) {
        onPwOrderEvent("wx", record);
    }

    @KafkaListener(containerFactory = "hdptWxXdcKafkaContainerFactory", id = "pw_order_xdc_kafka",
            topics = MqConst.PW_ORDER_COMPLETE_TOPIC,
            groupId = "${kafka.hdpt.wx-xdc.group}")
    public void onPwOrderEventFromXdc(ConsumerRecord<String, String> record) {
        onPwOrderEvent("xdc", record);
    }

    private void onPwOrderEvent(String from, ConsumerRecord<String, String> record) {
        log.info("pw order,from={},msg:{}", from, record.value());

        PwOrderEvent event = JSON.parseObject(record.value(), PwOrderEvent.class);

        if (!commonService.isSkillCardChannel(event.getSid())) {
            broActLayerService.invokeAllEffectActRefresh(event.getOpSid(), 0L);
        } else {
            log.info("this is a skill card channel");
        }

        try {
            hdzjEventDispatcher.notify(event.getUserUid(), RoleType.USER, event, event.getOrderId());
        } catch (Throwable e) {
            log.error("[handleMessage notify] err message:{} PwOrderEvent:{}", e.getMessage(), event, e);
        }
    }
}
