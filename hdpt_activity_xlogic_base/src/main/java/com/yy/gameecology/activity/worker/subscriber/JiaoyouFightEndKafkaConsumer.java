package com.yy.gameecology.activity.worker.subscriber;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.mq.ChannelFightEndEvent;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.MqConst;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.HdzjEventDispatcher;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * 交友乱斗事件
 *
 * <AUTHOR>
 * @since 2023/7/7 17:14
 **/
@ConditionalOnExpression(Const.EXPRESSION_NOT_HISTORY)
@Component
@Slf4j
public class JiaoyouFightEndKafkaConsumer {
    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Autowired
    private HdzjEventDispatcher hdzjEventDispatcher;

    private static final String EVENT_SEQ_KEY = "jiaoyou_fight_end_%s:%s";
    /**
     * 过期时间：2天，单位：秒
     */
    private static final long EXPIRE_SEC = 2 * 24 * 60 * 60;


    @KafkaListener(containerFactory = "jiaoyouWxKafkaContainerFactory", id = "jy_wx_kafka_fightEnd",
            topics = MqConst.JY_FIGHT_END_TOPIC,
            groupId = "${kafka.jiaoyou.fight_end.wx.group}")
    public void onJiaoyouFightEndMessageFromWx(ConsumerRecord<String, String> consumerRecord) {
        onJiaoyouFightEndMessage("wx", consumerRecord.value());
    }

    @KafkaListener(containerFactory = "jiaoyouSzKafkaContainerFactory", id = "jy_sz_kafka_fightEnd",
            topics = MqConst.JY_FIGHT_END_TOPIC,
            groupId = "${kafka.jiaoyou.fight_end.sz.group}")
    public void onJiaoyouFightEndMessageFromSz(ConsumerRecord<String, String> consumerRecord) {
        onJiaoyouFightEndMessage("sz", consumerRecord.value());
    }

    private void onJiaoyouFightEndMessage(String from, String payload) {
        log.info("onJiaoyouFightEndMessage from={},payload={}", from, payload);
        try {
            ChannelFightEndEvent event = JSON.parseObject(payload, ChannelFightEndEvent.class);
            handle(event);
        } catch (Exception ex) {
            log.error("onJiaoyouFightEndMessage error,from={},payload={}", from, payload, ex);
        }
    }

    private void handle(ChannelFightEndEvent fightEndEvent) {
        if (fightEndEvent == null) {
            log.info("jiaoyouFightEnd ignore.event is null");
            return;
        }

        String seq = fightEndEvent.getGameId();
        String seqKey = String.format(EVENT_SEQ_KEY, SysEvHelper.getGroup(), seq);
        if (!actRedisDao.setNX(redisConfigManager.temp_act_group, seqKey, StringUtil.ONE, EXPIRE_SEC)) {
            log.warn("jiaoyouFightEnd ignore. seq double,seq:{}", seq);
            return;
        }
        hdzjEventDispatcher.notify(fightEndEvent);
        log.info("jiaoyouFightEnd ok. seq:{}", seq);
    }
}
