package com.yy.gameecology.activity.worker.server;

import com.yy.gameecology.activity.config.svcsdk.SvcSDKCallback;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.service.SdkInitData;
import com.yy.service.SvcSDK;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR> 2020/4/29
 */
@Component
public class SvcSDKServer implements ApplicationListener<ContextRefreshedEvent> {

    private static final Logger log = LoggerFactory.getLogger(SvcSDKServer.class);

    @Value("${svcsdk.appId}")
    private int appId;

    @Value("${svcsdk.s2sRegKey}")
    private String s2sRegKey;

    @Autowired
    private SvcSDKCallback svcSDKCallback;

    private final AtomicBoolean start = new AtomicBoolean(false);

    private boolean started = false;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (SysEvHelper.isLocal() || SysEvHelper.isYuFa()) {
            return;
        }

        if (!start.compareAndSet(false, true)) {
            return;
        }

        // 判断是否需要跳过 svc sdk 的 初始化 - added by guoliping / 2021-09-14
        if(SysEvHelper.skipSvcsdkInit()) {
            log.warn("============================= svcsdk init is skipped!!! =============================");
            return;
        }

        try {
            SdkInitData init = new SdkInitData(appId, s2sRegKey, false, true, true, true);
            log.info("svcsdk init begin. appId:{} s2sRegKey:{}", appId, s2sRegKey);
            SvcSDK.getInstance().sdkInit(init);
            log.info("svcsdk init end. appId:{} s2sRegKey:{}", appId, s2sRegKey);

            SvcSDK.getInstance().setAppCallback(svcSDKCallback, null);
            log.info("svcsdk callback:{}", svcSDKCallback);

            SvcSDK.getInstance().sdkStart();
            log.info("svcsdk start. appId:{} s2sRegKey:{}", appId, s2sRegKey);
            started = true;
        } catch (Throwable e) {
            log.error("SvcSDK start fail. 请关注! err:{}", e.getMessage(), e);
        }
    }

    public boolean isStarted() {
        return started;
    }

    @PreDestroy
    public void destroy() {
    }
}
