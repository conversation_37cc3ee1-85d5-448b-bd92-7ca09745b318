package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.client.thrift.PeiwanThriftClient;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.bean.WechatRobotMsg;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.SystemUtil;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.HdztActorInfo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-01-25 16:37
 **/
@Service
public class EnrollmentNewService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private HdztActorInfoService hdztActorInfoService;

    @Autowired
    private PeiwanThriftClient peiwanThriftClient;

    @Autowired
    private EnrollmentService enrollmentService;


    @Autowired
    private CommonService commonService;

    @Autowired
    private WechatRobotService wechatRobotService;


    @Report
    public Set<String> getChannelTuan(Long actId, Long sid) {
        Set<String> tuan = peiwanThriftClient.queryPwChannelTuan(actId, sid);
        if (tuan == null) {
            return Sets.newHashSet();
        }
        return tuan;
    }

    //获取成员，如果同1个业务，同1个成员存在相同报名数据，此方法不适用
    @Report
    public EnrollmentInfo getFirstEnrolMember(Long actId, Long busiId, Integer roleType, String memberId) {

        List<EnrollmentInfo> enrollmentInfos = hdztRankingThriftClient
                .queryEnrollmentInfoNocache(actId, Convert.toLong(roleType), Collections.singletonList(memberId));
        if (CollectionUtils.isEmpty(enrollmentInfos)) {
            return null;
        }
        return enrollmentInfos.get(0);
    }

    /**
     * 尝试先从缓存获取，缓存没有再调用接口获取
     */
    public EnrollmentInfo tryGetFirstEnrolMemberCache(Long actId, Long busiId, Integer roleType, String memberId, int retry) {
        EnrollmentInfo enrollmentInfo = tryGetFirstEnrolMemberCache(actId, busiId, roleType, memberId);
        int count = 0;
        while (count <= retry && enrollmentInfo == null) {
            enrollmentInfo = tryGetFirstEnrolMemberCache(actId, busiId, roleType, memberId);
            count++;
            SysEvHelper.waiting(100);
        }
        return enrollmentInfo;
    }

    /**
     * 尝试先从缓存获取，缓存没有再调用接口获取
     */
    public EnrollmentInfo tryGetFirstEnrolMemberCache(Long actId, Long busiId, Integer roleType, String memberId) {
        EnrollmentInfo enrollmentInfo = enrollmentService.getFirstEnrolMember(actId, busiId, roleType, memberId);
        if (enrollmentInfo != null) {
            return enrollmentInfo;
        }
        return getFirstEnrolMember(actId, busiId, roleType, memberId);
    }

    //获取成员角色名称，如果同1个业务，同1个成员存在相同报名数据，此方法不适用
    @Report
    public Long getFirstEnrolDestRoleId(Long actId, Long busiId, Integer roleType, String memberId) {

        List<EnrollmentInfo> enrollmentInfos = hdztRankingThriftClient
                .queryEnrollmentInfoNocache(actId, Convert.toLong(roleType), Collections.singletonList(memberId));
        if (CollectionUtils.isEmpty(enrollmentInfos)) {
            return 0L;
        }
        return enrollmentInfos.get(0).getDestRoleId();
    }

    //获取成员角色名称，如果同1个业务，同1个成员存在相同报名数据，此方法不适用
    @Report
    public String getFirstEnrolDestRoleName(Long actId, Long busiId, Integer roleType, String memberId) {

        List<EnrollmentInfo> enrollmentInfos = hdztRankingThriftClient
                .queryEnrollmentInfoNocache(actId, Convert.toLong(roleType), Collections.singletonList(memberId));
        if (CollectionUtils.isEmpty(enrollmentInfos)) {
            return "";
        }
        EnrollmentInfo member = enrollmentInfos.get(0);
        Long roleId = member.getDestRoleId();
        HdztActorInfo hdztActorInfo = hdztActorInfoService.getHdztActorInfo(roleId);
        if (hdztActorInfo == null) {
            return "";
        }

        return hdztActorInfo.getName();
    }

    public String updateEnrollInfo(String seq, long opUid, long actId, String memberId, long roleType, long newRoleId, long signSid) {

        log.info("updateEnrollInfo begin,opUid:{},actId:{},memberId:{},roleType:{},newRoleId:{},seq:{}", opUid, actId, memberId, roleType, newRoleId, seq);
        List<EnrollmentInfo> enrollmentInfos = hdztRankingThriftClient.queryEnrollmentInfoNocache(actId, roleType, Collections.singletonList(memberId));
        if (CollectionUtils.isEmpty(enrollmentInfos)) {
            log.warn("updateEnrollInfo error,not found enroll info,opUid:{},actId:{},memberId:{},roleType:{},newRoleId:{},seq:{}", opUid, actId, memberId, roleType, newRoleId, seq);
            return "1001";
        }
        if (enrollmentInfos.size() > 1) {
            log.warn("updateEnrollInfo error,enroll size:{},opUid:{},actId:{},memberId:{},roleType:{},newRoleId:{},seq:{}", enrollmentInfos.size(), opUid, actId, memberId, roleType, newRoleId, seq);
            return "1002";
        }
        HdztActorInfo hdztActorInfo = hdztActorInfoService.getHdztActorInfo(newRoleId);
        if (hdztActorInfo == null) {
            return "1003";
        }
        if (hdztActorInfo.getType() != roleType) {
            return "1004";
        }

        EnrollmentInfo oldEnrollInfo = enrollmentInfos.get(0);

        EnrollmentInfo newEnrollInfo = new EnrollmentInfo();
        newEnrollInfo.setActId(oldEnrollInfo.getActId());
        newEnrollInfo.setMemberId(oldEnrollInfo.getMemberId());
        //这个字段没用到
        newEnrollInfo.setRoleBusiId(0);
        newEnrollInfo.setRoleType(oldEnrollInfo.getRoleType());
        newEnrollInfo.setDestRoleId(newRoleId);
        //src role id字段意义，在这里是作为老的分组
        newEnrollInfo.setSrcRoleId(newRoleId);
        newEnrollInfo.setStatus(oldEnrollInfo.getStatus());

        //签约公会如果有指定则用指定的，没指定则用老的
        long newSignSid = oldEnrollInfo.getSignSid();
        if (signSid > 0) {
            newSignSid = signSid;
        }
        long asid = commonService.getAsid(newSignSid);
        newEnrollInfo.setSignSid(newSignSid);
        newEnrollInfo.setSignAsid(asid);

        newEnrollInfo.setStartRankId(oldEnrollInfo.getStartRankId());
        newEnrollInfo.setStartPhaseId(oldEnrollInfo.getStartPhaseId());
        newEnrollInfo.setExtData(oldEnrollInfo.getExtData());

        String remark = String.format("人工调整分组,oldRoleId:%s,opUid:%s,ip:%s", oldEnrollInfo.getDestRoleId(), opUid, SystemUtil.getIp());
        int ret = hdztRankingThriftClient.saveEnrollment(actId, newEnrollInfo, true, remark, seq);
        if (ret != 0) {
            log.error("updateEnrollInfo error,enroll size:{},opUid:{},actId:{},memberId:{},roleType:{},newRoleId:{},ret:{},seq:{}", enrollmentInfos.size(), opUid, actId, memberId, roleType, newRoleId, ret
                    , seq);
            return "9999";

        }

        log.info("updateEnrollInfo ok,seq:{},actId:{},old:{},new:{}", seq, actId, JSON.toJSONString(oldEnrollInfo), JSON.toJSONString(newEnrollInfo));

        WechatRobotMsg msg = WechatRobotMsg.buildText()
                .content(String.format("分组调整成功,opUid:%s,actId:%s,memberId:%s,roleType:%s,newRoleId:%s,seq:%s", opUid, actId, memberId, roleType, newRoleId, seq))
                .build();
        wechatRobotService.notify(WechatRobotService.ACTIVITY_ROBOT_WEBHOOK, msg);


        return "ok";
    }

}
