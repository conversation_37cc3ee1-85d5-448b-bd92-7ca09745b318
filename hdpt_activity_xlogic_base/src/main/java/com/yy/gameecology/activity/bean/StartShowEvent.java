package com.yy.gameecology.activity.bean;

import com.yy.thrift.hdztranking.BusiId;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class StartShowEvent {

    protected String seq;

    protected BusiId busiId;

    protected long uid;

    protected Date eventTime;

    public StartShowEvent() {}

    public StartShowEvent(String seq, BusiId busiId, long uid, Date eventTime) {
        this.seq = seq;
        this.busiId = busiId;
        this.uid = uid;
        this.eventTime = eventTime;
    }
}
