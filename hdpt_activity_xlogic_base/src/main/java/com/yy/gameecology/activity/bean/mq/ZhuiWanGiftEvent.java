package com.yy.gameecology.activity.bean.mq;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/27 11:27
 **/
@Data
public class ZhuiWanGiftEvent {
    /**
     * 送礼序号
     */
    private String seq;

    /**
     * 34 追玩语音房
     */
    private int appid;

    /**
     * 送礼渠道，133 ios语音房，134 安卓语音房
     */
    private int usedChannel;

    /**
     * 顶级频道号
     */
    private long sid;

    /**
     * 子频道号
     */
    private long ssid;

    /**
     * 送礼uid
     */
    private long usedUid;

    /**
     * 房主uid，为0是没有
     */
    private long roomOwnerUid;

    /**
     * 收礼uid
     */
    private long anchorUid;

    /**
     * 送礼时间戳 (毫秒)
     */
    private long usedTimestamp;

    /**
     * 送礼列表
     */
    private List<UsedInfo> usedInfos;

    /**
     * 扩展字段  连送次数:{"comboInfo":{"comboHits":1}}
     */
    private String expand;

    /**
     * 礼物名字
     */
    private String name;

    /**
     * 礼物icon
     */
    private String staticIcon;

    @Data
    public static class UsedInfo {
        /**
         * 礼物id
         */
        private long propId;

        /**
         * 货币类型，68是金钻，与人民币1：1000
         */
        private int currencyType;

        /**
         * 虚拟货币礼物单价
         */
        private long amount;

        /**
         * 人民币分礼物单价, 单位:分. 100代表1元
         */
        private long rmbFenAmount;

        /**
         * 送礼数量
         */
        private long propCount;
    }
}
