package com.yy.gameecology.activity.service.actresult.builderimpl;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.service.actresult.ActResultMemberLoader;
import com.yy.gameecology.common.utils.Convert;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-11-17 16:27
 **/
@Component
public class RoomActResultBuilder extends BuilderBase implements ActResultMemberLoader {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Override
    public Map<String, Map<String, MemberInfo>> loadMemberInfo(long actId, long type, long roleType, List<String> memberIds) {
        List<Integer> roomIds = Lists.newArrayList();
        memberIds.forEach(x -> {
            roomIds.add(Convert.toInt(x, 0));
        });
        Map<Integer, RoomInfo> roomInfoMap = zhuiwanRoomInfoClient.roomInfoMapByRoomId(roomIds);

        Map<String, MemberInfo> memberInfoMap = Maps.newHashMap();
        for (String memberId : memberIds) {
            RoomInfo roomInfo = roomInfoMap.get(Convert.toInt(memberId, 0));
            if (roomInfo != null) {
                MemberInfo memberInfo = new MemberInfo();
                memberInfo.setName(roomInfo.getTitle());
                memberInfo.setLogo(roomInfo.getCover());
                memberInfoMap.put(memberId, memberInfo);
            }
        }

        return ImmutableMap.of(builderKey(type, roleType), memberInfoMap);
    }
}
