package com.yy.gameecology.activity.client.yrpc;

import cn.yy.ent.zhuiya.task.noaward.gen.pb.NoAwardTask;
import com.googlecode.protobuf.format.JsonFormat;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-09-20 14:39
 **/
@Component
public class ZhuiyaNoAwardTaskClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    @Reference(protocol = "yrpc", owner = "zhuiya_task", registry = {"yrpc-reg"}, lazy = true)
    private ZhuiyaNoAwardTask writeProxy;

    @Reference(protocol = "yrpc", owner = "zhuiya_task", registry = {"yrpc-reg"}, retries = 2, cluster = "failover")
    private ZhuiyaNoAwardTask readProxy;

    private ZhuiyaNoAwardTask getWriteProxy() {
        return writeProxy;
    }

    private ZhuiyaNoAwardTask getReadProxy() {
        return readProxy;
    }

    public NoAwardTask.ActiveUserTaskRsp activeUserTask(NoAwardTask.ActiveUserTaskReq req) {

        NoAwardTask.ActiveUserTaskRsp rsp = getWriteProxy().activeUserTask(req);
        log.info("activeUserTask req:{},rsp:{}", JsonFormat.printToString(req), JsonFormat.printToString(rsp));
        return rsp;
    }

    public NoAwardTask.GetUserTaskRsp getUserTask(NoAwardTask.GetUserTaskReq req) {

        NoAwardTask.GetUserTaskRsp rsp = getReadProxy().getUserTask(req);
        log.info("getUserTaskRsp req:{},rsp:{}", JsonFormat.printToString(req), JsonFormat.printToString(rsp));
        return rsp;
    }
}
