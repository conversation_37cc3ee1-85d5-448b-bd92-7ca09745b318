package com.yy.gameecology.activity.client.yrpc;

import com.google.common.cache.CacheBuilder;
import com.yy.gameecology.activity.bean.grade.GradeIcon;
import com.yy.protocol.pb.skillcard.grade.AnchorGrade;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

@Component
public class AnchorGradeClient {

    private static final ConcurrentMap<Long, GradeIcon> GRADE_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(3, TimeUnit.MINUTES)
            .maximumSize(2000)
            .<Long, GradeIcon>build()
            .asMap();

    @Reference(protocol = "yrpc", owner = "skillcard_server_yrpc", registry = "yrpc-reg")
    private AnchorGradeProvider proxy;

    public Map<Long, GradeIcon> batchGetAnchorGrade(Collection<Long> uids) {
        if (CollectionUtils.isEmpty(uids)) {
            return Collections.emptyMap();
        }

        Map<Long, GradeIcon> result = new HashMap<>(uids.size());
        Set<Long> queryUids = new HashSet<>(uids.size());
        for (Long uid : uids) {
            GradeIcon g = GRADE_CACHE.get(uid);
            if (g == null) {
                queryUids.add(uid);
                continue;
            }

            result.put(uid, g);
        }

        if (!queryUids.isEmpty()) {
            List<AnchorGrade.AnchorGradeIcon> icons = batchGetAnchorGradeIcon(queryUids);
            for (AnchorGrade.AnchorGradeIcon icon : icons) {
                GradeIcon gradeIcon = parseGradeIcon(icon);
                GRADE_CACHE.put(icon.getUid(), gradeIcon);
                result.put(icon.getUid(), gradeIcon);
            }
        }

        return result;
    }

    private List<AnchorGrade.AnchorGradeIcon> batchGetAnchorGradeIcon(Collection<Long> uids) {
        if (CollectionUtils.isEmpty(uids)) {
            return Collections.emptyList();
        }

        AnchorGrade.QueryIconReq req = AnchorGrade.QueryIconReq.newBuilder()
                .addAllUid(uids)
                .build();

        var rsp = proxy.queryIcon(req);
        if (rsp == null || rsp.getCode() != 0) {
            return Collections.emptyList();
        }

        return rsp.getIconList();
    }

    private GradeIcon parseGradeIcon(AnchorGrade.AnchorGradeIcon gradeIcon) {
        GradeIcon result = new GradeIcon();
        result.setIcon(gradeIcon.getIcon());
        result.setIconLong(gradeIcon.getIconLong());
        result.setIconBig(gradeIcon.getIconBig());
        result.setIconLongDynamic(gradeIcon.getIconLongDynamic());
        result.setLevel(gradeIcon.getLevel());
        return result;
    }
}
