/**
 * RankDataEvent.java / 2020年4月8日 上午11:59:58
 * 
 * Copyright (c) 2020, YY Inc. All Rights Reserved.
 * 
 * 郭立平[<EMAIL>]
 */
package com.yy.gameecology.activity.bean;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.gameecology.common.utils.DateUtil;

import java.util.Date;
import java.util.Map;

/** 
 * 排行数据事件：当某些行为发生时产生响应的数据事件（比如送礼数据、签到数据、玩法数据  等）
 * 
 * <AUTHOR>
 * @date 2020年4月8日 上午11:59:58 
 */
public class RankDataEvent {
    // extData 扩展属性名字定义 /////////////////////////////////////////////////////////////////////////////
    // 指示不做事件时延调整，用于一些定制的 大延迟 上报场景, 1:不调，其它值：要调 - added by guoliping/2022-03-02
    public static final String NOT_ADJUST_EVENT_TIME_DELAY = "not_adjust_event_time_delay";

    // 发起请求的业务标识
    private long busiId;

    // 活动ID
    private long actId = 0;

    // 在  actId 下唯一
    private String seq;
    
    // key 是参与者角色， val 是具体的参与者标识值
    private Map<Long, String> actors = Maps.newHashMap();
    
    // 发生事项标识（可以是礼物ID， 动作ID等等。。。）
    private String itemId;
    
    // 本次事件中 item 的次数
    private long count;
    
    // 本次事件中 总积分值（这个必须由业务方算好，因中台并不知道怎么算积分）
    private long score;
    
    // 时间戳，精确到毫秒
    private long timestamp;
    
    // 指定角色组合累计时的项目数量，key是角色组合串如 role1&role2&...，val是数量，累榜发生匹配时优先级高于 count（非必须）
    private Map<String, Long> roleCounts = Maps.newHashMap();
    
    // 指定角色组合累计时的项目积分值，key是角色组合串如 role1&role2&...，val是积分值，累榜发生匹配时优先级高于 score（非必须） 
    private Map<String, Long> roleScores = Maps.newHashMap();    
    
    // 指定榜单累计时的项目数量，key是榜单ID，val是数量，累榜发生匹配时优先级高于 roleCounts（非必须）
    private Map<Long, Long> rankCounts = Maps.newHashMap();
    
    // 指定榜单累计时的项目积分值，key是榜单ID，val是积分值，累榜发生匹配时优先级高于 roleScores（非必须）
    private Map<Long, Long> rankScores = Maps.newHashMap();
    
    // 用户IP，统计、防刷、等使用（非必须）
    private String ip;
    
    // 用户机器码，统计、防刷、等使用（非必须）
    private String mac;
    
    // 扩展长整形，协商使用
    private long extLong;
    
    // 扩展 map，协商使用
    private Map<String, String> extData = Maps.newHashMap();
    
    // 请求参数签名（使用 busiId 和 actId 下的组合key，签名算法待定，目前尚未启用）
    private String sign;

    public long getBusiId() {
        return busiId;
    }

    public void setBusiId(long busiId) {
        this.busiId = busiId;
    }

    public long getActId() {
        return actId;
    }

    public void setActId(long actId) {
        this.actId = actId;
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public Map<Long, String> getActors() {
        return actors;
    }

    public void setActors(Map<Long, String> actors) {
        this.actors = actors;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public long getCount() {
        return count;
    }

    public void setCount(long count) {
        this.count = count;
    }

    public long getScore() {
        return score;
    }

    public void setScore(long score) {
        this.score = score;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public Map<String, Long> getRoleCounts() {
        return roleCounts;
    }

    public void setRoleCounts(Map<String, Long> roleCounts) {
        this.roleCounts = roleCounts;
    }

    public Map<String, Long> getRoleScores() {
        return roleScores;
    }

    public void setRoleScores(Map<String, Long> roleScores) {
        this.roleScores = roleScores;
    }

    public Map<Long, Long> getRankCounts() {
        return rankCounts;
    }

    public void setRankCounts(Map<Long, Long> rankCounts) {
        this.rankCounts = rankCounts;
    }

    public Map<Long, Long> getRankScores() {
        return rankScores;
    }

    public void setRankScores(Map<Long, Long> rankScores) {
        this.rankScores = rankScores;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public long getExtLong() {
        return extLong;
    }

    public void setExtLong(long extLong) {
        this.extLong = extLong;
    }

    public Map<String, String> getExtData() {
        return extData;
    }

    public void setExtData(Map<String, String> extData) {
        this.extData = extData;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String detail() {
        return JSON.toJSONString(this);
    }
    
    public String toString() {
        return String.format("RankDataEvent:[actId:%s, busiId:%s, itemId:%s, actors:%s, time:%s, seq:%s]", actId, busiId,
            itemId, JSON.toJSONString(actors), DateUtil.format(new Date(timestamp)), seq);
    }

    public boolean notAdjustEventTimeDelay() {
        return extData != null && "1".equals(extData.get(NOT_ADJUST_EVENT_TIME_DELAY));
    }
}
