package com.yy.gameecology.activity.bean;


import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-04-22 14:43
 **/
public class RankAwardConfig {
    @ComponentAttrField(labelText = "榜单名次")
    private int rank;
    private int type;
    private String itemName;
    @ComponentAttrField(labelText = "奖励数量")
    private long awardValue;
    @ComponentAttrField(labelText = "奖励角色类型")
    private int awardMemberRoleType;
    private String desc;

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public long getAwardValue() {
        return awardValue;
    }

    public void setAwardValue(long awardValue) {
        this.awardValue = awardValue;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getAwardMemberRoleType() {
        return awardMemberRoleType;
    }

    public void setAwardMemberRoleType(int awardMemberRoleType) {
        this.awardMemberRoleType = awardMemberRoleType;
    }
}
