package com.yy.gameecology.activity.processor.ranking;

import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.common.bean.Template;

import javax.annotation.PostConstruct;
import java.util.List;

public abstract class BaseRankingProcessor implements RankingProcessor {

    @PostConstruct
    @NeedRecycle(author = "liqingyang", notRecycle = true)
    public void registerProcesser() {
        RankingProcessorManager.register(getActId(), getActRankId(), getProcessorType(), this);
    }
    
    abstract protected int getProcessorType();

    @Override
    public Long getActId() {
        return null;
    }

    @Override
    public List<Long> getActRankId() {
        return null;
    }

    public static Template getTemplateByBusiId(long busiId) {
        switch ((int) busiId) {
            case 0:
                return Template.any;
            case 400:
                return Template.gamebaby;
            case 500:
                return Template.makefriend;
            case 600:
                return Template.y<PERSON>zhan;
            case 900:
                return Template.peiwan;
            default:
                return Template.unknown;
        }
    }
}
