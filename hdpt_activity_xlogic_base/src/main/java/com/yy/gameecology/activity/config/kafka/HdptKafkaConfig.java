package com.yy.gameecology.activity.config.kafka;

import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

/**
 * 活动平台kafka配置
 *
 * <AUTHOR>
 * @since 2023/7/7 16:34
 **/
@Configuration
public class HdptKafkaConfig {
    @Bean
    @ConfigurationProperties("kafka.hdpt.wx")
    public KafkaProperties hdptWxKafkaProperties() {
        return new KafkaProperties();
    }

    @Bean
    public KafkaListenerContainerFactory hdptWxKafkaContainerFactory(KafkaProperties hdptWxKafkaProperties) {
        return KafkaConfig.createContainerFactory(hdptWxKafkaProperties);
    }

    @Bean
    @ConfigurationProperties("kafka.hdpt.wx-xdc")
    public KafkaProperties hdptWxXdcKafkaProperties() {
        return new KafkaProperties();
    }

    @Bean
    public KafkaListenerContainerFactory hdptWxXdcKafkaContainerFactory(KafkaProperties hdptWxXdcKafkaProperties) {
        return KafkaConfig.createContainerFactory(hdptWxXdcKafkaProperties);
    }


    @Bean
    public KafkaTemplate<String, String> hdptWxKafkaTemplate(KafkaProperties hdptWxKafkaProperties) {
        return createKafkaTemplate(hdptWxKafkaProperties);
    }

    @Bean
    public KafkaTemplate<String, String> hdptSzKafkaTemplate(KafkaProperties hdptWxXdcKafkaProperties) {
        return createKafkaTemplate(hdptWxXdcKafkaProperties);
    }



    private KafkaTemplate<String, String> createKafkaTemplate(KafkaProperties kafkaProperties) {
        ProducerFactory<String, String> producerFactory = new DefaultKafkaProducerFactory<>(kafkaProperties.buildProducerProperties());
        return new KafkaTemplate<>(producerFactory);
    }

}
