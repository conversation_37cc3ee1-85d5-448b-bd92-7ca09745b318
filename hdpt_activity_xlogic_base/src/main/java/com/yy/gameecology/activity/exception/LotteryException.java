package com.yy.gameecology.activity.exception;

import com.yy.gameecology.common.exception.SuperException;

/**
 * 抽奖相关异常
 * <AUTHOR>
 * @date 2020年5月24日 下午2:35:43
 */
public class LotteryException extends SuperException {
    private static final long serialVersionUID = -243791149820656490L;
    
    public LotteryException(String message, int code) {
		super(message, code);
	}

	//系统内部错误
	public static final int E = 5200;
	//无抽奖机会
	public static final int E_NO_AWARD_CHANCE = 5201;
	//无有效的奖品
	public static final int E_NO_VALID_AWARD = 5202;
	//无抽奖记录
	public static final int E_NO_AWARD_LOTT = 5203;
	//抽奖消耗道具不够
	public static final int E_NO_AWARD_DJ = 5204;
	//道具抽奖只限宝贝
	public static final int E_NO_AWARD_BABY = 5205;
	//不是活动道具
	public static final int E_NO_VALID_DJ = 5206;
	//兑换出错
	public static final int E_NO_VALID_EXCHANGE = 5207;
	//兑奖道具已空
	public static final int E_NO_AWARD = 5208;
	//不在时间段内
	public static final int E_NO_TIME = 5209;
	//时间配置错误
	public static final int E_NO_TIMECONF = 5210;
	//无效抽奖任务
	public static final int E_INVALID_LOTTERY_TASK = 5211;
	//不存在的抽奖任务
	public static final int E_NOT_EXIST_LOTTERY_TASK = 5212;
	// 未中奖
	public static final int E_NOT_HIT = 5299;
	// 发放状态更新出错
	public static final int E_ISSUE_STATUS_ERROR = 5300;
	// 不存在的业务项目
	public static final int E_NO_BUSI_ITEM = 5301;
	// 暂未实现的抽奖模型
	public static final int E_NOT_IMPLEMENT_MODEL = 5599;

	// 本次抽发奖已经完成
	public static final int E_LOTTERY_COMPLETED = 6000;
	// 流控处理中
	public static final int E_IN_FLOW_CONTROLL = 6001;
	// 奖品入库失败
	public static final int E_AWARD_UPDATE_FAILED = 7000;
	// 获取全局锁失败
	public static final int E_REDIS_LOCK_FAILED = 8000;
}
