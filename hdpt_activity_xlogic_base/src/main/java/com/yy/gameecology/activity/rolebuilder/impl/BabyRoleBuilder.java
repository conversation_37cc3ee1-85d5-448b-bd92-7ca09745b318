package com.yy.gameecology.activity.rolebuilder.impl;

import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.rankroleinfo.BabyRoleItem;
import com.yy.gameecology.activity.bean.rankroleinfo.UserBaseItem;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SpringBeanAwareFactory;
import com.yy.thrift.act_ext_support.MemberItemInfo;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.RoleType;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/9/17 0:31
 * @Modified:
 */
public class BabyRoleBuilder extends UserRoleBaseBuilder<BabyRoleItem> {
    // 可能在对应的bean初始化好前就调用了getBean-> 破坏自然的bean的生命周期，拿不到未完成初始化的bean
//    private OnMicService onMicService = SpringBeanAwareFactory.getBean(OnMicService.class);
//    private UserSubService userSubService = SpringBeanAwareFactory.getBean(UserSubService.class);
//    private EnrollmentService enrollmentService = SpringBeanAwareFactory.getBean(EnrollmentService.class);
//    private SignedService signedService = SpringBeanAwareFactory.getBean(SignedService.class);
//    private CommonService commonService = SpringBeanAwareFactory.getBean(CommonService.class);
    private Template template;

    private static final BabyRoleItem DEFAULT_OBJECT = new BabyRoleItem();

    static {
        DEFAULT_OBJECT.setKey("0");
        DEFAULT_OBJECT.setName("神秘主播");
        DEFAULT_OBJECT.setAvatarInfo(Const.IMAGE.DEFAULT_USER_LOGO);

    }

    @Override
    public BabyRoleItem getDefaultObject() {
        return DEFAULT_OBJECT;
    }

    @Override
    public BabyRoleItem createBankObject() {
        return new BabyRoleItem();
    }

    @Override
    public Map<String, BabyRoleItem> addExtraInfo(long actId, long roleId, long rankId, long uid, Map<String, BabyRoleItem> roleItemMap) {

        List<String> noSignBabyUidStrs = roleItemMap.values().stream()
                .filter(roleItem -> roleItem.getContractSid() == null)
                .map(UserBaseItem::getUid).map(String::valueOf).collect(Collectors.toList());

        //填充签约信息--报名信息中获取
        EnrollmentService enrollmentService = SpringBeanAwareFactory.getBean(EnrollmentService.class);
        CommonService commonService = SpringBeanAwareFactory.getBean(CommonService.class);
        Map<String, EnrollmentInfo> enrollmentInfoMap = enrollmentService.getFirstEnrollmentInfoMap(actId, noSignBabyUidStrs, RoleType.ANCHOR);

        for (Map.Entry<String, EnrollmentInfo> entry : enrollmentInfoMap.entrySet()) {
            EnrollmentInfo enrollmentInfo = entry.getValue();
            BabyRoleItem babyRankRoleItem = roleItemMap.get(entry.getKey());

            long signSid = enrollmentInfo.getSignSid();
            babyRankRoleItem.setContractSid(signSid);
            babyRankRoleItem.setContractAsid(commonService.getAsid(signSid));
        }


        List<Long> noSignBabyUids = roleItemMap.values().stream()
                .filter(roleItem -> roleItem.getContractSid() == null)
                .map(UserBaseItem::getUid).collect(Collectors.toList());

        //填充签约信息--报名业务端中获取
        if (!noSignBabyUids.isEmpty()) {
            SignedService signedService = SpringBeanAwareFactory.getBean(SignedService.class);
            Map<Long, ChannelInfoVo> channelInfoVoMap = signedService.getSignedInfoMap(noSignBabyUids, template.getCode());
            for (Long babyUid : noSignBabyUids) {
                ChannelInfoVo channelInfoVo = channelInfoVoMap.get(babyUid);
                if (channelInfoVo != null) {
                    BabyRoleItem babyRankRoleItem = roleItemMap.get(babyUid + "");
                    babyRankRoleItem.setContractSid(channelInfoVo.getSid());
                    babyRankRoleItem.setContractAsid(channelInfoVo.getAsId());
                }
            }
        }

        //填充关注主播信息
        Set<Long> noSubscribeUids = roleItemMap.values().stream()
                .filter(roleItem -> roleItem.getSubscribe() == null)
                .map(UserBaseItem::getUid).collect(Collectors.toSet());


        if (!noSubscribeUids.isEmpty() && uid > 0) {
            UserSubService userSubService = SpringBeanAwareFactory.getBean(UserSubService.class);
            Map<Long, Boolean> userSubscribes = userSubService.getUserSub(actId, uid, noSubscribeUids, template.getCode());
            for (Long babyUid : noSubscribeUids) {
                Boolean userSubscribe = userSubscribes.get(babyUid);
                if (userSubscribe != null) {
                    BabyRoleItem babyRankRoleItem = roleItemMap.get(babyUid + "");
                    babyRankRoleItem.setSubscribe(userSubscribe);
                }

            }
        }

        //填充开麦频道
        List<Long> noOnMicUids = roleItemMap.values().stream()
                .filter(roleItem -> roleItem.getSid() == null)
                .map(UserBaseItem::getUid).collect(Collectors.toList());

        if (!noOnMicUids.isEmpty()) {
            OnMicService onMicService = SpringBeanAwareFactory.getBean(OnMicService.class);
            for (Long babyUid : noOnMicUids) {
                ChannelInfoVo channelInfoVo = onMicService.getOnMicChannel(babyUid);
                if (channelInfoVo != null) {
                    BabyRoleItem babyRankRoleItem = roleItemMap.get(babyUid + "");
                    babyRankRoleItem.setSid(channelInfoVo.getSid());
                    babyRankRoleItem.setSsid(channelInfoVo.getSsid());
                }

            }
        }
        return roleItemMap;

    }

    @Override
    public BabyRoleItem buildRankItemByBase(MemberItemInfo memberItemInfo, BabyRoleItem roleItem) {
        super.buildRankItemByBase(memberItemInfo, roleItem);
        roleItem.setTemplateType(template.getCode());
        return roleItem;
    }

    public BabyRoleBuilder(Template template) {
        this.template = template;
    }

    @Override
    public Map<String, BabyRoleItem> buildRankByYy(Set<String> members) {
        Map<String, BabyRoleItem> babyRoleItemMap = super.buildRankByYy(members);
        for (BabyRoleItem roleItem : babyRoleItemMap.values()) {
            roleItem.setTemplateType(template.getCode());
        }
        return babyRoleItemMap;
    }

    @Override
    public Map<String, BabyRoleItem> buildRankByYyWithNickExt(Set<String> members) {
        Map<String, BabyRoleItem> babyRoleItemMap = super.buildRankByYyWithNickExt(members);
        for (BabyRoleItem roleItem : babyRoleItemMap.values()) {
            roleItem.setTemplateType(template.getCode());
        }
        return babyRoleItemMap;
    }
}
