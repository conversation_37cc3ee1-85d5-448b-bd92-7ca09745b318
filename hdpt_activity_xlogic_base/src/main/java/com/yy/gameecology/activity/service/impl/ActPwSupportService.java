package com.yy.gameecology.activity.service.impl;

import com.yy.gameecology.activity.bean.rankroleinfo.BabyRoleItem;
import com.yy.gameecology.activity.bean.rankroleinfo.TeamItem;
import com.yy.gameecology.activity.client.thrift.PeiwanThriftClient;
import com.yy.gameecology.activity.rolebuilder.RoleBuilder;
import com.yy.gameecology.activity.rolebuilder.impl.*;
import com.yy.gameecology.activity.service.ActSupportService;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.utils.Convert;
import com.yy.thrift.act_ext_support.MemberItemInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


/**
 * @Author: CXZ
 * @Desciption:陪玩 活动信息相关支持
 * @Date: 2020/9/17 0:31
 * @Modified:
 */
@Component
public class ActPwSupportService implements ActSupportService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    @Autowired
    PeiwanThriftClient peiwanThriftClient;

    @Override
    public Map<String, Map<String, MemberItemInfo>> queryMember(long actId, long rankId, Map<String, List<String>> memberIdMap) {
        return peiwanThriftClient.queryMember(actId, rankId, memberIdMap);
    }

    @Override
    public RoleBuilder getUserRankBuilder() {
        return userRankBuilder;
    }

    @Override
    public RoleBuilder getBabyRankBuilder() {
        return babyRankBuilder;
    }

    @Override
    public RoleBuilder getHostRankBuilder() {
        return babyRankBuilder;
    }

    @Override
    public RoleBuilder getGuildRankBuilder() {
        return guildRankBuilder;
    }

    @Override
    public RoleBuilder getSubGuildRankBuilder() {
        return subGuidRankBuilder;
    }

    @Override
    public RoleBuilder getTeamRankBuilder() {
        return teamRankBuilder;
    }

    @Override
    public RoleBuilder getWaiterRoleBuilder(){
        return waiterRoleBuilder;
    };

    private RoleBuilder userRankBuilder = new UserRoleBuilder();
    private RoleBuilder babyRankBuilder = new PwBabyRoleBuilder();
    private RoleBuilder guildRankBuilder = new GuildRoleBuilder();
    private RoleBuilder subGuidRankBuilder = new SubGuildRoleBuilder();
    private RoleBuilder teamRankBuilder = new PwTeamRoleBuilder();
    private RoleBuilder waiterRoleBuilder = new WaiterRoleBuilder();

    class PwTeamRoleBuilder extends TeamRoleBuilder {

        @Override
        public TeamItem buildRankItemByEx(MemberItemInfo memberItemInfo, TeamItem roleItem) {
            if (memberItemInfo.getExt() != null) {
                Map<String, String> ext = memberItemInfo.getExt();
                roleItem.setAsid(Convert.toLong(ext.get("asid")));
            }
            return roleItem;
        }

    }

    class PwBabyRoleBuilder extends BabyRoleBuilder {

        public PwBabyRoleBuilder() {
            super(Template.peiwan);
        }

        @Override
        public BabyRoleItem buildRankItemByEx(MemberItemInfo memberItemInfo, BabyRoleItem roleItem) {
            if (memberItemInfo.getExt() != null) {
                Map<String, String> ext = memberItemInfo.getExt();
                roleItem.setContractSid(Convert.toLong(ext.get("contractSid")));
                roleItem.setContractAsid(Convert.toLong(ext.get("contractAsid")));
            }
            return roleItem;
        }


    }
}
