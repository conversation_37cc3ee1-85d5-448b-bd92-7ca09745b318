package com.yy.gameecology.activity.client.thrift;

import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.thrift.zhuiwan_channel.FtsChannelService;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023.02.15 15:46
 */
@Component
public class ZhuiwanChannelClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Reference(protocol = "attach_nythrift_compact", owner = "${thrift_zhuiya_client_s2s_name}", registry = "consumer-reg", timeout = 30000, parameters = {"threads", "10"})
    private FtsChannelService.Iface proxy = null;

    @Reference(protocol = "attach_nythrift_compact", owner = "${thrift_zhuiya_client_s2s_name}", registry = "consumer-reg", timeout = 30000, parameters = {"threads", "10"}
            , retries = 2 , cluster = "failover")
    private FtsChannelService.Iface readProxy = null;

    public FtsChannelService.Iface getProxy() {
        return proxy;
    }

    public FtsChannelService.Iface getReadProxy() {
        return readProxy;
    }

    public List<ChannelInfo> batchGetOnlineChannels(int offset, int size) {
        List<ChannelInfo> channelInfos = new ArrayList<>();
        try {
            List<Map<Long, Long>> maps = this.getReadProxy().batchGetOnlineChannels(offset, size);
            for (Map<Long, Long> map : maps) {
                for (Map.Entry<Long, Long> entry : map.entrySet()) {
                    ChannelInfo channelInfo = new ChannelInfo();
                    channelInfo.setSid(entry.getKey());
                    channelInfo.setSsid(entry.getValue());
                    channelInfos.add(channelInfo);
                }
            }
        } catch (Exception e) {
            log.error("batchGetOnlineChannels error:{}", e.getMessage(), e);
        }
        return channelInfos;
    }
}
