package com.yy.gameecology.activity.bean.event;

import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2022-10-31 20:50
 **/
@Data
public class JyLayerPushEvent {
    /**
     * 唯一标识
     */
    private String producerSeqID;

    /**
     * 消息生成时间戳 秒
     */
    private long producerTime;

    /**
     * 通知类型 1-子频道广播 2-uid单播通知  兼容旧挂件
     */
    private int eventType;

    /**
     * 用户uid eventType=2时必填
     */
    private long uid;

    /**
     * 顶级频道 eventType=1时必填
     */
    private long sid;


    /**
     * 子频道 eventType=1时必填
     */
    private long ssid;

    /**
     * 挂件id
     */
    private long pendantID;

    /**
     * fromService
     */
    private String fromService;

    /**
     * 活动中台id
     */
    private String fromIP;

    private long activityID;

    /**
     * 挂件更新状态 1 -打开 2 -关闭 3 -更新 新挂件逻辑使用
     */
    private int status;

    /**
     * 挂件数据 透传给h5 由挂件服务方和h5协商 可选
     */
    private String payload;

}
