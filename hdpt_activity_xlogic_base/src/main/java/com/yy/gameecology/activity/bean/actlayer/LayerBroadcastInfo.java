package com.yy.gameecology.activity.bean.actlayer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.acttask.MemberCurTaskVo;
import com.yy.gameecology.common.utils.Convert;
import com.yy.protocol.pb.layer.LayerInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-28 21:18
 **/
public class LayerBroadcastInfo {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 活动id
     */
    private long actId;

    /**
     * 活动业务类型
     */
    private long actBusiId;

    //当前时间（毫秒）
    private long currentTime;

    //时序序列号
    private long sequence;

    //活动开始时间
    private long actBeginTime;


    //活动展示开始时间（毫秒）
    private long actBeginShowTime;

    //活动结束时间（毫秒）
    private long actEndTime;

    //活动展示结束时间（毫秒）
    private long actEndShowTime;


    //挂件主播开播业务类型 1-单人视频 2-双人视频 3-多人视频
    private int layerBabyType;

    //公会积分排名信息
    private LayerMemberItem channelInfo;

    //厅信息
    private LayerMemberItem subChannelInfo;

    //主播积分排名信息
    private List<LayerMemberItem> anchorInfo;



    //陪玩top n团信息
    private List<LayerMemberItem> topNGroupInfo;

    //其他组件tab元素信息(可能有多种类型，用itemType区分)
    private List<LayerMemberItem> extMemberItem = Lists.newArrayList();

    private Map<String,Object> ext = Maps.newHashMap();

    public List<LayerMemberItem> getTopNGroupInfo() {
        return topNGroupInfo;
    }

    public void setTopNGroupInfo(List<LayerMemberItem> topNGroupInfo) {
        this.topNGroupInfo = topNGroupInfo;
    }

    public long getSequence() {
        return sequence;
    }

    public void setSequence(long sequence) {
        this.sequence = sequence;
    }

    public long getActBeginShowTime() {
        return actBeginShowTime;
    }

    public void setActBeginShowTime(long actBeginShowTime) {
        this.actBeginShowTime = actBeginShowTime;
    }

    public LayerInfo.LayerBroadcast toBroadcastInfoPb() {
        LayerInfo.LayerBroadcast.Builder broadcastPb = LayerInfo.LayerBroadcast.newBuilder();

        broadcastPb.setActId(actId);
        broadcastPb.setActEndTime(actEndTime);
        broadcastPb.setActEndShowTime(actEndShowTime);
        broadcastPb.setActStartTime(actBeginTime);
        broadcastPb.setActStartShowTime(actBeginShowTime);

        //当前活动时间
        broadcastPb.setCurrentTime(currentTime);

        broadcastPb.setSequence(sequence == 0 ? currentTime : sequence);

        //--主播信息
        broadcastPb.setLayerBabyType(layerBabyType);
        if (CollectionUtils.isNotEmpty(anchorInfo)) {
            List<LayerInfo.LayerMemberItem> babyInfoPb = Lists.newArrayList();
            for (LayerMemberItem item : anchorInfo) {
                LayerInfo.LayerMemberItem pbItem = toLayerMemberItemPb(item);
                babyInfoPb.add(pbItem);
            }
            broadcastPb.addAllBabyInfo(babyInfoPb);
        }

        //---公会信息
        if (channelInfo != null) {
            LayerInfo.LayerMemberItem channelInfoPb = toLayerMemberItemPb(channelInfo);
            broadcastPb.setChannelInfo(channelInfoPb);
        }

        //厅信息
        if (subChannelInfo != null) {
            LayerInfo.LayerMemberItem subChannelInfoPb = toLayerMemberItemPb(subChannelInfo);
            broadcastPb.setSubChannelInfo(subChannelInfoPb);
        }


        //top n团信息
         if (CollectionUtils.isNotEmpty(topNGroupInfo)) {
             List<LayerInfo.LayerMemberItem> topNGroupInfoPb = new ArrayList<>();
             for (LayerMemberItem item : topNGroupInfo) {
                 topNGroupInfoPb.add(toLayerMemberItemPb(item));
             }
            broadcastPb.addAllGroup(topNGroupInfoPb);
        }

        //扩展信息
        if (CollectionUtils.isNotEmpty(extMemberItem)) {
            List<LayerInfo.LayerMemberItem> extItemPb = Lists.newArrayList();
            for (LayerMemberItem item : extMemberItem) {
                LayerInfo.LayerMemberItem pbItem = toLayerMemberItemPb(item);
                extItemPb.add(pbItem);
            }
            broadcastPb.addAllExtMemberItem(extItemPb);
        }

        String extStr = "";
        //扩展信息
        if (MapUtils.isNotEmpty(ext)) {
            extStr = JSON.toJSONString(ext);
        }
        broadcastPb.setExt(extStr);


        return broadcastPb.build();
    }

    public LayerInfo.LayerMemberItem toLayerMemberItemPb(LayerMemberItem item) {
        LayerInfo.LayerMemberItem.Builder layerMemberItem = LayerInfo.LayerMemberItem.newBuilder();
        if (item == null) {
            return null;
        }

        //结算状态
        layerMemberItem.setSettleStatus(item.getSettleStatus());
        layerMemberItem.setMemberId(Convert.toString(item.getMemberId()));
        layerMemberItem.setNickName(Convert.toString(item.getNickName()));
        layerMemberItem.setRoleId(item.getRoleId());
        layerMemberItem.setRoleName(Convert.toString(item.getRoleName()));
        layerMemberItem.setScore(item.getScore());
        layerMemberItem.setOffsetScore(item.getOffsetScore());
        layerMemberItem.setSort(item.getSort());
        layerMemberItem.setState(item.getState());
        layerMemberItem.setRank(item.getRank());
        layerMemberItem.setHourRank(item.getHourRank());
        layerMemberItem.setDayRank(item.getDayRank());
        layerMemberItem.setAddScore(item.getAddScore());
        layerMemberItem.setPrePhaseState(item.getPrePhaseState());
        layerMemberItem.setCurRankId(Convert.toLong(item.getCurRankId(),0));
        layerMemberItem.setCurRankName(Convert.toString(item.getCurRankName()));
        layerMemberItem.setCurRankNameShow(Convert.toString(item.getCurRankNameShow()));
        layerMemberItem.setCurRankExtJson(Convert.toString(item.getCurRankExtJson()));
        layerMemberItem.setLastPhaseTopN(item.getLastPhaseTopN());
        layerMemberItem.setDayScore(item.getDayScore());

        layerMemberItem.setLogo(Convert.toString(item.getLogo()));
        layerMemberItem.setAsid(Convert.toString(item.getAsid()));
        layerMemberItem.setSignSid(item.getSignSid());
        layerMemberItem.setLeftSeconds(item.getLeftSeconds());
        layerMemberItem.setPassDesc(Convert.toString(item.getPassDesc()));
        layerMemberItem.setPassStatusDesc(Convert.toString(item.getPassStatusDesc()));
        layerMemberItem.setSignStatus(item.getSignStatus());
        layerMemberItem.setCurPhaseId(Convert.toLong(item.getCurPhaseId(), 0));
        layerMemberItem.setGroupName(Convert.toString(item.getGroupName()));
        layerMemberItem.setGroupRank(item.getGroupRank());
        layerMemberItem.setGroupOffsetScore(item.getGroupOffsetScore());
        layerMemberItem.setItemType(item.getItemType());

        layerMemberItem.setTotalRank(item.getTotalRank());
        layerMemberItem.setTotalScore(item.getTotalScore());
        layerMemberItem.setLastPhaseRank(item.getLastPhaseRank());
        layerMemberItem.setExt(Convert.toString(JSON.toJSONString(item.getExt())));
        layerMemberItem.setViewSupport(Convert.toString(JSON.toJSONString(item.getViewSupport())));
        layerMemberItem.setViewStatus(item.getViewStatus());
        layerMemberItem.setLastPhaseTopTitle(Convert.toString(item.getLastPhaseTopTitle()));
        //礼物任务
        layerMemberItem.addAllMissions(BabyMissionItem.toMissionItemsPb(item.getMissions()));

        //pk信息
        if (item.getPkInfo() != null) {
            LayerInfo.PKInfo  pkInfo = toPKInfo(item.getPkInfo());
            if (pkInfo != null) {
                layerMemberItem.setPkInfo(pkInfo);
            }
        }

        //高光时刻
        if (item.getHighlightsTask() != null) {
            MemberCurTaskVo vo = item.getHighlightsTask();
            LayerInfo.MemberCurTask.Builder task = LayerInfo.MemberCurTask.newBuilder();
            task.setTaskName(Convert.toString(vo.getTaskName()));
            task.setMemberName(Convert.toString(vo.getMemberName()));
            task.setAvatarInfo(Convert.toString(vo.getAvatarInfo()));
            task.setCurScore(vo.getCurScore());
            task.setTaskScore(vo.getTaskScore());
            task.setTaskId(vo.getTaskId());
            layerMemberItem.setHighlightsTask(task.build());
        }


        //当前阶段信息
        if (item.getCurPhaseInfo() != null) {
            LayerInfo.PhaseInfo.Builder phaseInfoPb = LayerInfo.PhaseInfo.newBuilder();
            phaseInfoPb.setName(Convert.toString(item.getCurPhaseInfo().getName()));
            phaseInfoPb.setNameShow(Convert.toString(item.getCurPhaseInfo().getNameShow()));
            phaseInfoPb.setPhaseId(item.getCurPhaseInfo().getPhaseId());
            phaseInfoPb.setStartTime(item.getCurPhaseInfo().getStartTime());
            phaseInfoPb.setEndTime(item.getCurPhaseInfo().getEndTime());
            phaseInfoPb.setShowStartTime(item.getCurPhaseInfo().getShowBeginTime());
            phaseInfoPb.setShowEndTime(item.getCurPhaseInfo().getShowEndTime());
            phaseInfoPb.setExtJson(Convert.toString(item.getCurPhaseInfo().getExtJson()));
            layerMemberItem.setCurPhaseInfo(phaseInfoPb.build());
        }

        return layerMemberItem.build();
    }




    public static LayerInfo.PKInfo toPKInfo(PKInfo pkInfo) {
        if (pkInfo == null || CollectionUtils.isEmpty(pkInfo.getPkItems())) {
            return null;
        }
        LayerInfo.PKInfo.Builder builder = LayerInfo.PKInfo.newBuilder();
        builder.setPkLeftSeconds(pkInfo.getPkLeftSeconds());

        List<LayerInfo.PKItem> pkItems = Lists.newArrayList();
        for (PKItem pkItem : pkInfo.getPkItems()) {
            LayerInfo.PKItem pkItemPb = toPkItemPb(pkItem);
            if (pkItemPb != null) {
                pkItems.add(pkItemPb);
            }
        }
        builder.addAllPkItems(pkItems);
        return builder.build();
    }

    public static LayerInfo.PKItem toPkItemPb(PKItem pkItem) {
        if (pkItem == null) {
            return null;
        }
        LayerInfo.PKItem.Builder pkItemPb = LayerInfo.PKItem.newBuilder();
        pkItemPb.setAvatar(Convert.toString(pkItem.getAvatar()));
        pkItemPb.setNickName(Convert.toString(pkItem.getNickName()));
        pkItemPb.setPkValue(pkItem.getPkValue());
        pkItemPb.setMemberId(pkItem.getMemberId());
        pkItemPb.setSignAsId(Convert.toString(pkItem.getSignAsId(),""));
        return pkItemPb.build();

    }


    public int getLayerBabyType() {
        return layerBabyType;
    }

    public void setLayerBabyType(int layerBabyType) {
        this.layerBabyType = layerBabyType;
    }

    public LayerMemberItem getChannelInfo() {
        return channelInfo;
    }

    public void setChannelInfo(LayerMemberItem channelInfo) {
        this.channelInfo = channelInfo;
    }

    public List<LayerMemberItem> getAnchorInfo() {
        return anchorInfo;
    }

    public void setAnchorInfo(List<LayerMemberItem> anchorInfo) {
        this.anchorInfo = anchorInfo;
    }

    public LayerMemberItem getSubChannelInfo() {
        return subChannelInfo;
    }

    public void setSubChannelInfo(LayerMemberItem subChannelInfo) {
        this.subChannelInfo = subChannelInfo;
    }


    public long getCurrentTime() {
        return currentTime;
    }

    public void setCurrentTime(long currentTime) {
        this.currentTime = currentTime;
    }

    public long getActId() {
        return actId;
    }

    public void setActId(long actId) {
        this.actId = actId;
    }

    public List<LayerMemberItem> getExtMemberItem() {
        return extMemberItem;
    }

    public void setExtMemberItem(List<LayerMemberItem> extMemberItem) {
        this.extMemberItem = extMemberItem;
    }

    public long getActEndTime() {
        return actEndTime;
    }

    public void setActEndTime(long actEndTime) {
        this.actEndTime = actEndTime;
    }

    public long getActEndShowTime() {
        return actEndShowTime;
    }

    public void setActEndShowTime(long actEndShowTime) {
        this.actEndShowTime = actEndShowTime;
    }


    public long getActBeginTime() {
        return actBeginTime;
    }

    public void setActBeginTime(long actBeginTime) {
        this.actBeginTime = actBeginTime;
    }

    public Map<String, Object> getExt() {
        return ext;
    }

    public void setExt(Map<String, Object> ext) {
        this.ext = ext;
    }

    public long getActBusiId() {
        return actBusiId;
    }

    public void setActBusiId(long actBusiId) {
        this.actBusiId = actBusiId;
    }
}
