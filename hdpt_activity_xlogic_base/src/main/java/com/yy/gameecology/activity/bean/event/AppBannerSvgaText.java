package com.yy.gameecology.activity.bean.event;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-05-31 16:57
 **/
@Data
public class AppBannerSvgaText {
   /**
    * 富文本消息，html格式
    * {uid:n} ：表示需要nick替换
    * {uid:an}：表示需要头像icon+nick
    * {img}：表示需要使用imgs list按顺序取出替换
    */
   private String text;

   private List<String> imgs;

   private Map<String,Integer> fontSize;

   private int gravity;

   /**
    * 昵称最长长度,默认5
    */
   private int nameCountLimit = 5;

   public AppBannerSvgaText() {
   }

   public AppBannerSvgaText(String text) {
      this.text = text;
   }
}
