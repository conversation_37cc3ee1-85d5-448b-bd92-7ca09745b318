package com.yy.gameecology.activity.worker.timer;

import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.ScheduledExt;
import com.yy.gameecology.activity.service.OnlineChannelService;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.support.SysEvHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-29 12:13
 **/
@Component
public class OnlineChannelReloadTimer {
    @Autowired
    private OnlineChannelService onlineChannelService;
    @Autowired
    private TimerSupport timerSupport;

    @Autowired
    private Locker locker;

    /**
     * 从源头将频道上麦用户数据刷新到内存中
     */
    @Scheduled(cron = "0/3 * * * * ? ")
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @ScheduledExt(historyRun = true)
    @Report
    public void update2memory() {
        onlineChannelService.update2memory();
    }
}
