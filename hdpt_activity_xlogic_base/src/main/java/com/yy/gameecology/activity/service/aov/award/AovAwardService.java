package com.yy.gameecology.activity.service.aov.award;

import com.alibaba.fastjson.JSONObject;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.client.yrpc.HdptAovClient;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.activity.service.SvcSDKService;
import com.yy.gameecology.common.consts.AddFirstAwardConst;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.PBCommonNoticeType;
import com.yy.gameecology.common.consts.aov.AovConst;
import com.yy.gameecology.common.db.mapper.aov.AovMatchAwardRecordExtMapper;
import com.yy.gameecology.common.db.model.gameecology.aov.AovMatchAwardRecord;
import com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseAward;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.bean.aov.AovAwardRecord;
import com.yy.gameecology.hdzj.element.component.attr.AovAwardComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.zhuiwan.BaseRsp;
import com.yy.thrift.zhuiwan.UserBatchReleaseRsp;
import com.yy.thrift.zhuiwan.UserReleaseWithAccountInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class AovAwardService {

    private static final String ISSUE_SEQ = "aov_%d_%d_%d_%d";

    @Resource
    private AovMatchAwardRecordExtMapper aovMatchAwardRecordExtMapper;

    @Autowired
    protected SvcSDKService svcSDKService;

    @Autowired
    private HdptAovClient hdptAovClient;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;

    public void refreshSubmittedAwardState() {
        long floorId = 0;
        while (true) {
            List<AovMatchAwardRecord> records = aovMatchAwardRecordExtMapper.selectAwardList(null, AovConst.AwardState.SUBMITTED, floorId, 50);
            if (CollectionUtils.isEmpty(records)) {
                break;
            }

            for (AovMatchAwardRecord record : records) {
                String seq = String.format(ISSUE_SEQ, record.getActId(), record.getPhaseId(), record.getAwardType(), record.getUid());
                var rsp = zhuiWanPrizeIssueServiceClient.getUserReleaseWithAccountInfo(record.getUid(), seq);
                if (rsp == null || rsp.getCode() != 0) {
                    continue;
                }

                int status = rsp.getReleaseInfo().getStatus();
                if (status == 4) {
                    continue;
                }

                final int targetState;
                if (status == 1) {
                    targetState = AovConst.AwardState.FINISH;
                } else {
                    targetState = AovConst.AwardState.FAIL;
                }

                int rs = aovMatchAwardRecordExtMapper.batchUpdateAwardRecordState(Collections.singleton(record.getId()), AovConst.AwardState.SUBMITTED, targetState);
                log.info("batchUpdateAwardRecordState with id:{} rs:{}", record.getId(), rs);
            }

            floorId = records.getLast().getId();
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    public void tryGrantFirstGameAward(AovAwardComponentAttr attr, AovPhaseAward phaseAward, long gameId, long uid, Date time) {
        final long amount = phaseAward.getAwardAmount(), phaseId = 0;
        int count = aovMatchAwardRecordExtMapper.countUserAwardRecord(null, phaseId, AovConst.AwardType.FIRST_GAME, uid, null, null);
        if (count > 0) {
            log.info("tryGrantFirstGameAward first award count not zero gameId:{} uid:{}", gameId, uid);
            return;
        }

        String firstSeq = String.format("first_award:%d:%d:%d", attr.getActId(), gameId, uid);
        boolean added = hdptAovClient.tryAddFirstAward(AddFirstAwardConst.AOV, uid, firstSeq, attr.getActId() + "首胜奖励");
        if (!added) {
            log.warn("try add first award unsuccessful gameId:{} uid:{}", gameId, uid);
            return;
        }

        AovMatchAwardRecord record = new AovMatchAwardRecord();
        record.setActId(attr.getActId());
        record.setPhaseId(phaseId);
        record.setAwardType(AovConst.AwardType.FIRST_GAME);
        record.setUid(uid);
        record.setAmount(amount);
        record.setAwardState(AovConst.AwardState.GRANTED);
        record.setAwardDesc("首次参赛");
        record.setAwardTime(time);

        int rs = aovMatchAwardRecordExtMapper.saveAwardRecord(record);
        log.info("tryGrantFirstGameAward save first game award record with gameId:{} uid:{} rs:{}", gameId, uid, rs);

        if (rs > 0) {
            final String seq = String.format(ISSUE_SEQ, attr.getActId(), phaseId, AovConst.AwardType.FIRST_GAME, uid);
            Map<Long, Map<Long, Integer>> taskPackageIds = Map.of(phaseAward.getTaskId(), Map.of(phaseAward.getPackageId(), 1));
            Map<String, String> extData = Map.of("issue_seq_spec", seq);
            BatchWelfareResult result = hdztAwardServiceClient.doBatchWelfare(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), uid, phaseAward.getTaskId(), taskPackageIds, seq, 3, extData);
            if (result == null) {
                throw new RuntimeException("doWelfare fail");
            }
        }

        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> sendAwardNotice(attr.getActId(), uid, record.getId(), AovConst.AwardType.FIRST_GAME, amount));
    }

    private void sendAwardNotice(long actId, long uid, long awardId, int awardType, long amount) {
        JSONObject json = new JSONObject(2);
        json.put("awardType", awardType);
        json.put("amount", amount);
        json.put("awardId", awardId);
        String noticeValue = json.toJSONString();

        // 发单播
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType(PBCommonNoticeType.AOV_AWARD_NOTICE)
                .setNoticeValue(noticeValue);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        svcSDKService.unicastUid(uid, msg);
        log.info("sendAwardNotice with uid:{} noticeValue:{}", uid, noticeValue);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void tryGrantRoundAward(AovAwardComponentAttr attr, AovPhaseAward phaseAward, long uid, Date time) {
        long phaseId = phaseAward.getPhaseId(), amount = phaseAward.getAwardAmount();
        int roundNum = phaseAward.getRoundNum();
        int count = aovMatchAwardRecordExtMapper.countUserAwardRecord(null, phaseId, AovConst.AwardType.ROUND_AWARD, uid, null, null);
        if (count > 0) {
            log.info("tryGrantRoundAward skip user award count not zero roundNum:{} uid:{}", roundNum, uid);
            return;
        }

        AovMatchAwardRecord record = new AovMatchAwardRecord();
        record.setActId(attr.getActId());
        record.setPhaseId(phaseId);
        record.setAwardType(AovConst.AwardType.ROUND_AWARD);
        record.setUid(uid);
        record.setRoundNum(phaseAward.getRoundNum());
        record.setAmount(amount);
        record.setAwardState(AovConst.AwardState.GRANTED);
        record.setAwardDesc(phaseAward.getAwardDesc());
        record.setAwardTime(time);

        int rs = aovMatchAwardRecordExtMapper.saveAwardRecord(record);
        log.info("tryGrantRoundAward save award record with roundNum:{} uid:{} rs:{}", roundNum, uid, rs);
        if (rs <= 0) {
            throw new RuntimeException("saveAwardRecord fail");
        }

        //额外奖励
        if (Convert.toLong(phaseAward.getExtraPackageId(), 0) > 0) {
            AovMatchAwardRecord extraRecord = new AovMatchAwardRecord();
            extraRecord.setActId(attr.getActId());
            extraRecord.setPhaseId(phaseId);
            extraRecord.setAwardType(AovConst.AwardType.ROUND_EXTRA_AWARD);
            extraRecord.setUid(uid);
            extraRecord.setAmount(phaseAward.getExtraAwardAmount());
            extraRecord.setAwardState(AovConst.AwardState.GRANTED);
            extraRecord.setAwardDesc(phaseAward.getExtraAwardDesc());
            extraRecord.setAwardTime(time);

            rs = aovMatchAwardRecordExtMapper.saveAwardRecord(extraRecord);
            log.info("tryGrantRoundAward save award record with roundNum:{} uid:{} rs:{}", roundNum, uid, rs);

            if (rs <= 0) {
                throw new RuntimeException("saveAwardRecord fail");
            }
        }

        final String seq = String.format(ISSUE_SEQ, attr.getActId(), phaseId, AovConst.AwardType.ROUND_AWARD, uid);
        Map<Long, Map<Long, Integer>> taskPackageIds = Map.of(phaseAward.getTaskId(), Map.of(phaseAward.getPackageId(), 1));
        Map<String, String> extData = Map.of("issue_seq_spec", seq);
        BatchWelfareResult result = hdztAwardServiceClient.doBatchWelfare(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), uid, phaseAward.getTaskId(), taskPackageIds, seq, 3, extData);
        if (result == null) {
            throw new RuntimeException("tryGrantRoundAward doBatchWelfare fail");
        }

        if (Convert.toLong(phaseAward.getExtraTaskId(), 0) > 0) {
            final String extraSeq = String.format(ISSUE_SEQ, attr.getActId(), phaseId, AovConst.AwardType.ROUND_EXTRA_AWARD, uid);
            Map<Long, Map<Long, Integer>> extraTaskPackageIds = Map.of(phaseAward.getExtraTaskId(), Map.of(phaseAward.getExtraPackageId(), 1));
            Map<String, String> extraExtData = Map.of("issue_seq_spec", extraSeq);
            BatchWelfareResult exTraResult = hdztAwardServiceClient.doBatchWelfare(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), uid, phaseAward.getExtraTaskId(), extraTaskPackageIds, extraSeq, 3, extraExtData);
            if (exTraResult == null) {
                throw new RuntimeException("tryGrantRoundAwardExtra doBatchWelfare fail");
            }
        }

        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> sendAwardNotice(attr.getActId(), uid, record.getId(), AovConst.AwardType.ROUND_AWARD, amount));
    }

    public int countGrantedAward(long actId, long uid) {
        return aovMatchAwardRecordExtMapper.countUserAwardRecord(actId, null, null, uid, null, AovConst.AwardState.GRANTED);
    }

    public List<AovAwardRecord> queryUserAwardList(long actId, long uid) {
        List<AovAwardRecord> records = aovMatchAwardRecordExtMapper.selectUserAwardList(actId, uid, 100).stream().map(AovAwardRecord::new).toList();
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        Map<String, AovAwardRecord> seqRecordMap = new HashMap<>(records.size());
        List<String> seqList = new ArrayList<>(records.size());
        for (AovAwardRecord record : records) {
            if (record.getAwardState() == AovConst.AwardState.SUBMITTED) {
                String seq = String.format(ISSUE_SEQ, actId, record.getPhaseId(), record.getAwardType(), record.getUid());
                seqList.add(seq);
                seqRecordMap.put(ZhuiWanPrizeIssueServiceClient.ZHUIWAN_SEQ_PREFIX + seq, record);
            }
        }

        if (CollectionUtils.isEmpty(seqList)) {
            return records;
        }

        UserBatchReleaseRsp rsp = zhuiWanPrizeIssueServiceClient.batchGetUserReleaseInfo(uid, seqList);
        if (rsp == null || rsp.getCode() != 0) {
            log.warn("batchGetUserReleaseInfo fail with seqList:{}", seqList);
            return records;
        }

        List<UserReleaseWithAccountInfo> accountInfos = rsp.getReleaseInfoList();
        if (CollectionUtils.isEmpty(accountInfos)) {
            return records;
        }

        Set<Long> finishIds = new HashSet<>(seqList.size());
        Set<Long> failIds = new HashSet<>(seqList.size());

        for (UserReleaseWithAccountInfo accountInfo : accountInfos) {
            // 追玩订单状态,1=有效，2=发放错误，4=发放中，5=发放失败，6=等待完善信息 Q币这里没有2和5，追玩转化成了6
            if (accountInfo.getStatus() == 4) {
                continue;
            }

            AovAwardRecord record = seqRecordMap.get(accountInfo.getSeqId());
            if (accountInfo.getStatus() == 1) {
                finishIds.add(record.getAwardId());
                record.setAwardState(AovConst.AwardState.FINISH);
            } else {
                failIds.add(record.getAwardId());
                record.setAwardState(AovConst.AwardState.FAIL);
            }
        }

        if (!finishIds.isEmpty()) {
            int rs = aovMatchAwardRecordExtMapper.batchUpdateAwardRecordState(finishIds, AovConst.AwardState.SUBMITTED, AovConst.AwardState.FINISH);
            log.info("queryUserAwardList update award record state finish with ids:{} rs:{}", finishIds, rs);
        }

        if (!failIds.isEmpty()) {
            int rs = aovMatchAwardRecordExtMapper.batchUpdateAwardRecordState(failIds, AovConst.AwardState.SUBMITTED, AovConst.AwardState.FAIL);
            log.info("queryUserAwardList update award record state fail with ids:{} rs:{}", failIds, rs);
        }

        return records;
    }

    @Transactional(rollbackFor = Throwable.class)
    public Response<?> submitAwardAccountInfo(long awardId, long uid, String account, String mobile) {
        AovMatchAwardRecord record = aovMatchAwardRecordExtMapper.selectByPrimaryKey(awardId);
        if (record == null || record.getUid() != uid) {
            return Response.fail(400, "奖励不存在");
        }

        if (record.getAwardState() != AovConst.AwardState.GRANTED) {
            return Response.fail(400, "已提交，请刷新查看发放结果");
        }

        var checkResult = hdptAovClient.checkAwardAccount(uid, account, mobile, "领取奖励[" + awardId + "]");
        if (checkResult.getLeft() < 0) {
            return Response.fail(400, checkResult.getRight());
        }

        final long addId = checkResult.getLeft();

        int rs = aovMatchAwardRecordExtMapper.batchUpdateAwardRecordState(List.of(record.getId()), AovConst.AwardState.GRANTED, AovConst.AwardState.SUBMITTED);
        log.info("submitAwardAccountInfo update award record with id:{} rs:{}", record.getId(), rs);
        if (rs <= 0) {
            if (addId > 0) {
                hdptAovClient.rollbackAddAwardAccount(addId);
            }
            return Response.fail(400, "已提交，请刷新查看发放结果");
        }

        String seq = String.format(ISSUE_SEQ, record.getActId(), record.getPhaseId(), record.getAwardType(), record.getUid());
        BaseRsp rsp = zhuiWanPrizeIssueServiceClient.fillInUserAccountInfo(uid, seq, account, mobile);
        if (rsp == null) {
            if (addId > 0) {
                hdptAovClient.rollbackAddAwardAccount(addId);
            }
            throw new BusinessException(500, "提交失败，请稍后重试");
        }

        if (rsp.getResult() != 0) {
            if (rsp.getMessage().contains(ZhuiWanPrizeIssueServiceClient.NOT_NEED_FILLIN)) {
                return Response.fail(400, "已提交，请刷新查看发放结果");
            }

            if (addId > 0) {
                hdptAovClient.rollbackAddAwardAccount(addId);
            }
            throw new BusinessException(500, rsp.getMessage());
        }

        return Response.ok();
    }
}
