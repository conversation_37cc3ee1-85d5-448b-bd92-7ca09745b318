/**
 * HddjPayOkEvent.java / 2017年12月21日 下午1:04:32
 * 
 * Copyright (c) 2017, YY Inc. All Rights Reserved.
 * 
 * 郭立平[<EMAIL>]
 */

package com.yy.gameecology.activity.bean.gamebaby;

import com.alibaba.fastjson.JSON;

/** 
 * 模板道具支付成功事件
 * <AUTHOR>
 * @date 2017年12月21日 下午1:04:32 
 */
public class MbdjPayOkEvent extends YXBBEvent {

    private static final long serialVersionUID = 1L;

    private Transaction transaction;

    //private VoucherTransaction voucherTrans;

    /**
     * 礼物连击需求 (为了过渡保留)
     */
    //private Map<String, Integer> giftCombo;

    /**
     * 礼物连击
     */
    //private TranGiftComboBean giftComboInfo;

    private boolean onMic;

    //private Object extdat;

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    /*public Object getExtdat() {
        return extdat;
    }

    public void setExtdat(Object extdat) {
        this.extdat = extdat;
    }

    public VoucherTransaction getVoucherTrans() {
        return voucherTrans;
    }

    public void setVoucherTrans(VoucherTransaction voucherTrans) {
        this.voucherTrans = voucherTrans;
    }

    public Map<String, Integer> getGiftCombo() {
        return giftCombo;
    }

    public void setGiftCombo(Map<String, Integer> giftCombo) {
        this.giftCombo = giftCombo;
    }

    public boolean isOnMic() {
        return onMic;
    }

    public void setOnMic(boolean onMic) {
        this.onMic = onMic;
    }

    public TranGiftComboBean getGiftComboInfo() {
        return giftComboInfo;
    }

    public void setGiftComboInfo(TranGiftComboBean giftComboInfo) {
        this.giftComboInfo = giftComboInfo;
    }*/

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
