package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.ComponentDataHash;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ComponentStoreService {

    private static final String HASH_EXIST_SQL = "select count(1) from component_data_hash where act_id = ? and component_id = ? and cmpt_use_inx = ? and key_name = ? and hash_key_name = ?";

    private static final String HASH_GET_SQL = "select hash_value from component_data_hash where act_id = ? and component_id = ? and cmpt_use_inx = ? and key_name = ? and hash_key_name = ?";

    private static final String HASH_SAVE_SQL = "insert into component_data_hash (act_id, component_id, cmpt_use_inx, key_name, hash_key_name, hash_value, create_time) values (?, ?, ?, ?, ?, now()) on duplicate key update hash_value = values(hash_value), create_time=values(create_time)";

    private static final String HASH_UPDATE_SQL = "update component_data_hash set hash_value = ?, create_time = now() where where act_id = ? and component_id = ? and cmpt_use_inx = ? and key_name = ? and hash_key_name = ?";

    private static final String HASH_INSERT_IGNORE_SQL = "insert ignore into component_data_hash (act_id, component_id, cmpt_use_inx, key_name, hash_key_name, hash_value, create_time) values (?, ?, ?, ?, ?, ?, now())";

    private static final String HASH_DELETE_SQL = "delete from component_data_hash act_id = ? and component_id = ? and cmpt_use_inx = ? and key_name = ? and hash_key_name = ?";

    private static final String LIST_INSERT_IGNORE_SQL = "insert ignore into component_data_list (act_id, component_id, cmpt_use_inx, key_name, seq, member, list_value, create_time) values (?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String LIST_QUERY_MEMBER_SQL = "select list_value from component_data_list where act_id = ? and component_id = ? and cmpt_use_inx = ? and key_name = ? and member = ? order by create_time desc limit ?, ?";

    private static final String LIST_QUERY_ALL_SQL = "select list_value from component_data_list where act_id = ? and component_id = ? and cmpt_use_inx = ? and key_name = ? order by create_time desc limit ?, ?";

    @Autowired
    private GameecologyDao gameecologyDao;

    public boolean hashExist(long actId, int componentId, int cmptUseInx, String key, String hashKey) {
        Long count = gameecologyDao.getJdbcTemplate().queryForObject(HASH_EXIST_SQL, Long.class, actId, componentId, cmptUseInx, key, hashKey);
        return count != null && count > 0;
    }

    public boolean hashUpdate(long actId, int componentId, int cmptUseInx, String key, String hashKey, Object value) {
        final String hashValue;
        if (value == null || value instanceof String) {
            hashValue = (String) value;
        } else {
            hashValue = JSONUtils.toJsonString(value);
        }

        int rs = gameecologyDao.getJdbcTemplate().update(HASH_UPDATE_SQL, hashValue, actId, componentId, cmptUseInx, key, hashKey);

        return rs > 0;
    }

    public void hashSave(long actId, int componentId, int cmptUseInx, String key, String hashKey, Object value) {
        final String hashValue;
        if (value == null || value instanceof String) {
            hashValue = (String) value;
        } else {
            hashValue = JSONUtils.toJsonString(value);
        }

        gameecologyDao.getJdbcTemplate().update(HASH_SAVE_SQL, actId, componentId, cmptUseInx, key, hashKey, hashValue);
    }

    public void hashSet(long actId, int componentId, int cmptUseInx, String key, String hashKey, Object value) {
        boolean exist = hashExist(actId, componentId, cmptUseInx, key, hashKey);
        if (exist) {
            hashUpdate(actId, componentId, cmptUseInx, key, hashKey, value);
        } else {
            hashSave(actId, componentId, cmptUseInx, key, hashKey, value);
        }
    }

    public boolean hashSetNx(long actId, int componentId, int cmptUseInx, String key, String hashKey, Object value) {
        final String hashValue;
        if (value == null || value instanceof String) {
            hashValue = (String) value;
        } else {
            hashValue = JSONUtils.toJsonString(value);
        }

        return gameecologyDao.getJdbcTemplate().update(HASH_INSERT_IGNORE_SQL, actId, componentId, cmptUseInx, key, hashKey, hashValue) > 0;
    }

    public String hashGet(long actId, int componentId, int cmptUseInx, String key, String hashKey) {
        return gameecologyDao.getJdbcTemplate().queryForObject(HASH_GET_SQL, String.class, actId, componentId, cmptUseInx, key, hashKey);
    }

    public <T> T hashGet(long actId, int componentId, int cmptUseInx, String key, String hashKey, Class<T> clazz) {
        String hashValue = hashGet(actId, componentId, cmptUseInx, key, hashKey);
        if (hashValue == null || clazz.isAssignableFrom(String.class)) {
            return (T) hashValue;
        }

        return JSONUtils.parseObject(hashValue, clazz);
    }

    public Map<String, String> hashMutiGet(long actId, long componentId, long cmptUseInx, String key, Collection<String> hashKeys) {
        if (CollectionUtils.isEmpty(hashKeys)) {
            return Collections.emptyMap();
        }

        if (hashKeys.size() > 50) {
            throw new IllegalArgumentException("hashKeys size too big");
        }

        ComponentDataHash me = new ComponentDataHash();
        me.setActId(actId);
        me.setComponentId(componentId);
        me.setCmptUseInx(cmptUseInx);
        me.setKeyName(key);

        List<ComponentDataHash> records = gameecologyDao.select(ComponentDataHash.class, me, " and hash_key_name in (" + StringUtils.join(hashKeys, ',') + ")");
        return records.stream().collect(Collectors.toMap(ComponentDataHash::getHashKeyName, ComponentDataHash::getHashValue));
    }

    public Map<String, String> hashGetAll(long actId, long componentId, long cmptUseInx, String key) {
        ComponentDataHash me = new ComponentDataHash();
        me.setActId(actId);
        me.setComponentId(componentId);
        me.setCmptUseInx(cmptUseInx);
        me.setKeyName(key);

        List<ComponentDataHash> records = gameecologyDao.select(ComponentDataHash.class, me);
        return records.stream().collect(Collectors.toMap(ComponentDataHash::getHashKeyName, ComponentDataHash::getHashValue));
    }

    public boolean hashDelete(long actId, int componentId, int cmptUseInx, String key, String hashKey) {
        return gameecologyDao.getJdbcTemplate().update(HASH_DELETE_SQL, actId, componentId, cmptUseInx, key, hashKey) > 0;
    }

    public int hashDelete(long actId, long componentId, long cmptUseInx, String key, Collection<String> hashKeys) {
        if (CollectionUtils.isEmpty(hashKeys)) {
            return 0;
        }

        if (hashKeys.size() > 100) {
            throw new IllegalArgumentException("hashKeys size too big");
        }

        ComponentDataHash me = new ComponentDataHash();
        me.setActId(actId);
        me.setComponentId(componentId);
        me.setCmptUseInx(cmptUseInx);
        me.setKeyName(key);

        return gameecologyDao.delete(ComponentDataHash.class, me, " and hash_key_name in (" + StringUtils.join(hashKeys, ',') + ")");
    }

    public int hashDeleteAll(long actId, long componentId, long cmptUseInx, String key) {
        ComponentDataHash me = new ComponentDataHash();
        me.setActId(actId);
        me.setComponentId(componentId);
        me.setCmptUseInx(cmptUseInx);
        me.setKeyName(key);

        return gameecologyDao.delete(ComponentDataHash.class, me);
    }

    public boolean listAdd(long actId, int componentId, int cmptUseInx, String key, String seq, String member, Object value) {
        final String listValue;
        if (value == null || value instanceof String) {
            listValue = (String) value;
        } else {
            listValue = JSONUtils.toJsonString(value);
        }
        return gameecologyDao.getJdbcTemplate().update(LIST_INSERT_IGNORE_SQL, actId, componentId, cmptUseInx, key, seq, member, listValue) > 0;
    }

    public List<String> listQuery(long actId, int componentId, int cmptUseInx, String key, String member, int offset, int size) {
        return gameecologyDao.getJdbcTemplate().queryForList(LIST_QUERY_MEMBER_SQL, String.class, actId, componentId, cmptUseInx, key, member, offset, size);
    }

    public <T> List<T> listQuery(long actId, int componentId, int cmptUseInx, String key, String member, int offset, int size, Class<T> clazz) {
        List<String> listValues = listQuery(actId, componentId, cmptUseInx, key, member, offset, size);
        return convertListValues(listValues, clazz);
    }

    public List<String> listQuery(long actId, int componentId, int cmptUseInx, String key, int offset, int size) {
        return gameecologyDao.getJdbcTemplate().queryForList(LIST_QUERY_ALL_SQL, String.class, actId, componentId, cmptUseInx, key, offset, size);
    }

    public <T> List<T> listQuery(long actId, int componentId, int cmptUseInx, String key, int offset, int size, Class<T> clazz) {
        List<String> listValues = listQuery(actId, componentId, cmptUseInx, key, offset, size);
        return convertListValues(listValues, clazz);
    }

    @NotNull
    private static <T> List<T> convertListValues(List<String> listValues, Class<T> clazz) {
        if (CollectionUtils.isEmpty(listValues)) {
            return Collections.emptyList();
        }
        List<T> result = new ArrayList<>(listValues.size());
        for (String listValue : listValues) {
            T value = JSONUtils.parseObject(listValue, clazz);
            result.add(value);
        }

        return result;
    }
}
