package com.yy.gameecology.activity.bean.turnover;

import java.util.List;

/**
 * @Author: CXZ
 * @Desciption: 礼物信息
 * @Date: 2021/3/23 19:40
 * @Modified:
 */
public class PropsInfo {

    private String usable;
    private String visible;
    private int appId;
    private int propsId;
    private String name;
    private int type;
    private PropsDesc desc;
    private List<PropsPricing> pricingList;

    public String getUsable() {
        return usable;
    }

    public void setUsable(String usable) {
        this.usable = usable;
    }

    public String getVisible() {
        return visible;
    }

    public void setVisible(String visible) {
        this.visible = visible;
    }

    public int getAppId() {
        return appId;
    }

    public void setAppId(int appId) {
        this.appId = appId;
    }

    public int getPropsId() {
        return propsId;
    }

    public void setPropsId(int propsId) {
        this.propsId = propsId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public PropsDesc getDesc() {
        return desc;
    }

    public void setDesc(PropsDesc desc) {
        this.desc = desc;
    }

    public List<PropsPricing> getPricingList() {
        return pricingList;
    }

    public void setPricingList(List<PropsPricing> pricingList) {
        this.pricingList = pricingList;
    }
}
