package com.yy.gameecology.activity.bean.mq.hdzk;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-09-24 22:04
 **/
@Data
public class DayTaskCompleteEvent extends HdzkBaseEvent {
    public DayTaskCompleteEvent() {
        super(HdzkBaseEvent.DAY_TASK_COMPONENT_COMPLETE_TASK);
    }

    private List<Long> taskId = Lists.newArrayList();
    private int dayIndex;
    private String memberId;

    /**
     * 奖励发放奖池id
     */
    private long awardTaskId;

    /**
     * 奖励发放奖包id
     */
    private long awardPackageId;

    /**
     * 奖励发放数量
     */
    private int awardNum;
}
