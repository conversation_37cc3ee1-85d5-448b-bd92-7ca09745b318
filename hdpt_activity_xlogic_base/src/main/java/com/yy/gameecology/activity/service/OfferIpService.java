package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.client.AsynInit;
import com.yy.gameecology.common.utils.HttpClientHelper;
import com.yy.gameecology.common.utils.MyCharsets;
import com.yy.gameecology.common.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * @Author: CXZ
 * @Desciption: 公司ip服务
 * @Date: 2022/5/9 17:11
 * @Modified:
 */
@Service
public class OfferIpService implements AsynInit {

    protected static final Logger log = LoggerFactory.getLogger(OfferIpService.class);

    /**
     * 运维系统上申请的： https://s.sysop.yy.com/system/uaa/clientManagement
     * 可咨询 李武/何庭耀
     */
    public static final String CLIENT_ID = "sm_t0ktmhpwbxmq9wd";
    public static final String CLIENT_SECRET = "47bac3e6f69e4170856aae48a64b4c19";
    public static final String GET_TOKEN_URL = "https://s-backend.sysop.yy.com/auth/api/token?grant_type=client_credentials&client_id=%s&client_secret=%s";

    @Lazy
    @Autowired
    private OfferIpService self;


    private HttpClientHelper httpClientHelper = new HttpClientHelper(MyCharsets.UTF_8);

    private static final Set<String> OFFER_IP_SET = Sets.newConcurrentHashSet();

    /**
     * 办公网ip出口查询 - 黄胜龙提供
     * <p>
     * 办公网出口单IP地址获取接口:
     * json: http://s-backend.sysop.yy.com/net/api/officeIp/getIps
     * txt: http://s-backend.sysop.yy.com/net/api/officeIp/getIps?type=txt
     * 办公网出口CIDR格式地址获取接口:
     * json: http://s-backend.sysop.yy.com/net/api/officeIp/listCidr
     * txt: http://s-backend.sysop.yy.com/net/api/officeIp/listCidr?type=txt
     */
    private static final String OFFER_IP_URL = "http://s-backend.sysop.yy.com/net/api/officeIp/getIps";
    /**
     * 公司机器所有ip出口查询 -李武提供
     */
    private static final String ALL_MACHINE_IP_URL = "http://ws.cmdb.sysop.yy.com/webservice/ipPool/listAll.do";

    /**
     * 公司机器所有ip出口查询  -李武提供
     */
    private static final String MACHINE_IP_URL = "http://ws.cmdb.sysop.yy.com/webservice/ipPool/listAll.do?ip=";

    private static final String SUCCESS_CODE = "\"code\":0";


    private static final AtomicLong LAST_REFRESH_TIME = new AtomicLong(0);

    private static final Long REFRESH_INTERVAL = 60 * 60 * 1000L;

    /**
     * 获取应用访问 token
     *
     * @return
     * @throws IOException
     */
    public String getAccessToken() throws IOException {
        String url = String.format(GET_TOKEN_URL, CLIENT_ID, CLIENT_SECRET);
        String result = httpClientHelper.excuteGet(url);
        JSONObject jo = JSON.parseObject(result);
        final String codeStr = "code";
        if (jo.getIntValue(codeStr) == 0) {
            return jo.getJSONObject("data").getString("access_token");
        }
        return null;
    }


    /**
     * 查询办公网ip出口列表
     *
     * @return
     */
    public List<String> queryOfferIpList() {
        String result = "";
        try {
            String access_token = getAccessToken();
            if (StringUtil.isBlank(access_token)) {
                log.error("queryOfferIpList error@fail to obtain access_token");
            } else {
                ImmutableMap<String, String> headers = ImmutableMap.of("Authorization", access_token);
                result = httpClientHelper.excuteGetWithHeader(OFFER_IP_URL, headers);

                //返回状态异常
                if (result.contains(SUCCESS_CODE)) {
                    return JSON.parseObject(result).getObject("data", new TypeReference<List<String>>() {
                    });
                }

                log.warn("queryOfferIpList error@url:{},resp:{}", OFFER_IP_URL, result);
            }
        } catch (IOException e) {
            log.error("queryOfferIpList error@url:{},resp:{} {}", OFFER_IP_URL, result, e.getMessage(), e);
        }
        return Lists.newArrayList();
    }

    /**
     * 查询公司机器Ip出口列表
     *
     * @return
     */
    public List<String> queryAllMachineIpList() {
        String result = "";
        try {
            result = httpClientHelper.excuteGet(ALL_MACHINE_IP_URL);
            //返回状态异常
            if (!result.contains(SUCCESS_CODE)) {
                log.warn("queryAllMachineIpList error@url:{},resp:{}", ALL_MACHINE_IP_URL, result);
                return Lists.newArrayList();
            }
            List<Map<String, String>> ipMapList = JSON.parseObject(result).getObject("object", new TypeReference<List<Map<String, String>>>() {
            });

            return ipMapList.stream().map(i -> i.get("ip")).collect(Collectors.toList());
        } catch (IOException e) {
            log.error("queryAllMachineIpList error@url:{},resp:{} {}", ALL_MACHINE_IP_URL, result, e.getMessage(), e);
        }
        return Lists.newArrayList();
    }

    /**
     * 判断是否是公司机器ip
     *
     * @param ip
     * @return
     */
    @Cached(timeToLiveMillis = 300 * 1000)
    public boolean isMachineIpList(String ip) {
        if (StringUtils.isBlank(ip)) {
            return false;
        }
        String url = MACHINE_IP_URL + ip.trim();
        String result = "";
        try {
            result = httpClientHelper.excuteGet(url);
            JSONObject resultJson = JSON.parseObject(result);
            Boolean success = resultJson.getBoolean("success");
            log.info("isMachineIpList info@ip:{},result:{}", ip, success);
            return Boolean.TRUE.equals(success);
        } catch (IOException e) {
            log.error("isMachineIpList error@url:{},resp:{} {}", url, result, e.getMessage(), e);
        }
        return false;
    }

    public boolean isOfferIp(String ip) {
        boolean isOfferIp = OFFER_IP_SET.contains(ip);

        if (!isOfferIp && self.isMachineIpList(ip)) {
            OFFER_IP_SET.add(ip);
            isOfferIp = true;
        }
        //超过时间更新
        long lastRefreshTime = LAST_REFRESH_TIME.get();
        if (!isOfferIp && System.currentTimeMillis() - lastRefreshTime > REFRESH_INTERVAL) {
            refreshIpCache(lastRefreshTime);
        }
        log.info("isOfferIp info@ip:{},result:{}", ip, isOfferIp);
        return isOfferIp;
    }

    public void init() {
        refreshIpCache(0);
    }

    public void refreshIpCache(long lastRefreshTime) {
        if (lastRefreshTime == 0 || LAST_REFRESH_TIME.compareAndSet(lastRefreshTime, System.currentTimeMillis())) {
            List<String> offerIpList = queryOfferIpList();
            List<String> allMachineIpList = queryAllMachineIpList();
            OFFER_IP_SET.addAll(offerIpList);
            OFFER_IP_SET.addAll(allMachineIpList);
            LAST_REFRESH_TIME.set(System.currentTimeMillis());
            log.info("refreshIpCache done@offerIpList:{},allMachineIpList:{},OFFER_IP_SET:{}",
                    offerIpList.size(), allMachineIpList.size(), OFFER_IP_SET.size());
        }
    }

    @Override
    public void initResources() {
        init();
    }
}
