package com.yy.gameecology.activity.service.aov.phase;

import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.common.consts.aov.AovConst;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseTeamApplyMapper;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseTeamMapper;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseTeamMemberMapper;
import com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseTeamApply;
import com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseTeamMember;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class AovPhaseTeamApplyService {

    @Resource
    private AovPhaseTeamMapper aovPhaseTeamMapper;

    @Resource
    private AovPhaseTeamApplyMapper aovPhaseTeamApplyMapper;

    @Resource
    private AovPhaseTeamMemberMapper aovPhaseTeamMemberMapper;

    public void addApply(AovPhaseTeamApply aovPhaseTeamApply) {
        int rs = aovPhaseTeamApplyMapper.insert(aovPhaseTeamApply);
        log.info("addApply with teamId:{} uid:{} rs:{}", aovPhaseTeamApply.getTeamId(), aovPhaseTeamApply.getUid(), rs);
    }

    @Transactional(rollbackFor = Exception.class)
    public void apply(AovPhaseTeamApply aovPhaseTeamApply,long score) {
        AovPhaseTeamMember teamMember = new AovPhaseTeamMember(aovPhaseTeamApply.getTeamId(),
                aovPhaseTeamApply.getPhaseId(), aovPhaseTeamApply.getUid(), AovConst.AovTeamRole.MATE+"",
                0, new Date());
        int updateRow = aovPhaseTeamMapper.incrTeamCnt(aovPhaseTeamApply.getTeamId(), score);
        if(updateRow > 0) {
            aovPhaseTeamMemberMapper.insertAovPhaseTeamMember(teamMember);
        } else {
            throw new BusinessException(467, "审批失败，当前队伍已满人");
        }
        List<Long> applyIds = new ArrayList<>();
        applyIds.add(aovPhaseTeamApply.getId());
        int rs = aovPhaseTeamApplyMapper.batchUpdateState(applyIds, AovConst.AovTeamApplyState.ACCEPT, AovConst.AovTeamApplyState.INIT);
        log.info("batchUpdateState with applyIds:{} rs:{}", applyIds, rs);
        rs = aovPhaseTeamApplyMapper.batchUpdateUidState(aovPhaseTeamApply.getUid(),
                AovConst.AovTeamApplyState.CANCEL, AovConst.PhaseTeamState.INIT);
        log.info("batchUpdateUidState with uid:{} rs:{}", aovPhaseTeamApply.getUid(), rs);
    }

    public void updateState(long id, int state) {
        aovPhaseTeamApplyMapper.updateState(id, state);
    }

    public List<AovPhaseTeamApply> listUnHandleApply(long teamId, long uid) {
        return aovPhaseTeamApplyMapper.listUnHandleApply(teamId, uid);
    }

    public List<AovPhaseTeamApply> listUnHandleApplyByPhase(long phaseId, List<Long> teamIds, long uid) {
        return aovPhaseTeamApplyMapper.listUnHandleApplyByPhase(phaseId, teamIds, uid);
    }

    public List<AovPhaseTeamApply> listApply(long phaseId, long uid) {
        return aovPhaseTeamApplyMapper.selectByUid(phaseId, uid);
    }

    public List<AovPhaseTeamApply> listApplyByOwner(long teamId) {
        return aovPhaseTeamApplyMapper.selectByTeamId(teamId);
    }

    public AovPhaseTeamApply selectById(long id) {
        return aovPhaseTeamApplyMapper.selectById(id);
    }

}
