package com.yy.gameecology.activity.service.ext;

import com.google.common.collect.ImmutableMap;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.common.consts.HdztRankType;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.hdzj.element.component.attr.RankMilestoneReportComponentAttr;
import com.yy.thrift.hdztranking.Rank;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service("jyjfsMpPKHandler")
public class JYJFSPKMilestoneReportExtHandler implements RankMilestoneReportExtHandler {

    @Autowired
    private HdztRankingThriftClient rankingThriftClient;

    @Override
    public Map<String, String> buildExt(RankingScoreChanged event, RankMilestoneReportComponentAttr attr) {
        //积分赛补上总荣耀值
        Rank rank = rankingThriftClient.queryPointedMembersRanking(attr.getActId(), attr.getRankId() - 1000, attr.getPhaseId(), StringUtils.EMPTY, event.getMember(), ImmutableMap.of(RankExtParaKey.RANK_SCORE_SOURCE, HdztRankType.SRC));
        long totalScore = rank == null ? 0 : rank.getScore();
        return ImmutableMap.of("totalScore", String.valueOf(totalScore));
    }
}
