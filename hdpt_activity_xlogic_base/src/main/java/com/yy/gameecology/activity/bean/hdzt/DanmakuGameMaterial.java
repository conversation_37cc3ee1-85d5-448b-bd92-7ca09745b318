package com.yy.gameecology.activity.bean.hdzt;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrCollector;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

@Data
public class DanmakuGameMaterial {

    @ComponentAttrField(labelText = "游戏名称")
    private String gameName;

    @ComponentAttrField(labelText = "游戏appid(生产环境不一样)")
    private String appId;

    @ComponentAttrField(labelText = "游戏名素材url")
    private String gameNameUrl;

    @ComponentAttrField(labelText = "游戏封面url", propType = ComponentAttrCollector.PropType.IMAGE)
    private String gameUrl;

    @ComponentAttrField(labelText = "游戏介绍")
    private String gameDesc;
}
