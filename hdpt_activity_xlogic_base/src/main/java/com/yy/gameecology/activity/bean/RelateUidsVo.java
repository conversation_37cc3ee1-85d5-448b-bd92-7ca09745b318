package com.yy.gameecology.activity.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/5/7 16:56
 */
@Getter
@Setter
public class RelateUidsVo {

    public static final RelateUidsVo EMPTY = new RelateUidsVo();

    /**
     * 0:成功 1:数据不存在 2:参数错误  94:百度求错误 96:数据库操作错误 97:超过限制频率 98:没有权限访问 99:服务器异常
     */
    int rescode;

    /**
     * 手机关联uid列表
     */
    Set<Long> mobRelUids;

    /**
     * 身份证关联uid列表
     */
    Set<Long> idRelUids;

    public Set<Long> getAllRelUids() {
        int modRelSize = mobRelUids == null ? 0 : mobRelUids.size();
        int idRelSize = idRelUids == null ? 0 : idRelUids.size();
        Set<Long> uids = new HashSet<Long>(modRelSize + idRelSize);
        if (mobRelUids != null && !mobRelUids.isEmpty()) {
            uids.addAll(mobRelUids);
        }
        if (idRelUids != null && !idRelUids.isEmpty()) {
            uids.addAll(idRelUids);
        }
        return uids;
    }
}
