package com.yy.gameecology.activity.service.pepc;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.protobuf.Message;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.client.yrpc.PubgGameGatewayClient;
import com.yy.gameecology.activity.dao.mysql.PepcDao;
import com.yy.gameecology.common.consts.PepcConst;
import com.yy.gameecology.common.consts.pubg.PubgConst;
import com.yy.gameecology.common.db.mapper.pepc.PepcGameMapper;
import com.yy.gameecology.common.db.model.gameecology.pepc.PepcGame;
import com.yy.gameecology.common.db.model.gameecology.pepc.PepcGameLive;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.bean.pepc.GroupGameLiveItemVo;
import com.yy.gameecology.hdzj.bean.pepc.GroupGameLiveVo;
import com.yy.gameecology.hdzj.element.component.attr.PepcPhaseComponentAttr;
import com.yy.zhuiya.game.gateway.gen.pb.GameGateway;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-07 20:45
 **/
@Service
public class PepcGameLiveService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private PepcDao pepcDao;

    @Autowired
    private PepcGameMapper pepcGameMapper;

    @Autowired
    private PubgGameGatewayClient pubgGameGatewayClient;


    public void genGameLiveInfo(long actId, Date now, Date startTimeBegin, Date startTimeEnd) {
        List<PepcGame> pubgGames = pepcGameMapper.selectPepcGame(actId, startTimeBegin, startTimeEnd);
        if (CollectionUtils.isEmpty(pubgGames)) {
            return;
        }
        //查询腾讯接口拿直播数据
        for (PepcGame game : pubgGames) {
            genSingleLiveInfo(actId, now, game);
            SysEvHelper.waiting(100);
        }
    }

    private void genSingleLiveInfo(long actId, Date now, PepcGame game) {
        if (Convert.toLong(game.getMatchId(), 0L) == 0) {
            return;
        }
        if (now.before(game.getStartTime())) {
            return;
        }
        List<GameGateway.QueryLiveInfoRsp.LiveInfoList> liveInfoLists = pubgGameGatewayClient.queryLiveInfo(game.getMatchId());
        if (CollectionUtils.isEmpty(liveInfoLists)) {
            return;
        }
        GameGateway.QueryLiveInfoRsp.LiveInfoList liveInfoListItem = liveInfoLists.get(0);
        List<GameGateway.QueryLiveInfoRsp.LiveInfo> liveInfo = liveInfoListItem.getGameLiveInfoList();
        List<GameGateway.QueryLiveInfoRsp.LiveInfo> recordInfo = liveInfoListItem.getGameRecordInfoList();
        if (CollectionUtils.isEmpty(liveInfo) && CollectionUtils.isEmpty(recordInfo)) {
            log.info("genSingleLiveInfo empty,gameId:{},childId:{}", game.getId(), game.getMatchId());
            return;
        }
        saveLiveData(actId, game, recordInfo, liveInfoListItem.getEventType(), liveInfo);
    }

    private void saveLiveData(long actId, PepcGame game, List<GameGateway.QueryLiveInfoRsp.LiveInfo> recordInfo, String eventType, List<GameGateway.QueryLiveInfoRsp.LiveInfo> liveInfo) {
        String liveInfoJson = CollectionUtils.isNotEmpty(liveInfo) ? listToJson(liveInfo) : "";
        String recordInfoJson = CollectionUtils.isNotEmpty(recordInfo) ? listToJson(recordInfo) : "";

        //入库
        PepcGameLive exit = pepcDao.getPepcGameLive(actId, game.getId());
        if (exit == null) {
            PepcGameLive insert = new PepcGameLive();
            insert.setActId(actId);
            insert.setGameId(game.getId());
            insert.setPhaseId(game.getPhaseId());
            insert.setGroupCode(game.getGroupCode());
            insert.setSiteId(game.getSiteId());
            insert.setParentId(game.getParentId());
            insert.setMatchId(game.getMatchId());
            insert.setJoinFunc(game.getJoinFunc());
            insert.setModuleId(game.getModuleId());
            insert.setStartTime(game.getStartTime());
            insert.setState(PepcConst.GameLiveState.LIVE);
            insert.setGameLiveInfo(liveInfoJson);
            insert.setGameRecordInfo(recordInfoJson);
            insert.setCreateTime(new Date());
            pepcDao.addPepcGameLive(insert);
            log.info("saveLiveData add,eventType:{},live:{}", eventType, JSON.toJSONString(insert));

        } else if (PubgConst.LiveEventType.STOP.equals(eventType)) {
            boolean needUpdateRecord = StringUtil.isNotBlank(recordInfoJson) && !recordInfoJson.equals(exit.getGameRecordInfo());
            boolean needUpdateState = exit.getState() != PepcConst.GameLiveState.RECORD;
            boolean needUpdate = needUpdateRecord || needUpdateState;
            if (needUpdate) {
                pepcDao.updatePepcGameLive(exit.getId(), liveInfoJson, recordInfoJson, PepcConst.GameLiveState.RECORD);
                log.info("saveLiveData update,gameId:{},record:{},live:{}", game.getId(), recordInfoJson, liveInfoJson);
            }
        }
    }


    public String listToJson(List<? extends Message> messages) {
        List<JSONObject> jsonList = new ArrayList<>();
        for (Message message : messages) {
            // 将 Protobuf 消息转换为 JSON 字符串
            String jsonStr = JsonFormat.printToString(message);
            // 将 JSON 字符串解析为 JSONObject
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            jsonList.add(jsonObject);
        }
        // 使用 FastJSON 将列表转换为最终的 JSON 字符串
        return JSON.toJSONString(jsonList);
    }

    public GroupGameLiveVo queryGroupGameLive(PepcPhaseComponentAttr attr, Date now, String groupCode, Integer phaseId) {
        List<GroupGameLiveItemVo> liveList = Lists.newArrayList();

        List<PepcPhaseComponentAttr.PepcGameConfig> gameConfigs = attr.getGameConfig()
                .stream()
                .filter(p -> p.getPhaseId().equals(phaseId))
                .toList();

        List<PepcGameLive> lives = pepcDao.getPepcGameLive(attr.getActId(), phaseId, groupCode);
        List<PepcGame> games = pepcDao.getPepcGame(attr.getActId(), phaseId, groupCode);


        for (var gameConfig : gameConfigs) {
            GroupGameLiveItemVo vo = new GroupGameLiveItemVo();
            //---直播状态
            //赛事还没生成的时候就要显示，只能用配置作为基础数据显示，用时间匹配哪场直播，不能有时间重合的赛事，否则匹配不上
            Optional<PepcGameLive> pepcGameLive = lives.stream()
                    .filter(p -> p.getStartTime().getTime() == gameConfig.getStartTime().getTime())
                    .findFirst();
            vo.setStartTime(gameConfig.getStartTime().getTime());
            if (pepcGameLive.isEmpty()) {
                vo.setState(PepcConst.GameLiveState.NOT_BEGIN);
            } else {
                vo.setState(pepcGameLive.get().getState());
                String urlContent = pepcGameLive.get().getState().equals(PepcConst.GameLiveState.LIVE) ?
                        pepcGameLive.get().getGameLiveInfo()
                        : pepcGameLive.get().getGameRecordInfo();
                vo.setGameLiveInfo(urlContent);
            }

            //---比赛状态
            Optional<PepcGame> pepcGame = games.stream().filter(p -> p.getStartTime().getTime() == gameConfig.getStartTime().getTime()).findFirst();
            if (pepcGame.isEmpty()) {
                vo.setGameState(PepcConst.LiveGameState.NOT_BEGIN);
            } else {
                PepcGame gameData = pepcGame.get();
                if (now.before(gameData.getStartTime())) {
                    vo.setGameState(PepcConst.LiveGameState.NOT_BEGIN);
                } else if (now.after(gameData.getStartTime())
                           && !PepcConst.GameState.FINAL_STATE.contains(gameData.getState())) {
                    vo.setGameState(PepcConst.LiveGameState.PLAY_ING);
                } else {
                    vo.setGameState(PepcConst.LiveGameState.END);
                }

            }

            liveList.add(vo);
        }


        GroupGameLiveVo groupGameLiveVo = new GroupGameLiveVo();
        groupGameLiveVo.setLiveList(liveList);
        return groupGameLiveVo;
    }
}
