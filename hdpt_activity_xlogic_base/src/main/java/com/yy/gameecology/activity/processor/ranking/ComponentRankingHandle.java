package com.yy.gameecology.activity.processor.ranking;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;

import java.util.List;
import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption:  组件榜单数据扩展接口，聚焦对榜单展示数据的修改
 * @Date: 2021/7/2 0:31
 * @Modified:
 */
public interface ComponentRankingHandle<T extends ComponentAttr> {

    /**
     * 榜单数据查询接口，榜单扩展参数 需要配置rank_component_interceptor_
     * 组件扩展增加榜单的展示数据，增加objectList的数据
     * 实现方要保证实现有良好的过滤逻辑，只修改自己负责的榜单，其他直接返回
     *
     * @param attr
     * @param rankingInfo   榜单信息
     * @param rankId
     * @param phaseId
     * @param dateStr
     * @param count
     * @param pointedMember
     * @param ext
     * @return
     */
    List<Rank> queryRank(T attr, RankingInfo rankingInfo, long rankId, long phaseId, String dateStr, long count, String pointedMember, Map<String, String> ext);
}
