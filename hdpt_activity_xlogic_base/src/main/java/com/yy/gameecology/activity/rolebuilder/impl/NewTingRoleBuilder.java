package com.yy.gameecology.activity.rolebuilder.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.rankroleinfo.NewTingRoleItem;
import com.yy.gameecology.activity.rolebuilder.RoleBuilder;
import com.yy.gameecology.common.client.WebdbServiceClient;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SpringBeanAwareFactory;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.WebdbChannelInfo;
import com.yy.java.webdb.WebdbSubChannelInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021.09.13 15:04
 */
public class NewTingRoleBuilder implements RoleBuilder<NewTingRoleItem> {

    private static final NewTingRoleItem DEFAULT_OBJECT= new NewTingRoleItem();

//    private WebdbServiceClient webdbServiceClient = SpringBeanAwareFactory.getBean(WebdbServiceClient.class);
//    private WebdbThriftClient webdbThriftClient = SpringBeanAwareFactory.getBean(WebdbThriftClient.class);

    static {
        DEFAULT_OBJECT.setKey("0");
        DEFAULT_OBJECT.setName("神秘厅");
        DEFAULT_OBJECT.setAvatarInfo(Const.IMAGE.DEFAULT_CHANNEL_LOGO);
    }


    @Override
    public NewTingRoleItem getDefaultObject() {
        return DEFAULT_OBJECT;
    }

    @Override
    public NewTingRoleItem createBankObject() {
        return new NewTingRoleItem();
    }

    @Override
    public Map<String, NewTingRoleItem> buildRankByYy(Set<String> members) {
        if (CollectionUtils.isEmpty(members)){
            return Maps.newHashMap();
        }
        WebdbServiceClient webdbServiceClient = SpringBeanAwareFactory.getBean(WebdbServiceClient.class);
        Map<String, WebdbSubChannelInfo> channelInfos = webdbServiceClient.batchGetSubChannelInfo(Lists.newArrayList(members));
        //查询顶级频道的asid
        Set<Long> sids = members.stream().map(channel -> channel.split("_")[0])
                .filter(StringUtils::isNotBlank)
                .map(Long::parseLong)
                .collect(Collectors.toSet());
        WebdbThriftClient webdbThriftClient = SpringBeanAwareFactory.getBean(WebdbThriftClient.class);
        Map<Long, WebdbChannelInfo> sidChannelInfos = webdbThriftClient.batchGetChannelInfo(Lists.newArrayList(sids));
        NewTingRoleItem defaultObject = getDefaultObject();
        return members.stream().map(member -> {
            NewTingRoleItem rankItem = new NewTingRoleItem();
            rankItem.setKey(member);

            WebdbSubChannelInfo channelInfo = channelInfos.get(member);
            //通过顶级频道获取asid
            WebdbChannelInfo sidChannelInfo = sidChannelInfos.get(rankItem.getSid());

            //顶级频道的厅是查不到信息的
            if (rankItem.getSid() ==rankItem.getSsid()) {
                rankItem.setName(sidChannelInfo == null ? defaultObject.getName() : sidChannelInfo.getName());
            } else {
                rankItem.setName(channelInfo == null ? defaultObject.getName() : channelInfo.getName());
            }
            //rankItem.setAvatarInfo(channelInfo.getOrDefault("logo_url", defaultObject.getAvatarInfo()));
            long asid = Optional.ofNullable(sidChannelInfo).map(WebdbChannelInfo::getAsid).filter(StringUtils::isNumeric).map(Long::parseLong).orElse(rankItem.getSid());
            rankItem.setAsid(asid);
            rankItem.setAvatarInfo(sidChannelInfo == null ? defaultObject.getAvatarInfo() : WebdbUtils.getLogo(sidChannelInfo));

            return rankItem;
        }).collect(Collectors.toMap(NewTingRoleItem::getKey, Function.identity()));
    }
}
