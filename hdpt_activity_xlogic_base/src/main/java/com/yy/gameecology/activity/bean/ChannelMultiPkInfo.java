package com.yy.gameecology.activity.bean;

import java.util.Map;

/**
 * <AUTHOR> 2020/7/25
 */
public class ChannelMultiPkInfo {

    private long sid;

    private long ssid;

    private long host;

    private Map<Integer, Long> anchors;

    public long getSid() {
        return sid;
    }

    public void setSid(long sid) {
        this.sid = sid;
    }

    public long getSsid() {
        return ssid;
    }

    public void setSsid(long ssid) {
        this.ssid = ssid;
    }

    public long getHost() {
        return host;
    }

    public void setHost(long host) {
        this.host = host;
    }

    public Map<Integer, Long> getAnchors() {
        return anchors;
    }

    public void setAnchors(Map<Integer, Long> anchors) {
        this.anchors = anchors;
    }
}
