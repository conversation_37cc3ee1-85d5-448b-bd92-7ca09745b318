package com.yy.gameecology.activity.bean.mms;

/**
 * Autogenerated by Thrift Compiler (0.9.1)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */

import lombok.Data;

import java.util.List;

/**
 * 监控/举报信息
 */
@Data
public class MmsReport  {


    /**
     * 流水号（视频流系列编号 programid）
     */
    protected String serial; // required
    /**
     * 待审用户uid
     */
    protected long uid; // required
    /**
     * 上报时间
     */
    protected String reportTime; // required
    /**
     * 上报主体 （备注）， 由接入双方按照约定 json 结构上传，接入服务按照给定格式检查
     */
    protected String reportComment; // required
    /**
     * 上报附件
     */
    protected List<MmsReportAttc> attachments; // required
    /**
     * 上传人uid（非必填）
     */
    protected long uploadUid; // required
    /**
     * 举报级别（非必填）
     */
    protected String severity; // required
    /**
     * 频道ID（非必填）
     */
    protected long sid; // required
    /**
     * 子频道ID（非必填）
     */
    protected long ssid; // required
    /**
     * OWID（非必填）
     */
    protected long owid; // required
    /**
     * 人气（非必填）
     */
    protected long pcu; // required
    /**
     * 附带参数（非必填）
     */
    protected String extPar;

    protected String callback;


}

