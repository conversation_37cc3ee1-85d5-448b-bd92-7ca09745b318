package com.yy.gameecology.activity.dao.mysql;

import com.yy.gameecology.common.db.model.gameecology.pepc.*;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-01 10:41
 **/
@Repository
public class PepcDao {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Getter
    @Autowired
    private GameecologyDao gameecologyDao;


    public List<PepcTeam> getAllTeam(long actId) {
        PepcTeam where = new PepcTeam();
        where.setActId(actId);
        return gameecologyDao.select(PepcTeam.class, where);
    }

    public List<PepcTeamMember> getAllTeamMember(long actId) {
        PepcTeamMember where = new PepcTeamMember();
        where.setActId(actId);
        return gameecologyDao.select(PepcTeamMember.class, where);
    }

    public PepcTeamMember getPepcTeamMember(long actId, long uid) {
        PepcTeamMember where = new PepcTeamMember();
        where.setActId(actId);
        where.setMemberUid(uid);
        return gameecologyDao.selectOne(PepcTeamMember.class, where, "");
    }

    public long countPepcTeamMember(long actId, long uid) {
        PepcTeamMember where = new PepcTeamMember();
        where.setActId(actId);
        where.setMemberUid(uid);
        return gameecologyDao.count(PepcTeamMember.class, where);
    }

    public List<PepcPhaseInfo> getAllPepcPhaseInfo(long actId) {
        PepcPhaseInfo where = new PepcPhaseInfo();
        where.setActId(actId);
        return gameecologyDao.select(PepcPhaseInfo.class, where);
    }

    public long countPepcPhaseInfo(long actId) {
        PepcPhaseInfo where = new PepcPhaseInfo();
        where.setActId(actId);
        return gameecologyDao.count(PepcPhaseInfo.class, where);
    }

    public void savePepcPhaseInfo(List<PepcPhaseInfo> pepcPhaseInfos) {
        gameecologyDao.batchInsert(PepcPhaseInfo.class, pepcPhaseInfos, PepcPhaseInfo.TABLE_NAME);
    }



    public PepcPhaseInfo getPepcPhaseInfoByState(long actId, int state) {
        PepcPhaseInfo where = new PepcPhaseInfo();
        where.setActId(actId);
        where.setState(state);
        return gameecologyDao.selectOne(PepcPhaseInfo.class, where, "");
    }

    public void saveTeamGroup(List<PepcTeamGroup> groups) {
        if (CollectionUtils.isEmpty(groups)) {
            return;
        }
        gameecologyDao.batchInsert(PepcTeamGroup.class, groups, PepcTeamGroup.TABLE_NAME);
    }

    public List<PepcTeamGroup> getPepcTeamGroup(long actId, int phaseId) {
        PepcTeamGroup where = new PepcTeamGroup();
        where.setActId(actId);
        where.setPhaseId(phaseId);
        return gameecologyDao.select(PepcTeamGroup.class, where);
    }

    public List<PepcTeamGroup> getPepcTeamGroup(long actId, int phaseId, String groupCode) {
        PepcTeamGroup where = new PepcTeamGroup();
        where.setActId(actId);
        where.setPhaseId(phaseId);
        where.setGroupCode(groupCode);
        return gameecologyDao.select(PepcTeamGroup.class, where);
    }

    public PepcPhaseInfo getPepcPhaseInfo(long actId, int phaseId) {
        PepcPhaseInfo where = new PepcPhaseInfo();
        where.setActId(actId);
        where.setPhaseId(phaseId);
        return gameecologyDao.selectOne(PepcPhaseInfo.class, where, "");
    }

    public void savePepcTeamSchedule(List<PepcTeamSchedule> items) {
        gameecologyDao.batchInsert(PepcTeamSchedule.class, items, PepcTeamSchedule.TABLE_NAME);
    }

    public List<PepcTeamSchedule> getPepcTeamSchedule(long actId, int phaseId) {
        PepcTeamSchedule where = new PepcTeamSchedule();
        where.setActId(actId);
        where.setPhaseId(phaseId);
        return gameecologyDao.select(PepcTeamSchedule.class, where);
    }

    public List<PepcTeamSchedule> getPepcTeamSchedule(long actId, int phaseId, String groupCode) {
        PepcTeamSchedule where = new PepcTeamSchedule();
        where.setActId(actId);
        where.setGroupCode(groupCode);
        where.setPhaseId(phaseId);
        return gameecologyDao.select(PepcTeamSchedule.class, where);
    }

    public PepcTeamSchedule getPepcTeamSchedule(long actId, int phaseId, long teamId) {
        PepcTeamSchedule where = new PepcTeamSchedule();
        where.setActId(actId);
        where.setTeamId(teamId);
        where.setPhaseId(phaseId);
        return gameecologyDao.selectOne(PepcTeamSchedule.class, where,"");
    }

    public PepcGame getPepcGameById(long actId, long id) {
        PepcGame where = new PepcGame();
        where.setActId(actId);
        where.setId(id);
        return gameecologyDao.selectOne(PepcGame.class, where,"");
    }

    public List<PepcGame> getPepcGame(long actId, int state) {
        PepcGame where = new PepcGame();
        where.setActId(actId);
        where.setState(state);
        return gameecologyDao.select(PepcGame.class, where);
    }

    public List<PepcGame> getPepcGame(long actId, int phaseId, String groupCode) {
        PepcGame where = new PepcGame();
        where.setActId(actId);
        where.setPhaseId(phaseId);
        where.setGroupCode(groupCode);
        return gameecologyDao.select(PepcGame.class, where);
    }

    public List<PepcGame> getPepcGameByPhaseId(long actId, int phaseId) {
        PepcGame where = new PepcGame();
        where.setActId(actId);
        where.setPhaseId(phaseId);
        return gameecologyDao.select(PepcGame.class, where);
    }

    public void savePepcGame(List<PepcGame> items) {
        gameecologyDao.batchInsert(PepcGame.class, items, PepcGame.TABLE_NAME);
    }

    public List<PepcGameTeam> getGameTeam(long actId, int phaseId, String groupCode) {
        PepcGameTeam where = new PepcGameTeam();
        where.setActId(actId);
        where.setPhaseId(phaseId);
        where.setGroupCode(groupCode);
        return gameecologyDao.select(PepcGameTeam.class, where);
    }

    public List<PepcGameTeam> getGameTeamBySid(long actId, long sid, long ssid) {
        PepcGameTeam where = new PepcGameTeam();
        where.setActId(actId);
        where.setSid(sid);
        where.setSsid(ssid);
        return gameecologyDao.select(PepcGameTeam.class, where);
    }

    public List<PepcGameTeam> getGameTeamByGameId(long actId, long gameId) {
        PepcGameTeam where = new PepcGameTeam();
        where.setActId(actId);
        where.setGameId(gameId);
        return gameecologyDao.select(PepcGameTeam.class, where);
    }

    public List<PepcGameTeam> getGameTeam(long actId, long gameId, int state) {
        PepcGameTeam where = new PepcGameTeam();
        where.setActId(actId);
        where.setGameId(gameId);
        where.setState(state);
        return gameecologyDao.select(PepcGameTeam.class, where);
    }

    public List<PepcGameTeam> getGameTeam(long actId, Integer state) {
        PepcGameTeam where = new PepcGameTeam();
        where.setActId(actId);
        where.setState(state);
        return gameecologyDao.select(PepcGameTeam.class, where);
    }

    public Map<Long, PepcGameTeam> getGameTeamMap(long actId) {
        List<PepcGameTeam> teams = getGameTeam(actId, null);
        return teams.stream().collect(Collectors.toMap(PepcGameTeam::getId, p -> p));
    }

    public void savePepcGameTeam(List<PepcGameTeam> items) {
        gameecologyDao.batchInsert(PepcGameTeam.class, items, PepcGameTeam.TABLE_NAME);
    }


    public List<PepcGameMember> getPepcGameMember(long actId, int state) {
        PepcGameMember where = new PepcGameMember();
        where.setActId(actId);
        where.setState(state);
        return gameecologyDao.select(PepcGameMember.class, where);
    }

    public List<PepcGameMember> getPepcGameMember(long actId, long uid) {
        PepcGameMember where = new PepcGameMember();
        where.setActId(actId);
        where.setUid(uid);
        return gameecologyDao.select(PepcGameMember.class, where);
    }

    public List<PepcGameMember> getPepcGameMemberByGameId(long actId, long gameId) {
        PepcGameMember where = new PepcGameMember();
        where.setActId(actId);
        where.setGameId(gameId);
        return gameecologyDao.select(PepcGameMember.class, where);
    }

    public PepcGameMember getPepcGameMember(long actId, long gameId, long uid) {
        PepcGameMember where = new PepcGameMember();
        where.setActId(actId);
        where.setGameId(gameId);
        where.setUid(uid);
        return gameecologyDao.selectOne(PepcGameMember.class, where, "");
    }

    public void savePepcGameMember(List<PepcGameMember> items) {
        gameecologyDao.batchInsert(PepcGameMember.class, items, PepcGameMember.TABLE_NAME);
    }

    public int savePepcGameMemberMatchResult(long id, int state, int rank, String harm, String elimination, String assist,
                                             String cure, String hitRatio, String kda) {
        PepcGameMember where = new PepcGameMember();
        where.setId(id);

        PepcGameMember update = new PepcGameMember();
        update.setRank(rank);
        update.setHarm(harm);
        update.setElimination(elimination);
        update.setAssist(assist);
        update.setCure(cure);
        update.setHitRatio(hitRatio);
        update.setKda(kda);
        update.setState(state);

        return gameecologyDao.update(PepcGameMember.class, where, update);
    }

    public long countUserAwardRecord(Long actId, Long phaseId, Long uid, Integer awardType) {
        PepcAwardRecord where = new PepcAwardRecord();
        where.setActId(actId);
        where.setPhaseId(phaseId);
        where.setUid(uid);
        where.setAwardType(awardType);
        return gameecologyDao.count(PepcAwardRecord.class, where);
    }

    public long saveAwardRecord(PepcAwardRecord pepcAwardRecord) {
        return gameecologyDao.insertWithReturnAutoId(PepcAwardRecord.class, pepcAwardRecord);
    }

    public PepcGameLive getPepcGameLive(long actId, long gameId) {
        PepcGameLive pubgGameLiveWhere = new PepcGameLive();
        pubgGameLiveWhere.setActId(actId);
        pubgGameLiveWhere.setGameId(gameId);
        return gameecologyDao.selectOne(PepcGameLive.class, pubgGameLiveWhere, "");
    }

    public List<PepcGameLive> getPepcGameLive(long actId, int phaseId, String groupCode) {
        PepcGameLive pubgGameLiveWhere = new PepcGameLive();
        pubgGameLiveWhere.setActId(actId);
        pubgGameLiveWhere.setPhaseId(phaseId);
        pubgGameLiveWhere.setGroupCode(groupCode);
        return gameecologyDao.select(PepcGameLive.class, pubgGameLiveWhere);
    }

    public int addPepcGameLive(PepcGameLive pepcGameLive) {
        return gameecologyDao.insert(PepcGameLive.class, pepcGameLive);
    }

    public int updatePepcGameLive(Long id, String liveInfoJson, String recordInfoJson, int state) {
        PepcGameLive pepcGameLiveWhere = new PepcGameLive();
        pepcGameLiveWhere.setId(id);

        PepcGameLive update = new PepcGameLive();
        update.setGameLiveInfo(liveInfoJson);
        update.setGameRecordInfo(recordInfoJson);
        update.setState(state);
        return gameecologyDao.update(PepcGameLive.class, pepcGameLiveWhere, update);
    }

    public long countPepcSubscribe(long actId, long uid) {
        PepcSubscribe where = new PepcSubscribe();
        where.setPrevActId(actId);
        where.setUid(uid);
        return gameecologyDao.count(PepcSubscribe.class, where);
    }

}
