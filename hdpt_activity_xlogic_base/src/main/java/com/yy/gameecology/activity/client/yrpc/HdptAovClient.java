package com.yy.gameecology.activity.client.yrpc;

import com.yy.gameecology.common.consts.MobileEnrollAppId;
import com.yy.hdpt.aov.proto.HdptAov;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HdptAovClient {

    @Reference(protocol = "yrpc", owner = "hdpt_template_yrpc", registry = "yrpc-reg", lazy = true)
    private HdptAovService hdptAovService;

    public HdptAovService getProxy() {
        return hdptAovService;
    }

    public boolean tryAddFirstAward(int appid, long uid, String seq, String remark) {
        HdptAov.AddFirstAwardReq req = HdptAov.AddFirstAwardReq.newBuilder()
                .setAppId(appid)
                .setUid(uid)
                .setSeq(seq)
                .setRemark(remark)
                .build();

        try {
            var rsp = getProxy().tryAddFirstAward(req);
            return rsp != null && (rsp.getCode() == 0 || rsp.getCode() == 1);
        } catch (Exception e) {
            log.error("tryAddFirstAward fail", e);
            throw e;
        }
    }

    public Pair<Long, String> checkAwardAccount(long uid, String account, String mobile, String remark) {
        HdptAov.AddAwardAccountReq req = HdptAov.AddAwardAccountReq.newBuilder()
                .setAppId(MobileEnrollAppId.AppId.AOV_APP_ID)
                .setUid(uid)
                .setAccount(account)
                .setMobile(mobile)
                .setRemark(remark)
                .build();

        try {
            var rsp = hdptAovService.tryAddAwardAccount(req);
            if (rsp == null) {
                return Pair.of(-1L, "服务器繁忙，请售后再试");
            }

            if (rsp.getCode() == 0) {
                return Pair.of(rsp.getAddId(), StringUtils.EMPTY);
            }

            if (rsp.getCode() == 1) {
                return Pair.of(0L, StringUtils.EMPTY);
            }

            return Pair.of(-1L, rsp.getMessage());
        } catch (Exception e) {
            log.error("checkAwardAccount fail", e);
            return Pair.of(-1L, "服务器繁忙，请售后再试");
        }
    }


    public Pair<Long, String> checkAwardAccount(long uid, int actId, String account, String mobile, String remark) {
        HdptAov.AddAwardAccountReq req = HdptAov.AddAwardAccountReq.newBuilder()
                .setAppId(actId)
                .setUid(uid)
                .setAccount(account)
                .setMobile(mobile)
                .setRemark(remark)
                .build();

        try {
            var rsp = hdptAovService.tryAddAwardAccount(req);
            if (rsp == null) {
                return Pair.of(-1L, "服务器繁忙，请售后再试");
            }

            if (rsp.getCode() == 0) {
                return Pair.of(rsp.getAddId(), StringUtils.EMPTY);
            }

            if (rsp.getCode() == 1) {
                return Pair.of(0L, StringUtils.EMPTY);
            }

            return Pair.of(-1L, rsp.getMessage());
        } catch (Exception e) {
            log.error("checkAwardAccount fail", e);
            return Pair.of(-1L, "服务器繁忙，请售后再试");
        }
    }

    public boolean canMobileEnroll(long uid, String mobileHash, int mobileUidLimit, String remark) {
        var req = HdptAov.AddMobileEnrollmentReq.newBuilder()
                .setAppId(MobileEnrollAppId.AppId.AOV_APP_ID)
                .setUid(uid)
                .setMobileHash(mobileHash)
                .setRemark(remark)
                .setLimit(mobileUidLimit)
                .build();

        try {
            var rsp = hdptAovService.tryAddMobileEnrollment(req);
            return rsp != null && rsp.getCode() == 0;
        } catch (Exception e) {
            log.error("canMobileEnroll fail", e);
            return false;
        }
    }

    public boolean canMobileEnroll(long uid, String mobileHash, int actId, int mobileUidLimit, String remark) {
        var req = HdptAov.AddMobileEnrollmentReq.newBuilder()
                .setAppId(actId)
                .setUid(uid)
                .setMobileHash(mobileHash)
                .setRemark(remark)
                .setLimit(mobileUidLimit)
                .build();

        try {
            var rsp = hdptAovService.tryAddMobileEnrollment(req);
            return rsp != null && rsp.getCode() == 0;
        } catch (Exception e) {
            log.error("canMobileEnroll fail", e);
            return false;
        }
    }


    public void rollbackAddAwardAccount(long addId) {
        var req = HdptAov.RollbackAddAwardAccountReq.newBuilder().setAddId(addId).build();

        try {
            var rsp = hdptAovService.rollbackAddAwardAccount(req);
            log.info("rollbackAddAwardAccount success rsp:{}", rsp);
        } catch (Exception e) {
            log.error("rollbackAddAwardAccount fail", e);
        }
    }
}
