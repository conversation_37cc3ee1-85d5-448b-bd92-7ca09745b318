package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.client.yrpc.SvcPlatformGatewayClient;
import com.yy.gameecology.activity.config.proto.ProtoCodec;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.ShutdownHolder;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.protocol.pb.GameecologyActivity.GameEcologyMsg;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.svc_gateway.*;
import io.opentelemetry.api.trace.Span;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * use svc platform gateway
 *
 * <AUTHOR>
 * @since 2021/10/16
 */
@Slf4j
@Service
public class SvcSDKServiceV2Impl implements SvcSDKService {

    @Value("${svcsdk.appId:60124}")
    private int appId;

    /**
     * 唐少弟分配的广播组类型，1个业务部门用1个id，和追玩公用
     */
    private final long GROUP_TYPE = 1000;

    @Autowired
    private SvcPlatformGatewayClient client;

    @Autowired
    private ProtoCodec protoCodec;

    @Autowired
    private CommonService commonService;

    @Autowired
    private WhiteListService whiteListService;

    @Override
    public void sdkBcUsergroup(GameEcologyMsg message) {
        BroadcastUserGroupReq req = new BroadcastUserGroupReq();
        req.setUserGroupType(GROUP_TYPE);
        req.setUserGroupId(message.getUri());
        try {
            req.setData(covertMessage(message));
            SimpleResp resp = client.getProxy().sdkBcUsergroup(appId, req);
            if (resp == null || resp.getCode() != 0) {
                log.error("unicastUid error, see trace id find error");
            }

            log.info("sdkBcUsergroup uri:{} resp:{}", message.getUri(),
                    resp != null ? resp : StringUtils.EMPTY);

        } catch (Exception e) {
            log.error("sdkBcUsergroup error,e:{}", e.getMessage(), e);
        }
    }


    @Override
    @Report
    public void unicastUid(long uid, GameEcologyMsg message) {
        if (isNotNeedSend(message)) {
            return;
        }
        String traceId = Span.current().getSpanContext().getTraceId();
        Map<String, String> extMap = new HashMap<>();
        extMap.put("1", traceId);
        try {
            UnicastReq req = new UnicastReq();
            req.setUid(uid);
            req.setExt(extMap);
            req.setMessage(covertMessage(message));
            SimpleResp resp = client.getProxy().unicastToClient(appId, req);

            if (resp == null || resp.getCode() != 0) {
                log.error("unicastUid error, see trace id find error");
            }


            log.info("unicastUid uid:{} uri:{} resp:{}", uid, message.getUri(),
                    resp != null ? resp : StringUtils.EMPTY);
        } catch (Exception e) {
            log.error("unicastUid uid:{} msg:{} err:{}", uid, message, e.getMessage(), e);
        }
    }

    @Override
    @Report
    public void broadcastSub(long sid, long ssid, GameEcologyMsg message) {
        if (sid <= 0 && ssid <= 0) {
            log.warn("broadcastSub have been ignored because sid and ssid is empty,uri:{}",
                    message.getUri());
            return;
        }
        if (isNotNeedSend(message)) {
            return;
        }
        String traceId = Span.current().getSpanContext().getTraceId();
        Map<String, String> extMap = new HashMap<>();
        extMap.put("1", traceId);
        try {
            BroadcastReq req = new BroadcastReq();
            req.setSid(sid);
            req.setSsid(ssid);
            req.setMessage(covertMessage(message));
            req.setExt(extMap);
            SimpleResp resp = client.getProxy().broadcastSub(appId, req);

            if (resp == null || resp.getCode() != 0) {
                log.error("broadcastSub error, see trace id find error");
            }

            log.info("broadcastSub sid:{},ssid:{}, uri:{}, resp:{}", sid, ssid, message.getUri(),
                    resp != null ? resp : StringUtils.EMPTY);
        } catch (Exception e) {
            log.error("broadcastSub sid:{},ssid:{},msg:{}", sid, ssid, message, e);
        }
    }

    @Override
    @Report
    public void broadcastTop(long sid, GameEcologyMsg message) {
        if (sid <= 0) {
            log.warn("broadcastTop have been ignored because sid is empty,uri:{}",
                    message.getUri());
            return;
        }
        if (isNotNeedSend(message)) {
            return;
        }
        String traceId = Span.current().getSpanContext().getTraceId();
        Map<String, String> extMap = new HashMap<>();
        extMap.put("1", traceId);
        try {
            BroadcastReq req = new BroadcastReq();
            req.setSid(sid);
            req.setMessage(covertMessage(message));
            req.setExt(extMap);
            SimpleResp resp = client.getProxy().broadcastTop(appId, req);

            if (resp == null || resp.getCode() != 0) {
                log.error("broadcastTemplate error, see trace id find error");
            }
            
            
            log.info("broadcastTop sid:{}, uri:{}, resp:{}", sid, message.getUri(),
                    resp != null ? resp : StringUtils.EMPTY);
        } catch (Exception e) {
            log.error("broadcastTop sid:{},ssid:{},msg:{}", sid, message, e);
        }
    }

    @Override
    @Report
    public void broadcastTemplate(Template template, GameEcologyMsg message) {
        Assert.notNull(template, "template is null");
        if (isNotNeedSend(message)) {
            return;
        }
        List<ChannelInfo> channelInfos = commonService.queryOnlineChannel(template);
        broadcastTemplate(template, message, channelInfos);
    }

    @Override
    @Report
    public void broadcastTemplate(long actId, Template template, GameEcologyMsg message) {
        Assert.notNull(template, "template is null");
        if (isNotNeedSend(message)) {
            return;
        }
        List<ChannelInfo> channelInfos = commonService.queryBroChannels(actId, template);
        broadcastTemplate(template, message, channelInfos);
    }


    @Override
    @Report
    public void broadcastAllChanelsInPW(long actId, GameEcologyMsg message) {
        List<ChannelInfo> channelInfos = whiteListService.getPwAllChannel(actId);

        log.info("begin bro,actId:{},size:{},uri:{}", actId, channelInfos.size(), message.getUri());
        for (ChannelInfo channelInfo : channelInfos) {
            broadcastTop(channelInfo.getSid(), message);
        }
        log.info("bro end,actId:{},channel size:{},uri:{}", actId, channelInfos.size(), message.getUri());
    }

    @Override
    @Report
    public void broadcastAllChanelsInSkillCard(GameEcologyMsg message) {
        List<ChannelInfo> channelInfos = commonService.getSkillCardAllChannel();

        log.info("begin broadcastAllChanelsInSkillCard,size:{},uri:{}", channelInfos.size(), message.getUri());
        for (ChannelInfo channelInfo : channelInfos) {
            broadcastSub(channelInfo.getSid(), channelInfo.getSsid(), message);
        }
        log.info("bro broadcastAllChanelsInSkillCard,channel size:{},uri:{}", channelInfos.size(), message.getUri());
    }

    @Override
    @Report
    public void broadcastFamilyInSkillCard(long familyId, GameEcologyMsg message) {
        List<ChannelInfo> familySsids = commonService.listFamilySsid2(familyId);
        log.info("begin broadcastFamilyInSkillCard,familyId:{},size:{},uri:{}", familyId, familySsids.size(), message.getUri());
        for (ChannelInfo ting : familySsids) {
            broadcastSub(ting.getSid(), ting.getSsid(), message);
        }
        log.info("bro broadcastFamilyInSkillCard done,familyId:{},ting size:{},uri:{}", familyId, familySsids.size(), message.getUri());
    }

    @Override
    @Report
    public void broadcastTemplate(Template template, GameEcologyMsg message, long sid, long ssid) {
        Assert.notNull(template, "template is null");
        if (isNotNeedSend(message)) {
            return;
        }
        List<ChannelInfo> channelInfos = commonService.queryOnlineChannel(template);
        if (CollectionUtils.isEmpty(channelInfos)) {
            return;
        }
        List<ChannelInfo> matchChannelInfos = channelInfos.parallelStream()
                .filter(channelInfo -> (sid == channelInfo.getSid() || sid == 0)
                        && ssid != channelInfo.getSsid())
                .collect(Collectors.toList());
        broadcastTemplate(template, message, matchChannelInfos);
    }

    private boolean isNotNeedSend(GameEcologyMsg message) {
        Assert.notNull(message, "message is null");
        // || SysEvHelper.skipSvcsdkInit()
        if (SysEvHelper.isYuFa()) {
            return true;
        }
        if (isClose(message.getUri())) {
            return true;
        }

        if (ShutdownHolder.isShuttingDown()) {
            log.warn("server is shut down ,message not need send {}", JsonFormat.printToString(message));
        }

        return false;
    }

    private boolean isClose(int uri) {
        boolean close =
                Const.GEPM.getParamValueToInt(GeParamName.SVCSDK_BROADCAST_SWITCH + uri, 1) == 0;
        if (close) {
            log.info("svcsdk isClose uri:{} close:{}", uri, close);
        }
        return close;
    }

    private String covertMessage(GameEcologyMsg message) throws Exception {
        return Hex.encodeHexString(protoCodec.encode(message).array());
    }

    @Override
    public void broadcastTemplateExclude(Template template, GameEcologyMsg message, Set<String> exclude) {
        List<ChannelInfo> channelInfos = commonService.queryOnlineChannel(template);
        if (CollectionUtils.isEmpty(channelInfos)) {
            return;
        }

        if (CollectionUtils.isEmpty(exclude)) {
            broadcastTemplate(template, message, channelInfos);
            return;
        }

        channelInfos = channelInfos.stream().filter(channelInfo -> {
            String sidAndSsid = channelInfo.getSid() + StringUtil.UNDERSCORE + channelInfo.getSsid();
            String sid = String.valueOf(channelInfo.getSid());
            return !exclude.contains(sid) && !exclude.contains(sidAndSsid);
        }).toList();

        broadcastTemplate(template, message, channelInfos);
    }

    private void broadcastTemplate(Template template, GameEcologyMsg message,
                                   List<ChannelInfo> channelInfos) {
        if (CollectionUtils.isEmpty(channelInfos)) {
            return;
        }
        try {
            String msg = covertMessage(message);
            String traceId = Span.current().getSpanContext().getTraceId();
            Map<String, String> extMap = new HashMap<>();
            extMap.put("1", traceId);
            List<BroadcastReq> batchReq = channelInfos.stream().filter(Objects::nonNull)
                    .map(channelInfo -> {
                        BroadcastReq req = new BroadcastReq();
                        req.setSid(channelInfo.getSid());
                        req.setSsid(channelInfo.getSsid());
                        req.setMessage(msg);
                        req.setExt(extMap);
                        if(SysEvHelper.isDev()) {
                            log.info("broadcastTemplate,channelInfo:{}", JSON.toJSONString(channelInfo));
                        }
                        return req;
                    }).collect(Collectors.toList());

            BatchResp batchResp = null;
            boolean existSubSid = channelInfos.stream().anyMatch(p -> p.getSsid() > 0);
            if (!existSubSid && template == Template.SkillCard) {
                log.info("broadcastTemplate is SkillCard top sid");
                batchResp = client.getProxy().batchBroadcastTop(appId, batchReq);
            } else {
                batchResp = client.getProxy().batchBroadcastSub(appId, batchReq);
            }

            if (batchResp == null || batchResp.getCode() != 0) {
                log.error("broadcastTemplate error, see trace id find error");
            }

            log.info("broadcastTemplate is completed, template:{},msg:{},resp:{}", template, msg, batchResp);
        } catch (Exception e) {
            log.error("broadcastTemplate template:{} msg:{} err:{}", template, message, e.getMessage(), e);
        }
    }
}
