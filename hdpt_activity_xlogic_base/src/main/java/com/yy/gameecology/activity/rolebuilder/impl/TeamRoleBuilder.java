package com.yy.gameecology.activity.rolebuilder.impl;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.rankroleinfo.TeamItem;
import com.yy.gameecology.activity.rolebuilder.RoleBuilder;

import java.util.Map;
import java.util.Set;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/9/17 0:31
 * @Modified:
 */
public class TeamRoleBuilder implements RoleBuilder<TeamItem> {

    private static final TeamItem DEFAULT_OBJECT= new TeamItem();
    static  {
        DEFAULT_OBJECT.setKey("0");
        DEFAULT_OBJECT.setName("神秘团");
        DEFAULT_OBJECT.setAsid(0L);
        //团没有头像
        DEFAULT_OBJECT.setAvatarInfo("");
    }
    @Override
    public TeamItem getDefaultObject() {
        return DEFAULT_OBJECT;
    }

    @Override
    public TeamItem createBankObject() {
        return new TeamItem();
    }

    /**
     * 从yy获取信息-团目前只有陪玩有，并且不能从yy获取信息
     *
     * @param members
     * @return
     */
    @Override
    public Map<String, TeamItem> buildRankByYy(Set<String> members) {
        return Maps.newHashMap();
    }

}
