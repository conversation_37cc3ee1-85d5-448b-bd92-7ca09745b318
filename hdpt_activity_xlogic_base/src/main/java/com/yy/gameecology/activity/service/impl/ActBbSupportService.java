package com.yy.gameecology.activity.service.impl;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.rolebuilder.RoleBuilder;
import com.yy.gameecology.activity.rolebuilder.impl.BabyRoleBuilder;
import com.yy.gameecology.activity.rolebuilder.impl.GuildRoleBuilder;
import com.yy.gameecology.activity.rolebuilder.impl.SubGuildRoleBuilder;
import com.yy.gameecology.activity.rolebuilder.impl.UserRoleBuilder;
import com.yy.gameecology.activity.service.ActSupportService;
import com.yy.gameecology.common.bean.Template;
import com.yy.thrift.act_ext_support.MemberItemInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption: 宝贝 活动信息相关支持
 * @Date: 2020/9/17 0:31
 * @Modified:
 */
@Component
public class ActBbSupportService implements ActSupportService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Override
    public Map<String, Map<String, MemberItemInfo>> queryMember(long actId, long rankId, Map<String, List<String>> memberIdMap) {
        return Maps.newHashMap();
    }

    @Override
    public RoleBuilder getUserRankBuilder() {
        return userRankBuilder;
    }

    @Override
    public RoleBuilder getBabyRankBuilder() {
        return babyRankBuilder;
    }

    @Override
    public RoleBuilder getHostRankBuilder() {
        return babyRankBuilder;
    }

    @Override
    public RoleBuilder getGuildRankBuilder() {
        return guildRankBuilder;
    }

    @Override
    public RoleBuilder getSubGuildRankBuilder() {
        return subGuidRankBuilder;
    }

    @Override
    public RoleBuilder getTeamRankBuilder() {
        return null;
    }

    private RoleBuilder userRankBuilder = new UserRoleBuilder();
    private RoleBuilder babyRankBuilder = new BabyRoleBuilder(Template.gamebaby);
    private RoleBuilder guildRankBuilder = new GuildRoleBuilder();
    private RoleBuilder subGuidRankBuilder = new SubGuildRoleBuilder();


}
