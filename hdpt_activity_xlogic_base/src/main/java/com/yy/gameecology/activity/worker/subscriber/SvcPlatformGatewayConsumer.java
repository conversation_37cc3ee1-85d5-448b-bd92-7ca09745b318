package com.yy.gameecology.activity.worker.subscriber;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.SvcGatewayCallbackMessage;
import com.yy.gameecology.activity.config.svcsdk.SvcSDKCallback;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SysEvHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * 接收 svc上行消息
 *
 * <AUTHOR>
 * @since 2021/10/17
 */
@ConditionalOnExpression(Const.EXPRESSION_NOT_HISTORY)
@Component
@Slf4j
public class SvcPlatformGatewayConsumer {

    @Autowired
    private SvcSDKCallback svcSDKCallback;

    /**
     * svc platform gateway转发的上行消息
     */
    @KafkaListener(containerFactory = "svcGatewayWxContainerFactory", id = "kafka-svc-gateway-up-msg-wx",
            topics = "${yy.kafka.svc.gateway.wx.consumer.topic}",
            groupId = "${yy.kafka.svc.gateway.wx.consumer.group-id}")
    public void svcGatewayCallback(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        if (SysEvHelper.isDev()) {
            log.info("svc_platform_gateway_callback event:{}", value);
        }
        if (StringUtils.isBlank(value)) {
            return;
        }
        SvcGatewayCallbackMessage event = JSON.parseObject(value, SvcGatewayCallbackMessage.class);
        if (event == null) {
            return;
        }
        svcSDKCallback.onRecvProxyMsg(event);
        log.info("svc_platform_gateway_callback is completed,appid:{},uid:{}",
                event.getAppId(), event.getUid());
    }


}
