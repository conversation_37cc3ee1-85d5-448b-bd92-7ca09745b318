package cn.yy.ent.zhuiya.friendship.bean;

import com.duowan.udb.util.GsonUtil;

public class DelayedEvent {

    private String id;

    private String bucket;

    private long delay;

    private String body;

    private String fallbackUrl;

    private boolean overwrite;

    public DelayedEvent() {}

    private DelayedEvent(Builder builder) {
        this.id = builder.id;
        this.bucket = builder.bucket;
        this.delay = builder.delay;
        this.body = builder.body;
        this.fallbackUrl = builder.fallbackUrl;
        this.overwrite = builder.overwrite;
    }

    public String getId() {
        return this.id;
    }

    public void setId(final String id) {
        this.id = id;
    }

    public String getBucket() {
        return this.bucket;
    }

    public void setBucket(final String bucket) {
        this.bucket = bucket;
    }

    public long getDelay() {
        return this.delay;
    }

    public void setDelay(final long delay) {
        this.delay = delay;
    }

    public String getBody() {
        return this.body;
    }

    public void setBody(final String body) {
        this.body = body;
    }

    public boolean isOverwrite() {
        return overwrite;
    }

    public void setOverwrite(boolean overwrite) {
        this.overwrite = overwrite;
    }

    public String getFallbackUrl() {
        return this.fallbackUrl;
    }

    public void setFallbackUrl(final String fallbackUrl) {
        this.fallbackUrl = fallbackUrl;
    }

    public static class Builder {
        private String id;

        private String bucket;

        private long delay;

        private String body;

        private String fallbackUrl;

        private boolean overwrite;

        private Builder() {}

        public static Builder newBuilder() {
            return new Builder();
        }

        public Builder setId(String id) {
            this.id = id;
            return this;
        }

        public Builder setBucket(String bucket) {
            this.bucket = bucket;
            return this;
        }

        public Builder setDelay(long delay) {
            this.delay = delay;
            return this;
        }

        public Builder setBody(String body) {
            this.body = body;
            return this;
        }

        public Builder setFallbackUrl(final String fallbackUrl) {
            this.fallbackUrl = fallbackUrl;
            return this;
        }

        public Builder setOverwrite(boolean overwrite) {
            this.overwrite = overwrite;
            return this;
        }

        public DelayedEvent build() {
            return new DelayedEvent(this);
        }

    }

    @Override
    public String toString() {
        return GsonUtil.toJson(this);
    }
}
