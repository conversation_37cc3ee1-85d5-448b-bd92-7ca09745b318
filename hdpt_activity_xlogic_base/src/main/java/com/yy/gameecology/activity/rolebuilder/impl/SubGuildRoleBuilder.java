package com.yy.gameecology.activity.rolebuilder.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.rankroleinfo.SubGuildRoleItem;
import com.yy.gameecology.activity.client.thrift.TurnoverContractServiceThriftClient;
import com.yy.gameecology.activity.rolebuilder.RoleBuilder;
import com.yy.gameecology.common.client.WebdbServiceClient;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SpringBeanAwareFactory;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.WebdbChannelInfo;
import com.yy.java.webdb.WebdbSubChannelInfo;
import com.yy.thrift.turnover_contract.TRoomContract;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/9/17 0:31
 * @Modified:
 */
public class SubGuildRoleBuilder implements RoleBuilder<SubGuildRoleItem> {

//    private WebdbServiceClient webdbServiceClient = SpringBeanAwareFactory.getBean(WebdbServiceClient.class);
//    private WebdbThriftClient webdbThriftClient = SpringBeanAwareFactory.getBean(WebdbThriftClient.class);

    private static final SubGuildRoleItem DEFAULT_OBJECT= new SubGuildRoleItem();
    static {
        DEFAULT_OBJECT.setKey("0");
        DEFAULT_OBJECT.setName("神秘厅");
        DEFAULT_OBJECT.setAvatarInfo(Const.IMAGE.DEFAULT_USER_LOGO);
    }

    @Override
    public SubGuildRoleItem getDefaultObject() {
        return DEFAULT_OBJECT;
    }
    @Override
    public SubGuildRoleItem createBankObject() {
        return new SubGuildRoleItem();
    }

    /**
     * 从yy获取信息
     *
     * @param members
     * @return
     */
    @Override
    public Map<String, SubGuildRoleItem> buildRankByYy(Set<String> members) {
        if (CollectionUtils.isEmpty(members)){
            return Maps.newHashMap();
        }
        WebdbServiceClient webdbServiceClient = SpringBeanAwareFactory.getBean(WebdbServiceClient.class);
        TurnoverContractServiceThriftClient turnoverContractServiceThriftClient
                = SpringBeanAwareFactory.getBean(TurnoverContractServiceThriftClient.class);
        Map<String, WebdbSubChannelInfo> channelInfos = webdbServiceClient.batchGetSubChannelInfo(Lists.newArrayList(members));
        //查询顶级频道的asid
        Set<Long> sids = members.stream().map(channel -> channel.split("_")[0])
                .filter(StringUtils::isNotBlank)
                .map(Long::parseLong)
                .collect(Collectors.toSet());
        //查询房管厅签约信息
        Map<Long, TRoomContract> roomContractMap =
                turnoverContractServiceThriftClient.queryRoomContract();
        for (String member : members) {
            long ssid = Convert.toLong(member.split("_")[1]);
            if(roomContractMap.containsKey(ssid) && roomContractMap.get(ssid).getSid() != 0) {
                sids.add(roomContractMap.get(ssid).getSid());
            }
        }
        WebdbThriftClient webdbThriftClient = SpringBeanAwareFactory.getBean(WebdbThriftClient.class);
        Map<Long, WebdbChannelInfo> sidChannelInfos = webdbThriftClient.batchGetChannelInfo(Lists.newArrayList(sids));
        SubGuildRoleItem defaultObject = getDefaultObject();
        return members.stream().map(member -> {
            SubGuildRoleItem rankItem = new SubGuildRoleItem();
            rankItem.setKey(member);

            WebdbSubChannelInfo channelInfo = channelInfos.get(member);
            //通过顶级频道获取asid
            WebdbChannelInfo sidChannelInfo = sidChannelInfos.get(rankItem.getSid());

            //顶级频道的厅是查不到信息的
            if (rankItem.getSid().equals(rankItem.getSsid())) {
                rankItem.setName(sidChannelInfo == null ? defaultObject.getName() : sidChannelInfo.getName());
            } else {
                rankItem.setName(channelInfo == null ? defaultObject.getName() : channelInfo.getName());
            }
            //rankItem.setAvatarInfo(channelInfo.getOrDefault("logo_url", defaultObject.getAvatarInfo()));
            if(roomContractMap.containsKey(rankItem.getSsid()) &&
                    roomContractMap.get(rankItem.getSsid()).getSid() != 0) {
                //使用房管厅返回的签约公会
               sidChannelInfo = sidChannelInfos.get(roomContractMap.get(rankItem.getSsid()).getSid());
            }
            long asid = Optional.ofNullable(sidChannelInfo).map(WebdbChannelInfo::getAsid).filter(StringUtils::isNumeric).map(Long::parseLong).orElse(rankItem.getSid());
            rankItem.setAsid(asid);
            rankItem.setAvatarInfo(sidChannelInfo == null ? defaultObject.getAvatarInfo() : WebdbUtils.getLogo(sidChannelInfo));

            return rankItem;
        }).collect(Collectors.toMap(SubGuildRoleItem::getKey, Function.identity()));

    }

}
