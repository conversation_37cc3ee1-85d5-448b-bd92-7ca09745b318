package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.BroadcastTimerConfig;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class ConfigBroadCastService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private SvcSDKService svcSDKService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private GameecologyDao gameecologyDao;

    public void doBroadcast(long actId) {
        String curTime = commonService.getNowDateTime(actId).format(DateUtil.YYYY_MM_DD_HH_MM);
        BroadcastTimerConfig where = new BroadcastTimerConfig();
        where.setBroadcastTime(curTime);
        where.setStatus(1);
        where.setActId(actId);
        List<BroadcastTimerConfig> configs = gameecologyDao.getBroadcastTimerConfigs(where);
        if(CollectionUtils.isEmpty(configs)){
            log.info("doBroadcast skip@nothing to do now:{} for actId:{}", curTime, actId);
            return;
        }

        int inx = 0;
        for (BroadcastTimerConfig config : configs) {
            inx++;
            int id = config.getId().intValue();
            String ranges = config.getBroadcastRanges();
            log.info("doBroadcast ok@inx:{}/{}, actId:{}, id:{}, curTime:{}, ranges:{}", inx, configs.size(), actId, id, curTime, ranges);
            GameecologyActivity.Act202010_MatchPointNotify.Builder task = GameecologyActivity.Act202010_MatchPointNotify.newBuilder()
                    .setId(id).setActId(actId).setContent(config.getBroadcastTxt());
            GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.kAct202010_MatchPointNotify_VALUE)
                    .setAct202010MatchPointNotify(task).build();
            for (String range : ranges.split(StringUtil.COMMA)) {
                Template tmp = Template.findByValue(Integer.parseInt(range));
                if(tmp == null){
                    log.error("doBroadcast fail@inx:{}/{}, actId:{}, id:{}, curTime:{}, range:{}", inx, configs.size(), actId, id, curTime, range);
                } else {
                    svcSDKService.broadcastTemplate(tmp, msg);
                }
            }
        }
    }

    public void doPwBroadcast(long actId) {
        String curTime = commonService.getNowDateTime(actId).format(DateUtil.YYYY_MM_DD_HH_MM);
        BroadcastTimerConfig where = new BroadcastTimerConfig();
        where.setBroadcastTime(curTime);
        where.setStatus(1);
        where.setActId(actId);
        List<BroadcastTimerConfig> configs = gameecologyDao.getBroadcastTimerConfigs(where);
        if(CollectionUtils.isEmpty(configs)){
            log.info("doPwBroadcast skip@nothing to do now:{} for actId:{}", curTime, actId);
            return;
        }

        int inx = 0;
        for (BroadcastTimerConfig config : configs) {
            inx ++;
            int id = config.getId().intValue();
            String ranges = config.getBroadcastRanges();
            log.info("doPwBroadcast ok@inx:{}/{}, actId:{}, id:{}, curTime:{}, ranges:{}", inx, configs.size(), actId, id, curTime, ranges);
            GameecologyActivity.Act202010_MatchPointNotify.Builder task = GameecologyActivity.Act202010_MatchPointNotify.newBuilder()
                    .setId(id).setActId(actId).setContent(config.getBroadcastTxt());
            GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.kAct202010_MatchPointNotify_VALUE)
                    .setAct202010MatchPointNotify(task).build();
            if("4".equals(ranges)){
                svcSDKService.broadcastAllChanelsInPW(actId, msg);
            } else {
                log.error("doPwBroadcast fail@inx:{}/{}, actId:{}, id:{}, curTime:{}, not pw tmp!", inx, configs.size(), actId, id, curTime);
            }
        }
    }
}
