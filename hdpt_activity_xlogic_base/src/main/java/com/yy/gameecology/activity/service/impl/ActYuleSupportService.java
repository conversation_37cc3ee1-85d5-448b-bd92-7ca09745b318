package com.yy.gameecology.activity.service.impl;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.rankroleinfo.BabyRoleItem;
import com.yy.gameecology.activity.bean.rankroleinfo.RoleItem;
import com.yy.gameecology.activity.bean.rankroleinfo.UserBaseItem;
import com.yy.gameecology.activity.bean.rankroleinfo.UserRoleItem;
import com.yy.gameecology.activity.client.thrift.UserinfoThriftClient;
import com.yy.gameecology.activity.rolebuilder.RoleBuilder;
import com.yy.gameecology.activity.rolebuilder.impl.BabyRoleBuilder;
import com.yy.gameecology.activity.rolebuilder.impl.UserRoleBuilder;
import com.yy.gameecology.activity.service.ActSupportService;
import com.yy.gameecology.activity.service.yule.attention.Attention;
import com.yy.gameecology.activity.service.yule.lpf.Lpfm2YYP;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.client.WebdbUinfoClient;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.BatchUserInfoWithNickExt;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.thrift.act_ext_support.MemberItemInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 娱乐
 *
 * <AUTHOR>
 * @date 2022/4/18 17:36
 **/
@Component
public class ActYuleSupportService implements ActSupportService {
    private static Logger logger = LoggerFactory.getLogger(ActYuleSupportService.class);

    @Autowired
    private WebdbUinfoClient webdbUinfoClient;

    @Autowired
    private Attention attention;

    @Autowired
    private Lpfm2YYP lpfm2YYP;

    private RoleBuilder userRankBuilder = new YuleUserRoleBuilder();
    private RoleBuilder babyRankBuilder = new YuleBabyRoleBuilder();

    @Override
    public Map<String, Map<String, MemberItemInfo>> queryMember(long actId, long rankId, Map<String, List<String>> memberIdMap) {
        return Maps.newHashMap();
    }

    @Override
    public RoleBuilder getUserRankBuilder() {
        return userRankBuilder;
    }

    @Override
    public RoleBuilder getBabyRankBuilder() {
        return babyRankBuilder;
    }

    @Override
    public RoleBuilder getHostRankBuilder() {
        return null;
    }

    @Override
    public RoleBuilder getGuildRankBuilder() {
        return null;
    }

    @Override
    public RoleBuilder getSubGuildRankBuilder() {
        return null;
    }

    @Override
    public RoleBuilder getTeamRankBuilder() {
        return null;
    }

    class YuleUserRoleBuilder extends UserRoleBuilder {
        @Override
        public Map<String, UserRoleItem> buildRankByYy(Set<String> members) {
            if (CollectionUtils.isEmpty(members)) {
                return Maps.newHashMap();
            }

            int hostId = findHostId(members);

            List<Long> uids = members.stream().filter(StringUtil::notEmpty).map(Long::parseLong).collect(Collectors.toList());
            BatchUserInfoWithNickExt batched = webdbUinfoClient.batchGetUserInfoWithNickExt(uids, hostId);

            UserRoleItem defaultObject = getDefaultObject();
            return members.stream().filter(StringUtil::notEmpty).map(member -> {
                WebdbUserInfo userInfo = batched == null ? null : batched.getUserInfoMap().get(member);
                UserRoleItem rankItem = createBankObject();
                rankItem.setUid(Convert.toLong(member));
                rankItem.setYyno(Optional.ofNullable(userInfo).map(WebdbUserInfo::getYyno).map(Long::parseLong).orElse(0L));
                rankItem.setNick(Optional.ofNullable(userInfo).map(WebdbUserInfo::getNick).orElse(defaultObject.getNick()));
                rankItem.setAvatarInfo(getLogo(userInfo, defaultObject.getAvatarInfo()));
                return rankItem;
            }).collect(Collectors.toMap(UserBaseItem::getKey, Function.identity()));
        }
    }

    private String getLogo(WebdbUserInfo userInfo, String defaultAvatar) {
        if (userInfo == null) {
            return defaultAvatar;
        }

        String logo = userInfo.getLogoHd144();
        if (StringUtil.isNotBlank(logo)) {
            return logo;
        }

        logo = userInfo.getLogoHd100();
        if (StringUtil.isNotBlank(logo)) {
            return logo;
        }

        logo = WebdbUtils.getLogo(userInfo);
        if (StringUtil.isNotBlank(logo)) {
            return logo;
        }

        return defaultAvatar;
    }

    class YuleBabyRoleBuilder extends BabyRoleBuilder {
        public YuleBabyRoleBuilder() {
            super(Template.yule);
        }

        @Override
        public Map<String, BabyRoleItem> buildRankByYy(Set<String> members) {
            if (CollectionUtils.isEmpty(members)) {
                return Maps.newHashMap();
            }
            int hostId = findHostId(members);
            List<Long> uids = members.stream().filter(StringUtil::notEmpty).map(Long::parseLong).collect(Collectors.toList());
            BatchUserInfoWithNickExt batched = webdbUinfoClient.batchGetUserInfoWithNickExt(uids, hostId);
            BabyRoleItem defaultObject = getDefaultObject();
            return members.stream().filter(StringUtil::notEmpty).map(member -> {
                WebdbUserInfo userInfo = batched == null ? null : batched.getUserInfoMap().get(member);
                BabyRoleItem rankItem = createBankObject();
                rankItem.setUid(Convert.toLong(member));
                rankItem.setYyno(Optional.ofNullable(userInfo).map(WebdbUserInfo::getYyno).map(Long::parseLong).orElse(0L));
                rankItem.setNick(Optional.ofNullable(userInfo).map(WebdbUserInfo::getNick).orElse(defaultObject.getNick()));
                rankItem.setAvatarInfo(getLogo(userInfo, defaultObject.getAvatarInfo()));
                return rankItem;
            }).collect(Collectors.toMap(UserBaseItem::getKey, Function.identity()));
        }

        @Override
        public Map<String, BabyRoleItem> addExtraInfo(long actId, long roleId, long rankId, long uid, Map<String, BabyRoleItem> roleItemMap) {
            int hostId = 1;
            RoleItem babyRoleItem = roleItemMap.remove("hostId");
            if (babyRoleItem != null) {
                hostId = Convert.toInt(babyRoleItem.getKey().replace("host=", ""), hostId);
                if (hostId == 0) {
                    logger.info("addExtraInfo host = 0,{}", babyRoleItem.getKey());
                    hostId = 1;
                }
            }

            //填充关注主播信息
            List<Long> noSubscribeUids = roleItemMap.values().stream()
                    .filter(item -> item.getSubscribe() == null)
                    .map(BabyRoleItem::getUid).collect(Collectors.toList());
            if (!noSubscribeUids.isEmpty() && uid > 0) {
                List<Long> attentionAnchorIds = attention.attentionList(uid);
                for (Long babyUid : noSubscribeUids) {
                    BabyRoleItem babyRankRoleItem = roleItemMap.get(babyUid + "");
                    babyRankRoleItem.setSubscribe(attentionAnchorIds.contains(babyUid));
                }
            }

            //填充开麦频道
            List<Long> noOnMicUids = roleItemMap.values().stream()
                    .filter(item -> item.getSid() == null)
                    .map(BabyRoleItem::getUid).collect(Collectors.toList());

            if (!noOnMicUids.isEmpty()) {
                Map<String, Map<String, String>> respMap = lpfm2YYP.getPublishInfoByUids(hostId, noSubscribeUids);
                for (Long babyUid : noOnMicUids) {
                    Map<String, String> map = respMap.get(babyUid + "");
                    if (map != null) {
                        BabyRoleItem babyRankRoleItem = roleItemMap.get(babyUid + "");
                        babyRankRoleItem.setSid(Convert.toLong(map.get("sid"), 0));
                        babyRankRoleItem.setSsid(Convert.toLong(map.get("ssid"), 0));
                    }
                }
            }

            return roleItemMap;
        }
    }

    public Integer findHostId(Set<String> members) {
        String hostIdStr = "";
        for (String member : members) {
            if (member.startsWith("host=")) {
                hostIdStr = member;
            }
        }
        if (StringUtil.isEmpty(hostIdStr)) {
            return 1;
        }
        members.remove(hostIdStr);

        return Convert.toInt(hostIdStr.replace("host=", ""), 1);
    }
}
