package com.yy.gameecology.activity.bean.rank;

import com.yy.gameecology.common.utils.Convert;


public class TeamRankItem  extends PkRankItemBase {
    private Long id;
    private Long asid;
    private String name;
    private String avatarInfo;


    @Override
    public void setKey(String key) {
        setId(Convert.toLong(key));
    }

    @Override
    public String getKey() {
        return getId()+"";
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAvatarInfo() {
        return avatarInfo;
    }

    public void setAvatarInfo(String avatarInfo) {
        this.avatarInfo = avatarInfo;
    }

    public Long getAsid() {
        return asid;
    }

    public void setAsid(Long asid) {
        this.asid = asid;
    }
}
