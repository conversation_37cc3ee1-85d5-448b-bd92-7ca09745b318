package com.yy.gameecology.activity.bean.acttask;

import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-02-23 14:17
 **/
@Data
public class TaskShowItemVo {
    /**
     * 过任务道具名称
     */
    private String item;

    /**
     * 过任务道具id
     */
    private String itemId;

    /**
     * 已完成数量
     */
    private long completedCount;

    /**
     * 到当前阶段任务总数量，会递增
     */
    private long taskTotalCount;

    /**
     * 所有关卡任务总数量
     */
    private long allTaskTotalCount;

    /**
     * 任务名称 过关任务
     */
    private String taskName;

    /**
     * 任务扩展
     */
    private String taskExtJson;
}
