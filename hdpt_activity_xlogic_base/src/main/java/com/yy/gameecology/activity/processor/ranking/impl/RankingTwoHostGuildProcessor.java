package com.yy.gameecology.activity.processor.ranking.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.processor.ranking.BaseRankingProcessor;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import com.yy.thrift.hdztranking.RankingType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class RankingTwoHostGuildProcessor extends BaseRankingProcessor {
    private static Logger log = LoggerFactory.getLogger(RankingTwoHostGuildProcessor.class);

    @Override
    protected int getProcessorType() {
        return RankingType.TWO_HOST_GUILD.getValue();
    }

    @Override
    public List<Object> getRankInfo(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, Map<String, String> ext) {
       // throw new SuperException("未实现的榜单类型:" + RankingType.TWO_HOST_GUILD, 9999);
        log.warn("getRankInfo 未实现的榜单类型:{} rankReq:{}", RankingType.TWO_HOST_GUILD, JSON.toJSONString(rankReq));
        return Lists.newArrayList();
    }
}
