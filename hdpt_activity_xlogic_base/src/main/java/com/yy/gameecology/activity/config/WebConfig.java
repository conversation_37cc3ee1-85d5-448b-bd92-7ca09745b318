package com.yy.gameecology.activity.config;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.alibaba.fastjson.support.spring.JSONPResponseBodyAdvice;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> 2019/9/4
 */

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        FastJsonHttpMessageConverter messageConverter = new FastJsonHttpMessageConverter();
        //null的处理策略
        List<SerializerFeature> nullSerializerFeatures = Lists.newArrayList(SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullBooleanAsFalse,
                SerializerFeature.WriteNullListAsEmpty,
                SerializerFeature.WriteNullNumberAsZero,
                SerializerFeature.WriteNullStringAsEmpty
                );

        FastJsonConfig fastJsonConfig = messageConverter.getFastJsonConfig();
        List<SerializerFeature> serializerFeatureList = Lists.newArrayList();

        serializerFeatureList.addAll(Arrays.asList(fastJsonConfig.getSerializerFeatures()));
        serializerFeatureList.add(SerializerFeature.WriteMapNullValue);
        //取消循环引用的限制
        serializerFeatureList.add(SerializerFeature.DisableCircularReferenceDetect);
        serializerFeatureList.addAll(nullSerializerFeatures);

        SerializerFeature[] serializerFeatures= new SerializerFeature[serializerFeatureList.size()];
        serializerFeatureList.toArray(serializerFeatures);
        fastJsonConfig.setSerializerFeatures(serializerFeatures);

        converters.add(0, new StringHttpMessageConverter());
        converters.add(1, messageConverter);
    }

    @Bean
    public JSONPResponseBodyAdvice jsonpResponseBodyAdvice(){
        return new JSONPResponseBodyAdvice();
    }

    @Autowired
    private ErrorInterceptor errorInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        InterceptorRegistration registration = registry.addInterceptor(errorInterceptor);
        //所有路径都被拦截
        registration.addPathPatterns("/**");
    }
}

