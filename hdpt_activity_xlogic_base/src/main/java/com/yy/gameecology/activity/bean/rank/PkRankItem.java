package com.yy.gameecology.activity.bean.rank;

/**
 * @Author: CXZ
 * @Desciption: pk对对象
 * @Date: 2020/9/19 21:39
 * @Modified:
 */
public class PkRankItem extends RankItemBase {
    private RankValueItemBase item1;
    private RankValueItemBase item2;

    private Long value;


    public RankValueItemBase getItem1() {
        return item1;
    }

    public void setItem1(RankValueItemBase item1) {
        this.item1 = item1;
    }

    public RankValueItemBase getItem2() {
        return item2;
    }

    public void setItem2(RankValueItemBase item2) {
        this.item2 = item2;
    }

    public Long getValue() {
        return value;
    }

    @Override
    public void setValue(Long value) {
        this.value = value;
    }
}
