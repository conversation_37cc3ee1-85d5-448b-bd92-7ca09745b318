/*
 * @(#)Transaction.java 2017-12-23
 *
 * Copy Right@ 欢聚时代
 *
 * 代码生成: transaction 表的数据模型类  Transaction
 */
package com.yy.gameecology.activity.bean.gamebaby;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @功能说明
 * <pre>
 * transaction 表的数据模型类  Transaction
 * </pre>
 *
 * @版本更新
 * <pre>
 * 修改版本: 1.0.0 / 2017-12-23 / cgc
 * 修改说明：形成初始版本
 * 复审人：
 * </pre>
 */
@SuppressWarnings("serial")
public class Transaction implements java.io.Serializable {
	private static final Logger log = LoggerFactory.getLogger(Transaction.class);

	private Long id;
	private Long creditUid;
	private Long debitUid;
	private String status;
	private Date createTime;
	private String creditSid;
	private String creditSsid;
	private BigDecimal creditAmount;
	private String debitSid;
	private String debitSsid;
	private BigDecimal debitAmount;
	private Long channelId;
	private Long channelSsid;
	private String userIp;
	/**
	 * '00' mimi ios
	 * '01' mimi 安卓
	 * '02'安卓,约战app  充值红贝来源(先充值Y币，再买红贝)
	 * '03'约战安卓
	 * '04' 约战IOS
	 * '05' 追玩安卓
	 * '06' 追玩IOS
	 * '07' yomi安卓
	 * '08' yomi-ios
	 * '12' pc
	 *
	 */
	private String mac;
	private String summary;
	private Date lastUpdateTime;
	private Long busiType;
	private String extdata;
	private Long signId;

	public void setId(Long id){
		this.id = id;
	}

	public Long getId(){
		return id;
	}

	public void setCreditUid(Long creditUid){
		this.creditUid = creditUid;
	}

	public Long getCreditUid(){
		return creditUid;
	}

	public void setDebitUid(Long debitUid){
		this.debitUid = debitUid;
	}

	public Long getDebitUid(){
		return debitUid;
	}

	public void setStatus(String status){
		this.status = status;
	}

	public String getStatus(){
		return status;
	}

	public void setCreateTime(Date createTime){
		this.createTime = createTime;
	}

	public Date getCreateTime(){
		return createTime;
	}

	public void setCreditSid(String creditSid){
		this.creditSid = creditSid;
	}

	public String getCreditSid(){
		return creditSid;
	}

	public void setCreditSsid(String creditSsid){
		this.creditSsid = creditSsid;
	}

	public String getCreditSsid(){
		return creditSsid;
	}

	public void setCreditAmount(BigDecimal creditAmount){
		this.creditAmount = creditAmount;
	}

	public BigDecimal getCreditAmount(){
		return creditAmount;
	}

	public void setDebitSid(String debitSid){
		this.debitSid = debitSid;
	}

	public String getDebitSid(){
		return debitSid;
	}

	public void setDebitSsid(String debitSsid){
		this.debitSsid = debitSsid;
	}

	public String getDebitSsid(){
		return debitSsid;
	}

	public void setDebitAmount(BigDecimal debitAmount){
		this.debitAmount = debitAmount;
	}

	public BigDecimal getDebitAmount(){
		return debitAmount;
	}

	public void setChannelId(Long channelId){
		this.channelId = channelId;
	}

	public Long getChannelId(){
		return channelId;
	}

	public void setChannelSsid(Long channelSsid){
		this.channelSsid = channelSsid;
	}

	public Long getChannelSsid(){
		return channelSsid;
	}

	public void setUserIp(String userIp){
		this.userIp = userIp;
	}

	public String getUserIp(){
		return userIp;
	}

	public void setMac(String mac){
		this.mac = mac;
	}

	public String getMac(){
		return mac;
	}

	public void setSummary(String summary){
		this.summary = summary;
	}

	public String getSummary(){
		return summary;
	}

	public void setLastUpdateTime(Date lastUpdateTime){
		this.lastUpdateTime = lastUpdateTime;
	}

	public Date getLastUpdateTime(){
		return lastUpdateTime;
	}

	public void setBusiType(Long busiType){
		this.busiType = busiType;
	}

	public Long getBusiType(){
		return busiType;
	}

	public void setExtdata(String extdata){
		this.extdata = extdata;
	}

	public String getExtdata(){
		return extdata;
	}

	public void setSignId(Long signId){
		this.signId = signId;
	}

	public Long getSignId(){
		return signId;
	}

	@Override
	public String toString() {
	    return JSON.toJSONString(this);
	}
}
