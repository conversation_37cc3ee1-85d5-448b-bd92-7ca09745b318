package com.yy.gameecology.activity.bean;

import java.util.UUID;

/**
 * @Author: CXZ
 * @Desciption: 用户直播间停留事件
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
public class UserRemainEvent {
    private long actId;
    private long uid;
    private long sid;
    private long ssid;
    private long busiId;
    /**
     * 停留时间，单位秒
     */
    private long remainTime;
    private String seq = UUID.randomUUID().toString();

    public UserRemainEvent() {
    }

    public UserRemainEvent(long actId, long uid, long sid, long ssid, long busiId, long remainTime) {
        this.actId = actId;
        this.uid = uid;
        this.sid = sid;
        this.ssid = ssid;
        this.busiId = busiId;
        this.remainTime = remainTime;
    }

    public long getActId() {
        return actId;
    }

    public void setActId(long actId) {
        this.actId = actId;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public long getSid() {
        return sid;
    }

    public void setSid(long sid) {
        this.sid = sid;
    }

    public long getSsid() {
        return ssid;
    }

    public void setSsid(long ssid) {
        this.ssid = ssid;
    }

    public long getBusiId() {
        return busiId;
    }

    public void setBusiId(long busiId) {
        this.busiId = busiId;
    }

    public long getRemainTime() {
        return remainTime;
    }

    public void setRemainTime(long remainTime) {
        this.remainTime = remainTime;
    }

    public String getSeq() {
        return seq;
    }
}
