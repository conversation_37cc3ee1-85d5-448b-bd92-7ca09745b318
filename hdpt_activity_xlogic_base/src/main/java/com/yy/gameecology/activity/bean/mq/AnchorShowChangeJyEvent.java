package com.yy.gameecology.activity.bean.mq;


/**
 * desc:交友开播变更事件
 *
 * @createBy 曾文帜
 * @create 2021-01-24 17:32
 **/
public class AnchorShowChangeJyEvent extends JyCommHeader {


    private String seqId;
    private String event;
    private Long uid;
    private Long compereUid;
    private Long sid;
    private Long ssid;
    /**
     * //1 -主持 2 -嘉宾 3 -候选嘉宾 4 -游客
     *
     * 嘉宾，就是上座，如果是普通用户，应该就是下座
     */
    private Long role;
    private Long playMode;
    private Long platform;
    private Long timestamp;
    private Long position;

    public String getSeqId() {
        return seqId;
    }

    public void setSeqId(String seqId) {
        this.seqId = seqId;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getCompereUid() {
        return compereUid;
    }

    public void setCompereUid(Long compereUid) {
        this.compereUid = compereUid;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getSsid() {
        return ssid;
    }

    public void setSsid(Long ssid) {
        this.ssid = ssid;
    }

    public Long getRole() {
        return role;
    }

    public void setRole(Long role) {
        this.role = role;
    }

    public Long getPlayMode() {
        return playMode;
    }

    public void setPlayMode(Long playMode) {
        this.playMode = playMode;
    }

    public Long getPlatform() {
        return platform;
    }

    public void setPlatform(Long platform) {
        this.platform = platform;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public Long getPosition() {
        return position;
    }

    public void setPosition(Long position) {
        this.position = position;
    }

}
