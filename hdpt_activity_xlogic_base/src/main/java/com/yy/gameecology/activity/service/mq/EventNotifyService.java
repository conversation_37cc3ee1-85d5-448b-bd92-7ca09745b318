package com.yy.gameecology.activity.service.mq;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.mq.RankPhaseTimeEndEvent;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/4/21 14:19
 **/
@Service
public class EventNotifyService {
    private Logger log = LoggerFactory.getLogger(EventNotifyService.class);

    @Value("${kafka.ge.ranking.events.outer.topic}")
    private String rankingEventsTopic;

    @Autowired
    @Qualifier("hdztWxKafkaTemplate")
    private KafkaTemplate<String, String> hdztWxKafkaTemplate;

    @Autowired
    @Qualifier("hdztSzKafkaTemplate")
    private KafkaTemplate<String, String> hdztSzKafkaTemplate;

    public boolean sendPhaseEndEvent(RankPhaseTimeEndEvent event) {
        Clock clock = new Clock();
        long times = -1;
        try {
            String message = JSON.toJSONString(event);
            try {
                hdztWxKafkaTemplate.send(rankingEventsTopic, message).get(10, TimeUnit.SECONDS);

                //TODO 配合娱乐测试环境测试 kafka双消费，待删除
                if(SysEvHelper.isDev()){
                    log.info("test dup sent kafka");
                    hdztSzKafkaTemplate.send(rankingEventsTopic, message).get(10, TimeUnit.SECONDS);
                }


                log.info("sendPhaseEndEvent wx done@message:{} {}", message, clock.tag());
                return true;
            } catch (Exception eWx) {
                log.error("sendPhaseEndEvent wx exception@seq:{}, err:{} {}", event.getSeq(), eWx.getMessage(), clock.tag(), eWx);
                try {
                    hdztSzKafkaTemplate.send(rankingEventsTopic, message).get(10, TimeUnit.SECONDS);
                    log.info("sendPhaseEndEvent sz done@message:{} {}", message, clock.tag());
                    return true;
                } catch (Exception eSz) {
                    log.error("sendMessage sz exception@message:{}, err:{} {}", message, eSz.getMessage(), clock.tag(), eSz);

                    // TODO 失败处理

                    return false;
                }
            }
        } catch (Throwable t) {
            log.error("sendPhaseEndEvent exception@event:{}, err:{} {}", event, t.getMessage(), clock.tag(), t);
        }

        return false;
    }
}
