package com.yy.gameecology.activity.client.yrpc;

import com.yy.ent.mobile.baymax.domain.pb.Baymax;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class BaymaxServiceClient {

    @Value("${s2s.name:hdzk_activity}")
    private String service;

    @Reference(protocol = "yrpc", owner = "baymax", registry = {"yrpc-reg"}, retries = 5)
    private BaymaxService proxy;

    public int add(String biz, String val) {
        Baymax.AddReq req = Baymax.AddReq.newBuilder()
                .setBiz(biz)
                .setVal(val)
                .setService(service)
                .build();

        try {
            Baymax.AddResp resp = proxy.add(req);
            if (resp == null) {
                log.error("baymax add whitelist resp is null biz:{} val:{}", biz, val);
                return 500;
            }

            if (resp.getCode() != 0) {
                log.error("baymax add whitelist fail resp:{}", resp);
            }

            return resp.getCode();
        } catch (Exception e) {
            log.error("baymax add whitelist fail:", e);
        }


        return 500;
    }

    public int batchAdd(String biz, List<String> val) {
        Baymax.BatchAddReq req = Baymax.BatchAddReq.newBuilder()
                .setBiz(biz)
                .addAllVal(val)
                .setService(service)
                .build();

        try {
            Baymax.AddResp resp = proxy.batchAdd(req);
            if (resp == null) {
                log.error("baymax batchAdd whitelist resp is null biz:{} val:{}", biz, val);
                return 500;
            }

            if (resp.getCode() != 0) {
                log.error("baymax batchAdd whitelist fail resp:{}", resp);
            }

            return resp.getCode();
        } catch (Exception e) {
            log.error("baymax batchAdd whitelist fail:", e);
        }


        return 500;
    }
}
