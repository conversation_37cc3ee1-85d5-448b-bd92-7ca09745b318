package com.yy.gameecology.activity.client.yrpc;

import com.yy.protocol.pb.shop.ZhuiyaShopServer;
import lombok.experimental.Delegate;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2024/3/22
 */
@Component
public class ZhuiyaShopServerClient implements ZhuiyaShopServerYrpc{

    @Reference(protocol = "yrpc", owner = "zhuiya_shop_yrpc", registry = "yrpc-reg")
    private ZhuiyaShopServerYrpc zhuiyaShopServerYrpc;

    @Reference(protocol = "yrpc", owner = "zhuiya_shop_yrpc", registry = "yrpc-reg", retries = 2, cluster = "failover")
    private ZhuiyaShopServerYrpc zhuiyaShopServerReadYrpc;

    @Override
    public ZhuiyaShopServer.ListShopItemResp listShopItem(ZhuiyaShopServer.ListShopItemReq req) {
        return zhuiyaShopServerReadYrpc.listShopItem(req);
    }

    @Override
    public ZhuiyaShopServer.BuyShopItemResp buyShopItem(ZhuiyaShopServer.BuyShopItemReq req) {
        return zhuiyaShopServerYrpc.buyShopItem(req);
    }

    @Override
    public ZhuiyaShopServer.BuyHistoryResp buyHistory(ZhuiyaShopServer.BuyHistoryReq req) {
        return zhuiyaShopServerReadYrpc.buyHistory(req);
    }
}
