package com.yy.gameecology.activity.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 定时器注解扩展信息
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ScheduledExt {
    /**
     * 历史环境是否可以运行，默认不可以运行
     **/
    boolean historyRun() default false;

}
