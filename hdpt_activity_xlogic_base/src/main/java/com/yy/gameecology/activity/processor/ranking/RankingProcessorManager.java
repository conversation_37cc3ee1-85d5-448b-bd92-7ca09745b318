package com.yy.gameecology.activity.processor.ranking;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.common.utils.Convert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

public class RankingProcessorManager {
    // 保存所有处理器， key = ${actId}_${rankId}_${rankingType}， 若 actId、rankId 为 null 则 # 代替
    private static Map<String, RankingProcessor> rankingTypeProcessors = Maps.newHashMap();

    /**
     * 注册榜单类型处理器，禁止覆盖
     */
    public static void register(Long actId, List<Long> rankIds, Integer rankingType, RankingProcessor rankingProcessor) {
        if(!CollectionUtils.isEmpty(rankIds) && actId == null) {
            throw new RuntimeException("榜单ID非空时 活动ID不可空:" + Convert.toString(rankIds));
        }

        List<String> keys = makeProcessorKeys(actId, rankIds, rankingType);
        for(String key : keys) {
            RankingProcessor previousRankingProcessor = rankingTypeProcessors.put(key, rankingProcessor);
            if (previousRankingProcessor != null) {
                throw new RuntimeException("存在相同处理器标记：" + key + "，请保证处理逻辑的唯一性。");
            }
        }
    }

    /**
     * 提取榜单处理器，提取优先顺序为：  榜单级别 -> 活动 -> 通用
     */
    public static RankingProcessor getRankingProcessor(Long actId, Long rankId, Integer rankingType) {
        // 尝试取榜单级别的处理器
        String rankLevelKey = makeProcessorKey(actId, rankId, rankingType);
        RankingProcessor processor = rankingTypeProcessors.get(rankLevelKey);
        if(processor != null) {
            return processor;
        }

        // 尝试取活动级别的处理器
        String actLevelKey = makeProcessorKey(actId, null, rankingType);
        processor = rankingTypeProcessors.get(actLevelKey);
        if(processor != null) {
            return processor;
        }

        // 最后取通用级别的处理器
        String commLevelKey = makeProcessorKey(null, null, rankingType);
        processor = rankingTypeProcessors.get(commLevelKey);
        return processor;
    }

    // 组装处理器的 Map key
    private static List<String> makeProcessorKeys(Long actId, List<Long> rankIds, Integer rankingType) {
        List<String> keys = Lists.newArrayList();
        String actIdSubKey = actId == null ? "#" : actId.toString();
        if(CollectionUtils.isEmpty(rankIds)) {
            keys.add(String.format("%s_%s_%s", actIdSubKey, "#", rankingType));
        } else {
            for(Long rankIdSubKey : rankIds) {
                keys.add(String.format("%s_%s_%s", actIdSubKey, rankIdSubKey, rankingType));
            }
        }
        return keys;
    }

    // 组装处理器的 Map key
    private static String makeProcessorKey(Long actId, Long rankId, Integer rankingType) {
        String actIdSubKey = actId == null ? "#" : actId.toString();
        String rankIdSubKey = rankId == null ? "#" : rankId.toString();
        return String.format("%s_%s_%s", actIdSubKey, rankIdSubKey, rankingType);
    }
}
