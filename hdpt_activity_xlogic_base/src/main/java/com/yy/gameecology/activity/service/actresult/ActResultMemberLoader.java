package com.yy.gameecology.activity.service.actresult;

import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.WebdbUserInfo;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

public interface ActResultMemberLoader {
    /**
     * 读取荣耀殿堂成员基础信息，例如昵称、头像等
     */
    Map<String, Map<String, MemberInfo>> loadMemberInfo(long actId, long type, long roleType, List<String> memberIds);

    default MemberInfo toMemberInfo(WebdbUserInfo userInfo) {
        MemberInfo memberInfo = new MemberInfo();
        String nick = userInfo == null ? "" : userInfo.getNick();
        memberInfo.setName(nick);
        String avatar = WebdbUtils.getLogo(userInfo);
        String hdlogo = userInfo == null ? StringUtils.EMPTY : userInfo.getLogohd();
        memberInfo.setLogo(StringUtil.isBlank(hdlogo) ? avatar : hdlogo);
        memberInfo.setHdLogo(hdlogo);
        return memberInfo;
    }
}
