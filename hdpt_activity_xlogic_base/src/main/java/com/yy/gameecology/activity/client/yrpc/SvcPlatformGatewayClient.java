package com.yy.gameecology.activity.client.yrpc;

import com.yy.thrift.svc_gateway.SvcPlatformGateway;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.rpc.ProtoHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/10/11
 */
@Component
public class SvcPlatformGatewayClient {

    public static final int SUCCESS_CODE = 0;
    @Value("${svcsdk.appId}")
    private String setid;

    /**
     * 使用set
     **/
    @Reference(protocol = "nythrift", owner = "svc_platform_gateway", lbrouters = "set", loadbalance = "smart", registry = {"yrpc-reg"}, lazy = true)
    private SvcPlatformGateway.Iface proxy;

    public SvcPlatformGateway.Iface getProxy() {
        ProtoHelper.setLbRouterSetid(setid);
        return proxy;
    }

}
