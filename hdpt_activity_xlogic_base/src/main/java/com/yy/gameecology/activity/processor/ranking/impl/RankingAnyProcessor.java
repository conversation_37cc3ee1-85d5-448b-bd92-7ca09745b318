package com.yy.gameecology.activity.processor.ranking.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.RankItemAny;
import com.yy.gameecology.activity.bean.hdzt.RankMemberInfo;
import com.yy.gameecology.activity.bean.rank.*;
import com.yy.gameecology.activity.bean.rankroleinfo.RoleItem;
import com.yy.gameecology.activity.processor.ranking.BaseRankingProcessor;
import com.yy.gameecology.activity.service.EnrollmentService;
import com.yy.gameecology.activity.service.HdztRankGenRoleService;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.NickExt;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.hdztranking.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class RankingAnyProcessor extends BaseRankingProcessor {
    private static Logger log = LoggerFactory.getLogger(RankingAnyProcessor.class);

    @Autowired
    private EnrollmentService enrollmentService;

    @Autowired
    private HdztRankGenRoleService hdztRankGenRoleService;

    @Override
    protected int getProcessorType() {
        return RankingType.ANY.getValue();
    }

    @Override
    public List<Object> getRankInfo(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, Map<String, String> ext) {
        if (CollectionUtils.isEmpty(ranks)) {
            return Lists.newArrayList();
        }
        List<RankItemAny> rankItems = genRankBaseInfo(rankReq.getActId(), rankReq.getRankId(), ranks, rankReq.getShowZeroItem(), rankingInfo, rankReq.getPointedMember());

        // 这里返回了用户信息
        Map<String, Map<String, RoleItem>> rankRoleInfoMaps = hdztRankGenRoleService.getRankRoleInfo(rankReq, rankingInfo, rankItems);

        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(rankItems.size() * 2);

        List<Object> list = new ArrayList<>(rankItems.size());

        for (RankItemAny rankItem : rankItems) {
            List<RankItemBase> record = getOneRecord(rankItem, rankRoleInfoMaps);
            //目前只有cp和单个，后面可以优化
            if (record.size() == 2) {
                RankItemUserAnchor cpRankItem = new RankItemUserAnchor();
                BabyRankItem babyItem;
                UserRankItem userItem;
                if (record.get(0) instanceof BabyRankItem) {
                    babyItem = (BabyRankItem) record.get(0);
                    userItem = (UserRankItem) record.get(1);
                } else {
                    babyItem = (BabyRankItem) record.get(1);
                    userItem = (UserRankItem) record.get(0);
                }

                String anchorNickExt = MapUtils.getString(babyItem.getViewExt(), "nickExt");
                if (StringUtils.startsWith(anchorNickExt, StringUtil.OPEN_BRACE)) {
                    NickExt nickExt = JSON.parseObject(anchorNickExt, NickExt.class);
                    multiNickUsers.putAll(nickExt.getUsers());
                    babyItem.getViewExt().remove("nickExt");
                }

                String userNickExt = MapUtils.getString(userItem.getViewExt(), "nickExt");
                if (StringUtils.startsWith(userNickExt, StringUtil.OPEN_BRACE)) {
                    NickExt nickExt = JSON.parseObject(userNickExt, NickExt.class);
                    multiNickUsers.putAll(nickExt.getUsers());
                    userItem.getViewExt().remove("nickExt");
                }

                cpRankItem.setBabyRankItem(babyItem);
                cpRankItem.setUserRankItem(userItem);
                cpRankItem.setRank(rankItem.getRank());
                cpRankItem.setItemDesc(rankItem.getItemDesc());
                cpRankItem.setKey(rankItem.getRawRankMembers());
                cpRankItem.setScore(rankItem.getScore());
                list.add(cpRankItem);
            } else {
                RankItemBase rankItemBase = record.get(0);
                String baseNickExt = MapUtils.getString(rankItemBase.getViewExt(), "nickExt");
                if (StringUtils.startsWith(baseNickExt, StringUtil.OPEN_BRACE)) {
                    NickExt nickExt = JSON.parseObject(baseNickExt, NickExt.class);
                    multiNickUsers.putAll(nickExt.getUsers());
                    rankItemBase.getViewExt().remove("nickExt");
                }
                list.add(rankItemBase);
            }
        }

        if (!multiNickUsers.isEmpty()) {
            ext.put(RankExtParaKey.OUT_MULTI_NICK_USERS, JSON.toJSONString(multiNickUsers));
        }

        return list;
    }


    /**
     * 构建榜单最基本信息 成员/排名/分数
     */
    public List<RankItemAny> genRankBaseInfo(Long actId, Long rankId, List<Rank> ranks, boolean showZeroItem, RankingInfo rankingInfo, String pointMember) {
        List<RankItemAny> rankItemAnies = Lists.newArrayList();
        List<List<RoleDetail>> roleDetailLists = rankingInfo.getRoleItemConfig().getRoles();
        Map<Long, RoleDetail> roleDetailMap = toRoleDetailMap(roleDetailLists);

        for (int index = 0; index < ranks.size(); index++) {
            Rank rank = ranks.get(index);
            // 先做控制、数据检查
            if (!showZeroItem && Convert.toLong(rank.getScore(), 0) == 0 && !rank.getMember().equals(pointMember)) {
                continue;
            }
            String[] members = parseMembers(actId, rankId, roleDetailLists.get(0).size(), rank.getMember());

            // 通过报名信息，定位角色组
            List<RoleDetail> roleDetailList = findRoleDetailList(actId, members, roleDetailLists, roleDetailMap);

            // 处理复合成员的每一个子成员
            List<RankMemberInfo> rankMemberInfos = Lists.newArrayList();
            for (int i = 0; i < members.length; i++) {
                String memberId = members[i];
                RoleDetail roleDetail = roleDetailList.get(i);
                rankMemberInfos.add(RankMemberInfo.toRankMemberInfo(memberId, roleDetail));
            }

            // 组装成员信息
            RankItemAny rankItemAny = new RankItemAny();
            rankItemAny.setRawRankMembers(rank.getMember());
            rankItemAny.setRankMemberInfos(rankMemberInfos);
            rankItemAny.setRank(rank.getRank());
            rankItemAny.setScore(rank.getScore());
            rankItemAny.setItemDesc(rank.getItemDesc());
            rankItemAnies.add(rankItemAny);
        }

        return rankItemAnies;
    }

    /**
     * 解析成员
     */
    private String[] parseMembers(Long actId, Long rankId, int memberNum, String rankMembers) {
        String[] members = rankMembers.split("\\|");
        if (members.length != memberNum) {
            String errMsg = String.format("CP榜元素个数和配置不一致，actId:%s,rankId:%s,member:%s", actId, rankId, rankMembers);
            log.error("genRankBaseInfo error@{}", errMsg);
            throw new RuntimeException(errMsg);
        }
        return members;
    }


    /**
     * 将两层 list 的 RoleDetail 转成 一个 map
     */
    public static Map<Long, RoleDetail> toRoleDetailMap(List<List<RoleDetail>> roleDetailLists) {
        Map<Long, RoleDetail> roleDetailMap = Maps.newHashMap();
        for (List<RoleDetail> roleDetailList : roleDetailLists) {
            for (RoleDetail roleDetail : roleDetailList) {
                roleDetailMap.put(roleDetail.getRoleId(), roleDetail);
            }
        }
        return roleDetailMap;
    }

    /**
     * 根据报名信息定位计榜成员的角色组配置
     *
     * @param actId           - 活动ID
     * @param members         - 计榜的各成员，相同成员类型下只能有一个在 entryMap 中指定类型的榜单下报名（sid/uid 可能有相同的值！）
     * @param roleDetailLists - 保留了角色分组顺序的 计榜角色配置信息
     * @param roleDetailMap   - 角色详细信息Map（从 roleDetailLists 中转换得到的）
     * @return
     */
    private List<RoleDetail> findRoleDetailList(long actId, String[] members, List<List<RoleDetail>> roleDetailLists, Map<Long, RoleDetail> roleDetailMap) {
        for (int i = 0; i < members.length; i++) {
            List<EnrollmentInfo> enrollmentInfos = enrollmentService.getEntryConfigInfo(actId, members[i]);

            if (CollectionUtils.isEmpty(enrollmentInfos)) {
                continue;
            }

            for (EnrollmentInfo enrollmentInfo : enrollmentInfos) {
                long destRoleId = enrollmentInfo.getDestRoleId();
                RoleDetail roleDetail = roleDetailMap.get(destRoleId);
                if (roleDetail != null) {
                    List<RoleDetail> roleDetailList = findRoleDetailList(roleDetail, roleDetailLists);
                    RoleDetail samePosRoleDetail = roleDetailList.get(i);
                    // 找到匹配的就立即返回（不考虑后续匹配情况，只要报名成员在一个混合角色组配置中只出现一次，这个结果就是精确可靠的)
                    if (samePosRoleDetail.getRoleId() == destRoleId) {
                        return roleDetailList;
                    }
                }
            }
        }

        // members下所有成员都没有报名信息 或 其他情况没有匹配到，则返回第一个角色组配置
        return roleDetailLists.get(0);
    }

    /**
     * 定位 roleDetail 在两层 list 的 RoleDetail 中所处位置对象
     * 本函数能工作依赖于 roleDetail 确实是 roleDetailLists 中的对象
     */
    private List<RoleDetail> findRoleDetailList(RoleDetail roleDetail, List<List<RoleDetail>> roleDetailLists) {
        for (List<RoleDetail> roleDetailList : roleDetailLists) {
            if (roleDetailList.contains(roleDetail)) {
                return roleDetailList;
            }
        }
        return null;
    }


    /**
     * 初始化一个榜单成员对象
     *
     * @param rankMemberInfo
     * @param rankItem
     * @param rankItemBase
     * @return
     */
    private RankItemBase initRankItemBase(RankMemberInfo rankMemberInfo, RankItemAny rankItem, RankItemBase rankItemBase) {
        rankItemBase.setKey(rankMemberInfo.getMemberId());
        rankItemBase.setValue(rankItem.getScore());
        rankItemBase.setRank(rankItem.getRank());
        return rankItemBase;
    }


    private List<RankItemBase> getOneRecord(RankItemAny rankItem, Map<String, Map<String, RoleItem>> rankRoleInfoMaps) {
        List<RankItemBase> result = Lists.newArrayList();
        for (RankMemberInfo rm : rankItem.getRankMemberInfos()) {
            String key = hdztRankGenRoleService.getRoleCreateType(rm);
            Map<String, RoleItem> rankRoleItemMap = rankRoleInfoMaps.get(key);

            RankItemBase rankItemBase = initRankItem(rankItem, rm);
            RoleItem rankRoleItem = null;
            if (!rankRoleItemMap.isEmpty()) {
                rankRoleItem = rankRoleItemMap.get(rm.getMemberId());
            }
            if (rankRoleItem != null) {
                BeanUtils.copyProperties(rankRoleItem, rankItemBase);
            }
            result.add(rankItemBase);
        }
        return result;
    }


    /**
     * 按角色分类初始化一个榜单信息对象
     *
     * @param rankItem
     * @param rankMemberInfo
     * @return
     */
    private RankItemBase initRankItem(RankItemAny rankItem, RankMemberInfo rankMemberInfo) {

        RankItemBase rankItemBase;
        long roleType = rankMemberInfo.getRoleType();
        if (roleType == RoleType.USER.getValue()) {
            rankItemBase = initRankItemBase(rankMemberInfo, rankItem, new UserRankItem());
        } else if (roleType == RoleType.ANCHOR.getValue()) {
            rankItemBase = initRankItemBase(rankMemberInfo, rankItem, new BabyRankItem());
        } else if (roleType == RoleType.HOST.getValue()) {
            rankItemBase = initRankItemBase(rankMemberInfo, rankItem, new BabyRankItem());
        } else if (roleType == RoleType.GUILD.getValue()) {
            rankItemBase = initRankItemBase(rankMemberInfo, rankItem, new GuildRankItem());
        } else if (roleType == RoleType.HALL.getValue()) {
            rankItemBase = initRankItemBase(rankMemberInfo, rankItem, new SubGuildRankItem());
        } else if (roleType == RoleType.PWTUAN.getValue()) {
            rankItemBase = initRankItemBase(rankMemberInfo, rankItem, new TeamRankItem());
        } else if (roleType == RoleType.WAITER.getValue()) {
            rankItemBase = initRankItemBase(rankMemberInfo, rankItem, new WaiterItem());
        } else if (roleType == RoleType.TING.getValue()) {
            rankItemBase = initRankItemBase(rankMemberInfo, rankItem, new NewTingRankItem());
        } else if (roleType == RoleType.TING_MGR.getValue()) {
            rankItemBase = initRankItemBase(rankMemberInfo, rankItem, new TingMgrRankItem());
        } else if (roleType == RoleType.ANY.getValue()) {
            rankItemBase = initRankItemBase(rankMemberInfo, rankItem, new AnyRankItem());
        } else if (roleType == RoleType.FAMILY.getValue()) {
            rankItemBase = initRankItemBase(rankMemberInfo, rankItem, new FamilyRankItem());
        } else if (roleType == RoleType.ROOM.getValue()) {
            rankItemBase = initRankItemBase(rankMemberInfo, rankItem, new RoomRankItem());
        }else {
            throw new SuperException("不能处理的角色类型：" + roleType, 9999);
        }
        return rankItemBase;

    }
}
