package com.yy.gameecology.activity.dao.mysql;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.common.consts.BattleMode;
import com.yy.gameecology.common.consts.wzry.WzryGameState;
import com.yy.gameecology.common.db.model.gameecology.wzry.*;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.element.component.attr.WzryGameComponentAttr;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-23 11:03
 **/
@Component
public class WzryDao {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private GameecologyDao gameecologyDao;

    public List<WzryGameView> queryWzryGameView(long actId, Date now, WzryGameComponentAttr attr, int daySpan, int page, int pageSize) {

        Date endTime = DateUtil.add(DateUtil.getDayBeginTime(now), daySpan);
        WzryGameView where = new WzryGameView();
        where.setActId(actId);

        String afterWhere = String.format(" and sign_up_end_time>'%s' and start_time<'%s' order by start_time asc limit %s,%s ", DateUtil.format(now), DateUtil.format(endTime), page * pageSize, pageSize);
        return gameecologyDao.select(WzryGameView.class, where, afterWhere);
    }

    public List<WzryGameView> queryNeedAllocationTeamGameView(long actId, Date now, int batch) {
        WzryGameView where = new WzryGameView();
        where.setActId(actId);
        where.setBattleMode(BattleMode.GAME_1V1);
        where.setState(WzryGameState.INIT);
        String afterWhere = String.format(" and '%s' > sign_up_end_time limit %s ", DateUtil.format(now), batch);

        return gameecologyDao.select(WzryGameView.class, where, afterWhere);
    }


    /**
     * 取赛宝房间，报名时间截止，需要结算的数据
     */
    public List<WzryGame> queryNeedCancelSbTimeOutGame(long actId, Date gmeStartTimeEnd, int batch) {
        WzryGame where = new WzryGame();
        where.setActId(actId);
        where.setState(WzryGameState.INIT_TEAM);
        String afterWhere = String.format(" and  start_time<'%s' and childid<>'' limit %s ", DateUtil.format(gmeStartTimeEnd), batch);
        return gameecologyDao.select(WzryGame.class, where, afterWhere);
    }

    public List<WzryGame> queryWzryGame(long actId, int state, int batch) {
        WzryGame where = new WzryGame();
        where.setActId(actId);
        where.setState(state);
        String afterWhere = String.format(" and childid<>'' order by  last_check_time asc limit %s ", batch);
        return gameecologyDao.select(WzryGame.class, where, afterWhere);
    }

    public List<WzryGame> queryWzryGameByGameViewCode(long actId, int state, String gameViewCode) {
        WzryGame where = new WzryGame();
        where.setActId(actId);
        where.setState(state);
        where.setGameViewCode(gameViewCode);
        return gameecologyDao.select(WzryGame.class, where, "");
    }

    public List<WzryGameView> queryMyJoinGameView(long actId, long uid, Date startTimeStart, Date startTimeEnd) {
        WzryGameView where = new WzryGameView();
        where.setActId(actId);
        String afterWhere = " and start_time>='%s' and start_time<='%s' and id in (select game_view_id from wzry_game_view_team where uid=%s )   order by start_time desc";
        afterWhere = String.format(afterWhere, DateUtil.format(startTimeStart), DateUtil.format(startTimeEnd), uid);
        return gameecologyDao.select(WzryGameView.class, where, afterWhere);
    }

    /**
     * @return key gameViewCode
     */
    public Map<String, WzryGame> queryMyJoinGame(long actId, long uid, List<String> gameViewCode) {
        Map<String, WzryGame> gameMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(gameViewCode)) {
            return gameMap;
        }
        List<String> gameViewWhere = gameViewCode.stream().map(p -> "'" + p + "'").toList();
        WzryGame where = new WzryGame();
        where.setActId(actId);
        String afterWhere = " and id in (select game_id from wzry_game_team where uid=%s ) and game_view_code in(%s)  order by start_time asc";
        afterWhere = String.format(afterWhere, uid, StringUtils.join(gameViewWhere, ","));
        List<WzryGame> wzryGames = gameecologyDao.select(WzryGame.class, where, afterWhere);
        for (WzryGame game : wzryGames) {
            gameMap.put(game.getGameViewCode(), game);
        }

        return gameMap;
    }

    public int updateGameEnters(long gameId, int maxEnters) {
        String updateGame = " update  wzry_game set enters = enters + 1 where enters<? and id = ?";
        return gameecologyDao.update(updateGame, maxEnters, gameId);
    }

    public int updateGameViewEnters(long gameViewId, int maxEnters) {
        String updateGameView = " update  wzry_game_view set enters = enters + 1 where enters<? and id = ? ";
        return gameecologyDao.update(updateGameView, maxEnters, gameViewId);
    }

    public void updateAllocationTeam(long actId, long gameViewId, Long uid) {
        WzryGameViewTeam where = new WzryGameViewTeam();
        where.setActId(actId);
        where.setUid(uid);
        where.setGameViewId(gameViewId);

        WzryGameViewTeam to = new WzryGameViewTeam();
        to.setAllocationTeam(1);

        gameecologyDao.update(WzryGameViewTeam.class, where, to);

    }


    public List<WzryGame> getWzryMyGameCloseGame(Long actId, long uid, int page, int pageSize) {
        WzryGame where = new WzryGame();
        where.setActId(actId);
        String afterWhere = "  and id in (select game_id from wzry_game_team where uid=%s ) and state in (%s) order by start_time desc limit %s,%s ";
        afterWhere = String.format(afterWhere, uid, StringUtils.join(WzryGameState.CLOSE_STATES, ","), page * pageSize, pageSize);
        return gameecologyDao.select(WzryGame.class, where, afterWhere);
    }

    public Map<Long, Integer> queryUserTeam(long actId, List<Long> gameIds, Long uid) {
        if (CollectionUtils.isEmpty(gameIds)) {
            return Maps.newHashMap();
        }
        WzryGameTeam where = new WzryGameTeam();
        where.setActId(actId);
        where.setUid(uid);
        String afterWhere = String.format(" and game_id in(%s) ", StringUtils.join(gameIds, ","));
        List<WzryGameTeam> teams = gameecologyDao.select(WzryGameTeam.class, where, afterWhere);
        return teams.stream().collect(Collectors.toMap(WzryGameTeam::getGameId, WzryGameTeam::getLimitTeam));
    }

    public int updateWzryGameStatus(long actId, Date now, long gameId, int status, Integer winnerTeam) {

        WzryGame where = new WzryGame();
        where.setActId(actId);
        where.setId(gameId);

        WzryGame to = new WzryGame();
        to.setState(status);
        if (WzryGameState.ALL_IN_SB_ROOM == status) {
            to.setSaiBaoRoomFullTime(now);
        }
        if (winnerTeam != null) {
            to.setWinnerTeam(winnerTeam);
        }


        return gameecologyDao.update(WzryGame.class, where, to);
    }


    public int updateWzryGameViewStatus(long actId, String gameViewCode, int state) {
        WzryGameView where = new WzryGameView();
        where.setActId(actId);
        where.setGameViewCode(gameViewCode);

        WzryGameView to = new WzryGameView();
        to.setState(state);

        return gameecologyDao.update(WzryGameView.class, where, to);
    }

    public WzryGame queryWzryGame(long actId, String gameCode) {
        WzryGame where = new WzryGame();
        where.setActId(actId);
        where.setGameCode(gameCode);
        return gameecologyDao.selectOne(WzryGame.class, where, "");
    }

    public WzryGameView queryWzryGameView(long actId, String gameViewCode) {
        WzryGameView where = new WzryGameView();
        where.setActId(actId);
        where.setGameViewCode(gameViewCode);
        return gameecologyDao.selectOne(WzryGameView.class, where, "");
    }

    public WzryGame queryLastStartTimeWzryGame(long actId, List<Long> gameIds) {
        WzryGame where = new WzryGame();
        where.setActId(actId);
        String afterWhere = String.format(" and id in (%s) order by start_time desc limit 1", StringUtils.join(gameIds, ","));
        return gameecologyDao.selectOne(WzryGame.class, where, afterWhere);
    }

    public List<WzryGameTeam> queryWzryGameTeam(long actId, long gameId) {
        WzryGameTeam gameTeamWhere = new WzryGameTeam();
        gameTeamWhere.setActId(actId);
        gameTeamWhere.setGameId(gameId);
        return gameecologyDao.select(WzryGameTeam.class, gameTeamWhere, "");
    }

    public int[] batchUpdateGameTeamGameSeat(List<WzryGameTeam> wzryGameTeams) {

        return gameecologyDao.getJdbcTemplate().batchUpdate(
                "update wzry_game_team set in_game =? ,in_game_record_time =now() where act_id = ? and id =? ",
                new BatchPreparedStatementSetter() {
                    public void setValues(PreparedStatement ps, int i)
                            throws SQLException {
                        WzryGameTeam item = wzryGameTeams.get(i);
                        ps.setInt(1, item.getInGame());
                        ps.setLong(2, item.getActId());
                        ps.setLong(3, item.getId());
                    }

                    public int getBatchSize() {
                        return wzryGameTeams.size();
                    }
                });

    }

    public List<WzryGame> queryNeedCloseGameOverList(long actId, int batch) {
        WzryGame where = new WzryGame();
        where.setActId(actId);
        where.setState(WzryGameState.BEGIN_GAME);
        String afterWhere = String.format(" and  childid<>'' order by last_check_time asc limit %s ", batch);
        return gameecologyDao.select(WzryGame.class, where, afterWhere);
    }

    public int updateWzryGameLastCheckTime(long id, Date lastCheckTime) {
        WzryGame where = new WzryGame();
        where.setId(id);

        WzryGame update = new WzryGame();
        update.setLastCheckTime(lastCheckTime);

        return gameecologyDao.update(WzryGame.class, where, update);
    }

    public List<WzryGame> queryNeedInitSbGame(long actId, Date signStartTimeEndTime, int size) {
        WzryGame where = new WzryGame();
        where.setActId(actId);
        String afterWhere = " and sign_up_start_time <'%s' and childid ='' order by sign_up_start_time desc limit %s ";
        afterWhere = String.format(afterWhere, DateUtil.format(signStartTimeEndTime), size);
        return gameecologyDao.select(WzryGame.class, where, afterWhere);
    }

    public boolean existRoomPoll(long sid, long ssid) {
        WzryRoomPool where = new WzryRoomPool();
        where.setSid(sid);
        where.setSsid(ssid);
        return gameecologyDao.count(WzryRoomPool.class, where, "") > 0;
    }

    public boolean existRoomChannel(long actId, long sid, long ssid) {
        WzryGameChannel where = new WzryGameChannel();
        where.setActId(actId);
        where.setSid(sid);
        where.setSsid(ssid);
        return gameecologyDao.count(WzryGameChannel.class, where, "") > 0;
    }



    public List<WzryGameChannel> queryGameChannel(Long actId, List<Long> gameIds) {
        if (CollectionUtils.isEmpty(gameIds)) {
            return Lists.newArrayList();
        }
        WzryGameChannel where = new WzryGameChannel();
        where.setActId(actId);
        String afterWhere = String.format(" and game_id in(%s) ", StringUtils.join(gameIds, ","));
        return gameecologyDao.select(WzryGameChannel.class, where, afterWhere);
    }

    /**
     * @return key gameId value team
     */
    public Map<Long, Integer> queryUserTeam(Long actId, Long uid, List<Long> gameIds) {
        if (CollectionUtils.isEmpty(gameIds)) {
            return Maps.newHashMap();
        }
        WzryGameTeam where = new WzryGameTeam();
        where.setActId(actId);
        where.setUid(uid);
        String afterWhere = String.format(" and game_id in(%s) ", StringUtils.join(gameIds, ","));
        List<WzryGameTeam> wzryGameTeams = gameecologyDao.select(WzryGameTeam.class, where, afterWhere);

        return wzryGameTeams.stream().collect(Collectors.toMap(WzryGameTeam::getGameId, WzryGameTeam::getLimitTeam));
    }

    public List<WzryGameViewTeam> queryWzryGameViewTeam(long actId, long gameViewId) {
        WzryGameViewTeam where = new WzryGameViewTeam();
        where.setActId(actId);
        where.setGameViewId(gameViewId);
        return gameecologyDao.select(WzryGameViewTeam.class, where, " order by create_time desc");
    }

    public WzryGame joinAllocation5V5Game(long actId, String gameViewCode) {
        WzryGame gameWhere = new WzryGame();
        gameWhere.setActId(actId);
        gameWhere.setGameViewCode(gameViewCode);
        String afterWhere = String.format("and enters< %s order by enters desc ", BattleMode.GAME_5V5_ENTER_LIMIT);
        return gameecologyDao.selectOne(WzryGame.class, gameWhere, afterWhere);
    }

    /**
     * 比赛开始时间和报名开始时间，取最近的时间
     */
    public Date getRecentlyGameTime(long actId, Date now) {
        Date startGameTime = getRecentlyStartGame(actId, now);
        Date signUpGameTime = getRecentlySignUpStartGame(actId, now);
        if (startGameTime == null || signUpGameTime == null) {
            return startGameTime == null ? signUpGameTime : startGameTime;
        }

        return signUpGameTime.before(startGameTime) ? signUpGameTime : startGameTime;
    }

    public Date getRecentlyStartGame(long actId, Date now) {
        WzryGame where = new WzryGame();
        where.setActId(actId);
        String afterWhere = String.format(" and  start_time>'%s'  order by  start_time asc limit 1 ", DateUtil.format(now));
        WzryGame result = gameecologyDao.selectOne(WzryGame.class, where, afterWhere);
        if (result == null) {
            return null;
        }
        return result.getStartTime();
    }

    public Date getRecentlySignUpStartGame(long actId, Date now) {
        WzryGame where = new WzryGame();
        where.setActId(actId);
        String afterWhere = String.format(" and  sign_up_start_time>'%s'  order by  sign_up_start_time asc limit 1 ", DateUtil.format(now));
        WzryGame result = gameecologyDao.selectOne(WzryGame.class, where, afterWhere);
        if (result == null) {
            return null;
        }
        return result.getSignUpStartTime();
    }

    public long queryJoinGameUserDistinctCount(long actId, Date start, Date end) {
        String sql = "select count(distinct uid) from wzry_game_view_team where act_id =%s   and create_time>='%s' and create_time<'%s'";
        sql = String.format(sql, actId, DateUtil.format(start), DateUtil.format(end));
        return Convert.toLong(gameecologyDao.queryForObject(sql, Long.class), 0);
    }

    public long queryJoinGameUserCount(long actId, Date start, Date end) {
        String sql = "select count( uid) from wzry_game_view_team where act_id =%s   and create_time>='%s' and create_time<'%s'";
        sql = String.format(sql, actId, DateUtil.format(start), DateUtil.format(end));
        return Convert.toLong(gameecologyDao.queryForObject(sql, Long.class), 0);
    }

    public long queryWinnerUserCount(long actId, int battleMode, Date start, Date end) {
        String sql = "select sum(enters)/2 from hdzk7_down.wzry_game where act_id =%s and state=900 and battle_mode = %s and start_time>='%s ' and start_time<'%s '";
        sql = String.format(sql, actId, battleMode, DateUtil.format(start), DateUtil.format(end));
        return Convert.toLong(gameecologyDao.queryForObject(sql, Long.class), 0);
    }


}
