package com.yy.gameecology.activity.bean.xpush;


import com.alibaba.fastjson.annotation.JSONField;

import java.util.Date;

public class PushMsg {

    private Long id;

    /**
     *   推送对象 1 全部 2 牵线师
     *
     */
    private Integer target;

    /**
     *   1 即时推送 2定时推送
     *
     */
    private Byte type;

    private PushRole role;

    /**
     *   推送时间
     *
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

    /**
     *   标题
     *
     */
    private String title;

    /**
     *
     */
    private String image;

    /**
     *   链接
     *
     */
    private String link;

    /**
     *   操作人uid
     *
     */
    private Long operateUid;

    /**
     *   创建时间
     *
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     *
     *   1 待推送 2已推送 3 已取消
     *
     */
    private Byte status;

    private String content;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getTarget() {
        return target;
    }

    public void setTarget(Integer target) {
        this.target = target;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public PushRole getRole() {
        return role;
    }

    public void setRole(PushRole role) {
        this.role = role;
    }

    public Date getPushTime() {
        return pushTime;
    }

    public void setPushTime(Date pushTime) {
        this.pushTime = pushTime;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public Long getOperateUid() {
        return operateUid;
    }

    public void setOperateUid(Long operateUid) {
        this.operateUid = operateUid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
