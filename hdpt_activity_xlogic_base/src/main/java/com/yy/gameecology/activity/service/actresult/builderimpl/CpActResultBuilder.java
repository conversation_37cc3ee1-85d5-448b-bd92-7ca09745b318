package com.yy.gameecology.activity.service.actresult.builderimpl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.actresult.ActResultMemberLoader;
import com.yy.gameecology.activity.service.impl.ActPwSupportService;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-11-17 16:12
 **/
@Component
public class CpActResultBuilder extends BuilderBase implements ActResultMemberLoader {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    @Autowired
    private CommonService commonService;

    @Override
    public Map<String, Map<String, MemberInfo>> loadMemberInfo(long actId, long type, long roleType, List<String> memberIds) {
        if (CollectionUtils.isEmpty(memberIds)) {
            return Collections.emptyMap();
        }

        Map<String, Map<String, MemberInfo>> memberInfoMapResult = new HashMap<>(2);

        List<Long> userIds = Lists.newArrayList();
        List<Long> anchors = Lists.newArrayList();
        Set<Long> uids = new HashSet<>(memberIds.size() * 2);
        memberIds.forEach(x -> {
            String[] array = x.split("\\|");
            long userUid = Convert.toLong(array[0], 0), anchorUid = Convert.toLong(array[1], 0);
            if (userUid > 0) {
                userIds.add(userUid);
                uids.add(userUid);
            }

            if (anchorUid > 0) {
                anchors.add(anchorUid);
                uids.add(anchorUid);
            }
        });

        if (uids.isEmpty()) {
            return Collections.emptyMap();
        }

        //用户
        Map<Long, WebdbUserInfo> userInfoMap = commonService.batchYyUserInfo(List.copyOf(uids));

        Map<String, MemberInfo> userMemberInfoMap = new HashMap<>(userIds.size());
        for (Long memberId : userIds) {
            WebdbUserInfo userInfo = userInfoMap.get(memberId);

            MemberInfo memberInfo = toMemberInfo(userInfo);
            userMemberInfoMap.put(String.valueOf(memberId), memberInfo);
        }
        memberInfoMapResult.put(builderKey(type, RoleType.USER.getValue()), userMemberInfoMap);

        //主播
        Map<String, MemberInfo> anchorMemberInfoMap = new HashMap<>(anchors.size());
        for (Long memberId : anchors) {
            WebdbUserInfo userInfo = userInfoMap.get(memberId);

            MemberInfo memberInfo = toMemberInfo(userInfo);
            anchorMemberInfoMap.put(String.valueOf(memberId), memberInfo);
        }
        memberInfoMapResult.put(builderKey(type, RoleType.ANCHOR.getValue()), anchorMemberInfoMap);


        return memberInfoMapResult;
    }
}
