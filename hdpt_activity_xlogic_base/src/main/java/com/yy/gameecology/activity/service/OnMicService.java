package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-21 19:27
 **/
@Service
public class OnMicService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private OnlineChannelService onlineChannelService;

    /**
     * 获取主播正在开播频道对应关系
     */
    public ChannelInfoVo getOnMicChannel(Long uid) {
        return onlineChannelService.getChannelInfoVo(uid);
    }

    public long getFirstOnMicCache(Long sid, Long ssid) {
        OnlineChannelInfo onlineChannelInfo = onlineChannelService.get(sid, ssid);
        if (onlineChannelInfo == null) {
            return 0L;
        }
        List<Long> anchorIds = onlineChannelInfo.getEffectAnchorId();
        if (CollectionUtils.isEmpty(anchorIds)) {
            return 0L;
        }
        return anchorIds.get(0);
    }

}
