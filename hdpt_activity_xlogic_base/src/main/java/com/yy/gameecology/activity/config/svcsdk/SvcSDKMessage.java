package com.yy.gameecology.activity.config.svcsdk;

import com.yy.gameecology.common.utils.AESUtil;

import java.util.Map;

/**
 * <AUTHOR> 2020/7/22
 */
public class SvcSDKMessage {

    private long suid;

    private long uid;

    private long sid;

    private long ssid;

    private byte[] data;

    private Map<Long, String> ext;

    public SvcSDKMessage(long suid, long uid, long sid, long ssid, byte[] data,
                         Map<Long, String> ext) {
        this.suid = suid;
        this.uid = uid;
        this.sid = sid;
        this.ssid = ssid;
        this.data = data;
        this.ext = ext;
    }

    public long getSuid() {
        return suid;
    }

    public long getUid() {
        return uid;
    }

    public long getSid() {
        return sid;
    }

    public long getSsid() {
        return ssid;
    }

    public byte[] getData() {
        return data;
    }

    public Map<Long, String> getExt() {
        return ext;
    }

    @Override
    public String toString() {
        return "SvcSDKMessage{" +
                "suid=" + suid +
                ", uid=" + uid +
                ", sid=" + sid +
                ", ssid=" + ssid +
                ", data=" + AESUtil.byte2hex(data) +
                ", ext=" + ext +
                '}';
    }
}
