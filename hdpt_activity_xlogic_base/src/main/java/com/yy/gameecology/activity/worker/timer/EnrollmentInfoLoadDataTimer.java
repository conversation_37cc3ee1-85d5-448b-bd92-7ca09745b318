package com.yy.gameecology.activity.worker.timer;

import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.ScheduledExt;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.service.EnrollmentService;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.consts.Const;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-26 21:46
 **/
@ConditionalOnExpression(Const.EXPRESSION_NOT_HISTORY)
@Component
public class EnrollmentInfoLoadDataTimer {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private EnrollmentService enrollmentService;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private TimerSupport timerSupport;

    @Scheduled(cron = "0 0/2 * * * ? ")
    @ScheduledExt(historyRun = true)
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Report
    public void invoke() {
        enrollmentService.loadEnrollmentInfoData();
    }

    /**
     * 更新报名信息签约频道号
     */
    @Scheduled(cron = "0/10 * * * * ?")
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Report
    public void updateSignSid() {
        List<ActivityInfoVo> activityInfoVos = hdztRankingThriftClient.queryEffectActInfos();
        if(CollectionUtils.isEmpty(activityInfoVos)){
            log.warn("activityInfoVos is null");
            return;
        }
        activityInfoVos.forEach(x -> {
            timerSupport.work("updateSignSid_" + x.getActId(), 60,
                    () -> enrollmentService.updateEnrollSignSid(x.getActId()));
        });
    }
}
