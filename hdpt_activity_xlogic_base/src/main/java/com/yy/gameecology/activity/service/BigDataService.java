package com.yy.gameecology.activity.service;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.bigdata.ActivityReport;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @Author: CXZ
 * @Desciption: 海猫大数据
 * @Date: 2021/4/10 16:09
 * @Modified: 大数据文件采集任务：https://cloud.bigda.com/integration/dataexchange/detail/6000115278?tab=info
 * 大数据hive表： https://cloud.bigda.com/metadata/hive/table/gameecology.hdzt_activity_report
 */
@Service
public class BigDataService {

    // 普通日志器
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    // 这个是打海猫上报文件专用的日志器，千万不能用来打其它日志，否则海猫数据入库解析可能失败！！！ - added by guoliping / 20201-04-12
    private Logger harlog = LoggerFactory.getLogger("HDZT_ACTIVITY_REPORT");

    @Autowired
    private CommonService commonService;

    /**
     * 功能描述:每分钟时主动输出一行空日志，强制触发log器按小时滚动产生上小时日志文件
     *
     * <AUTHOR>
     * @date 2021/4/12 18:04
     */
    @Scheduled(cron = "0 */1 * * * ?")
    @NeedRecycle(author = "guoliping", notRecycle = true)
    public void roll() {
        harlog.info("");
        log.info("roll ok!!! -> " + DateUtil.today());
    }

    /**
     * @param scoreType @see com.yy.gameecology.common.consts.BigDataScoreType
     */
    public void saveNoRankDataToFile(long actId, BusiId busiId, long busiTime, String member, RoleType roleType, long score, int scoreType) {
        saveNoRankDataToFile(actId, busiId, busiTime, member, roleType, score, scoreType, "", 0, 0L);
    }

    /**
     * @param scoreType @see com.yy.gameecology.common.consts.BigDataScoreType
     */
    public void saveNoRankDataToFile(long actId, BusiId busiId, long busiTime, String member, RoleType roleType, long score, int scoreType, String ext) {
        saveNoRankDataToFile(actId, busiId, busiTime, member, roleType, score, scoreType, ext, 0, 0L);
    }

    /**
     * @param scoreType @see com.yy.gameecology.common.consts.BigDataScoreType
     */
    public void saveNoRankDataToFile(long actId, BusiId busiId, long busiTime, String member, RoleType roleType, long score, int scoreType, String ext, int extInt, long extLong) {
        saveNoRankDataToFile("", actId, busiId, busiTime, member, roleType, score, scoreType, ext, extInt, extLong);
    }

    public void saveNoRankDataToFile(String seq, long actId, BusiId busiId, long busiTime, String member, RoleType roleType, long score, int scoreType, String ext, int extInt, long extLong) {
        try {
            ActivityReport report = new ActivityReport();
            report.setSeq(seq);
            report.setActId(actId);
            report.setBusiId(busiId != null ? busiId.getValue() : 0);
            report.setActor(member);
            report.setActorType(roleType.getValue());
            report.setScore(score);
            report.setScoreType(scoreType);
            report.setBusiTime(busiTime);
            report.setReportTime(System.currentTimeMillis());
            report.setExtString(ext);
            report.setExtInt(extInt);
            report.setExtLong(extLong);
            saveDataToFile(Lists.newArrayList(report));
        } catch (Exception e) {
            log.error("saveNoRankDataToFile error@actId:{},busiId:{},busiTime:{},member:{},roleType:{},score:{},scoreType:{},ext:{},extInt:{},extLong:{} {}",
                    actId, busiId, busiTime, member, roleType, score, scoreType, ext, extInt, extLong,
                    e.getMessage(), e);
        }
    }

    public void saveDataToFile(ActivityReport activityReport) {
        saveDataToFile(Lists.newArrayList(activityReport));
    }

    public void saveDataToFile(List<ActivityReport> activityReportList) {
        for (ActivityReport activityReport : activityReportList) {
            if (activityReport.getReportType() == null) {
                activityReport.setReportType(getReportType(activityReport.getActId()));
            }

            //harlog.info(JSON.toJSONString(activityReport));
            // 海猫那边用json需要 json 的字段顺序和 hive表顺序一样，这是一个不可接受的约束，改用分隔符 + 固定字段顺序模式解决
            harlog.info(activityReport.toString());
        }
    }

    /**
     * 正式数据是 0
     *
     * @param actId
     * @return
     */
    private int getReportType(Long actId) {
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return 0;
        }
        return 1;
    }
}
