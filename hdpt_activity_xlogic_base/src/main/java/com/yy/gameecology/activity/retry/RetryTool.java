package com.yy.gameecology.activity.retry;

import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.ThreadPoolNames;
import com.yy.gameecology.common.support.SpringBeanAwareFactory;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.Callable;

/**
 * 重试工具类
 *
 * <AUTHOR>
 * @since 2023/7/21 11:24
 **/
@Slf4j
@Component
public class RetryTool {

    @Autowired
    private ThreadPoolManager threadPoolManager;

    /**
     * 通过redis SETNX 限制最多执行一次
     *
     * @param actId          活动id
     * @param seq            序列号,用于幂等校验
     * @param expiredSeconds 序列号缓存过期时间
     * @param task           需要执行的任务
     * @return
     */
    public static void withRetryCheck(long actId, String seq, long expiredSeconds, Runnable task) {
        RedisConfigManager redisConfigManager = SpringBeanAwareFactory.getBean(RedisConfigManager.class);
        String groupCode = redisConfigManager.getGroupCode(actId);

        ActRedisGroupDao redisGroupDao = SpringBeanAwareFactory.getBean(ActRedisGroupDao.class);

        String newSeq = Const.addActivityPrefix(actId, "withRetryCheck:" + seq);
        boolean firstExecute = redisGroupDao.setNX(groupCode, newSeq, DateUtil.today(), expiredSeconds);
        if (firstExecute) {
            task.run();
        } else {
            log.warn("withRetryCheck not first execute,actId={},seq={}", actId, seq);
        }
    }



    /**
     * seq默认缓存一天
     */
    public static void withRetryCheck(long actId, String seq, Runnable task) {
        withRetryCheck(actId, seq, DateUtil.ONE_DAY_SECONDS, task);
    }

    public  void asyWithRetryCheck(long actId, String seq, long expiredSeconds, Runnable task,String threadPoolNames) {
        threadPoolManager.get(threadPoolNames).execute(() -> {
            withRetryCheck(actId,seq,expiredSeconds,task);
        });
    }

    /**
     * 同步重试执行
     * @param task 执行代码
     * @param maxRetries 最大重试次数
     * @param delay 出现异常重试间隔
     */
    public static <T> T executeWithRetry(Callable<T> task, int maxRetries, long delay) throws Exception {
        int retry = 0;
        while (true) {
            try {
                return task.call();
            } catch (Exception e) {
                log.warn("withRetryCheck error,retry:{},maxRetries:{},delay:{},e:{}", retry, maxRetries, delay, e.getMessage(), e);
                if (retry >= maxRetries) {
                    throw e;
                }
                SysEvHelper.waiting(delay);
                retry++;
            }
        }
    }

}
