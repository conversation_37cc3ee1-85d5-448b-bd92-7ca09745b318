package com.yy.gameecology.activity.bean.actlayer;



import java.util.List;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-09-12 15:01
 **/
public class PKInfo {

    private List<PKItem> pkItems;
    private long pkLeftSeconds;
    /**
     * 调整pk还未完成时 true
     */
    private boolean inSettle;

    public List<Integer> pkAward;

    public List<PKItem> getPkItems() {
        return pkItems;
    }

    public void setPkItems(List<PKItem> pkItems) {
        this.pkItems = pkItems;
    }

    public long getPkLeftSeconds() {
        return pkLeftSeconds;
    }

    public void setPkLeftSeconds(long pkLeftSeconds) {
        this.pkLeftSeconds = pkLeftSeconds;
    }

    public boolean isInSettle() {
        return inSettle;
    }

    public void setInSettle(boolean inSettle) {
        this.inSettle = inSettle;
    }

    public List<Integer> getPkAward() {
        return pkAward;
    }

    public void setPkAward(List<Integer> pkAward) {
        this.pkAward = pkAward;
    }
}
