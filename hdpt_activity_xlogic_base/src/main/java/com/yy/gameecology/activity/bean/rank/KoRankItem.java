package com.yy.gameecology.activity.bean.rank;

/**
 * @Author: CXZ
 * @Desciption: ko
 * @Date: 2020/11/16 23:40
 * @Modified:
 */
public class KoRankItem {


    /**
     *
     */
    private RankItemBase winner;
    /**
     *
     */
    private RankItemBase loser;
    /**
     * 相差时间
     */
    private Long diff;
    /**
     * 开始ko时间戳
     */
    private Long startTime;
    /**
     * 结束ko时间戳
     */
    private Long endTime;
    /**
     * 倒计时，单位毫秒
     */
    private Long countDown;

    public RankItemBase getWinner() {
        return winner;
    }

    public void setWinner(RankItemBase winner) {
        this.winner = winner;
    }

    public RankItemBase getLoser() {
        return loser;
    }

    public void setLoser(RankItemBase loser) {
        this.loser = loser;
    }

    public Long getDiff() {
        return diff;
    }

    public void setDiff(Long diff) {
        this.diff = diff;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Long getCountDown() {
        return countDown;
    }

    public void setCountDown(Long countDown) {
        this.countDown = countDown;
    }
}
