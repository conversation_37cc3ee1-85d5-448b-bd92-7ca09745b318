package com.yy.gameecology.activity.annotation;

import com.yy.protocol.pb.GameecologyActivity.PacketType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 方法签名可为:
 *
 *  public void foo()
 *  public void foo(GameEcologyMsg msg)
 *  public GameEcologyMsg foo()
 *  public GameEcologyMsg foo(GameEcologyMsg msg)
 *
 * <AUTHOR> 2020/7/22
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ProtoHandler {

    PacketType value();

}
