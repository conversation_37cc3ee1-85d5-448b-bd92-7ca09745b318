package com.yy.gameecology.activity.bean.rank;

import com.yy.gameecology.common.utils.Convert;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-21 15:29
 **/
public class SubGuildRankItem extends PkRankItemBase {
    private Long sid;
    private Long ssid;
    private Long asid;
    private String name;
    private String avatarInfo;



    @Override
    public void setKey(String key) {

        String[] members = key.split("_");
        setSid(Convert.toLong(members[0]));
        setSsid(Convert.toLong(members[1]));
    }

    @Override
    public String getKey() {
        return getSid()+"_"+getSsid();
    }


    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }



    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAvatarInfo() {
        return avatarInfo;
    }

    public void setAvatarInfo(String avatarInfo) {
        this.avatarInfo = avatarInfo;
    }


    public Long getSsid() {
        return ssid;
    }

    public void setSsid(Long ssid) {
        this.ssid = ssid;
    }

    public Long getAsid() {
        return asid;
    }

    public void setAsid(Long asid) {
        this.asid = asid;
    }
}
