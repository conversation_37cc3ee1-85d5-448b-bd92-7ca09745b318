package com.yy.gameecology.activity.bean;


import com.yy.gameecology.common.bean.MultiNickItem;

import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-12-01 11:34
 **/
public class ActResultInfoNew {
    /**
     * key group id
     */
    private Map<String, ActResultGroupVoNew> groupVoMap;

    //多昵称
    private Map<String, Map<String, MultiNickItem>> nickExtUsers;

    public Map<String, ActResultGroupVoNew> getGroupVoMap() {
        return groupVoMap;
    }

    public void setGroupVoMap(Map<String, ActResultGroupVoNew> groupVoMap) {
        this.groupVoMap = groupVoMap;
    }

    public Map<String, Map<String, MultiNickItem>> getNickExtUsers() {
        return nickExtUsers;
    }

    public void setNickExtUsers(Map<String, Map<String, MultiNickItem>> nickExtUsers) {
        this.nickExtUsers = nickExtUsers;
    }
}
