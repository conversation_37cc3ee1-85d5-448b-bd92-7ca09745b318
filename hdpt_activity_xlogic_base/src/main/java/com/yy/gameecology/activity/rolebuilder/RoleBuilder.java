package com.yy.gameecology.activity.rolebuilder;

import com.yy.gameecology.activity.bean.rankroleinfo.RoleItem;
import com.yy.thrift.act_ext_support.MemberItemInfo;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/9/17 0:31
 * @Modified:
 */
public interface RoleBuilder<T extends RoleItem> {

    /**
     * 获取默认属性的对象，不可直接使用，只能用于赋值
     * @return
     */
     T getDefaultObject();


    /**
     * 创建对象
     * @return
     */
    T createBankObject();
    /**
     * 从yy获取信息
     * @param members
     * @return
     */
    Map<String,T> buildRankByYy(Set<String> members);

    default Map<String,T> buildRankByYyWithNickExt(Set<String> members) {
        throw new UnsupportedOperationException("not supported");
    }


    /**
     * 补充额外的信息
     * @param actId
     * @param roleId
     * @param uid
     * @param roleItemMap
     * @param rankId
     * @return
     */
    default Map<String,T> addExtraInfo(long actId,long roleId,long rankId,long uid, Map<String,T> roleItemMap) {
        return roleItemMap;
    }

    /**
     * 填充基本信息
     * @param memberItemInfo
     * @param roleItem
     * @return
     */
    default T buildRankItemByBase(MemberItemInfo memberItemInfo, T roleItem) {
        T defaultObject = getDefaultObject();
        Optional<MemberItemInfo> memberInfo = Optional.ofNullable(memberItemInfo);

        roleItem.setKey(memberItemInfo.getBaseFieldMemberId());
        roleItem.setName(memberInfo.map(MemberItemInfo::getBaseFieldMemberName).orElse(defaultObject.getName()));
        roleItem.setAvatarInfo(memberInfo.map(MemberItemInfo::getBaseFieldMemberUrl).orElse(defaultObject.getAvatarInfo()));
        return roleItem;
    }

    /**
     * 填充扩展信息
     * @param memberItemInfo
     * @param roleItem
     * @return
     */
    default T buildRankItemByEx(MemberItemInfo memberItemInfo, T roleItem) {
        return roleItem;
    }
}
