package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.hdzt.*;
import com.yy.gameecology.activity.bean.rank.*;
import com.yy.gameecology.activity.bean.rankroleinfo.RoleItem;
import com.yy.gameecology.activity.bean.uniqcp.UniqCpRankItem;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.processor.ranking.RankingProcessor;
import com.yy.gameecology.activity.processor.ranking.RankingProcessorManager;
import com.yy.gameecology.activity.processor.ranking.impl.RankingAnyProcessor;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.db.model.gameecology.ActShowRankConfig;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.java.webdb.WebdbChannelInfo;
import com.yy.thrift.hdztranking.*;
import jnr.ffi.Struct;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-16 20:44
 **/
@Service
public class HdztRankService {

    private static final ConcurrentMap<String, RankInfo> RANK_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(5, TimeUnit.SECONDS)
            .maximumSize(1000).<String, RankInfo>build().asMap();

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private OnMicService onMicService;

    @Autowired
    private UserSubService userSubService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private RankingAnyProcessor rankingAnyProcessor;

    @Autowired
    private HdztRankGenRoleService hdztRankGenRoleService;

    @Lazy
    @Autowired
    private HdztRankServiceUseOne handelByUseOne;

    @Autowired
    private ActInfoService actInfoService;

    @Autowired
    private CacheService cacheService;

    @Lazy
    @Autowired
    private HdztRankService hdztRankService;

    /**
     * 批量查询榜单阶段的mvp
     */
    @Cached(timeToLiveMillis = 10 * 1000)
    public Map<Long, List<Object>> batchPhaseGroupMvp(long actId, String rankIds) {
        Clock clock = new Clock();
        List<Long> rankIdList = Stream.of(rankIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        Map<String, QueryRankingRequest> rankingRequests = Maps.newHashMap();
        Map<Long, RankingInfo> rankingInfoMap = hdztRankingThriftClient.queryRankConfig(actId, rankIdList);
        clock.tag();
        //根据阶段id生成请求数据
        Long now = commonService.getNow(actId).getTime();
        Map<Long, List<Long>> queryPhaseMap = Maps.newHashMap();
        for (Map.Entry<Long, RankingInfo> entry : rankingInfoMap.entrySet()) {

            RankingInfo rankingInfo = entry.getValue();
            Long rankId = entry.getKey();

            List<Long> phaseIds = rankingInfo.getPhasesMap().values().stream()
                    .sorted(Comparator.comparing(RankingPhaseInfo::getBeginTime))
                    .map(RankingPhaseInfo::getPhaseId).collect(Collectors.toList());

            List queryPhases = Lists.newArrayList();
            Map<Long, RankingPhaseInfo> phasesMap = rankingInfo.getPhasesMap();
            for (Long phaseId : phaseIds) {
                RankingPhaseInfo phaseInfo = phasesMap.get(phaseId);
                //已经结束的阶段
                if (now > phaseInfo.getEndTime()) {
                    QueryRankingRequest rankingRequest = new QueryRankingRequest();
                    rankingRequest.setActId(actId);
                    rankingRequest.setRankingId(rankId);
                    rankingRequest.setPhaseId(phaseId);
                    rankingRequest.setRankingCount(1);

                    rankingRequests.put(rankId + "_" + phaseId, rankingRequest);
                    queryPhases.add(phaseId);
                } else {
                    break;
                }
            }
            queryPhaseMap.put(rankId, queryPhases);
        }

        if (rankingRequests.isEmpty()) {
            return Maps.newHashMap();
        }

        //批量请求
        Map<String, BatchRankingItem> rankMap = hdztRankingThriftClient.queryBatchRanking(rankingRequests, Collections.EMPTY_MAP);
        clock.tag();
        Map<Long, List<Object>> rankListMap = Maps.newHashMap();
        for (Long rankId : rankIdList) {

            List<Rank> mvpRanks = Lists.newArrayList();

            List<Long> phaseIds = queryPhaseMap.getOrDefault(rankId, Collections.EMPTY_LIST);

            for (Long phaseId : phaseIds) {

                BatchRankingItem rankingItem = rankMap.get(rankId + "_" + phaseId);
                List<Rank> ranks = rankingItem.getData();
                if (ranks.isEmpty()) {
                    mvpRanks.add(new Rank(StringUtil.ZERO, -1, 0L, ""));
                    log.error("phaseGroupMvp error rank is null,actId:{},rankId:{},phaseId = {} ", actId, rankId, phaseId);
                } else {
                    mvpRanks.add(ranks.get(0));
                }
            }
            List<Object> rankItems = Lists.newArrayList();
            if (!mvpRanks.isEmpty()) {
                GetRankReq getRankReq = new GetRankReq();
                getRankReq.setRankId(rankId);
                getRankReq.setActId(actId);
                getRankReq.setShowZeroItem(true);
                rankItems = rankingAnyProcessor.getRankInfo(getRankReq, rankingInfoMap.get(rankId), mvpRanks, Maps.newHashMap());
            }
            rankListMap.put(rankId, rankItems);
        }
        clock.tag();
        return rankListMap;
    }


    //榜单成员当前积分和排名
    @Cached(timeToLiveMillis = 5 * 1000)
    public RankCurItemVo queryCurItem(Long actId, Long rankId, Long sid, Long ssid, Long userId, String dateStr) {
        //公会、厅、主播
        RankCurItemVo rankCurItemVo = new RankCurItemVo();
        if (sid == null || ssid == null) {
            return rankCurItemVo;
        }

        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, rankId);

        if (rankingInfo == null || rankingInfo.getCurrentPhase() == null) {
            log.warn("rankingInfo is null,actId:{},rankId:{}", actId, rankId);
            return rankCurItemVo;
        }
        List<List<RoleDetail>> roleDetailLists = Optional.of(rankingInfo.getRoleItemConfig())
                .map(RoleItemConfig::getRoles)
                .orElse(Collections.EMPTY_LIST);

        Set<Integer> roleTypes = roleDetailLists.stream()
                .flatMap(Collection::stream)
                .map(RoleDetail::getRoleType)
                .map(Long::intValue)
                .collect(Collectors.toSet());

        if (roleTypes.isEmpty()) {
            log.warn("roleDetailLists is null,actId:{},rankId:{}", actId, rankId);
            return null;
        }

        long phaseId = rankingInfo.getCurrentPhase().getPhaseId();

        String actorId = "";
        int memberType = 0;
        if (roleTypes.contains(RoleType.ANCHOR.getValue())) {
            memberType = RankMemberType.BABY;
            long uid = onMicService.getFirstOnMicCache(sid, ssid);
            actorId = uid + "";
        } else if (roleTypes.contains(RoleType.GUILD.getValue())) {
            memberType = RankMemberType.GUILD;
            actorId = sid + "";
        } else if (roleTypes.contains(RoleType.HALL.getValue())) {
            memberType = RankMemberType.HALD;
            actorId = sid + "_" + ssid;
        } else if (roleTypes.contains(RoleType.USER.getValue()) && userId != null) {
            memberType = RankMemberType.USER;
            actorId = userId + "";

        }
        if (StringUtil.isBlank(actorId) || StringUtil.ZERO.equals(actorId)) {
            return rankCurItemVo;
        }

        if (dateStr == null) {
            dateStr = "";
        }

        //---查询当前成员积分
        ActorQueryItem item = new ActorQueryItem(rankId, phaseId, actorId, dateStr, false, 0, 1, Maps.newHashMap());

        List<ActorInfoItem> actorInfoItems = hdztRankingThriftClient.queryActorRankingInfo(actId, Lists.newArrayList(item));
        if (CollectionUtils.isEmpty(actorInfoItems)) {
            return rankCurItemVo;
        }
        ActorInfoItem actorInfoItem = actorInfoItems.get(0);
        Rank rank = new Rank(actorInfoItem.getActorId(), (int) actorInfoItem.getRank(), actorInfoItem.getScore(), "");
        List<RankItemAny> rankItems = rankingAnyProcessor.genRankBaseInfo(actId, rankId, Lists.newArrayList(rank), true, rankingInfo, "");

        GetRankReq getRankReq = new GetRankReq();
        getRankReq.setRankId(rankId);
        getRankReq.setActId(actId);
        getRankReq.setShowZeroItem(true);

        Map<String, Map<String, RoleItem>> rankRoleInfoMaps = hdztRankGenRoleService.getRankRoleInfo(getRankReq, rankingInfo, rankItems);
        RoleItem roleItem = rankRoleInfoMaps.values().stream().map(Map::values).flatMap(Collection::stream).findFirst().get();

        BasicRankItemVo curItem = new BasicRankItemVo();
        curItem.setRank(actorInfoItem.getRank());
        curItem.setValue(actorInfoItem.getScore());
        curItem.setNick(roleItem.getName());
        rankCurItemVo.setCurItem(curItem);
        rankCurItemVo.setCurPhaseId(phaseId);
        rankCurItemVo.setMemberType(memberType);
        return rankCurItemVo;
    }


    private long RANK_INFO_HIT = 0;

    private long RANK_INFO_CACHE_HIT = 0;

    private long RANK_INFO_SOURCE_HIT = 0;

    /**
     * 查询通用榜单信息(榜单数据分值变更较快，不适合缓存太久)
     */
    public RankInfo getRankInfo(GetRankReq rankReq) {
        log.info("getRankInfo,RANK_INFO_HIT:{},RANK_INFO_SOURCE_HIT:{},RANK_INFO_CACHE_HIT:{}", RANK_INFO_HIT, RANK_INFO_SOURCE_HIT, RANK_INFO_CACHE_HIT);
        RANK_INFO_HIT++;

        //所有查询强制不使用缓存（高优先级）
        if (Const.ONESTR.equals(cacheService.getActAttrValue(rankReq.getActId(), GeActAttrConst.QUERY_RANK_ALL_NOT_USE_CACHE))) {
            return loadRankInfo(rankReq);
        }

        //全局启用缓存，谨慎开启！！！！
        boolean allCache = Const.ONESTR.equals(cacheService.getActAttrValue(rankReq.getActId(), GeActAttrConst.QUERY_RANK_ALL_USE_CACHE));
        boolean paraUseCache = rankReq.getUseCache() == Const.ONE;
        if (allCache || paraUseCache) {
            RankInfo rankInfo = loadRankInfoCache(rankReq);
            if (rankInfo != null) {
                RANK_INFO_CACHE_HIT++;
                log.info("rank from cache");
                return rankInfo;
            }
            rankInfo = loadRankInfo(rankReq);
            putRankInfoCache(rankReq, rankInfo);
            return rankInfo;
        }

        return loadRankInfo(rankReq);
    }

    public RankInfo loadRankInfoCache(GetRankReq rankReq) {
        String cacheKey = rankReq.getCommonCacheKey();
        if (StringUtil.isBlank(cacheKey)) {
            return null;
        }
        return RANK_CACHE.get(cacheKey);
    }

    public void putRankInfoCache(GetRankReq rankReq, RankInfo rankInfo) {
        String cacheKey = rankReq.getCommonCacheKey();
        if (StringUtil.isBlank(cacheKey) || rankInfo == null) {
            return;
        }
        RANK_CACHE.put(cacheKey, rankInfo);
    }

    public RankInfo loadRankInfo(GetRankReq rankReq) {
        RANK_INFO_SOURCE_HIT++;
        long actId = rankReq.getActId();
        long rankId = rankReq.getRankId();
        long phaseId = rankReq.getPhaseId();

        RankInfo rankInfo = new RankInfo();

        //中台榜单基础信息
        Map<String, String> ext = Maps.newHashMap();
        if (!StringUtil.isBlank(rankReq.getFindSrcMember())) {
            ext.put(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, rankReq.getFindSrcMember());
        }

        //填充榜单配置信息
        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(rankReq.getActId(), rankReq.getRankId(), rankReq.getPhaseId());
        if (rankingInfo == null) {
            log.warn("rankingInfo not found,actId:{},rankId:{}", rankReq.getActId(), rankReq.getRankId());
            return rankInfo;
        }

        long currentTime = commonService.getNow(actId).getTime();
        rankInfo.setCurrentTime(currentTime);
        rankInfo.setRankName(rankingInfo.getRankingName());
        RankingPhaseInfo currentPhase = rankingInfo.getCurrentPhase();

        String queryDateStr = rankReq.getDateStr();

        if (rankingInfo.getTimeKey() == 0) {
            queryDateStr = "";
        }
        if (commonService.inSettle(actId, rankId, phaseId, queryDateStr)) {
            rankInfo.setInSettle(1);
            return rankInfo;
        }

        if (currentPhase == null) {
            rankInfo.setPassCount(0L);
            rankInfo.setTotalCount(0L);
        } else {
            rankInfo.setPassCount(currentPhase.getPassCount());
            rankInfo.setTotalCount(currentPhase.getTotalCount());
        }

        return handelByUseOne.handelByUseOne(rankReq, rankingInfo, rankInfo, ext);
    }

    /**
     * 榜单接口简化版-可以指定查询榜单下不同的key，权限较大，一般不对外部使用
     */
    public RankInfo getRankInfo(Long actId, Long rankId, Long phaseId, String dateStr,
                                String findSrcMember, Integer queryRankType, Integer rankCount) {
        RankInfo rankInfo = new RankInfo();

        //中台榜单基础信息
        Map<String, String> ext = Maps.newHashMap();
        if (!StringUtil.isBlank(findSrcMember)) {
            ext.put(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, findSrcMember);
        }

        long rankingCount = rankCount;

        //填充榜单配置信息
        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, rankId, phaseId);
        if (rankingInfo == null) {
            log.warn("rankingInfo not found,actId:{},rankId:{}", actId, rankId);
            return rankInfo;
        }
        rankInfo.setCurrentTime(commonService.getNow(actId).getTime());
        rankInfo.setRankName(rankingInfo.getRankingName());
        RankingPhaseInfo currentPhase = rankingInfo.getCurrentPhase();

        if (currentPhase == null) {
            rankInfo.setPassCount(0L);
            rankInfo.setTotalCount(0L);
        } else {
            rankInfo.setPassCount(currentPhase.getPassCount());
            rankInfo.setTotalCount(currentPhase.getTotalCount());
        }

        if (queryRankType != null && queryRankType > 0) {
            ext.put(RankExtParaKey.QUERY_RANK_TYPE, queryRankType + "");
        }

        List<Rank> ranks = hdztRankingThriftClient.queryRankingCache(actId, rankId, phaseId, dateStr, rankingCount, "", ext);
        //指定对象是，空榜会返回-1
        if (ranks.isEmpty() || ranks.get(0).getRank() == -1) {
            rankInfo.setList(Lists.newArrayList());
            return rankInfo;
        }

        GetRankReq getRankReq = new GetRankReq();
        getRankReq.setRankId(rankId);
        getRankReq.setActId(actId);
        getRankReq.setPhaseId(phaseId);
        getRankReq.setShowZeroItem(true);
        RankingProcessor rankingProcessor = RankingProcessorManager.getRankingProcessor(actId, rankId, (int) rankingInfo.getRankingType());
        if (rankingProcessor == null) {
            log.warn("getRankInfo 未实现的榜单类型:{} actId:{},rankId:{}", rankingInfo.getRankingType(), actId, rankId);
            return rankInfo;
        }

        List<Object> objectList = rankingProcessor.getRankInfo(getRankReq, rankingInfo, ranks, ext);
        rankInfo.setList(objectList);
        return rankInfo;
    }


    //构建榜单最基本信息 成员/排名/分数
    public List<RankItem> genRankBaseInfo(Long actId, Long rankId, List<Rank> ranks, Integer rankType, boolean showZeroItem) {
        List<RankItem> rankItems = Lists.newArrayList();
        for (Rank rank : ranks) {
            // 指定成员榜单信息一定不可以忽略！
            String itemDesc = rank.getItemDesc();
            if (!"POINTED_MEMBER".equals(itemDesc) && !showZeroItem && Convert.toLong(rank.getScore(), 0) == 0) {
                continue;
            }
            RankItem item = new RankItem();
            //如果是情侣榜，member 用 | 隔开成员
            if (GeRankType.PAIR_RANK == rankType || GeRankType.ALL_HOUR_RANK_PAIR_RANK == rankType) {
                String[] members = rank.getMember().split("\\|");
                if (members.length < 2) {
                    String errMsg = String.format("cp榜找不到2个元素，actId:%s,rankId:%s,rankType:%s,member:%s", actId, rankId, rankType, rank.getMember());
                    log.error(errMsg);
                    throw new RuntimeException(errMsg);
                }
                //用户
                item.setMember(members[0]);
                //主播
                item.setMember2(members[1]);
            } else {
                item.setMember(rank.getMember());
            }

            item.setRank(rank.getRank());
            item.setScore(rank.getScore());
            item.setItemDesc(itemDesc);

            rankItems.add(item);
        }
        return rankItems;
    }

    /**
     * 生成普通用户类榜单成员信息
     */
    public List<UserRankItem> genUserRankItems(List<RankItem> rankBaseItems, Integer templateType) {
        List<UserRankItem> res = Lists.newArrayList();
        if (CollectionUtils.isEmpty(rankBaseItems)) {
            return res;
        }

        List<Long> allMembers = rankBaseItems.stream().map(x -> Convert.toLong(x.getMember(), 0)).collect(Collectors.toList());
        Map<Long, UserInfoVo> ftsUserInfoMap
                = userInfoService.getUserInfo(allMembers, Template.getTemplate(templateType));

        for (RankItem baseInfoItem : rankBaseItems) {
            UserRankItem rankItem = buildUserRankItem(baseInfoItem, ftsUserInfoMap);
            res.add(rankItem);
        }

        return res;
    }


    private UserRankItem buildUserRankItem(RankItem baseInfoItem, Map<Long, UserInfoVo> ftsUserInfoMap) {
        UserRankItem rankItem = new UserRankItem();
        rankItem.setValue(baseInfoItem.getScore());
        rankItem.setRank(baseInfoItem.getRank());

        Long uid = Convert.toLong(baseInfoItem.getMember(), 0);
        UserInfoVo userInfo = ftsUserInfoMap.get(uid);
        if (userInfo != null) {
            rankItem.setUid(userInfo.getUid());
            rankItem.setNick(userInfo.getNick());
            rankItem.setAvatarInfo(userInfo.getAvatarUrl());
            rankItem.setNobleGrade(userInfo.getNobleId() + "");
            rankItem.setGeNobleGrade(userInfo.getEcologyNobleId() + "");
            //todo 约战、宝贝贵族信息暂时不要,后面时间够再和产品提加
        }
        return rankItem;
    }

    private BabyRankItem buildBabyRankItem(Long actId, RankItem baseInfoItem, Map<Long, UserInfoVo> ftsUserInfoMap, Map<Long, Boolean> userSubscribes) {
        String memberId = baseInfoItem.getMember();
        //cp榜
        if (!StringUtil.isBlank(baseInfoItem.getMember2())) {
            memberId = baseInfoItem.getMember2();
        }

        BabyRankItem rankItem = new BabyRankItem();
        rankItem.setValue(baseInfoItem.getScore());
        rankItem.setRank(baseInfoItem.getRank());
        //用户信息
        Long uid = Convert.toLong(memberId, 0);
        UserInfoVo userInfo = ftsUserInfoMap.get(uid);
        if (userInfo != null) {
            rankItem.setUid(userInfo.getUid());
            rankItem.setNick(userInfo.getNick());
            rankItem.setAvatarInfo(userInfo.getAvatarUrl());
        }
        //获取正在开播频道
        ChannelInfoVo onMicChannel = onMicService.getOnMicChannel(uid);
        if (onMicChannel != null) {
            rankItem.setSid(onMicChannel.getSid());
            rankItem.setSsid(onMicChannel.getSsid());
        }

        //置是否关注当前主播
        if (MapUtils.isNotEmpty(userSubscribes) && userSubscribes.getOrDefault(uid, false)) {
            rankItem.setSubscribe(true);
        } else {
            rankItem.setSubscribe(false);
        }

        return rankItem;
    }


    /**
     * 生成公会类榜单成员信息
     */
    public List<GuildRankItem> genGuildRankItems(List<RankItem> rankBaseItems, Integer templateType) {
        List<GuildRankItem> res = Lists.newArrayList();
        if (CollectionUtils.isEmpty(rankBaseItems)) {
            return res;
        }

        Set<Long> allMembers = rankBaseItems.stream().map(x -> Convert.toLong(x.getMember(), 0)).collect(Collectors.toSet());
        Map<Long, WebdbChannelInfo> channelInfos = webdbThriftClient.batchGetChannelInfo(new ArrayList<>(allMembers));
        for (RankItem baseInfoItem : rankBaseItems) {

            Long sid = Convert.toLong(baseInfoItem.getMember(), 0);
            GuildRankItem rankItem = new GuildRankItem();
            Long score = baseInfoItem.getScore();
            rankItem.setValue(score);
            rankItem.setRank(baseInfoItem.getRank());
            rankItem.setSid(sid);

            //频道基础信息
            WebdbChannelInfo channelInfo = channelInfos.get(sid);
            if (channelInfo != null) {
                rankItem.setAsid(Convert.toLong(channelInfo.getAsid(), sid));
                rankItem.setName(channelInfo.getName());
                rankItem.setAvatarInfo(WebdbUtils.getLogo(channelInfo));
            }

            res.add(rankItem);
        }

        return res;
    }

    /**
     * 生成用户:主播 cp类榜单成员信息
     */
    public List<RankItemUserAnchor> genCpRankItems(Long actId, Long uid, List<RankItem> rankBaseItems, int templateType) {
        List<RankItemUserAnchor> res = Lists.newArrayList();
        if (CollectionUtils.isEmpty(rankBaseItems)) {
            return res;
        }

        Set<Long> allUserMembers = rankBaseItems.stream().map(x -> Convert.toLong(x.getMember(), 0)).collect(Collectors.toSet());
        Set<Long> allBabyMembers = rankBaseItems.stream().map(x -> Convert.toLong(x.getMember2(), 0)).collect(Collectors.toSet());

        //榜单所有成员uid
        Set<Long> allMembers = Sets.newHashSet();
        allMembers.addAll(allUserMembers);
        allMembers.addAll(allBabyMembers);
        Map<Long, UserInfoVo> ftsUserInfoMap
                = userInfoService.getUserInfo(new ArrayList<>(allMembers), Template.getTemplate(templateType));

        //用户关注主播信息
        Map<Long, Boolean> userSubscribes = userSubService.getUserSub(actId, uid, allMembers, templateType);

        for (RankItem baseInfoItem : rankBaseItems) {
            RankItemUserAnchor rankItem = new RankItemUserAnchor();
            rankItem.setBabyRankItem(buildBabyRankItem(actId, baseInfoItem, ftsUserInfoMap, userSubscribes));
            rankItem.setUserRankItem(buildUserRankItem(baseInfoItem, ftsUserInfoMap));
            rankItem.setItemDesc(baseInfoItem.getItemDesc());
            rankItem.setScore(baseInfoItem.getScore());
            res.add(rankItem);
        }

        return res;
    }

    public List<RankInfoItem<RankItemUserAnchor>> genHourCpRankItems(Long actId, Long rankId, Long uid, int templateType, long rankingCount, int rankType, boolean showZeroItem) {
        List<RankInfoItem<RankItemUserAnchor>> result = Lists.newArrayList();

        String currentHour = DateUtil.format(commonService.getNow(actId), DateUtil.PATTERN_YYYYMMDD);
        Map<String, List<Rank>> rankMap = hdztRankingThriftClient.queryAllHourRank(actId, rankId, rankingCount);
        Map<String, List<Rank>> dayRankMap = Maps.newLinkedHashMap();
        for (String key : rankMap.keySet()) {
            //当前小时不展示
            if (key.equals(currentHour)) {
                continue;
            }
            List<Rank> ranks = rankMap.getOrDefault(key, Lists.newArrayList());
            String dayCode = key.substring(0, 8);
            List<Rank> dayRank = dayRankMap.getOrDefault(dayCode, Lists.newArrayList());
            dayRank.addAll(ranks);
            dayRankMap.put(dayCode, dayRank);
        }

        for (String dayCode : dayRankMap.keySet()) {
            List<Rank> ranks = dayRankMap.get(dayCode);
            RankInfoItem<RankItemUserAnchor> rankItemRankInfoItem = new RankInfoItem<>();
            rankItemRankInfoItem.setName(dayCode);
            if (CollectionUtils.isNotEmpty(ranks)) {
                List<RankItem> rankItems = genRankBaseInfo(actId, rankId, ranks, rankType, showZeroItem);
                List<RankItemUserAnchor> rankItemUserAnchors = genCpRankItems(actId, uid, rankItems, templateType);
                rankItemRankInfoItem.setList(rankItemUserAnchors);
            }
            result.add(rankItemRankInfoItem);
        }

        Collections.sort(result, new Comparator<RankInfoItem<RankItemUserAnchor>>() {
            @Override
            public int compare(RankInfoItem<RankItemUserAnchor> o1, RankInfoItem<RankItemUserAnchor> o2) {
                if (StringUtil.isNumeric(o1.getName())) {
                    return Convert.toInt(o1.getName(), 0) - Convert.toInt(o2.getName(), 0) > 0 ? 1 : -1;
                }
                return 0;
            }
        });

        return result;
    }


    /**
     * 生成pk类榜单成员信息
     */
    public List<GuildPkRankItem> genPkRankItems(long rankId, String dateStr, List<RankItem> rankItems) {
        throw new SuperException("功能已取消#E311081", SuperException.E_UNKNOWN);
    }

    public static boolean isValidPointedMember(String pointedMember) {
        return !StringUtil.isBlank(pointedMember) && !StringUtil.ZERO.equals(pointedMember);
    }


    public List<Rank> getRanking(QueryRankingRequest request) {
        return hdztRankingThriftClient.queryRanking(request.actId, request.rankingId, request.phaseId, request.dateStr,
                request.rankingCount, request.extData);
    }


    /**
     * 功能描述：为阶段结束事件提取指定范围的成员ID（如用户UID、频道号等）
     *
     * <AUTHOR>
     * @date 2021/4/16 10:45
     */
    public Map<Integer, Rank> readyRankReceivers(long actId, long rankId, long phaseId, long timeKey, String endTime,
                                                 long maxRank, String rankType, String primaryRankMember) {

        try {
            Date date = DateUtil.getDate(endTime, DateUtil.DEFAULT_PATTERN);
            String dateStr = TimeKeyHelper.getTimeCode(timeKey, date);
            QueryRankingRequest request = new QueryRankingRequest();
            request.setActId(actId);
            request.setRankingId(rankId);
            request.setRankingCount(maxRank);
            request.setPhaseId(phaseId);
            request.setDateStr(dateStr);
            request.setRankType(rankType);
            request.setFindSrcMember(StringUtil.trim(primaryRankMember));
            QuerySingleRankingResult result = hdztRankingThriftClient.getProxy().queryRanking(request);
            if (result.getCode() != 0) {
                return null;
            }
            return result.getData().stream().collect(Collectors.toMap(Rank::getRank, Function.identity()));
        } catch (Throwable t) {
            return null;
        }
    }

    public Map<Integer, Rank> readyRankReceivers(long actId, long rankId, long phaseId, long timeKey, String endTime,
                                                 long maxRank, String rankType) {
        return readyRankReceivers(actId, rankId, phaseId, timeKey, endTime, maxRank, rankType, null);
    }

    /**
     * 小时榜剩余时间倒计时
     *
     * @param actId  活动id
     * @param rankId 榜单id
     * @return 剩余时间-秒， -1代表不在小时榜倒计时范围内
     */
    public long hourRankLeftSeconds(Long actId, Long rankId) {
        if (!actInfoService.inActTime(actId)) {
            return -1;
        }
        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, rankId);
        if (rankingInfo == null) {
            return -1;
        }
        //非小时榜
        if (rankingInfo.getTimeKey() != TimeKeyHelper.TIME_KEY_BY_HOUR) {
            return -1;
        }

        Date now = commonService.getNow(actId);

        // 当前时间不在 榜单的累计时间内
        if (now.getTime() < rankingInfo.getCalBeginTime() || now.getTime() > rankingInfo.getCalEndTime()) {
            return -1;
        }

        String startTimeStr = DateUtil.format(now, DateUtil.PATTERN_TYPE5) + " " + rankingInfo.getTimeKeyBegin();
        Date startTime = DateUtil.getDate(startTimeStr);
        String endTimeStr = DateUtil.format(now, DateUtil.PATTERN_TYPE5) + " " + rankingInfo.getTimeKeyEnd();
        Date endTime = DateUtil.getDate(endTimeStr);

        //不在显示时间范围内
        if (now.getTime() < startTime.getTime() || now.getTime() > endTime.getTime()) {
            return -1;
        }

        return DateUtil.getHourLeftSeconds(now);
    }


    public long halfHourRankLeftSeconds(Long actId, Long rankId) {
        if (!actInfoService.inActTime(actId)) {
            return -1;
        }
        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, rankId);
        if (rankingInfo == null) {
            return -1;
        }
        //非小时榜
        if (rankingInfo.getTimeKey() != TimeKeyHelper.TIME_KEY_BY_30_MIN) {
            return -1;
        }

        Date now = commonService.getNow(actId);

        // 当前时间不在 榜单的累计时间内
        if (now.getTime() < rankingInfo.getCalBeginTime() || now.getTime() > rankingInfo.getCalEndTime()) {
            return -1;
        }

        return DateUtil.get30MinLeftSeconds(now);
    }

    /**
     * 获取在展示时间内有效的榜单展示配置
     */
    public List<ActShowRankConfig> getEffectShowRankConfig(Long actId, Date now) {
        List<ActShowRankConfig> result = cacheService.getActShowRankConfig(actId);
        if (CollectionUtils.isEmpty(result)) {
            return Lists.newArrayList();
        }
        return result.stream()
                .filter(x -> now.getTime() >= x.getStartTime().getTime() && now.getTime() <= x.getEndTime().getTime())
                .collect(Collectors.toList());
    }

    @Cached(timeToLiveMillis = 3 * 1000)
    public QueryRankingByScoreResponse queryRankingByScoreCache(long actId, long rankId, long phaseId, String subKeyTime, String queryType) {
        return queryRankingByScore(actId, rankId, phaseId, subKeyTime, queryType);
    }

    public QueryRankingByScoreResponse queryRankingByScore(long actId, long rankId, long phaseId, String subKeyTime, String queryType) {
        return this.queryRankingByScore(actId, rankId, phaseId, subKeyTime, queryType, 0, Long.MAX_VALUE);
    }

    public QueryRankingByScoreResponse queryRankingByScore(long actId, long rankId, long phaseId, String subKeyTime,
                                                           String queryType, long fromScore, long toScore) {
        return this.queryRankingByScore(actId, rankId, phaseId, subKeyTime, null, null, queryType, fromScore, toScore, null, 4);
    }

    /**
     * 按分值范围查询数据
     */
    public QueryRankingByScoreResponse queryRankingByScore(long actId, long rankId, long phaseId, String subKeyTime, String subKeyMember, String subKeyItem,
                                                           String queryType, long fromScore, long toScore, Map<String, String> extData, int retry) {
        Clock clock = new Clock();
        QueryRankingByScoreRequest request = new QueryRankingByScoreRequest();
        request.setActId(actId);
        request.setRankingId(rankId);
        request.setPhaseId(phaseId);
        request.setSubKeyTime(subKeyTime);
        request.setSubKeyMember(subKeyMember);
        request.setSubKeyItem(subKeyItem);
        request.setQueryType(queryType);
        request.setFromScore(fromScore);
        request.setToScore(toScore);
        request.setExtData(extData);

        for (int i = 0; i <= retry; i++) {
            try {
                return hdztRankingThriftClient.getProxy().queryRankingByScore(request);
            } catch (Throwable e) {
                if (i == retry) {
                    log.error("queryRankingByScore fail@request:{}, err:{} {}", request, e.getMessage(), clock.tag(), e);
                } else {
                    log.warn("queryRankingByScore wrong@request:{}, err:{} {}", request, e.getMessage(), clock.tag());
                    SysEvHelper.waiting(300);
                }
            }
        }
        return null;
    }

    /**
     * 根据CP的其中一个成员查询CP榜分值最高的一对CP
     *
     * @param actId
     * @param rankingId
     * @param phaseId
     * @param dateStr
     * @param member 可以是主播、也可以是用户。返回包含member的最高分值（分值相同看排名）的CP
     * @return
     */
    public UniqCpRankItem queryUniqCpTopActorInfo(String findSrcMembers, long actId, long rankingId, long phaseId, String dateStr, String member) {
        Map<String, String> data = new HashMap<>(6);
        data.put("actId", String.valueOf(actId));
        data.put("rankId", String.valueOf(rankingId));
        data.put("phaseId", String.valueOf(phaseId));
        data.put("dateStr", dateStr);
        data.put("findSrcMembers", StringUtils.isEmpty(findSrcMembers) ? "_" : findSrcMembers);
        data.put("pointMember", member);

        Map<String, String> result = hdztRankingThriftClient.invoke(BusiId.HDZT.getValue(), actId, data, "queryUniqPointedCp");
        if (MapUtils.isEmpty(result)) {
            return null;
        }

        UniqCpRankItem rs = new UniqCpRankItem();
        rs.setRank(MapUtils.getIntValue(result, "rank", -1));
        rs.setScore(MapUtils.getLongValue(result, "score", 0));
        rs.setMember(MapUtils.getString(result, "member"));
        rs.setGap(MapUtils.getLongValue(result, "gap", 0));

        return rs;
    }

    public Map<String, List<Rank>> queryBatchSrcRanks(long actId, long rankingId, long rankingCount, long phaseId, String dateStr, List<String> findSrcMembers, String rankType, Map<String, String> ext) {
        Map<String, String> data = new HashMap<>(10);
        data.put("actId", String.valueOf(actId));
        data.put("rankingId", String.valueOf(rankingId));
        data.put("rankingCount", String.valueOf(rankingCount));
        data.put("phaseId", String.valueOf(phaseId));
        data.put("dateStr", dateStr == null ? StringUtils.EMPTY : dateStr);
        data.put("findSrcMembers", JSON.toJSONString(findSrcMembers));
        data.put("rankType", rankType);
        data.put("ext", ext == null ? "{}" : JSON.toJSONString(ext));

        Map<String, String> result = hdztRankingThriftClient.invoke(BusiId.HDZT.getValue(), actId, data, "queryBatchSrcRanks");
        if (MapUtils.isEmpty(result)) {
            return Collections.emptyMap();
        }

        Map<String, List<Rank>> rs = new HashMap<>(result.size());
        for (Map.Entry<String, String> entry : result.entrySet()) {
            List<Rank> ranks = JSON.parseArray(entry.getValue(), Rank.class);
            rs.put(entry.getKey(), ranks);
        }


        return rs;
    }
}
