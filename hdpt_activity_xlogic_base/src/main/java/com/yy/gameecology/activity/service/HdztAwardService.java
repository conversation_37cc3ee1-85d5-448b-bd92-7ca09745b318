package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.hdzt.AwardRecordVo;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.bean.CpUid;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.thrift.hdztaward.*;
import com.yy.thrift.hdztranking.BusiId;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.regex.Pattern;

/**
 * desc:
 *
 * @createBy 郭立平
 * @create 2021-01-14 18:36
 **/
@Service
public class HdztAwardService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private final static int MAX_RANKING_COUNT = 1000;

    private static final Pattern AWARD_FILE_NAME_PATTERN = Pattern.compile("^award_file_[\\w\\d]+\\.txt$");

    @Autowired
    private CommonService commonService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private OnlineChannelService onlineChannelService;

    // 使用 @Lazy 注入自身，用于解决从类内部访问自身AOP方法失效问题
    @Lazy
    @Autowired
    private HdztAwardService myself;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    /**
     * 获取我的中奖记录列表信息，根据指示和配置参数看是否使用缓存
     */
    public List<AwardRecordVo> getMyAwardRecords(long uid, long busiId, List<Integer> taskIds, List<Long> packageIds, boolean bUseCache) throws TException {
        return getMyAwardRecords(uid, busiId, taskIds, packageIds, bUseCache, true);
    }

    /**
     * 获取全服中奖记录
     */
    public List<AwardRecordVo> getAwardRecords(long busiId, List<Integer> taskIds, List<Long> packageIds, boolean bUseCache) throws TException {
        return getMyAwardRecords(0, busiId, taskIds, packageIds, bUseCache, true);
    }

    /**
     * 获取我的中奖记录列表信息，根据指示和配置参数看是否使用缓存
     */
    public List<AwardRecordVo> getMyAwardRecords(long uid, long busiId, List<Integer> taskIds, List<Long> packageIds, boolean bUseCache, boolean isLimit) throws TException {
        // 后台配置的使用缓存开关，当发现性能问题时，可打开！默认关闭
        boolean globalCacheFlag = Const.isGeOneFlag(GeParamName.GET_MY_AWARD_RECORDS_BY_CACHE, 0);
        if (globalCacheFlag || bUseCache) {
            return myself.getMyAwardRecordsByCache(uid, busiId, taskIds, packageIds, isLimit);
        } else {
            return getMyAwardRecordsNoCache(uid, busiId, taskIds, packageIds, isLimit);
        }
    }

    public List<AwardRecordVo> getAwardRecordsByScene(long actId, long busiId, String scene, int size) throws TException {
        return getAwardRecordsByScene(actId, busiId, 0, scene, size);
    }

    public List<AwardRecordVo> getAwardRecordsByScene(long actId, long busiId, long uid, String scene, int size) throws TException {
        var result = hdztAwardServiceClient.getAwardRecordByScene(busiId, uid, actId + StringUtil.VERTICAL_BAR + scene, size);
        if (result == null || result.retCode != 0) {
            log.error("getAwardRecordsByScene fail result:{}", result);
            return Collections.emptyList();
        }

        var awardList = result.awardList;
        return getAwardRecordVos(awardList);
    }

    /**
     * 从缓存获取我的中奖纪录（默认缓存5分钟）
     */
    @Cached(timeToLiveMillis = 300 * 1000)
    public List<AwardRecordVo> getMyAwardRecordsByCache(long uid, long busiId, List<Integer> taskIds, List<Long> packageIds, boolean isLimit) throws TException {
        return getMyAwardRecordsNoCache(uid, busiId, taskIds, packageIds, isLimit);
    }


    /**
     * 从接口获取我的中奖纪录
     */
    /**
     * @param uid
     * @param busiId
     * @param taskIds
     * @param packageIds
     * @param isLimit    是否限制返回数量
     * @return
     * @throws TException
     */
    private List<AwardRecordVo> getMyAwardRecordsNoCache(long uid, long busiId, List<Integer> taskIds, List<Long> packageIds, boolean isLimit) throws TException {
        AwardRecordResult awardRecordResult = hdztAwardServiceClient.getUserAwardRecordListEx(busiId, uid, taskIds, packageIds, isLimit);
        List<AwardRecordInfo> userAwardRecordList = awardRecordResult.getAwardList();
        return getAwardRecordVos(userAwardRecordList);
    }

    @NotNull
    private static List<AwardRecordVo> getAwardRecordVos(List<AwardRecordInfo> userAwardRecordList) {
        if (CollectionUtils.isEmpty(userAwardRecordList)) {
            return Collections.emptyList();
        }
        List<AwardRecordVo> data = new ArrayList<>(userAwardRecordList.size());
        Set<String> dupShowSet = new HashSet<>(userAwardRecordList.size());
        for (AwardRecordInfo awardRecordInfo : userAwardRecordList) {
            awardRecordInfo.getExt();
            AwardRecordVo item = new AwardRecordVo();
            item.setTime(awardRecordInfo.getCTime());
            item.setPrize(awardRecordInfo.getPackageName());
            item.setAmount(awardRecordInfo.getAmount());
            item.setUid(awardRecordInfo.getUid());
            item.setPackageId(awardRecordInfo.getPackageId());
            item.setUnit(awardRecordInfo.getUnit());
            item.setViewExtjson(awardRecordInfo.getViewExtjson());
            item.setPackageImage(awardRecordInfo.getPackageImage());
            item.setChoiceImage(awardRecordInfo.getChoiceImage());
            item.setMouseoverTips(awardRecordInfo.getMouseoverTips());
            Map<String, String> ext = awardRecordInfo.getExt();
            if (ext != null) {
                // 对方CP
                if (ext.containsKey(HdztAwardServiceClient.CP_UID)) {
                    String cpUidStr = ext.get(HdztAwardServiceClient.CP_UID);
                    item.setCpUid(Convert.toLong(cpUidStr));
                }

                // 透传的拓展
                if (ext.containsKey(HdztAwardServiceClient.VIEW_EXT)) {
                    item.setViewExtjson(ext.get(HdztAwardServiceClient.VIEW_EXT));
                }

                //cp显示去重
                if (ext.containsKey(HdztAwardServiceClient.DUP_SHOW_SEQ)) {
                    String dupSeq = ext.get(HdztAwardServiceClient.DUP_SHOW_SEQ);
                    if (StringUtil.isNotBlank(dupSeq) && dupShowSet.contains(dupSeq)) {
                        continue;
                    }
                    dupShowSet.add(dupSeq);
                }

                //如果有cpMember存在 uid 和 cp信息 要按照cpMember的方式排序
                if (ext.containsKey(HdztAwardServiceClient.CP_MEMBER)) {
                    String cpMember = ext.get(HdztAwardServiceClient.CP_MEMBER);
                    item.setCpMember(cpMember);
                }
            }
            data.add(item);
        }
        return data;
    }

    /**
     * 获取轮播中奖纪录
     */
    public List<JSONObject> getRollAwardRecords(long actId, long busiId, boolean bUseCache) throws TException {
        // 后台配置的使用缓存开关，当发现性能问题时，可打开！默认打开
        boolean globalCacheFlag = Const.isGeOneFlag(GeParamName.GET_ROLL_AWARD_RECORDS_BY_CACHE, 1);

        if (globalCacheFlag || bUseCache) {
            return myself.getRollAwardRecordsByCache(actId, busiId);
        } else {
            return getRollAwardRecordsNoCache(actId, busiId);
        }
    }

    /**
     * 从缓存获取轮播中奖纪录（默认缓存30秒）
     */
    @Cached(timeToLiveMillis = 30 * 1000)
    public List<JSONObject> getRollAwardRecordsByCache(long actId, long busiId) throws TException {
        return getRollAwardRecordsNoCache(actId, busiId);
    }

    /**
     * 从接口获取轮播中奖纪录
     */
    private List<JSONObject> getRollAwardRecordsNoCache(long actId, long busiId) throws TException {
        Clock clock = new Clock();
        AwardRecordResult awardRecordResult = hdztAwardServiceClient.getProxy(false).getAwardRecordListEx(busiId, actId);
        List<AwardRecordInfo> userAwardRecordList = awardRecordResult.getAwardList();
        List<JSONObject> data = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userAwardRecordList)) {
            for (AwardRecordInfo awardRecordInfo : userAwardRecordList) {
                JSONObject item = new JSONObject();
                item.put("nick", commonService.getUserInfo(awardRecordInfo.getUid(), false).getNick());
                item.put("prize", awardRecordInfo.getPackageName());
                data.add(item);
            }
        }
        //log.info("getRollAwardRecordsNoCache ok@actId:{}, busiId:{}, size:{} {}", actId, busiId, data.size(), clock.tag());
        return data;
    }

    /**
     * fileName 必须是 award_file_*.txt格式 ，*是补充部分，路径在/data/tmp
     *
     * @param fileName
     * @throws IOException
     */
    public void batchAwardByFile(String fileName) throws IOException {

        Assert.isTrue(AWARD_FILE_NAME_PATTERN.matcher(fileName).find(), "fileName error");
        List<String> source = FileUtils.readLines(new File(File.separator + "data" + File.separator + "tmp" + File.separator + fileName), "UTF-8");
        int retry = 3;

        for (int i = 0; i < source.size(); i++) {
            String item = source.get(i);

            String[] itemA = item.split(StringUtil.COMMA);
            if (itemA.length != 5) {
                log.error("batchAwardByFile param size error,fileName:{},index:{} item:{}", fileName, i, item);
                continue;
            }
            try {

                String seq = itemA[0];
                int busiId = Convert.toInt(itemA[1]);
                long uid = Convert.toLong(itemA[2]);
                long taskId = Convert.toLong(itemA[3]);
                long packageId = Convert.toLong(itemA[4]);

                Assert.hasText(seq, "seq null");
                Assert.notNull(BusiId.findByValue(busiId), "busiId error," + busiId);
                Assert.isTrue(uid > 0, "uid error");
                Assert.isTrue(taskId > 0, "taskId error");
                Assert.isTrue(packageId > 0, "packageId error");


                Map<Long, Integer> packageIds = Maps.newHashMap(Collections.singletonMap(packageId, 1));
                Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap(Collections.singletonMap(taskId, packageIds));

                BatchWelfareResult result = hdztAwardServiceClient.doBatchWelfare(DateUtil.getNowYyyyMMddHHmmss(), busiId, uid, 0, taskPackageIds, seq, retry + 1, Maps.newHashMap());
                if (result != null && result.getCode() == 0) {
                    log.info("batchAwardByFile ok@with busiId:{}, uid:{}, taskPackageIds:{}, seq:{}", busiId, uid, JSON.toJSONString(taskPackageIds), seq);
                } else {
                    log.error("batchAwardByFile fail@with busiId:{}, uid:{}, taskPackageIds:{}, seq:{},result:{}", busiId, uid, JSON.toJSONString(taskPackageIds), seq, result == null ? "null" : JSONObject.toJSONString(result));
                }
                SysEvHelper.waiting(100);

            } catch (Exception e) {
                log.error("batchAwardByFile error,item:{},e:{}", item, e.getMessage(), e);
            }
        }
        log.info("batchAwardByFile done@fileName:{}", fileName);

    }

    /**
     * 测试发放奖品的接口
     *
     * @param packageId
     * @param itemId
     * @param uid
     * @param loginUid
     * @return
     */
    public Response releasePackageItem(long packageId, long itemId, long uid, long loginUid, String seq) {

        SimpleResult result = null;
        try {
            String admins = Const.getGeValue("", "releasePackageItemAdmins", "");
            if (SysEvHelper.isDeploy() && !ArrayUtils.contains(admins.split(StringUtil.COMMA), loginUid + "")) {
                return Response.fail(-1, "生产环境无权限操作!");
            }
            String secret = Const.getGeValue("", "releasePackageItemSecret" + SysEvHelper.getGroup(), "");
            result = hdztAwardServiceClient.getProxy(true).releasePackageItem(secret, packageId, itemId, uid, seq);
            log.info("releasePackageItem done@loginUid:{},packageId:{},itemId:{},uid:{},seq:{}, result:{}",
                    loginUid, packageId, itemId, uid, seq, JSON.toJSONString(result));
            return new Response(result.getCode(), result.getReason());
        } catch (TException e) {
            log.info("releasePackageItem done@loginUid:{},packageId:{},itemId:{},uid:{},seq:{} {}",
                    loginUid, packageId, itemId, uid, seq, e.getMessage(), e);
            return Response.fail(result.getCode(), "系统繁忙!");
        }

    }

    /**
     * 测试发放奖品的接口
     *
     * @param uid
     * @param loginUid
     * @return
     */
    public Response releaseTaskItem(long taskId, long uid, long loginUid, String seq) {
        try {
            String admins = Const.getGeValue("", "releasePackageItemAdmins", "");
            if (SysEvHelper.isDeploy() && !ArrayUtils.contains(admins.split(StringUtil.COMMA), loginUid + "")) {
                return Response.fail(-1, "生产环境无权限操作!");
            }
            Map<Long, Integer> packageMap = Maps.newHashMap();
            Map<Long, AwardModelInfo> tasks = hdztAwardServiceClient.queryAwardTasks(taskId);
            for (Map.Entry<Long, AwardModelInfo> entry : tasks.entrySet()) {
                AwardModelInfo value = entry.getValue();
                long packageId = value.getPackageId();
                packageMap.put(packageId, 1);
            }
            Map<Long, Map<Long, Integer>> taskPackageMap = Maps.newHashMap();
            taskPackageMap.put(taskId, packageMap);
            BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doBatchWelfare(seq, uid, taskPackageMap, DateUtil.getNowYyyyMMddHHmmss(), 1, Maps.newHashMap());
            log.info("releaseTaskItem done@loginUid:{},taskId:{},uid:{},seq:{}, result:{}",
                    loginUid, taskId, uid, seq, batchWelfareResult);
            return new Response(batchWelfareResult.getCode(), batchWelfareResult.getReason(), batchWelfareResult);
        } catch (Exception e) {
            log.info("releaseTaskItem done@loginUid:{},taskId:{},uid:{},seq:{} {}",
                    loginUid, taskId, uid, seq, e.getMessage(), e);
            return Response.fail(500, "系统繁忙!");
        }

    }

    public List<String> toAwardNameList(long taskId, Map<Long, Integer> packageIdNums) throws TException {
        List<String> list = Lists.newArrayList();
        Map<Long, AwardModelInfo> map = hdztAwardServiceClient.queryAwardTasks(taskId, false);
        for (Long packageId : packageIdNums.keySet()) {
            AwardModelInfo ami = map.get(packageId);
            String award = ami.getPackageName() + "*" + packageIdNums.get(packageId);
            list.add(award);
        }
        return list;
    }

    public Map<Long, AwardModelInfo> queryAwardTasks(long taskId, boolean withPkItem) throws TException {
        return hdztAwardServiceClient.queryAwardTasks(taskId, withPkItem);
    }

    public AwardModelInfo queryAwardTasks(long taskId, long packageId, boolean withPkItem) throws TException {
        Map<Long, AwardModelInfo> longAwardModelInfoMap = hdztAwardServiceClient.queryAwardTasks(taskId, withPkItem);
        return longAwardModelInfoMap == null ? null : longAwardModelInfoMap.get(packageId);
    }

    @NotNull
    public List<AwardRecordVo> getAwardRecordVos(List<AwardRecordVo> list, Map<String, Map<String, MultiNickItem>> nickExtUsers, boolean needJump) {
        Set<Long> uids = new HashSet<>(list.size() * 2);
        for (AwardRecordVo item : list) {
            uids.add(item.getUid());
            if (item.getCpUid() > 0) {
                uids.add(item.getCpUid());
            }
        }

        Map<Long, UserInfoVo> userBaseInfoMap = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids), nickExtUsers, false, Template.all.getCode());

        for (AwardRecordVo item : list) {
            var userBaseInfo = userBaseInfoMap.get(item.getUid());
            if (userBaseInfo != null) {
                item.setNick(userBaseInfo.getNick());
                item.setAvatar(userBaseInfo.getAvatarUrl());
            } else {
                item.setNick("神秘用户");
            }
            var cpBaseInfo = userBaseInfoMap.get(item.getCpUid());
            if (cpBaseInfo != null) {
                item.setCpNick(cpBaseInfo.getNick());
                item.setCpAvatar(cpBaseInfo.getAvatarUrl());
            }

            // 对非全服中奖记录有影响
            if(StringUtils.contains(item.getCpMember(), StringUtil.VERTICAL_BAR)) {
                CpUid cpUid = Const.splitCpMember(item.getCpMember());
                // 保证cp是anchor
                if(cpUid.getUserUid() != item.getUid()) {
                    String anchorNick = item.getNick();
                    String anchorAvatar = item.getAvatar();
                    long uid = item.getUid();
                    item.setNick(item.getCpNick());
                    item.setAvatar(item.getCpAvatar());
                    item.setCpNick(anchorNick);
                    item.setCpAvatar(anchorAvatar);
                    item.setUid(item.getCpUid());
                    item.setCpUid(uid);
                }

                Map<String, Map<String, MultiNickItem>> nickExtUsersT = new HashMap<>(2);
                nickExtUsersT.put(item.getUid()+"", nickExtUsers.get(item.getUid()+""));
                nickExtUsersT.put(item.getCpUid()+"", nickExtUsers.get(item.getCpUid()+""));
                item.setNickExtUsers(MapUtils.isEmpty(nickExtUsersT) ? StringUtils.EMPTY : JSON.toJSONString(nickExtUsersT));

                if(needJump) {
                    long anchorUid = cpUid.getAnchorUid();
                    ChannelInfoVo channelInfoVo = onlineChannelService.getChannelInfoVo(anchorUid);
                    if(channelInfoVo != null) {
                        item.setSid(channelInfoVo.getSid());
                        item.setSsid(channelInfoVo.getSsid());
                    }
                }
            }

            item.setGiftNum(item.getAmount());
            if(!StringUtil.isEmpty(item.getViewExtjson())) {
                if (StringUtil.isJson(item.getViewExtjson())) {
                    JSONObject jsonObject = JSON.parseObject(item.getViewExtjson());
                    if(jsonObject.containsKey("giftNum")) {
                        item.setGiftNum(jsonObject.getLong("giftNum"));
                    }
                }
            }
        }

        return list;
    }
}
