package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.protocol.pb.GameecologyActivity.GameEcologyMsg;
import com.yy.thrift.broadcast.Template;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> 2020/7/10
 */
public interface SvcSDKService {

    /**
     * 大类 - 代表序列化为PB(varchar16)
     */
    int PB_MAX_TYPE = 99;

    void sdkBcUsergroup(GameEcologyMsg message);

    /**
     * 函数功能：单播
     */
    void unicastUid(long uid, GameEcologyMsg message);

    /**
     * 函数功能：子频道广播
     * 注意：这里子频道广播 svck sdk只会看ssid,如果顶级频道 sid错了，也会广播到唯一的ssid !!!!
     */
    void broadcastSub(long sid, long ssid, GameEcologyMsg message);

    /**
     * 函数功能：顶级频道广播
     */
    void broadcastTop(long sid, GameEcologyMsg message);

    /**
     * 函数功能：模板全服广播
     */
    void broadcastTemplate(Template template, GameEcologyMsg message);

    /**
     * 函数功能：模板全服广播
     * 区分与 com.yy.gameecology.activity.service.SvcSDKService#broadcastTemplate(com.yy.thrift.broadcast.Template, com.yy.protocol.pb.GameecologyActivity.GameEcologyMsg)
     * 可以根据actId 添加活动自定义频道
     */
    void broadcastTemplate(long actId, Template template, GameEcologyMsg message);

    /**
     * 函数功能：陪玩全频道广播
     */
    void broadcastAllChanelsInPW(long actId, GameEcologyMsg message);

    /**
     * 函数功能：技能卡全频道广播
     */
    void broadcastAllChanelsInSkillCard(GameEcologyMsg message);

    void broadcastFamilyInSkillCard(long familyId, GameEcologyMsg msg);

    /**
     * 飞行任务广播
     *
     * @param template
     * @param message
     * @param sid
     * @param ssid
     */
    void broadcastTemplate(Template template, GameEcologyMsg message, long sid, long ssid);

    /**
     * 过滤掉某些频道
     * @param template
     * @param message
     * @param exclude ["sid_ssid", "sid"]
     */
    default void broadcastTemplateExclude(Template template, GameEcologyMsg message, Set<String> exclude) {}

}
