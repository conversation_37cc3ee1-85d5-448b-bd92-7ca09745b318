package com.yy.gameecology.activity.service.aov.phase;

import com.yy.gameecology.activity.bean.PageResponse;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.client.WebdbUinfoClient;
import com.yy.gameecology.common.db.mapper.aov.AovWhitelistMapper;
import com.yy.gameecology.common.db.model.gameecology.aov.AovWhitelist;
import com.yy.gameecology.hdzj.bean.aov.AovWhitelistInfo;
import com.yy.java.webdb.WebdbUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AovWhitelistService {

    @Resource
    private AovWhitelistMapper aovWhitelistMapper;

    @Autowired
    private WebdbUinfoClient webdbUinfoClient;

    @Cached(timeToLiveMillis = 30 * 1000)
    public boolean inWhitelist(long actId, long uid) {
        return aovWhitelistMapper.countByUniqKey(actId, null, uid) > 0;
    }

    @Cached(timeToLiveMillis = 30 * 1000)
    public boolean inWhitelist(long actId, long phaseId, long uid) {
        return aovWhitelistMapper.countByUniqKey(actId, phaseId, uid) > 0;
    }

    public PageResponse<AovWhitelistInfo> queryPageWhitelist(long actId, Long phaseId, Long inviter, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        var list = aovWhitelistMapper.selectWhitelist(actId, phaseId, inviter, offset, pageSize);
        int count = aovWhitelistMapper.countWhitelist(actId, phaseId, inviter);

        if (CollectionUtils.isEmpty(list)) {
            return PageResponse.success(Collections.emptyList(), count, list.size() >= pageSize, null);
        }

        List<Long> uids = list.stream().map(AovWhitelist::getUid).toList();

        Map<Long, WebdbUserInfo> userInfoMap = webdbUinfoClient.batchGetUserInfo(uids);
        List<AovWhitelistInfo> infos = list.stream().map(AovWhitelistInfo::new).toList();
        infos.forEach(info -> {
            long uid = info.getUid();
            WebdbUserInfo userInfo = userInfoMap.get(uid);
            if (userInfo != null && StringUtils.isNumeric(userInfo.getYyno())) {
                info.setYy(Long.parseLong(userInfo.getYyno()));
            }

            if (userInfo != null) {
                info.setNick(userInfo.getNick());
            }
        });

        return PageResponse.success(infos, count, list.size() >= pageSize, null);
    }

    public void batchAddWhitelist(long actId, long phaseId, long inviter, List<Long> uids) {
        List<AovWhitelist> records = new ArrayList<>(uids.size());
        Date now = new Date();

        for (long uid : uids) {
            AovWhitelist record = new AovWhitelist();
            record.setActId(actId);
            record.setPhaseId(phaseId);
            record.setUid(uid);
            record.setInviter(inviter);
            record.setCreateTime(now);

            records.add(record);
        }

        int rs = aovWhitelistMapper.batchInsertWhitelist(records);
        log.info("batchAddWhitelist with uids:{} inviter:{} rs:{}", uids, inviter, rs);
    }

    public void deleteWhitelist(long actId, Long phaseId, Long inviter, long uid) {
        int rs = aovWhitelistMapper.deleteByUniqKey(actId, phaseId, inviter, uid);
        log.info("deleteWhitelist with actId:{} phaseId:{} inviter:{} uid:{} rs:{}", actId, phaseId, inviter, uid, rs);
    }
}
