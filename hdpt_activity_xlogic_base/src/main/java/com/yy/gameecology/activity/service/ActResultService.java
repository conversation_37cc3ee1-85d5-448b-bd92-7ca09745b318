package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.ScheduledExt;
import com.yy.gameecology.activity.bean.*;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.client.thrift.TurnoverContractServiceThriftClient;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.activity.service.actresult.ActResultFactory;
import com.yy.gameecology.activity.service.actresult.ActResultMemberLoader;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.consts.ActResultType;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.model.gameecology.ActResult;
import com.yy.gameecology.common.db.model.gameecology.ActResultGroup;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.JsonUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.component.HonorHallRankComponent;
import com.yy.gameecology.hdzj.element.component.attr.HonorHallComponentAttr;
import com.yy.java.webdb.WebdbChannelInfo;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.RoleType;
import com.yy.thrift.turnover_contract.TRoomContract;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-12-01 11:47
 **/
@Service
public class ActResultService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private GameecologyDao gameecologyDao;

    @Autowired
    private ActResultFactory actResultFactory;

    @Autowired
    private CommonService commonService;

    @Autowired
    private EnrollmentService enrollmentService;

    @Autowired
    private OnMicService onMicService;


    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private ActInfoService actInfoService;

    @Autowired
    private TurnoverContractServiceThriftClient turnoverContractServiceThriftClient;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private UserInfoService userInfoService;

    /**
     * 在活动展示时间内，且memberid有值的 act result
     * key===actId_role_type_memberId   value===ActResult
     */
    private Map<String, List<ActResult>> IN_SHOW_TIME_HAS_VALUE_ACT_RESULT = Maps.newHashMap();


    /**
     * 和 queryActResultNew() 的区别是返回的元素有的是数组， 有的是单个元素
     *
     * @param actId  活动id
     * @param status -1的时候查询所有的,给产品预览用,其他查询已发布状态的
     * @return
     */
    @Deprecated
    @Cached(timeToLiveMillis = 10 * 1000L)
    public ActResultInfo queryActResult(long actId, List<Integer> types, int status) {
        ActResultInfo actResultInfo = new ActResultInfo();

        ActResultGroup groupWhere = new ActResultGroup();
        groupWhere.setActId(actId);
        String groupAfterWhere = String.format(" and type in (%s) order by sort asc ", StringUtils.join(types, ","));
        List<ActResultGroup> actResultGroups = gameecologyDao.select(ActResultGroup.class, groupWhere, groupAfterWhere);
        if (CollectionUtils.isEmpty(actResultGroups)) {
            return actResultInfo;
        }

        ActResult where = new ActResult();
        where.setActId(actId);
        String afterWhere = String.format("and type in (%s)  order by rank asc ", StringUtils.join(types, ","));
        List<ActResult> actResults = gameecologyDao.select(ActResult.class, where, afterWhere);
        if (CollectionUtils.isEmpty(actResults)) {
            return actResultInfo;
        }

        //key-- type_roleType value-- memberId 成员id集合
        Map<String, List<String>> roleTypeMemberId = Maps.newHashMap();
        actResults.forEach(x -> {
            if (StringUtil.isBlank(x.getMemberId())) {
                return;
            }
            String key = x.getType() + "_" + x.getRoleType();
            List<String> memberIds = roleTypeMemberId.getOrDefault(key, Lists.newArrayList());
            memberIds.add(x.getMemberId());
            roleTypeMemberId.put(key, memberIds);
        });

        //key-- type_roleType, key memberId, value-- memberId 成员昵称头像信息
        Map<String, Map<String, MemberInfo>> roleTypeMemberInfo = queryMemberInfo(actId, roleTypeMemberId);


        Map<String, ActResultGroupVo> groupVoMap = Maps.newLinkedHashMap();
        for (ActResultGroup group : actResultGroups) {

            ActResultGroupVo actResultGroupVo = new ActResultGroupVo();
            actResultGroupVo.setGroupId(group.getGroupId());
            actResultGroupVo.setGroupName(group.getGroupName());
            actResultGroupVo.setType(group.getType());

            List<ActResultVo> actResultVos = Lists.newArrayList();
            //分组成员信息
            actResults.stream().filter(x -> x.getGroupId().equals(group.getGroupId())).forEach(item -> {
                //List<ActResultVo> items = Lists.newArrayList();
                ActResultVo actResultVo = new ActResultVo();
                String memberId = item.getMemberId();
                int type = item.getType();
                if (StringUtil.isBlank(memberId)) {
                    //ActResultVo empty = new ActResultVo();
                    actResultVo.setTitle(item.getTitle());
                    actResultVo.setRank(item.getRank());
                    //items.add(empty);
                    if (item.getRoleType() == 100200) {
                        ActResultVo empty2 = new ActResultVo();
                        empty2.setTitle(item.getTitle());
                        empty2.setRank(item.getRank());
                        //items.add(empty);
                    }
                    //todo 暂不处理cp  处理cp会变成二维数组,前端难于处理
                } else if (memberId.contains("|")) {
                    String[] memberIds = memberId.split("\\|");

                    String userUid = memberIds[0];
                    ActResultVo userInfo = buildActResult(actId, userUid, item, status);
                    MemberInfo userMemberInfo = roleTypeMemberInfo
                            .getOrDefault(type + "_" + RoleType.USER.getValue(), Maps.newHashMap())
                            .getOrDefault(userUid, null);
                    if (userMemberInfo != null) {
                        userInfo.setNick(convert2Nick(userMemberInfo.getName(), userUid));
                        userInfo.setAvatarInfo(userMemberInfo.getLogo());
                    }
                    //items.add(userInfo);

                    String anchorUid = memberIds[1];
                    ActResultVo anchorInfo = buildActResult(actId, anchorUid, item, status);
                    MemberInfo anchorMemberInfo = roleTypeMemberInfo
                            .getOrDefault(type + "_" + RoleType.ANCHOR.getValue(), Maps.newHashMap())
                            .getOrDefault(anchorUid, null);
                    if (anchorMemberInfo != null) {
                        anchorInfo.setNick(convert2Nick(anchorMemberInfo.getName(), anchorUid));
                        anchorInfo.setAvatarInfo(anchorMemberInfo.getLogo());
                    }
                    //items.add(anchorInfo);

                } else {
                    actResultVo = buildActResult(actId, memberId, item, status);
                    MemberInfo memberInfo = roleTypeMemberInfo
                            .getOrDefault(type + "_" + item.getRoleType(), Maps.newHashMap())
                            .getOrDefault(memberId, null);
                    if (memberInfo != null) {
                        actResultVo.setNick(convert2Nick(memberInfo.getName(), memberId));
                        actResultVo.setAvatarInfo(memberInfo.getLogo());
                    }
                    //主播增加开播状态
                    if (actResultVo.getRoleType() == 200) {
                        ChannelInfoVo channelInfoVo = onMicService.getOnMicChannel(Convert.toLong(actResultVo.getMemberId(), 0));
                        if (channelInfoVo != null) {
                            actResultVo.setSid(channelInfoVo.getSid());
                            actResultVo.setSsid(channelInfoVo.getSsid());
                        }
                    }
                    //items.add(actResultVo);
                }
                actResultVos.add(actResultVo);
            });
            actResultGroupVo.setActResults(actResultVos);
            groupVoMap.put(group.getGroupId(), actResultGroupVo);
        }

        actResultInfo.setGroupVoMap(groupVoMap);
        return actResultInfo;
    }

    public List<ActResult> listActResult(long actId, List<Integer> types) {
        ActResultGroup groupWhere = new ActResultGroup();
        groupWhere.setActId(actId);
        String groupAfterWhere = String.format(" and type in (%s) order by sort asc ", StringUtils.join(types, ","));
        List<ActResultGroup> actResultGroups = gameecologyDao.select(ActResultGroup.class, groupWhere, groupAfterWhere);
        if (CollectionUtils.isEmpty(actResultGroups)) {
            return null;
        }

        ActResult where = new ActResult();
        where.setActId(actId);
        String afterWhere = String.format("and type in (%s)  order by show_order, rank asc ", StringUtils.join(types, ","));

        return gameecologyDao.select(ActResult.class, where, afterWhere);
    }

    /**
     * 和 queryActResult() 的区别是返回的元素全部是 数组的数组
     *
     * @param actId  活动id
     * @param status -1的时候查询所有的,给产品预览用,其他查询已发布状态的
     * @return
     */
    @Cached(timeToLiveMillis = 10 * 1000L)
    public ActResultInfoNew queryActResultNew(long actId, List<Integer> types, int status) {
        ActResultInfoNew actResultInfo = new ActResultInfoNew();

        ActResultGroup groupWhere = new ActResultGroup();
        groupWhere.setActId(actId);
        String groupAfterWhere = String.format(" and type in (%s) order by sort asc ", StringUtils.join(types, ","));
        List<ActResultGroup> actResultGroups = gameecologyDao.select(ActResultGroup.class, groupWhere, groupAfterWhere);
        if (CollectionUtils.isEmpty(actResultGroups)) {
            return actResultInfo;
        }

        ActResult where = new ActResult();
        where.setActId(actId);
        String afterWhere = String.format("and type in (%s)  order by show_order, rank asc ", StringUtils.join(types, ","));
        List<ActResult> actResults = gameecologyDao.select(ActResult.class, where, afterWhere);
        if (CollectionUtils.isEmpty(actResults)) {
            return actResultInfo;
        }

        //key-- type_roleType value-- memberId 成员id集合
        Map<String, List<String>> roleTypeMemberId = Maps.newHashMap();
        actResults.forEach(x -> {
            if (StringUtil.isBlank(x.getMemberId())) {
                return;
            }
            String key = x.getType() + "_" + x.getRoleType();
            List<String> memberIds = roleTypeMemberId.getOrDefault(key, Lists.newArrayList());
            memberIds.add(x.getMemberId());
            roleTypeMemberId.put(key, memberIds);

            //添加贡献榜成员
            if (!StringUtil.isBlank(x.getContributor()) && x.getContributor().contains(HonorHallRankComponent.HonorHallContributeRank.EXT_KEY)) {
                JSONObject extJo = JSONObject.parseObject(x.getContributor());
                HonorHallRankComponent.HonorHallContributeRank honorHallContributeRank =
                        JsonUtil.toObject(extJo.getString(HonorHallRankComponent.HonorHallContributeRank.EXT_KEY), HonorHallRankComponent.HonorHallContributeRank.class);
                if (honorHallContributeRank != null) {
                    key = x.getType() + "_" + honorHallContributeRank.getRoleType();
                    List<String> contributeMembers = honorHallContributeRank.getMemberIds();
                    memberIds = roleTypeMemberId.getOrDefault(key, Lists.newArrayList());
                    memberIds.addAll(contributeMembers);
                    roleTypeMemberId.put(key, memberIds);
                }
            }
        });

        //key-- type_roleType, key memberId, value-- memberId 成员昵称头像信息
        Map<String, Map<String, MemberInfo>> roleTypeMemberInfo = queryMemberInfo(actId, roleTypeMemberId);

        Map<Long, TRoomContract> roomContractMap =
                turnoverContractServiceThriftClient.queryRoomContract();
        Set<Long> sids = new HashSet<>();
        for (Long sid : roomContractMap.keySet()) {
            sids.add(roomContractMap.get(sid).getSid());
        }
        Map<Long, WebdbChannelInfo> sidChannelInfos = webdbThriftClient.batchGetChannelInfo(Lists.newArrayList(sids));
        //多昵称
        Set<Long> uids = getAllUserList(roleTypeMemberId);
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers,810);

        Map<String, ActResultGroupVoNew> groupVoMap = Maps.newLinkedHashMap();
        for (ActResultGroup group : actResultGroups) {

            ActResultGroupVoNew actResultGroupVo = new ActResultGroupVoNew();
            actResultGroupVo.setGroupId(group.getGroupId());
            actResultGroupVo.setGroupName(group.getGroupName());
            actResultGroupVo.setType(group.getType());

            ActResult firstResult = actResults.stream().filter(x -> x.getGroupId().equals(group.getGroupId())).findFirst().orElse(null);
            if(firstResult!=null){
                actResultGroupVo.setRoleType(firstResult.getRoleType());
            }

            List<List<ActResultVo>> actResultVos = Lists.newArrayList();
            //分组成员信息
            actResults.stream().filter(x -> x.getGroupId().equals(group.getGroupId())).forEach(item -> {
                List<ActResultVo> items = Lists.newArrayList();
                ActResultVo actResultVo = new ActResultVo();
                String memberId = item.getMemberId();
                int type = item.getType();
                if (StringUtil.isBlank(memberId)) {
                    actResultVo.setTitle(item.getTitle());
                    actResultVo.setRank(item.getRank());
                    items.add(actResultVo);
                    if (item.getRoleType() == 100200) {
                        ActResultVo empty = new ActResultVo();
                        empty.setTitle(item.getTitle());
                        empty.setRank(item.getRank());
                        items.add(empty);
                    }
                } else if (memberId.contains("|")) {
                    //todo 暂不处理cp  处理cp会变成二维数组,前端难于处理
                    String[] memberIds = memberId.split("\\|");

                    String userUid = memberIds[0];
                    ActResultVo userInfo = buildActResult(actId, userUid, item, status);
                    MemberInfo userMemberInfo = roleTypeMemberInfo
                            .getOrDefault(type + "_" + RoleType.USER.getValue(), Maps.newHashMap())
                            .getOrDefault(userUid, null);
                    if (userMemberInfo != null) {
                        userInfo.setNick(convert2Nick(userMemberInfo.getName(), userUid));
                        userInfo.setAvatarInfo(userMemberInfo.getLogo());
                    }
                    items.add(userInfo);

                    String anchorUid = memberIds[1];
                    ActResultVo anchorInfo = buildActResult(actId, anchorUid, item, status);
                    MemberInfo anchorMemberInfo = roleTypeMemberInfo
                            .getOrDefault(type + "_" + RoleType.ANCHOR.getValue(), Maps.newHashMap())
                            .getOrDefault(anchorUid, null);
                    if (anchorMemberInfo != null) {
                        anchorInfo.setNick(convert2Nick(anchorMemberInfo.getName(), anchorUid));
                        anchorInfo.setAvatarInfo(anchorMemberInfo.getLogo());
                    }
                    items.add(anchorInfo);

                } else {
                    actResultVo = buildActResult(actId, memberId, item, status);
                    MemberInfo memberInfo = roleTypeMemberInfo
                            .getOrDefault(type + "_" + item.getRoleType(), Maps.newHashMap())
                            .getOrDefault(memberId, null);
                    if (memberInfo != null) {
                        actResultVo.setNick(convert2Nick(memberInfo.getName(), memberId));
                        actResultVo.setAvatarInfo(memberInfo.getLogo());
                        actResultVo.setAsid(memberInfo.getAsid());
                        if (memberId.contains("_")) {
                            long ssid = Convert.toLong(memberId.split("_")[1]);
                            if (roomContractMap.containsKey(ssid)) {
                                TRoomContract tRoomContract = roomContractMap.get(ssid);
                                if (StringUtil.isEmpty(sidChannelInfos.get(tRoomContract.getSid()).getAsid())) {
                                    actResultVo.setAsid(tRoomContract.getSid() + "");
                                } else {
                                    actResultVo.setAsid(sidChannelInfos.get(tRoomContract.getSid()).getAsid());
                                }
                            }
                        }
                    }
                    //贡献榜头像
                    List<String> contributeAvatars = getContributeAvatars(actResultVo, roleTypeMemberInfo);
                    if (CollectionUtils.isNotEmpty(contributeAvatars)) {
                        actResultVo.setContributeAvatars(contributeAvatars);
                    }
                    //主播增加开播状态
                    if (actResultVo.getRoleType() == 200) {
                        ChannelInfoVo channelInfoVo = onMicService.getOnMicChannel(Convert.toLong(actResultVo.getMemberId(), 0));
                        if (channelInfoVo != null) {
                            actResultVo.setSid(channelInfoVo.getSid());
                            actResultVo.setSsid(channelInfoVo.getSsid());
                        }
                    }
                    items.add(actResultVo);
                }
                actResultVos.add(items);
            });
            actResultGroupVo.setActResults(actResultVos);


            groupVoMap.put(group.getGroupId(), actResultGroupVo);
        }

        actResultInfo.setGroupVoMap(groupVoMap);
        actResultInfo.setNickExtUsers(multiNickUsers);
        return actResultInfo;
    }


    private Set<Long> getAllUserList(Map<String, List<String>> roleTypeMemberId) {
        Set<Long> uids =  new HashSet<>();
        for (String key : roleTypeMemberId.keySet()) {
            String[] keyArray = key.split("_");
            int roleType = Convert.toInt(keyArray[1], 0);
            List<String> memberIds = roleTypeMemberId.get(key);

            if(roleType==100200){
                memberIds.forEach(x -> {
                    String[] array = x.split("\\|");
                    long userUid = Convert.toLong(array[0], 0), anchorUid = Convert.toLong(array[1], 0);
                    if (userUid > 0) {
                        uids.add(userUid);
                    }

                    if (anchorUid > 0) {
                        uids.add(anchorUid);
                    }
                });
            }else if (roleType == RoleType.ANCHOR.getValue()
                    || roleType == RoleType.USER.getValue()){
                memberIds.forEach(x -> {
                    uids.add(Convert.toLong(x));
                });
            }
        }

        return uids;
    }
    private List<String> getContributeAvatars(ActResultVo item, Map<String, Map<String, MemberInfo>> roleTypeMemberInfo) {
        List<String> contributeAvatars = Lists.newArrayList();
        if (!StringUtil.isBlank(item.getContributor()) &&
                item.getContributor().contains(HonorHallRankComponent.HonorHallContributeRank.EXT_KEY)) {
            JSONObject extJo = JSONObject.parseObject(item.getContributor());
            HonorHallRankComponent.HonorHallContributeRank honorHallContributeRank =
                    JsonUtil.toObject(extJo.getString(HonorHallRankComponent.HonorHallContributeRank.EXT_KEY),
                            HonorHallRankComponent.HonorHallContributeRank.class);
            if (honorHallContributeRank != null) {
                String key = item.getType() + "_" + honorHallContributeRank.getRoleType();
                List<String> contributeMembers = honorHallContributeRank.getMemberIds();
                for (String contributeMember : contributeMembers) {
                    MemberInfo memberInfo = roleTypeMemberInfo
                            .getOrDefault(key, Maps.newHashMap())
                            .getOrDefault(contributeMember, null);
                    if (memberInfo != null) {
                        contributeAvatars.add(memberInfo.getLogo());
                    }
                }
            }
        }

        return contributeAvatars;
    }

    public String convert2Nick(String nick, String memberId) {
        if (true) {
            return nick;
        }
        if (nick == null) {
            nick = "";
        }
        return "【" + memberId + "】" + nick;
    }

    public ActResultVo buildActResult(long actId, String realMemberId, ActResult item, int statusPara) {
        ActResultVo actResultVo = new ActResultVo();
        if (!Const.ONE.equals(item.getStatus()) && statusPara != -1) {
            actResultVo.setMemberId("");
        } else {
            actResultVo.setMemberId(realMemberId);
        }
        actResultVo.setType(item.getType());
        actResultVo.setTitle(item.getTitle());
        actResultVo.setRoleType(item.getRoleType());
        actResultVo.setRank(item.getRank());
        actResultVo.setExtData(item.getExtData());
        actResultVo.setContributor(item.getContributor());
        EnrollmentInfo enrollmentInfo = enrollmentService.getFirstEnrolMember(actId, 0L, item.getRoleType() == 100200 ? 200 : item.getRoleType(), realMemberId);
        if (enrollmentInfo != null) {
            actResultVo.setBusiId(enrollmentInfo.getRoleBusiId());
            actResultVo.setRoleId(enrollmentInfo.getDestRoleId());
        }
        return actResultVo;
    }

    /**
     * 查询成员信息
     *
     * @param roleTypeMemberId // key-- type_roleType value-- memberId 成员id集合
     * @return key-- type_roleType, key memberId, value-- memberId 成员昵称头像信息
     */
    public Map<String, Map<String, MemberInfo>> queryMemberInfo(long actId, Map<String, List<String>> roleTypeMemberId) {
        Map<String, Map<String, MemberInfo>> memberMap = Maps.newHashMap();
        for (String key : roleTypeMemberId.keySet()) {
            String[] keyArray = key.split("_");
            int type = Convert.toInt(keyArray[0], 0);
            int roleType = Convert.toInt(keyArray[1], 0);
            List<String> memberIds = roleTypeMemberId.get(key);

            ActResultMemberLoader actResultMemberLoader = actResultFactory.getActResultMemberBuilder(actId, type, roleType);
            Map<String, Map<String, MemberInfo>> result = actResultMemberLoader.loadMemberInfo(actId, type, roleType, memberIds);
            if (MapUtils.isNotEmpty(result)) {
                //人工合并map
                for (String resultKey : result.keySet()) {
                    if (memberMap.containsKey(resultKey)) {
                        Map<String, MemberInfo> oriMember = memberMap.get(resultKey);
                        oriMember.putAll(result.get(resultKey));
                        memberMap.put(resultKey, oriMember);

                    } else {
                        memberMap.put(resultKey, result.get(resultKey));
                    }
                }
            }

        }

        return memberMap;
    }

    private static final Map<Integer, Integer> TYPE_2_BUSI_ID_MAP = Maps.newHashMap();

    static {
        TYPE_2_BUSI_ID_MAP.put(1, BusiId.MAKE_FRIEND.getValue());
    }

    /**
     * @param type 1-交友 2-约战 3-宝贝 4-互动全品类 5-陪玩
     **/
    private ActSupportService getActSupportService(int type) {
        Integer busiId = TYPE_2_BUSI_ID_MAP.get(type);
        if (busiId != null) {
            return ActSupportService.getInstance(busiId);
        }

        return null;
    }

    public int[] batchUpdate(List<ActResult> actResults) {
        return gameecologyDao.batchActResultUpdate(actResults);
    }

    @PostConstruct
    @Scheduled(cron = "0 0/2 * * * ? ")
    @ScheduledExt(historyRun = true)
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    public void loadInShowTimeActResult2Cache() {
        List<ActivityInfoVo> activityInfoVos = hdztRankingThriftClient.queryEffectActInfos();
        List<Long> actIds = Lists.newArrayList();
        activityInfoVos.forEach(act -> {
            Date now = commonService.getNow(act.getActId());
            if (actInfoService.inActShowTime(now, act)) {
                actIds.add(act.getActId());
            }
        });
        if (CollectionUtils.isEmpty(actIds)) {
            IN_SHOW_TIME_HAS_VALUE_ACT_RESULT = Maps.newHashMap();
            return;
        }

        String afterWhere = String.format(" and act_id in (%s)", StringUtils.join(actIds, ","));
        List<ActResult> actResults = gameecologyDao.select(ActResult.class, null, afterWhere);
        if (CollectionUtils.isEmpty(actResults)) {
            IN_SHOW_TIME_HAS_VALUE_ACT_RESULT = Maps.newHashMap();
        }

        Map<String, List<ActResult>> actResultMap = Maps.newHashMap();
        for (ActResult item : actResults) {
            if (StringUtil.isEmpty(item.getMemberId())) {
                continue;
            }
            String key = buildInShowTimeResultKey(item.getActId(), item.getGroupId(), item.getRoleType(), item.getMemberId());
            List<ActResult> memberResult = actResultMap.getOrDefault(key, Lists.newArrayList());
            memberResult.add(item);
            actResultMap.put(key, memberResult);
        }

        IN_SHOW_TIME_HAS_VALUE_ACT_RESULT = actResultMap;

    }

    /**
     * 有多个的时候返回第一个
     */
    public ActResult queryInShowTimeLayerResultCache(long actId, String groupId, long roleType, String memberId) {
        String key = buildInShowTimeResultKey(actId, groupId, roleType, memberId);
        List<ActResult> results = IN_SHOW_TIME_HAS_VALUE_ACT_RESULT.get(key);
        if (CollectionUtils.isEmpty(results)) {
            return null;
        }
        for (ActResult item : results) {
            if (item.getType() == ActResultType.LAYER_HONOR_RESULT) {
                return item;
            }
        }
        return null;
    }

    private String buildInShowTimeResultKey(long actId, String groupId, long roleType, String memberId) {
        return actId + "_" + groupId + "_" + roleType + "_" + memberId;
    }
}
