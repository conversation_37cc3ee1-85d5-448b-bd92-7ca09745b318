package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.acttask.TaskShowItemVo;
import com.yy.gameecology.activity.bean.hdzt.RankPhasePair;
import com.yy.gameecology.activity.bean.hdzt.TaskUserInfoVo;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.common.utils.Convert;
import com.yy.thrift.hdztranking.QueryUserTaskResponse;
import com.yy.thrift.hdztranking.UserTaskItem;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-30 14:07
 **/
@Service
public class HdztTaskService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private UserTaskService userTaskService;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    /**
     * 读取当前任务完成情况
     *
     * @param uid
     * @param actId
     * @param rankId
     * @param phaseId
     * @return
     */
    public TaskUserInfoVo getUserTaskInfo(Long uid, Long actId, Long rankId, Long phaseId, String dateStr) {
        if (Convert.toLong(uid, 0) == 0) {
            return null;
        }
        return userTaskService.getUserTaskInfo(uid, actId, rankId, phaseId, dateStr);
    }

    /**
     * 读取用户多个任务
     */
    public List<TaskUserInfoVo> getUserTaskInfos(long uid, long actId, List<RankPhasePair> rankPhasePair, String dateStr) {
        List<TaskUserInfoVo> taskUserInfoVos = Lists.newArrayList();
        for (RankPhasePair item : rankPhasePair) {
            TaskUserInfoVo vo = userTaskService.getUserTaskInfo(uid, actId, item.getRankId(), item.getPhaseId(), dateStr);
            if (vo == null) {
                continue;
            }
            vo.setRankId(item.getRankId());
            vo.setPhaseId(item.getPhaseId());
            taskUserInfoVos.add(vo);
        }

        return taskUserInfoVos;
    }

    public List<TaskShowItemVo> queryTaskItem(long actId, long rankId, long phaseId, String dateCode, String member) {
        QueryUserTaskResponse response = hdztRankingThriftClient.queryUserTaskInfo(actId, rankId, phaseId, dateCode, member);
        log.info("MultipleTaskShowComponent queryTaskItem actId:{},rankId:{},phaseId:{},dateCode:{},member:{},response:{}",actId,rankId,phaseId,dateCode,member, JSON.toJSON(response));
        Map<String, UserTaskItem> itemMap = response.getItems();
        if (MapUtils.isEmpty(itemMap)) {
            return Lists.newArrayList();
        }
        List<TaskShowItemVo> itemVos = Lists.newArrayList();
        for (String itemId : itemMap.keySet()) {
            UserTaskItem taskItem = itemMap.get(itemId);

            TaskShowItemVo showItemVo = new TaskShowItemVo();
            showItemVo.setItem(taskItem.getItemName());
            showItemVo.setItemId(taskItem.getItemId());
            showItemVo.setCompletedCount(taskItem.getCurTaskScore());
            showItemVo.setTaskTotalCount(taskItem.getCurTaskScoreConfig());
            showItemVo.setAllTaskTotalCount(taskItem.getAllTaskScoreConfig());
            showItemVo.setTaskName(taskItem.getTaskName());
            showItemVo.setTaskExtJson(taskItem.getTaskExtJson());
            itemVos.add(showItemVo);

        }
        return itemVos;
    }
}
