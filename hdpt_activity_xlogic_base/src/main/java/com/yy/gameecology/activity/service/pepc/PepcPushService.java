package com.yy.gameecology.activity.service.pepc;

import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.CommonService;

import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.PepcConst;

import com.yy.gameecology.common.db.mapper.pepc.*;
import com.yy.gameecology.common.db.model.gameecology.aov.AovGame;

import com.yy.gameecology.common.db.model.gameecology.pepc.PepcTeam;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.element.component.attr.PepcActPushComponentAttr;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class PepcPushService {

    @Resource
    private PepcTeamMapper pepcTeamMapper;

    @Resource
    private PepcTeamMemberMapper pepcTeamMemberMapper;

    @Resource
    private PepcMatchAwardRecordExtMapper pepcMatchAwardRecordExtMapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    private static final String MSG_TPL = """
            数据日期：${statDate}
            【本期赛事数据】
            预约人数（去重）：${subscribedCnt}
            报名人数（去重）：${signupCnt}
            成功报名队伍数：${signupSucTeamCnt}
            成功参赛人数（去重）：${gamedCnt}
            【本期赛事数据】
            冠军获奖人数：${round1AwardCnt}
            亚军获奖人数：${round2AwardCnt}
            4强获奖人数：${round3AwardCnt}
            8强获奖人数：${round4AwardCnt}
            【营收数据】
            首胜奖励成功领取人数：${firstWinCnt}
            历史Q币奖励成功领取人数：${awardCnt}
            历史Q币奖励成功领取金额：${awardAmount}
            """;

    private static final String GAME_LIVE = """
            比赛：${roundName}
            蓝方：${team1}
            红方：${team2}
            时间：${startTime}
            地址：${liveUrl}
            """;


    @Autowired
    private PepcPhaseSubscribeMapper pepcPhaseSubscribeMapper;
    @Autowired
    private PepcGameMemberMapper pepcGameMemberMapper;

    public void sendGameLiveInfo(long actId, AovGame game, String url) {
        List<PepcTeam> teams = pepcTeamMapper.selectByIds(List.of(game.getCamp1TeamId(), game.getCamp2TeamId()));
        String team1 = teams.stream().filter(team -> Objects.equals(team.getId(), game.getCamp1TeamId())).map(PepcTeam::getTeamName).findFirst().orElse("队伍1");
        String team2 = teams.stream().filter(team -> Objects.equals(team.getId(), game.getCamp2TeamId())).map(PepcTeam::getTeamName).findFirst().orElse("队伍2");
        String startTime = DateFormatUtils.format(game.getStartTime(), DateUtil.DEFAULT_PATTERN);

        TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig());
        Template template = engine.getTemplate(GAME_LIVE);
        String msg = template.render(Map.of("roundName", game.getRoundName(), "team1", team1, "team2", team2, "startTime", startTime, "liveUrl", url));

        String infoflowMsg = commonService.buildActRuliuMsg(actId, false, "王者荣耀巅峰赛直播信息", msg);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT, infoflowMsg, Collections.emptyList());
    }

    public ReportData getReportData(PepcActPushComponentAttr attr, Date now) {

        ReportData result = new ReportData();
        final long actId = attr.getActId();
        result.setActId(actId);
        result.setStatDate(DateFormatUtils.format(now, DateUtil.PATTERN_TYPE2));
        result.setSubscribedCnt(getSubscribedCnt(actId));
        result.setSignupCnt(getSignupCnt(actId));
        result.setSignupSucTeamCnt(getSignupSucTeamCnt(actId));
        result.setGamedCnt(getGamedCnt(actId));
        result.setRound1AwardCnt(getRoundAwardCnt(attr.getActId(), Arrays.asList(1L)));
        result.setRound2AwardCnt(getRoundAwardCnt(attr.getActId(), Arrays.asList(2L)));
        result.setRound3AwardCnt(getRoundAwardCnt(attr.getActId(), Arrays.asList(3L,4L)));
        result.setRound4AwardCnt(getRoundAwardCnt(attr.getActId(), Arrays.asList(5L,6L,7L,8L)));
        result.setFirstWinCnt(getFirstWinCnt(attr.getActId()));
        result.setAwardCnt(getAwardCnt(attr.getActId()));
        result.setAwardAmount(getAwardAmount(attr.getActId()));

        return result;
    }

    public void sendStatisticNotice(PepcActPushComponentAttr attr, Date now) {
        ReportData data = getReportData(attr, now);
        if (data == null) {
            return;
        }
        TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig());
        Template template = engine.getTemplate(MSG_TPL);
        String msg = template.render(JSON.parseObject(JSON.toJSONString(data)));

        String infoflowMsg = commonService.buildActRuliuMsg(attr.getActId(), false, "和平精英荣耀巅峰赛日报", msg);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_ACT_MOBILE_USER_GROWTH, infoflowMsg, Collections.emptyList());
    }

    public long getSubscribedCnt(long actId) {
        return pepcPhaseSubscribeMapper.countSubscribes(actId);
    }

    public long getSignupCnt(long actId) {
        return pepcTeamMemberMapper.countMembers(actId, null);
    }

    public long getSignupSucTeamCnt(long actId) {
        return pepcTeamMapper.countTeam(actId, null, 4);
    }

    public long getGamedCnt(long actId) {
        return pepcGameMemberMapper.countSucGameUidCount(actId,80L);
    }

    // 首杀
    public long getFirstWinCnt(long actId) {
        return pepcMatchAwardRecordExtMapper.countUserAwardRecord(actId, null,  PepcConst.AwardType.FIRST_GAME, null, PepcConst.AwardState.FINISH);
    }

    // todo
    public long getAwardCnt(long actId) {
        return pepcMatchAwardRecordExtMapper.countUserAwardRecord(actId, null, null, null, PepcConst.AwardState.FINISH);
    }

    // todo
    public long getAwardAmount(long actId) {
        return pepcMatchAwardRecordExtMapper.sumAwardAmount(actId, null, null, null,  PepcConst.AwardState.FINISH);
    }

    // pepc_award_record RANK 1~8
    public long getRoundAwardCnt(long actId, List<Long> roundNum) {
        return pepcMatchAwardRecordExtMapper.CountAwardRankingNum(actId, roundNum);
    }

    @Getter
    @Setter
    public static class ReportData {
        protected long actId;

        protected String statDate;

        /**
         * 本期订阅（预约）人数
         */
        protected long subscribedCnt;

        /**
         * 本期报名（创建队伍 + 加入队伍）人数
         */
        protected long signupCnt;

        /**
         * 本期报名成功队伍数
         */
        protected long signupSucTeamCnt;

        /**
         * 本期成功完成比赛队伍数
         */
//        protected long gamedTeamCnt;

        /**
         * 本期成功参赛uid数
         */
        protected long gamedCnt;

        /**
         * 获得冠军奖励人数
         */
        protected long round1AwardCnt;

        /**
         * 获得亚军奖励人数
         */
        protected long round2AwardCnt;

        /**
         * 获得4强奖励人数
         */
        protected long round3AwardCnt;

        /**
         * 获得8强奖励人数
         */
        protected long round4AwardCnt;

        /**
         * 获得16强奖励人数
         */
        protected long round5AwardCnt;

        /**
         * 获得32强奖励人数
         */
        protected long round6AwardCnt;

        /**
         * 成功领取首胜奖励的人数
         */
        protected long firstWinCnt;

        /**
         * 成功领取奖励的人数
         */
        protected long awardCnt;

        /**
         * 成功领取奖励的金额（点券）
         */
        protected long awardAmount;
    }
}
