package com.yy.gameecology.activity.config.kafka;

import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.MDCUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

/**
 * <AUTHOR> 2021/4/19
 */
@Configuration
public class KafkaConfig {

    private static final Logger log = LoggerFactory.getLogger(KafkaConfig.class);

    /* ################################ 活动平台kafka ################################## */

    @Bean
    @ConfigurationProperties("kafka.zhuiya")
    public KafkaProperties zhuiyaKafkaProperties() {
        return new KafkaProperties();
    }

    @Bean
    public KafkaTemplate<String, String> zhuiyaKafkaTemplate(KafkaProperties zhuiyaKafkaProperties) {
        return createKafkaTemplate(zhuiyaKafkaProperties);
    }

    @Bean
    @ConfigurationProperties("kafka.hdzt-wx")
    public KafkaProperties hdztWxKafkaProperties() {
        return new KafkaProperties();
    }

    @Bean
    public KafkaListenerContainerFactory hdztWxContainerFactory(KafkaProperties hdztWxKafkaProperties) {
        return createContainerFactory(hdztWxKafkaProperties);
    }

    @Bean
    public KafkaTemplate<String, String> hdztWxKafkaTemplate(KafkaProperties hdztWxKafkaProperties) {
        return createKafkaTemplate(hdztWxKafkaProperties);
    }

    @Bean
    @ConfigurationProperties("kafka.hdzt-sz")
    public KafkaProperties hdztSzKafkaProperties() {
        return new KafkaProperties();
    }

    @Bean
    public KafkaListenerContainerFactory hdztSzContainerFactory(KafkaProperties hdztSzKafkaProperties) {
        return createContainerFactory(hdztSzKafkaProperties);
    }

    @Bean
    public KafkaTemplate<String, String> hdztSzKafkaTemplate(KafkaProperties hdztSzKafkaProperties) {
        return createKafkaTemplate(hdztSzKafkaProperties);
    }

    private KafkaTemplate<String, String> createKafkaTemplate(KafkaProperties kafkaProperties) {
        ProducerFactory<String, String> producerFactory = new DefaultKafkaProducerFactory<>(kafkaProperties.buildProducerProperties());
        return new KafkaTemplate<>(producerFactory);
    }


    /* ################################ yy jiaoyou event ################################## */

    @Bean
    @ConfigurationProperties("kafka.jiaoyou-wx")
    public KafkaProperties jiaoyouWxKafkaProperties() {
        return new KafkaProperties();
    }


    @Bean
    public KafkaTemplate<String, String> jiaoyouWxKafkaTemplate(KafkaProperties jiaoyouWxKafkaProperties) {
        return createKafkaTemplate(jiaoyouWxKafkaProperties);
    }


    @Bean
    @ConfigurationProperties("kafka.jiaoyou-sz")
    public KafkaProperties jiaoyouSzKafkaProperties() {
        return new KafkaProperties();
    }


    @Bean
    public KafkaTemplate<String, String> jiaoyouSzKafkaTemplate(KafkaProperties jiaoyouSzKafkaProperties) {
        return createKafkaTemplate(jiaoyouSzKafkaProperties);
    }
    
    @Bean
    @ConfigurationProperties("kafka.jiaoyou.cross")
    public KafkaProperties jiaoyouCrossKafkaProperties() {
        return new KafkaProperties();
    }
    
    
    @Bean
    public KafkaTemplate<String, String> jiaoyouCrossKafkaTemplate(KafkaProperties jiaoyouCrossKafkaProperties) {
        return createKafkaTemplate(jiaoyouCrossKafkaProperties);
    }


    /* ################################ 追玩 topic event ################################## */
    @Bean
    @ConfigurationProperties("kafka.zhuiwan")
    public KafkaProperties zhuiwanKafkaProperties() {
        return new KafkaProperties();
    }

    @Bean
    public KafkaTemplate<String, String> zhuiwanKafkaTemplate(KafkaProperties zhuiwanKafkaProperties) {
        return createKafkaTemplate(zhuiwanKafkaProperties);
    }

    @Bean
    public KafkaListenerContainerFactory zhuiwanContainerFactory(KafkaProperties zhuiwanKafkaProperties) {
        return createContainerFactory(zhuiwanKafkaProperties);
    }

    /**
     * 消费者抛出异常, 会自动重试, 重试超过上限, 会塞到死信队列
     *
     * @param kafkaProperties
     * @return
     */
    public static ConcurrentKafkaListenerContainerFactory createContainerFactory(KafkaProperties kafkaProperties) {
        ConcurrentKafkaListenerContainerFactory containerFactory = new ConcurrentKafkaListenerContainerFactory();
        if (SysEvHelper.isLocal() || SysEvHelper.isYuFa()) {
            log.warn("当前环境默认不启动Consumer.");
            containerFactory.setAutoStartup(false);
        }
        //设置过大容易导致负载压在某台机器上
        containerFactory.setConcurrency(2);
        //消费失败不自动提交
        kafkaProperties.getConsumer().setEnableAutoCommit(false);
        //消费失败重试处理
//        containerFactory.setErrorHandler(kafkaErrorHandler());
        //消费者工厂
        containerFactory.setConsumerFactory(new DefaultKafkaConsumerFactory(kafkaProperties.buildConsumerProperties()));
        //日志追踪
        containerFactory.setRecordInterceptor(record -> {
            String topic = record.topic();
            MDCUtils.putContext("kafka=" + topic);
            return record;
        });
        return containerFactory;
    }
//
//    @Bean
//    public ErrorHandler kafkaErrorHandler() {
//        return new SeekToCurrentErrorHandler(deadMessageHandler());
//    }
//
//    @Bean
//    public KafkaDeadMessageHandler deadMessageHandler() {
//        return new ConfirmDeadMessageHandler();
//    }
}
