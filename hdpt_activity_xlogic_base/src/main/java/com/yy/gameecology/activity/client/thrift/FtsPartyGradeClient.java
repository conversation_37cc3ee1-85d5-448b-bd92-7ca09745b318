package com.yy.gameecology.activity.client.thrift;

import com.yy.thrift.fts_party_grade.FtsPartyGradeService;
import com.yy.thrift.fts_party_grade.UserCreditResp;
import com.yy.thrift.fts_party_grade.commonResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2023/8/24
 */
@Slf4j
@Component
public class FtsPartyGradeClient {

    @Reference(protocol = "nythrift", owner = "${thrift_fts_party_grade_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"})
    private FtsPartyGradeService.Iface proxy;

    @Reference(protocol = "nythrift", owner = "${thrift_fts_party_grade_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"},
            retries = 2 , cluster = "failover")
    private FtsPartyGradeService.Iface readProxy;

    public long queryUserCreditInfo(long uid) {
        try {
            UserCreditResp resp = readProxy.QueryUserCreditInfo(uid);
            log.info("queryUserCreditInfo uid:{} resp:{}", uid, resp);
            if (resp.ret == 0) {
                return resp.credit;
            }
            if (resp.ret == 1) {
                log.error("queryUserCreditInfo error,req:{},rsp:{}", uid, resp);
                return -1;
            }
        } catch (Exception e) {
            log.error("queryUserCreditInfo uid:{} err:{}", uid, e.getMessage(), e);
        }
        throw new RuntimeException("查询信用分失败");
    }

    public void updateUserCreditInfo(long uid) {
        try {
            commonResp resp = proxy.UpdateUserCreditInfo(uid);
            if (resp == null || resp.getRet() != 0) {
                log.error("UpdateUserCreditInfo error,uid:{},rsp:{}", uid, resp);
            }
            log.info("updateUserCreditInfo uid:{} resp:{}", uid, resp);
        } catch (Exception e) {
            log.error("updateUserCreditInfo uid:{} err:{}", uid, e.getMessage(), e);
        }
    }

    public FtsPartyGradeService.Iface getProxy() {
        return proxy;
    }

    public FtsPartyGradeService.Iface getReadProxy() {
        return readProxy;
    }
}
