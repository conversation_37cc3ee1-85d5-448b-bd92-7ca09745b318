package com.yy.gameecology.activity.client.thrift;

import com.yy.thrift.turnover.TUserAccount;
import com.yy.thrift.turnover.TUserAccountService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-17 15:08
 **/
@Component
public class TurnoverAccountServiceClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Reference(protocol = "nythrift_compact", owner = "${turnoverService_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"})
    public TUserAccountService.Iface proxy = null;

    @Reference(protocol = "nythrift_compact", owner = "${turnoverService_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"}
            , retries = 2 , cluster = "failover")
    public TUserAccountService.Iface readProxy = null;

    public TUserAccountService.Iface getProxy() {
        return proxy;
    }

    public TUserAccountService.Iface getReadProxy() {
        return readProxy;
    }

    /**
     * 营收可用货币余额查询（不含冻结余额）
     *
     */
    public long getUserAccountFilterByUidSlave(long uid, int appId, int currencyType) {

        try {
            var result = getReadProxy().getUserAccountFilterByUidSlave(uid, appId,0,"",false);
            log.info("getUserAccountByUidAndType uid:{},appid:{},currencyType:{},result:{}", uid, appId, currencyType, result);
            if(CollectionUtils.isEmpty(result)){
                return 0;
            }
            return result.stream().filter(p->p.getCurrencyType().getValue()==currencyType).mapToLong(TUserAccount::getAmount).sum();
        } catch (Exception e) {
            log.error("getUserAccountFilterByUidSlave error,uid:{},appid:{},type:{},e:{}", uid, appId, currencyType, e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }
}
