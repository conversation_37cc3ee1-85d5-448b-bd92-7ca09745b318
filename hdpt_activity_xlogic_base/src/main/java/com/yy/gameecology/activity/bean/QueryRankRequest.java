package com.yy.gameecology.activity.bean;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/12/31 16:31
 * @Modified:
 */
public class QueryRankRequest {

    @NotNull
    @Min(1)
    private Long actId;
    @NotNull
    @Min(1)
    private Long rankId;
    private Long phaseId;
    private String dateStr;
    private String findSrcMember;
    private Long rankCount;
    private String showZeroItem;
    private Long useNewWay;
    private String pointedMember;
    private Integer rankType;
    private Integer showType;
    private String useLoginUid;
    private String extQuery;
    private String isFilterWord;
    private String group;

    private Integer useCache;

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public Long getActId() {
        return actId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }

    public Long getRankId() {
        return rankId;
    }

    public void setRankId(Long rankId) {
        this.rankId = rankId;
    }

    public Long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(Long phaseId) {
        this.phaseId = phaseId;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public String getFindSrcMember() {
        return findSrcMember;
    }

    public void setFindSrcMember(String findSrcMember) {
        this.findSrcMember = findSrcMember;
    }

    public Long getRankCount() {
        return rankCount;
    }

    public void setRankCount(Long rankCount) {
        this.rankCount = rankCount;
    }

    public String getShowZeroItem() {
        return showZeroItem;
    }

    public void setShowZeroItem(String showZeroItem) {
        this.showZeroItem = showZeroItem;
    }

    public Long getUseNewWay() {
        return useNewWay;
    }

    public void setUseNewWay(Long useNewWay) {
        this.useNewWay = useNewWay;
    }

    public String getPointedMember() {
        return pointedMember;
    }

    public void setPointedMember(String pointedMember) {
        this.pointedMember = pointedMember;
    }

    public Integer getRankType() {
        return rankType;
    }

    public void setRankType(Integer rankType) {
        this.rankType = rankType;
    }

    public Integer getShowType() {
        return showType;
    }

    public void setShowType(Integer showType) {
        this.showType = showType;
    }

    public String getUseLoginUid() {
        return useLoginUid;
    }

    public void setUseLoginUid(String useLoginUid) {
        this.useLoginUid = useLoginUid;
    }

    public String getExtQuery() {
        return extQuery;
    }

    public void setExtQuery(String extQuery) {
        this.extQuery = extQuery;
    }

    public String getIsFilterWord() {
        return isFilterWord;
    }

    public void setIsFilterWord(String isFilterWord) {
        this.isFilterWord = isFilterWord;
    }

    public Integer getUseCache() {
        return useCache;
    }

    public void setUseCache(Integer useCache) {
        this.useCache = useCache;
    }
}
