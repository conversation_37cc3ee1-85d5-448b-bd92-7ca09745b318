package com.yy.gameecology.activity.bean.actlayer;

import com.google.common.collect.Lists;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.protocol.pb.layer.LayerInfo;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * desc:主播完成任务进度信息
 *
 * @createBy 曾文帜
 * @create 2020-07-28 21:15
 **/
public class BabyMissionItem {
    //已完成任务数量
    private long completedCount;

    //总任务数量
    private long totalCount;

    //总关卡数
    private long allTaskCount;

    //任务礼物名称
    private String giftName;

    private String giftId;

    //任务礼等级
    private long level;

    //当前任务名称
    private String taskName;

    //礼物图片
    private String giftBgUrl;

    //过此任务各个关卡需要的分数等配置
    private List<TaskItem> taskItems = Lists.newArrayList();

    /**
     * 当前已完成的轮次
     **/
    private long curRound;

    public long getCurRound() {
        return curRound;
    }

    public void setCurRound(long curRound) {
        this.curRound = curRound;
    }

    public long getCompletedCount() {
        return completedCount;
    }

    public void setCompletedCount(long completedCount) {
        this.completedCount = completedCount;
    }

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public String getGiftName() {
        return giftName;
    }

    public void setGiftName(String giftName) {
        this.giftName = giftName;
    }

    public long getLevel() {
        return level;
    }

    public void setLevel(long level) {
        this.level = level;
    }

    public String getGiftBgUrl() {
        return giftBgUrl;
    }

    public void setGiftBgUrl(String giftBgUrl) {
        this.giftBgUrl = giftBgUrl;
    }

    public String getGiftId() {
        return giftId;
    }

    public void setGiftId(String giftId) {
        this.giftId = giftId;
    }

    public long getAllTaskCount() {
        return allTaskCount;
    }

    public void setAllTaskCount(long allTaskCount) {
        this.allTaskCount = allTaskCount;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public List<TaskItem> getTaskItems() {
        return taskItems;
    }

    public void setTaskItems(List<TaskItem> taskItems) {
        this.taskItems = taskItems;
    }

    public static List<LayerInfo.BabyMissionItem_Simple> toMissionItemsPb(List<BabyMissionItem> missions) {
        List<LayerInfo.BabyMissionItem_Simple> simples = Lists.newArrayList();
        if (CollectionUtils.isEmpty(missions)) {
            return simples;
        }
        for (BabyMissionItem item : missions) {
            LayerInfo.BabyMissionItem_Simple.Builder simpleItem = LayerInfo.BabyMissionItem_Simple.newBuilder();
            simpleItem.setCompletedCount(Convert.toInt(item.getCompletedCount()));
            simpleItem.setGiftBgUrl(Convert.toString(item.getGiftBgUrl()));
            simpleItem.setGiftName(Convert.toString(item.getGiftName()));
            simpleItem.setLevel(Convert.toInt(item.getLevel()));
            simpleItem.setTotalCount(Convert.toInt(item.getTotalCount()));
            simpleItem.setTaskName(Convert.toString(item.getTaskName()));

            if (!CollectionUtils.isEmpty(item.getTaskItems())) {
                List<LayerInfo.TaskItem> taskItems = Lists.newArrayList();
                for (TaskItem taskItem : item.getTaskItems()) {
                    LayerInfo.TaskItem.Builder builder = LayerInfo.TaskItem.newBuilder();
                    builder.setPassValue(taskItem.getPassValue());
                    builder.setExtjson(Convert.toString(taskItem.getExtjson()));
                    builder.setRemark(Convert.toString(taskItem.getRemark()));
                    builder.setTaskId(taskItem.getTaskId());
                    builder.setName(StringUtil.isEmpty(taskItem.getName()) ? "" : taskItem.getName());
                    taskItems.add(builder.build());
                }
                simpleItem.addAllTaskItems(taskItems);
            }

            simples.add(simpleItem.build());
        }
        return simples;
    }

}
