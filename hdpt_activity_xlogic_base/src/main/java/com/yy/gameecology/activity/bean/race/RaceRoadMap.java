package com.yy.gameecology.activity.bean.race;



import com.yy.gameecology.common.bean.MultiNickItem;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class RaceRoadMap {

    private List<RaceRoadMapCpVo> list;

    private Map<String, Map<String, MultiNickItem>> nickExtUsers;

    private String giftLogo;
    private long giftAmount;
    private Integer giftCount;
    private boolean broBanner;
    private String top1Svga;

    private long uid;

    private String avatar;

    private boolean sign;

}
