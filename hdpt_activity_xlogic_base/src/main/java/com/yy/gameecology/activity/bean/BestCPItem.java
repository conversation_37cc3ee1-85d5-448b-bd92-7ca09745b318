package com.yy.gameecology.activity.bean;

/**
 * <AUTHOR>
 * @date 2021.06.25 17:03
 */
public class BestCPItem {
    private String time;
    private long score;
    private long playerUid;
    private String playerNick;
    private String playerLogo;
    private long anchorUid;
    private String anchorNick;
    private String anchorLogo;

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public long getScore() {
        return score;
    }

    public void setScore(long score) {
        this.score = score;
    }

    public long getPlayerUid() {
        return playerUid;
    }

    public void setPlayerUid(long playerUid) {
        this.playerUid = playerUid;
    }

    public String getPlayerNick() {
        return playerNick;
    }

    public void setPlayerNick(String playerNick) {
        this.playerNick = playerNick;
    }

    public String getPlayerLogo() {
        return playerLogo;
    }

    public void setPlayerLogo(String playerLogo) {
        this.playerLogo = playerLogo;
    }

    public long getAnchorUid() {
        return anchorUid;
    }

    public void setAnchorUid(long anchorUid) {
        this.anchorUid = anchorUid;
    }

    public String getAnchorNick() {
        return anchorNick;
    }

    public void setAnchorNick(String anchorNick) {
        this.anchorNick = anchorNick;
    }

    public String getAnchorLogo() {
        return anchorLogo;
    }

    public void setAnchorLogo(String anchorLogo) {
        this.anchorLogo = anchorLogo;
    }
}
