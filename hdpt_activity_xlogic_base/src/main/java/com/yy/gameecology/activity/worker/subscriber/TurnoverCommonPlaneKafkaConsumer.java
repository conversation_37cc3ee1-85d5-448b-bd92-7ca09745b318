package com.yy.gameecology.activity.worker.subscriber;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.TurnoverCommonPlaneEvent;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.MqConst;
import com.yy.gameecology.hdzj.HdzjEventDispatcher;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/7/10 10:03
 **/
@ConditionalOnExpression(Const.EXPRESSION_NOT_HISTORY)
@Component
@Slf4j
public class TurnoverCommonPlaneKafkaConsumer {
    @Autowired
    private HdzjEventDispatcher hdzjEventDispatcher;

    @KafkaListener(containerFactory = "jiaoyouWxKafkaContainerFactory", id = "turnover_common_plane_wx",
            topics = MqConst.TURNOVER_COMMON_PLANE_TOPIC,
            groupId = "${kafka.jiaoyou.wx.group}")
    public void onTurnoverCommonPlaneFromWx(ConsumerRecord<String, String> consumerRecord) {
        onTurnoverCommonPlane("wx", consumerRecord.value());
    }

    @KafkaListener(containerFactory = "jiaoyouSzKafkaContainerFactory", id = "turnover_common_plane_sz",
            topics = MqConst.TURNOVER_COMMON_PLANE_TOPIC,
            groupId = "${kafka.jiaoyou.sz.group}")
    public void onTurnoverCommonPlaneFromSz(ConsumerRecord<String, String> consumerRecord) {
        onTurnoverCommonPlane("sz", consumerRecord.value());
    }

    private void onTurnoverCommonPlane(String from, String payload) {
        log.info("onTurnoverCommonPlane  from={}.event:{}", from, payload);
        TurnoverCommonPlaneEvent event = JSON.parseObject(payload, TurnoverCommonPlaneEvent.class);

        try {
            hdzjEventDispatcher.notify(event.getActId(), event, event.getSeqId());
        } catch (Throwable e) {
            log.error("onTurnoverCommonPlane err TurnoverCommonPlaneEvent:{}", event, e);
        }
    }
}
