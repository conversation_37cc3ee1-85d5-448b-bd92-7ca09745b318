package com.yy.gameecology.activity.service.layer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.element.component.attr.ActLayerConfigComponentAttr;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * desc:挂件扩展信息管理
 *
 * @createBy 曾文帜
 * @create 2021-07-30 15:15
 **/
@Component
public class ActLayerInfoExtService implements BeanPostProcessor {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private LayerConfigService layerConfigService;

    /**
     * 存放外部活动扩展处理器
     */
    final private Map<Long, LayerSupport> layerSupportMap = Maps.newConcurrentMap();

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        Class<?> targetClass = AopProxyUtils.ultimateTargetClass(bean);
        if (LayerSupport.class.isAssignableFrom(targetClass)) {
            LayerSupport layerSupport = (LayerSupport) bean;
            long actId = layerSupport.getActId();
            if (layerSupportMap.containsKey(actId)) {
                log.error("register layerSupportMap error,actId exist:{},{}", actId, layerSupport.getClass().getName());
                return bean;
            }
            log.info("register layerSupportMap actId:{},class:{}", actId, layerSupport.getClass().getName());
            layerSupportMap.put(actId, layerSupport);
        }

        return bean;
    }

    /**
     * 增加自定义扩展信息
     */
    public Map<String, Object> extendLayer(long actId, long sid, long ssid, Date now, Map<String, Object> ext) {
        Map<String, Object> extRet = Maps.newHashMap();
        //通用扩展器
        for (Long commonActId : LayerSupport.COMMON_ACT_IDS) {
            Map<String, Object> extInfo = layerSupportMap.get(commonActId).buildLayerExtInfo(commonActId, sid, ssid, now, ext);
            if (MapUtils.isNotEmpty(extInfo)) {
                extRet.putAll(extInfo);
            }
        }
        //各个活动自定义扩展
        if (layerSupportMap.containsKey(actId)) {
            LayerSupport support = layerSupportMap.get(actId);
            Map<String, Object> extInfo = support.buildLayerExtInfo(actId, sid, ssid, now, ext);
            if (MapUtils.isNotEmpty(extInfo)) {
                extRet.putAll(extInfo);
            }
        }
        //组件扩展信息
        ActLayerConfigComponentAttr attr = layerConfigService.getLayerAttrConfig(actId, false);
        if (attr != null && CollectionUtils.isNotEmpty(attr.getExtCmptIds())) {
            for (long cmptId : attr.getExtCmptIds()) {
                checkCmptConfig(actId, layerSupportMap.get(cmptId));
                Map<String, Object> extInfo = layerSupportMap.get(cmptId).buildLayerExtInfo(actId, sid, ssid, now, ext);
                if (MapUtils.isNotEmpty(extInfo)) {
                    extRet.putAll(extInfo);
                }
            }
        }
        return extRet;
    }


    /**
     * 增加自定义扩展信息
     * catch 一下 预防加载而已信息出错导致挂件出不来
     */
    public Map<String, Object> extendLayerMemberItem(long actId, LayerMemberItem layerMemberItem) {
        Map<String, Object> extRet = Maps.newHashMap();
        try {
            //通用扩展器
            for (Long commonActId : LayerSupport.COMMON_ACT_IDS) {
                Map<String, Object> extInfo = getItemExtInfoBySupportService(commonActId, actId, layerMemberItem);
                if (MapUtils.isNotEmpty(extInfo)) {
                    extRet.putAll(extInfo);
                }
            }
            //各个活动自定义扩展
            Map<String, Object> actExtInfo = getItemExtInfoBySupportService(actId, actId, layerMemberItem);
            if (MapUtils.isNotEmpty(actExtInfo)) {
                extRet.putAll(actExtInfo);
            }
            //组件扩展信息
            ActLayerConfigComponentAttr attr = layerConfigService.getLayerAttrConfig(actId, false);
            if (attr != null && CollectionUtils.isNotEmpty(attr.getExtCmptIds())) {
                for (long cmptId : attr.getExtCmptIds()) {
                    checkCmptConfig(actId, layerSupportMap.get(cmptId));
                    Map<String, Object> cmptExtInfo = getItemExtInfoBySupportService(cmptId, actId, layerMemberItem);
                    if (MapUtils.isNotEmpty(cmptExtInfo)) {
                        extRet.putAll(cmptExtInfo);
                    }
                }
            }

        } catch (Exception e) {
            log.error("extendLayerMemberItem error, actId:{} item:{}", actId, JSON.toJSONString(layerMemberItem), e);
        }

        return extRet;
    }

    private Map<String, Object> getItemExtInfoBySupportService(long serviceId, long actId, LayerMemberItem layerMemberItem) {
        if (layerSupportMap.containsKey(serviceId)) {
            return layerSupportMap.get(serviceId).buildItemMemberExtInfo(actId, layerMemberItem, null);
        }
        return null;
    }


    public LayerBroadcastInfo customBroadcastInTheEnd(long actId, LayerBroadcastInfo layerInfo) {
        try {
            // 通用扩展
            for (Long commonActId : LayerSupport.COMMON_ACT_IDS) {
                layerInfo = layerSupportMap.get(commonActId).customBroadcastInTheEnd(layerInfo);
            }

            // 单个业务的通用扩展
            if (layerSupportMap.containsKey(layerInfo.getActBusiId())) {
                layerInfo = layerSupportMap.get(layerInfo.getActBusiId()).customBroadcastInTheEnd(layerInfo);
            }

            //各个活动自定义扩展
            if (layerSupportMap.containsKey(actId)) {
                LayerSupport support = layerSupportMap.get(actId);
                layerInfo = support.customBroadcastInTheEnd(layerInfo);
            }
            //组件扩展
            ActLayerConfigComponentAttr attr = layerConfigService.getLayerAttrConfig(actId, false);
            if (attr != null && CollectionUtils.isNotEmpty(attr.getExtCmptIds())) {
                for (long cmptId : attr.getExtCmptIds()) {
                    if (layerSupportMap.containsKey(cmptId)) {
                        checkCmptConfig(actId, layerSupportMap.get(cmptId));
                        layerInfo = layerSupportMap.get(cmptId).customBroadcastInTheEnd(layerInfo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("customBroadcastInTheEnd error,e:{}", e.getMessage(), e);
        }

        return layerInfo;
    }


    /**
     * 检查挂件扩展信息配置，依赖的组件有无配置，如果没配置则抛出异常
     */
    private void checkCmptConfig(long actId, LayerSupport cmptLayerSupport) {
        BaseActComponent component = (BaseActComponent) cmptLayerSupport;
        if (component.tryGetUniqueComponentAttr(actId) == null) {
            log.error("layer ext cmpt not config 挂件依赖扩展组件未配置 actId:{},cmptId:{}", actId, component.getComponentId());
            throw new SuperException("layer ext cmpt not config,cmptId:" + component.getComponentId(), SuperException.E_CONF_ILLEGAL);
        }
    }


}
