package com.yy.gameecology.activity.bean.kite;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

@Data
public class VisitNumsGiftVo {

    @ComponentAttrField(labelText = "膜拜人数")
    private Long visitNum;

    @ComponentAttrField(labelText = "礼物图片")
    private String giftLogo;

    @ComponentAttrField(labelText = "礼物名称")
    private String giftName;

    @ComponentAttrField(labelText = "奖池ID")
    private long taskId;

    @ComponentAttrField(labelText = "奖包ID")
    private long packageId;
}
