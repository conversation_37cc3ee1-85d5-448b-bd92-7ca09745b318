package com.yy.gameecology.activity.config.kafka;

import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.KafkaListenerContainerFactory;

@Configuration
public class SubChannelTextKafkaConfig {

    @Bean
    @ConfigurationProperties("kafka.subchannel.chat")
    public KafkaProperties subchannelTextChatKafkaProperties() {
        return new KafkaProperties();
    }

    @Bean
    public KafkaListenerContainerFactory subchannelTextChatKafkaContainerFactory(KafkaProperties subchannelTextChatKafkaProperties) {
        return KafkaConfig.createContainerFactory(subchannelTextChatKafkaProperties);
    }

}
