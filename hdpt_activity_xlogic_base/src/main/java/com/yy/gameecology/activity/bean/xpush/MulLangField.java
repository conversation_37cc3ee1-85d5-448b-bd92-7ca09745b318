package com.yy.gameecology.activity.bean.xpush;

import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName
 * @description key为多语言配置的key，非空则到多语言平台查询对应的内容，内容的格式同format
 * format为不需要解析多语言的直接推送内容模板，key为空时，此字段生效
 * values为format的参数值，通过String.format(format, values)得出推送的内容结果
 * @date 2019/8/14 14:06
 */
@Data
public class MulLangField {
    /**
     * 多语言的键
     */
    private String key;
    /**
     * 内容格式，key、format二选一：xxx%syyy%szzz
     */
    private String format;
    /**
     * 内容格式的参数值,默认是Collections.emptyList()非null
     */
    private String[] values;
}
