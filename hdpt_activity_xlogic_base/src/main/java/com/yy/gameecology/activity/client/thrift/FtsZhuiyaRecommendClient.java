package com.yy.gameecology.activity.client.thrift;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import com.yy.thrift.fts_recommend.DirectMeta;
import com.yy.thrift.fts_recommend.DirectRecLibraryInfo;
import com.yy.thrift.fts_recommend.DirectRecLibraryRetV2;
import com.yy.thrift.fts_recommend.FtsZhuiYaRecommendService;
import com.yy.thrift.fts_supplement.FtsSupplementService;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-03-13 18:48
 **/
@Component
public class FtsZhuiyaRecommendClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Reference(protocol = "nythrift", owner = "${thrift_fts_zhuiya_recommond_client_s2s_name}", registry = "consumer-reg", timeout = 1000, parameters = {"threads", "10"})
    @Getter
    private FtsZhuiYaRecommendService.Iface proxy;

    @Reference(protocol = "nythrift", owner = "${thrift_fts_zhuiya_recommond_client_s2s_name}", registry = "consumer-reg", timeout = 1000, parameters = {"threads", "10"}
            , retries = 2, cluster = "failover")
    @Getter
    private FtsZhuiYaRecommendService.Iface readProxy;

    public ChannelInfo getDirectRecommendLibrary(long recommendId) {
        var ret = getDirectRecommendLibraryMap(Lists.newArrayList(recommendId));
        if (ret == null) {
            return null;
        }
        for (DirectMeta meta : ret.keySet()) {
            if (meta.getLibID() == recommendId) {
                var item = ret.get(meta);
                return new ChannelInfo(item.getSid(), item.getSsid());
            }
        }

        return null;
    }


    public Map<DirectMeta, DirectRecLibraryInfo> getDirectRecommendLibraryMap(List<Long> recommendRoomList) {
        if (CollectionUtils.isNotEmpty(recommendRoomList)) {
            List<DirectMeta> metaList = recommendRoomList.stream().map(value -> {
                DirectMeta item = new DirectMeta();
                item.setLibID(value);
                return item;
            }).collect(Collectors.toList());
            DirectRecLibraryRetV2 ret = getDirectRecommendLibraryV2(metaList);
            log.info("getDirectRecommendLibraryV2 metaList{},ret{}", JSONUtils.toJsonString(metaList), JSONUtils.toJsonString(ret));
            if (ret != null && ret.getRet() == 0) {
                return ret.getRecMap();
            }
        }
        return Collections.emptyMap();
    }

    public DirectRecLibraryRetV2 getDirectRecommendLibraryV2(List<DirectMeta> metaList) {
        try {
            return getReadProxy().GetDirectRecommendLibraryV2(metaList);
        } catch (Exception e) {
            log.error("GetDirectRecommendLibraryV2,metaList:{}", metaList, e);
            return null;
        }

    }

}
