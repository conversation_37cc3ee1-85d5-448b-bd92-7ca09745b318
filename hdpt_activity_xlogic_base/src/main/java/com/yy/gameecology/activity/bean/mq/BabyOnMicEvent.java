package com.yy.gameecology.activity.bean.mq;

import com.yy.gameecology.activity.bean.gamebaby.YXBBEvent;

/**
 * desc:宝贝首麦上麦通知
 *
 * @createBy 曾文帜
 * @create 2021-01-25 11:51
 **/
public class BabyOnMicEvent extends YXBBEvent {

    /**
     * @Fields serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    private long babyUid;

    private long sid;

    private long ssid;

    private long time;

    public long getBabyUid() {
        return babyUid;
    }

    public void setBabyUid(long babyUid) {
        this.babyUid = babyUid;
    }

    public long getSid() {
        return sid;
    }

    public void setSid(long sid) {
        this.sid = sid;
    }

    public long getSsid() {
        return ssid;
    }

    public void setSsid(long ssid) {
        this.ssid = ssid;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

}
