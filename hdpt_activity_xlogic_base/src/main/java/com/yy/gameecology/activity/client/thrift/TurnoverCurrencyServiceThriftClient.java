package com.yy.gameecology.activity.client.thrift;

import com.yy.thrift.turnover.TCurrencyService;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-03-06 11:54
 **/
@Component
public class TurnoverCurrencyServiceThriftClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Reference(protocol = "nythrift_compact", owner = "${turnoverService_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"})
    public TCurrencyService.Iface proxy = null;

    @Reference(protocol = "nythrift_compact", owner = "${turnoverService_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"}
            , retries = 2 , cluster = "failover")
    public TCurrencyService.Iface readProxy = null;

    public TCurrencyService.Iface getProxy() {
        return proxy;
    }

    public TCurrencyService.Iface getReadProxy() {
        return readProxy;
    }

    /**
     * 查询提现申请数量、提现金额等汇总信息
     *
     * @param appid
     * @param timeGreaterThan 起始时间，闭区间，申请时间>=
     * @param timeLessThan    结束时间，开区间，申请时间<
     * @return userNum：提现人数，applyRealAmount：提现金额（单位：厘）
     */
    public Map<String, String> queryMonthSettleApplyInfo(int appid, long timeGreaterThan, long timeLessThan) {
        try {
            return getReadProxy().queryMonthSettleApplyInfo(appid, timeGreaterThan, timeLessThan);
        } catch (Exception e) {
            log.error("queryMonthSettleApplyInfo error,appid:{},timeGreaterThan:{},timeLessThan:{},e:{}", appid, timeGreaterThan, timeLessThan, e.getMessage(), e);
            return null;
        }
    }

}
