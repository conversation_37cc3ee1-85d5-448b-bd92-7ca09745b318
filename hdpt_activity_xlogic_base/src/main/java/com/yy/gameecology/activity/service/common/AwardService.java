package com.yy.gameecology.activity.service.common;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.hdzj.bean.AwardVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/7/7
 */
@Service
@Slf4j
public class AwardService {

    private ImmutableListMultimap<String, AwardConfig> awardConfigMap;

    @Autowired
    private ActRedisGroupDao redis;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @PostConstruct
    @NeedRecycle(author = "zengyuan", notRecycle = true)
    public void init() {
        ImmutableListMultimap.Builder<String, AwardConfig> builder = ImmutableListMultimap.builder();
        awardConfigMap = builder.build();
    }



    private AwardVO buildAwardVO(PrizeVO prize, int count) {
        return AwardVO.builder()
                .name("")
                .taskId(prize.getTaskId())
                .packageId(prize.getPackageId())
                .count(count)
                .build();
    }


    @AllArgsConstructor
    @Getter
    public enum AwardType {
        /**
         * 发奖模式-忽略概率和数量
         */
        ALL("all"),
        /**
         * 忽略概率，随机数量
         */
        RandomByMaxLimit("RandomByMaxLimit");
        private final String type;


    }


}

















