/**
 * SecuserinfoThriftClient.java / 2018年3月28日 下午5:34:53
 * <p>
 * Copyright (c) 2017, YY Inc. All Rights Reserved.
 * <p>
 * 郭立平[<EMAIL>]
 */

package com.yy.gameecology.activity.client.thrift;

import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.client.AsynInit;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.secuserinfo.*;
import com.yy.thrift.secuserinfo.secuserinfo_service.Iface;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * UDB 的票据服务等安全相关 thrift 接口
 *
 * <AUTHOR>
 * @date 2018年3月28日 下午5:34:53
 */
@Component
public class SecuserinfoThriftClient {

    private static final Logger log = LoggerFactory.getLogger(SecuserinfoThriftClient.class);

    @Reference(protocol = "nythrift", owner = "__", url = "${thrift.service-agent.url}", timeout = 3000, parameters = {"threads", "10"}, cluster = "failover")
    private Iface proxy;

    @Reference(protocol = "nythrift", owner = "__", url = "${thrift.service-agent.url}", timeout = 3000, parameters = {"threads", "10"}, cluster = "failover"
            , retries = 2)
    private Iface readProxy;

    public static AuthorizeMsg getAuthorizeMsg() {
        AuthorizeMsg authorizeMsg = new AuthorizeMsg();
        authorizeMsg.setAuthKey(Const.APP_SECRET);
        authorizeMsg.setAuthUser(Const.APP_ID);

        HashMap<String, String> keyvalue = new HashMap<>(1);
        keyvalue.put("auth-type", "4");
        authorizeMsg.setKeyvalue(keyvalue);
        return authorizeMsg;
    }

    public Iface getProxy() {
        return proxy;
    }

    public Iface getReadProxy() {
        return readProxy;
    }

    /**
     * 通过udb票据验证，获得yyuid, 验证成功返回正确的uid，验证失败返回 0 值
     *
     * <AUTHOR>
     * @date 2018年3月29日 下午2:56:06
     */
    @Report
    public long getYyuidByTicket(String ticket, String appid) throws TException {
        String udbAppid = StringUtil.isBlank(appid) ? "5060" : appid.trim();
        Clock clock = new Clock();
        SaVerifyAppTokenReq req = new SaVerifyAppTokenReq();
        req.setAuthmsg(SecuserinfoThriftClient.getAuthorizeMsg());
        // 业务id,票据类型目前固定为5060
        req.setAppid(udbAppid);
        // 票据对应的yyuid，如果无法获取，则置为0
        req.setYyuid(0);
        req.setToken(ticket);
        // 排障用
        req.setContext(java.util.UUID.randomUUID().toString());
        req.setEncoding_type(TokenEncodingType.BASE64_WITH_URL.getValue());

        SaVerifyAppTokenRes resp = getReadProxy().lg_secuserinfo_verify_apptokenEx64Sa(req);

        // -217 为重放,重复请求
        int rescode = resp.getRescode();
        if (rescode != ResCode.SUI_VERIFY_SUCCESS.getValue()) {
            log.warn("getYyuidByTicket fail@rescode:{}, appid:{} , ticket:{} {}", rescode, udbAppid, ticket,
                    clock.tag());
            throw new SuperException("用户验证失败！rescode:" + rescode, 4008);
        }

        long yyuid = resp.getYyuid();
        return yyuid;
    }
}
