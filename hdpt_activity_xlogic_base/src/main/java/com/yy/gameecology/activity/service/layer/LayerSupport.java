package com.yy.gameecology.activity.service.layer;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 **/
public interface LayerSupport {

    /**
     * 通用扩展器-虚拟活动id
     */
    List<Long> COMMON_ACT_IDS = Lists.newArrayList(0L);

    /**
     * 挂件扩展信息 key，不同扩展不要重复,否则实现类会覆盖，目前有：
     * 1、活动id---活动定制类用
     * 2、组件id---非定制组件类用
     * 3、业务id---通用业务实现类用
     * 4、0--通用扩展（LayerSupportService用了， 其他不要用）
     */
    long getActId();

    /**
     * 自定义广播信息，会不会这个太灵活了，会导致不知道改了啥?
     *
     * @param source 广播数据源
     * @return 广播结果
     */
    default LayerBroadcastInfo customBroadcastInTheEnd(LayerBroadcastInfo source){
        return source;
    }

    /**
     * 给挂件增加扩展信息
     */
    default Map<String, Object> buildLayerExtInfo(long actId, long sid, long ssid, Date now, Map<String, Object> ext) {
        return null;
    }

    /**
     * 给挂件成员增加扩展信息
     */
    default Map<String, Object> buildItemMemberExtInfo(long actId, LayerMemberItem layerMemberItem, Map<String, Object> ext) {
        return null;
    }


    //根据具体需求，增加变化的扩展点
    //...

}
