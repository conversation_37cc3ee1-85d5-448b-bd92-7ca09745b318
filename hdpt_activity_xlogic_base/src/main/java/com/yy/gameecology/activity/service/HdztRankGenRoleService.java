package com.yy.gameecology.activity.service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.FtsRoomMgrInfoVo;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.RankItemAny;
import com.yy.gameecology.activity.bean.hdzt.RankMemberInfo;
import com.yy.gameecology.activity.bean.rankroleinfo.RoleItem;
import com.yy.gameecology.activity.client.thrift.FtsRecommendDataThriftClient;
import com.yy.gameecology.activity.client.thrift.FtsRoomManagerThriftClient;
import com.yy.gameecology.activity.rolebuilder.RoleBuilder;
import com.yy.gameecology.common.consts.RankDataSource;
import com.yy.gameecology.common.utils.Convert;
import com.yy.thrift.act_ext_support.MemberItemInfo;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RankingInfo;
import com.yy.thrift.hdztranking.RoleDetail;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/9/17 0:31
 * @Modified:
 */
@Service
public class HdztRankGenRoleService {

    /**
     * 50051：交友-多人厅
     * 50055：交友-综合厅
     */
    public static final Set<String> TING_ROLES = ImmutableSet.of("50050", "50051", "50052", "50053", "50054", "50055", "50056", "50057", "50058", "50059", "50060");

    /**
     * 角色Id映射关系，陪玩那边只处理各个roleType的第一个roleId
     */
    private static final Map<String, Long> ROLE_TRANSITION_MAP = new ImmutableMap.Builder<String, Long>()
            .put("900-200", 90001L)
            .put("900-201", 90070L)
            .put("900-400", 90004L)
            .put("900-401", 90004L)
            .put("900-402", 90060L)
            .build();

    /**
     * 从业务端查询信息
     */
    private static final Integer BASE_DATA_FROM_BUSINESS = 1;
    /**
     * 从yy查询基础信息
     */
    private static final Integer BASE_DATA_FROM_YY = 2;
    /**
     * yy的基础信息+加业务的扩展信息
     */
    private static final Integer BASE_DATA_FROM_YY_AND_BUSINESS = 3;
    /**
     * 增加扩展信息，如神豪的贵族等级，主播的签约信息
     */
    private static final Integer ADD_EXT_DATA = 1;

    @Autowired
    private FtsRecommendDataThriftClient ftsRecommendDataThriftClient;


    @Autowired
    private FtsRoomManagerThriftClient ftsRoomManagerThriftClient;

    /**
     * 批量获取榜单成员信息
     *
     * @param rankReq
     * @param rankingInfo
     * @param rankItems
     * @return
     */
    public Map<String, Map<String, RoleItem>> getRankRoleInfo(GetRankReq rankReq, RankingInfo rankingInfo, List<RankItemAny> rankItems) {
        //按角色的生成方式分类
        Map<String, List<RankMemberInfo>> rankMemberInfosMap =
                rankItems.stream().map(RankItemAny::getRankMemberInfos)
                        .flatMap(Collection::stream)
                        .collect(Collectors.groupingBy(this::getRoleCreateType));
        //没有登录
        long uid = rankReq.getLoginUid() == null ? 0 : rankReq.getLoginUid();
        Map<String, Map<String, RoleItem>> rankRoleInfoMaps = Maps.newHashMap();

        //关键字过滤
        String sensitiveWordConfig = Convert.toString(rankReq.getSensitiveWordFilter());

        for (Map.Entry<String, List<RankMemberInfo>> entry : rankMemberInfosMap.entrySet()) {
            List<RankMemberInfo> rankMemberInfos = entry.getValue();
            List<String> members = rankMemberInfos.stream().map(RankMemberInfo::getMemberId).collect(Collectors.toList());
            RoleDetail roleDetails = new RoleDetail();
            BeanUtils.copyProperties(rankMemberInfos.get(0), roleDetails);

            // 娱乐增加HostId
            if (roleDetails.getRoleBusiId() == RankDataSource.YULE) {
                members.add(rankReq.getHostIdStr());
            }

            Map<String, RoleItem> rankRoleItemMap = (Map<String, RoleItem>) genRankItemMap(rankingInfo, uid, roleDetails, members, sensitiveWordConfig, rankReq.nickExt());
            rankRoleInfoMaps.put(entry.getKey(), rankRoleItemMap);
        }
        return rankRoleInfoMaps;
    }

    /**
     * 获取一个角色信息生成类型
     *
     * @param rankMemberInfo
     * @return
     */
    public String getRoleCreateType(RankMemberInfo rankMemberInfo) {
        return rankMemberInfo.getRoleType() + "_" + rankMemberInfo.getRoleId() + "_" + rankMemberInfo.getRoleHint();
    }

    public Map<String, ? extends RoleItem> genRankItemMap(long actId, long rankId, long busiId, long roleType,
                                                          long roleId, long hint, List<String> members, long uid) {
        return genRankItemMap(actId, rankId, busiId, roleType, roleId, hint, members, uid, "");
    }

    /**
     * @param actId
     * @param rankId
     * @param busiId
     * @param roleType
     * @param roleId
     * @param hint       数据来源控制 个位指示基础数据来源，1=数据来着业务，
     *                   2=数据来着yy，只有基础数据，3=数据来自yy的基础+业务的扩展数据
     *                   十位表示是否添加扩展数据，例如交友用户的贵族信息
     * @param members
     * @param uid
     * @param nameFilter 名称关键词过滤
     * @return
     */
    public Map<String, ? extends RoleItem> genRankItemMap(long actId, long rankId, long busiId, long roleType,
                                                          long roleId, long hint, List<String> members, long uid, String nameFilter) {
        if (CollectionUtils.isEmpty(members)) {
            return Maps.newHashMap();
        }
        members.removeIf(("0")::equals);
        if (CollectionUtils.isEmpty(members)) {
            return Maps.newHashMap();
        }
        ActSupportService actSupportClientService = ActSupportService.getInstance((int) busiId);
        RoleBuilder rankBuilder = actSupportClientService.getRankBuilder((int) roleType);
        Map<String, RoleItem> rankItemMap = Maps.newHashMap();
        int addNickExt = (int) (hint / 100);
        int baseFrom = (int) (hint % 10);
        int extInfo = ((int) (hint / 10)) % 10;
        Map<String, List<String>> memberIdMap = Maps.newHashMap();
        final String queryRoleId = String.valueOf(ROLE_TRANSITION_MAP.getOrDefault(busiId + "-" + roleType, roleId));
        memberIdMap.put(queryRoleId + "", members);
        if (baseFrom == BASE_DATA_FROM_BUSINESS) {

            Map<String, Map<String, MemberItemInfo>> memberMap = actSupportClientService.queryMember(actId, rankId, memberIdMap);
            Map<String, MemberItemInfo> memberItemInfoMap = memberMap.getOrDefault(queryRoleId, Collections.EMPTY_MAP);
            for (String member : members) {
                MemberItemInfo memberItemInfo = memberItemInfoMap.get(member);
                RoleItem rankItem;
                if (memberItemInfo != null) {
                    rankItem = rankBuilder.buildRankItemByBase(memberItemInfo, rankBuilder.createBankObject());
                    rankItem = rankBuilder.buildRankItemByEx(memberItemInfo, rankItem);
                    rankItemMap.put(member, rankItem);
                }
            }
        } else if (baseFrom == BASE_DATA_FROM_YY) {
            if (addNickExt > 0) {
                rankItemMap = rankBuilder.buildRankByYyWithNickExt(Set.copyOf(members));
            } else {
                rankItemMap = rankBuilder.buildRankByYy(Sets.newHashSet(members));
            }

            // 厅角色返回厅管推荐图
            // 优先拿厅管图-->boss后台厅推荐图(新增)-->公会头像
            if (TING_ROLES.contains(queryRoleId)) {
                Map<String, String> roleMemberPicMap = ftsRecommendDataThriftClient.batchGetRoomMgrPicByChannel(members);
                // boss后台配置的推荐图,key:sid_ssid
                Map<String, String> recommendPicMap = ftsRecommendDataThriftClient.batchGetRecommendConfigPicture(members, changeToBusinessType(busiId));
                for (String memberId : rankItemMap.keySet()) {
                    setAvatar(rankItemMap.get(memberId), roleMemberPicMap.get(memberId), recommendPicMap.get(memberId));
                }

                //房管厅优先展示取房管后台的昵称
                Map<String, FtsRoomMgrInfoVo> roomMgrInfoMap = ftsRoomManagerThriftClient.ftsBatchGetRoomMgrInfoByCh(members);
                for (String memberId : rankItemMap.keySet()) {
                    if(roomMgrInfoMap!=null && roomMgrInfoMap.containsKey(memberId) && StringUtils.isNotEmpty(roomMgrInfoMap.get(memberId).getName())) {
                        rankItemMap.get(memberId).setName(roomMgrInfoMap.get(memberId).getName());
                    }
                }


            }
        } else if (baseFrom == BASE_DATA_FROM_YY_AND_BUSINESS) {
            rankItemMap = rankBuilder.buildRankByYy(Sets.newHashSet(members));

            Map<String, Map<String, MemberItemInfo>> memberMap = actSupportClientService.queryMember(actId, rankId, memberIdMap);
            Map<String, MemberItemInfo> memberItemInfoMap = memberMap.getOrDefault(queryRoleId, Collections.EMPTY_MAP);
            for (String member : members) {
                MemberItemInfo memberItemInfo = memberItemInfoMap.get(member);
                if (memberItemInfo != null) {
                    rankBuilder.buildRankItemByEx(memberItemInfo, rankItemMap.get(member));
                }
            }

        }

        if (busiId == RankDataSource.YULE) {
            String hostId = removeHostId(members);
            RoleItem roleItem = new RoleItem();
            roleItem.setKey(hostId);
            rankItemMap.put("hostId", roleItem);
        }

        //补充额外的信息
        if (extInfo == ADD_EXT_DATA) {
            rankBuilder.addExtraInfo(actId, (int) roleId, rankId, uid, rankItemMap);
        }
        RoleItem defaultRankItem = rankBuilder.getDefaultObject();
        // 检查-查不到的对象会生成默认对象
        for (String member : members) {
            if (!rankItemMap.containsKey(member)) {
                RoleItem roleItem = rankBuilder.createBankObject();
                BeanUtils.copyProperties(defaultRankItem, roleItem);
                roleItem.setKey(member);
                rankItemMap.put(member, roleItem);
            }
        }
        //关键词过滤
        if (StringUtils.isNotBlank(nameFilter)) {
            String[] sensitiveWordConfigA = nameFilter.split(",");
            //正则Pattern只编译一次，提高效率
            Pattern sensitiveWordsPattern = Pattern.compile(sensitiveWordConfigA[0]);
            String replaceWord = sensitiveWordConfigA.length == 2 ? sensitiveWordConfigA[1] : "";
            rankItemMap.values().forEach(rankItem -> {
                String name = sensitiveWordsPattern.matcher(rankItem.getName()).replaceAll(replaceWord);
                rankItem.setName(name);
            });
        }

        return rankItemMap;
    }

    private String removeHostId(List<String> members) {
        String hostId = "";
        for (String member : members) {
            if (member.startsWith("host=")) {
                hostId = member;
            }
        }
        members.remove(hostId);

        return hostId;
    }

    private void setAvatar(RoleItem roleItem, String roomMgrPic, String recommendPic) {
        if (StringUtils.isNotEmpty(roomMgrPic)) {
            roleItem.setAvatarInfo(roomMgrPic);
            return;
        }

        if (StringUtils.isNotEmpty(recommendPic)) {
            roleItem.setAvatarInfo(recommendPic);
        }
    }

    /**
     * 1 交友 2 约战 3 宝贝 4 打通房 101 派单房 201 游戏房 301 语音房 401 YY开黑房 501 技能卡房
     **/
    public static int changeToBusinessType(long busiId) {
        if (BusiId.MAKE_FRIEND.getValue() == busiId) {
            return 1;
        }
        if (BusiId.GAME_BABY.getValue() == busiId) {
            return 3;
        }
        if (BusiId.SKILL_CARD.getValue() == busiId) {
            return 501;
        }

        return 0;
    }

    /**
     * @param rankingInfo
     * @param uid
     * @param roleDetail
     * @param members
     * @return
     */
    public Map<String, ? extends RoleItem> genRankItemMap(RankingInfo rankingInfo, long uid, RoleDetail roleDetail
            , List<String> members, String nameFilter, boolean nickExt) {
        // 没有指定hostId，则不查询多昵称
        long roleHit = roleDetail.roleHint;
        if (roleHit > 100 && !nickExt) {
            roleHit %= 100;
        }
        return genRankItemMap(rankingInfo.getActId(), rankingInfo.getRankingId(), roleDetail.getRoleBusiId()
                , roleDetail.getRoleType(), roleDetail.getRoleId(), roleHit, members, uid, nameFilter);
    }
}
