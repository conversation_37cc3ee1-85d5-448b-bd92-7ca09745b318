package com.yy.gameecology.activity.service.aov.phase;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.consts.aov.AovConst;
import com.yy.gameecology.common.db.mapper.aov.*;
import com.yy.gameecology.common.db.model.gameecology.aov.*;
import com.yy.gameecology.hdzj.element.component.attr.AovPhaseComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.AovTeamComponentAttr;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@Component
public class AovPhaseJobService {

    @Resource
    private AovPhaseMapper aovPhaseMapper;

    @Resource
    private AovPhaseTeamMapper aovPhaseTeamMapper;

    @Resource
    private AovPhaseAwardExtMapper aovPhaseAwardExtMapper;

    @Resource
    private AovPhaseRoundExtMapper aovPhaseRoundExtMapper;

    @Resource
    private AovPhaseMatchExtMapper aovPhaseMatchExtMapper;

    @Transactional(rollbackFor = Throwable.class)
    public void initPhase(AovPhaseComponentAttr attr, final Date signupStartTime, long prevActId, long prevPhaseId) {
        Date signupEndTime = DateUtils.addSeconds(signupStartTime, (int) attr.getSignUpDuration().toSeconds());
        Date adjustEndTime = DateUtils.addSeconds(signupEndTime, (int) attr.getAdjustDuration().toSeconds());

        List<AovPhaseRound> rounds = new ArrayList<>(attr.getRoundConfigs().size());
        Map<Integer, LocalDate> startDates = new HashMap<>(attr.getRoundConfigs().size());
        Map<Integer, LocalDate> endDates = new HashMap<>(attr.getRoundConfigs().size());
        Map<Integer, LocalDate> roundDates = new HashMap<>(attr.getRoundConfigs().size());

        final LocalDate signupEndDate = DateUtils.addMinutes(signupEndTime, -10).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate lastDate = signupEndDate;
        for (AovPhaseComponentAttr.ActRoundConfig roundConfig : attr.getRoundConfigs()) {
            final LocalDate localDate =  signupEndDate.plusDays(roundConfig.getSignupEndDayOffset());
            LocalDateTime startLocalDateTime = LocalDateTime.of(localDate, roundConfig.getStartTime());
            Date startTime = Date.from(startLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());

            LocalDateTime endLocalDateTime = LocalDateTime.of(localDate, roundConfig.getEndTime());
            Date endTime = Date.from(endLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
            AovPhaseRound item = new AovPhaseRound();
            item.setRoundNum(roundConfig.getRoundNum());
            item.setRoundName(roundConfig.getName());
            item.setBo(roundConfig.getBo());
            item.setBattleMode(roundConfig.getBattleMode());
            item.setStartTime(startTime);
            item.setEndTime(endTime);
            item.setState(AovConst.RoundState.INIT);
            item.setCreateGame(roundConfig.getCreateGame() ? 1 : 0);
            rounds.add(item);

            int groupId = roundConfig.getGroupId();
            startDates.compute(groupId, (k, v) -> {
               if (v == null) {
                   return localDate;
               }

               return v.isAfter(localDate) ? localDate : v;
            });

            endDates.compute(groupId, (k, v) -> {
                if (v == null) {
                    return localDate;
                }

                return v.isBefore(localDate) ? localDate : v;
            });

            lastDate = lastDate.isBefore(localDate) ? localDate : lastDate;

            roundDates.put(roundConfig.getRoundNum(), localDate);
        }

        List<RoundGroup> roundGroups = new ArrayList<>(attr.getRoundGroupConfigs().size());
        for (var entry : attr.getRoundGroupConfigs().entrySet()) {
            int groupId = entry.getKey();
            LocalDate startDate = startDates.get(groupId);
            LocalDate endDate = endDates.get(groupId);
            Assert.notNull(startDate, "group start date cannot be null");
            Assert.notNull(endDate, "group end date cannot be null");
            LocalDateTime startTime = LocalDateTime.of(startDate, entry.getValue().getStartTime());
            LocalDateTime endTime = LocalDateTime.of(endDate, entry.getValue().getEndTime());
            RoundGroup group = new RoundGroup();
            group.setName(entry.getValue().getGroupName());
            group.setStartTime(Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()));
            group.setEndTime(Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()));

            roundGroups.add(group);
        }

        Date phaseEndTime = Date.from(lastDate.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());
        AovPhase phase = new AovPhase(attr.getActId(), prevActId, prevPhaseId, signupStartTime, signupEndTime, adjustEndTime,
                attr.getAwardInfo(), JSON.toJSONString(roundGroups), signupStartTime,
                phaseEndTime, AovConst.PhaseState.INIT, new Date());

        // 插入phase
        int rs = aovPhaseMapper.insertAndGetId(phase);
        log.info("insert phase with prevActId:{} prevPhaseId:{} rs:{}", prevActId, prevPhaseId, rs);
        if (rs <= 0) {
            return;
        }

        final long phaseId = phase.getId();

        // 插入轮次
        rounds.forEach(round -> round.setPhaseId(phaseId));
        rs = aovPhaseRoundExtMapper.batchInsertOrUpdate(rounds);
        log.info("batch insert phase round with phaseId:{} rs:{}", phase.getId(), rs);

        // 对局配置
        if (CollectionUtils.isNotEmpty(attr.getMatchConfigs())) {
            List<AovPhaseMatch> matches = new ArrayList<>(attr.getMatchConfigs().size());
            for (AovPhaseComponentAttr.ActMatchConfig matchConfig : attr.getMatchConfigs()) {
                Assert.isTrue(matchConfig.getNodeIndex1() % 2 == 0, "nodeIndex1 must be even");
                Assert.isTrue(matchConfig.getNodeIndex1() + 1 == matchConfig.getNodeIndex2(), "nodeIndex2 must be nodeIndex1 + 1");
                int roundNum = matchConfig.getRoundNum();
                LocalDate roundDate = roundDates.get(roundNum);
                Assert.notNull(roundDate, "round date cannot be null");

                LocalDateTime startTime = LocalDateTime.of(roundDate, matchConfig.getStartTime());

                AovPhaseMatch match = new AovPhaseMatch();
                match.setPhaseId(phaseId);
                match.setRoundNum(roundNum);
                match.setRoundName(matchConfig.getName());
                match.setNodeIndex1(matchConfig.getNodeIndex1());
                match.setNodeIndex2(matchConfig.getNodeIndex2());
                match.setBo(matchConfig.getBo());
                match.setBattleMode(matchConfig.getBattleMode());
                match.setStartTime(Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()));

                matches.add(match);
            }

            rs = aovPhaseMatchExtMapper.batchInsertPhaseMatches(matches);
            log.info("batchInsertPhaseMatches with size:{} rs:{}", matches.size(), rs);
        }

        // 插入奖励配置
        List<AovPhaseComponentAttr.PhaseAwardConfig> awardConfigs = attr.getPhaseAwardConfigs();
        if (CollectionUtils.isNotEmpty(awardConfigs)) {
            List<AovPhaseAward> records = new ArrayList<>(awardConfigs.size());
            for (AovPhaseComponentAttr.PhaseAwardConfig config : awardConfigs) {
                AovPhaseAward record = new AovPhaseAward();
                record.setPhaseId(phaseId);
                record.setRoundNum(config.getRoundNum());
                record.setMemberSize(config.getMemberSize());
                //平分奖励
                record.setTaskId(config.getTAwardTskId());
                record.setPackageId(config.getTAwardPkgId());
                record.setAwardAmount(config.getAwardAmount());
                record.setAwardDesc(config.getAwardDesc());
                //额外奖励
                record.setExtraAwardAmount(config.getExtraAwardAmount());
                record.setExtraTaskId(config.getTAwardTskExtraId());
                record.setExtraPackageId(config.getTAwardPkgExtraId());
                record.setExtraAwardDesc(config.getExtraAwardDesc());
                records.add(record);
            }

            rs = aovPhaseAwardExtMapper.batchInsertPhaseAwards(records);
            log.info("initPhase save awardConfig with size:{} rs:{}", awardConfigs.size(), rs);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void initTeam(AovTeamComponentAttr attr, AovPhase aovPhase) {
        //更新无效队伍
        int teamNUm = aovPhaseTeamMapper.invalidTeam(aovPhase.getId(), attr.getTeamMemberMin(), AovConst.PhaseTeamState.NO_ENOUGH);
        log.info("invalid team num:{}", teamNUm);
        //计算合法队伍数量
        List<AovPhaseTeam> aovPhaseTeamList =
                aovPhaseTeamMapper.selectInitTeam(aovPhase.getId(), attr.getTeamMemberMin(), AovConst.PhaseTeamState.INIT);
        //数量不够 更新阶段状态
        int teamSize = aovPhaseTeamList.size();
        if(teamSize < attr.getTeamNumMin()) {
            //update phase
            aovPhaseMapper.updatePhaseState(aovPhase.getId(), AovConst.PhaseState.INIT, AovConst.PhaseState.CANCEL);
            return;
        }

        if(teamSize > attr.getTeamNumMax()) {
            List<Long> teamIds = new ArrayList<>();
            for(int i = attr.getTeamNumMax(); i < teamSize; i++) {
                teamIds.add(aovPhaseTeamList.get(i).getId());
            }
            aovPhaseTeamMapper.updateState(teamIds, AovConst.PhaseTeamState.OVER_LIMIT);
        }
        teamSize = Math.min(attr.getTeamNumMax(), teamSize);
        List<Long> teamIds = new ArrayList<>();
        for(int i=0; i< teamSize; i++) {
            teamIds.add(aovPhaseTeamList.get(i).getId());
        }
        aovPhaseTeamMapper.updateState(teamIds, AovConst.PhaseTeamState.SUCC);
        aovPhaseMapper.updatePhaseState(aovPhase.getId(), AovConst.PhaseState.INIT, AovConst.PhaseState.SUCC);
    }

    @Getter
    @Setter
    public static class RoundGroup {

        protected Date startTime;

        protected Date endTime;

        protected String name;
    }
}
