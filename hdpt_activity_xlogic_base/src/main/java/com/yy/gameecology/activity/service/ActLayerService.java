package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.bean.actlayer.ActInfoPara;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.bean.hdzt.RankCurItemVo;
import com.yy.gameecology.common.bean.Template;

import java.util.Date;

public interface ActLayerService {

    default Long getCurrentPhaseIdByMemberId(long actId, long memberId) {
        return 0L;
    }

    default Long getCurrentPhaseId(long actId, int memberType) {
        return 0L;
    }

    default public String getHourRankDateStr() {
        return "";
    }


    default LayerBroadcastInfo buildLayerInfo(Long actId, Long sid, Long ssid) {
        return null;
    }

    default LayerBroadcastInfo buildLayerInfo(Template template, ActInfoPara actInfoPara, OnlineChannelInfo channelInfoPara, Date now) {
        return null;
    }

    default RankCurItemVo queryCurItem(Long actId, Long sid, Long ssid, Long rankId) {
        return null;
    }
}
