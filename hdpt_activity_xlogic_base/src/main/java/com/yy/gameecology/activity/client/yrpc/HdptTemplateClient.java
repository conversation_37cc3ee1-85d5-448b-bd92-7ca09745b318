package com.yy.gameecology.activity.client.yrpc;

import com.yy.gameecology.common.consts.Const;
import com.yy.hdpt.template.proto.HdptTemplate;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-05-30 16:38
 **/
@Component
public class HdptTemplateClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Reference(protocol = "yrpc", owner = "hdpt_template_yrpc", registry = "yrpc-reg", lazy = true, retries = 2, cluster = "failover")
    private HdptTemplateService hdptAovReadService;

    public HdptTemplateService getReadProxy() {
        return hdptAovReadService;
    }


    /**
     *
     * @return
     */
    public List<HdptTemplate.XlogicTableConfigItem> getEffectXlogicTableConfig() {
        return getReadProxy().getEffectXlogicTableConfig(HdptTemplate.XlogicTableConfigRequest.newBuilder().setState(Const.ONE).build()).getItemsList();
    }
}
