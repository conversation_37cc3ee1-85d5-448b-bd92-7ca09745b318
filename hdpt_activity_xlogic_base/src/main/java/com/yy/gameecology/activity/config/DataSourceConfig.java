
package com.yy.gameecology.activity.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
public class DataSourceConfig {

    @Bean
    @Primary // 这个标记是用来开启多bean支持的，必须配合 @Qualifier 进行定向引用！！！-added by guoliping/2017-06-13
    @ConfigurationProperties(prefix = "mysql.gameecology")
    public DataSource gameecologyDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean
    @Primary // 这个标记是用来开启多bean支持的，必须配合 @Qualifier 进行定向引用！！！-added by guoliping/2017-06-13
    public JdbcTemplate gameecologyJdbcTemplate(@Qualifier("gameecologyDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    //事务管理器
    @Bean
    public DataSourceTransactionManager transactionManager(@Qualifier("gameecologyDataSource") DataSource dataSource){ //事务管理
        return new DataSourceTransactionManager(dataSource);
    }
}
