package com.yy.gameecology.activity.bean.driftbottle;


import com.yy.gameecology.common.bean.MultiNickItem;
import lombok.Data;

import java.util.Map;

@Data
public class RoadMapCpInfoVo {
    private long userUid;
    private String userLogo;
    private String userNick;
    private long babyUid;
    private String babyLogo;
    private String babyNick;

    /**
     * 增加的公里
     */
    private long addStep;

    /**
     * 当前总公里
     */
    private long cureentStep;

    /**
     * 多昵称
     */
    private Map<String, Map<String, MultiNickItem>> nickExtUsers;

}
