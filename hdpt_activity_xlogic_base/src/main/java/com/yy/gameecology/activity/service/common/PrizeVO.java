package com.yy.gameecology.activity.service.common;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 奖品
 *
 * <AUTHOR>
 * @since 2021/7/7
 */
@Getter
@Builder
@ToString
public class PrizeVO {

    private final Long prizeId;

    private final Long taskId;

    private final Long packageId;

    private final Integer min;

    private final Integer max;

    private final Long limit;
    //概率
    private final BigDecimal probability;
}
