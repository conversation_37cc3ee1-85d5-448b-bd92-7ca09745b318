package com.yy.gameecology.activity.bean;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023.03.09 17:24
 * 营收通用飞机事件
 * {
 *
 *      "seqId": "xxxxx", //唯一标识
 *
 *      "sendUid" : 50014431, //送礼UID
 *
 *      "sid" : 87814665, //频道
 *
 *      "ssid" : 87814665, //子频道
 *
 *      "compereUid" : 50014431, //当前主持
 *
 *      "actId" : 1, // 活动中台的活动id
 *
 *      "appId" : 1, // 营收appId 2 -交友 34 -语音房 36 -宝贝
 *
 *      "platform" : 0, // 平台
 *
 *      "channel" : 0, // 营收渠道：usedChannel
 *
 *      "systemInfo" : 10, // 系统
 *
 *      "propsId" : 20004, // 礼物id
 *
 *      "count": 10, // 每次连击的礼物数量
 *
 *      "comboHits" : 5, // 连击次数
 *
 *      "broadcastType" : 4, // 广播类型 1 -子频道 2 -全频道 3 -全业务 4 -全平台
 *
 *      "broadcastLevel" : 3, // 广播等级 1 2 3 4
 *
 *      "star" : 3, // 流光等级
 *
 *      "receivers" : [ //收礼人信息
 *
 *        {
 *
 *           "uid": 12345, // 收礼人UID
 *
 *           "role": 0, // 角色 暂未使用
 *
 *           "pos": 0, // 嘉宾信息 暂未使用
 *
 *        }
 *
 *      ],
 *
 *      "timestamp" : 1618562828100, //时间戳 注：毫秒级
 *
 *      "usedMulti" : 0, // 0 -普通送礼 1 -全麦打赏 2 -多人打赏
 *
 * }
 */
@Data
public class TurnoverCommonPlaneEvent {
    private String seqId;

    //送礼UID
    private long sendUid;

    private long sid;

    private long ssid;

    //当前主持
    private long compereUid;

    // 活动中台的活动id
    private long actId;

    // 营收appId 2 -交友 34 -语音房 36 -宝贝 58 -丫丫
    private int appId;

    // 平台
    private int platform;

    // 营收渠道：usedChannel
    private long channel;

    // 系统
    private long systemInfo;

    // 礼物id
    private long propsId;

    // 每次连击的礼物数量
    private long count;

    // 连击次数
    private long comboHits;

    // 广播类型 1 -子频道 2 -全频道 3 -全业务 4 -全平台
    private int broadcastType;

    // 广播等级 1 2 3 4
    private int broadcastLevel;

    // 流光等级
    private int star;

    //收礼人信息
    private List<Receiver> receivers;

    //时间戳 注：毫秒级
    private long timestamp;

    // 0 -普通送礼 1 -全麦打赏 2 -多人打赏
    private int usedMulti;

}

