package com.yy.gameecology.activity.bean.event;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 配置文档参考 http://jywiki.yy.com/docs/share/cross_event_push#akyqn0
 * 客户端文档：https://doc.yy.com/pages/viewpage.action?pageId=169423990
 * </pre>
 * <AUTHOR>
 * @date 2023-01-05 20:31
 **/
@Data
public class AppBannerSvgaConfig2 {
    /**
     * 1 -支持跳转 0 -不支持跳转 注：svga需要有跳转按钮
     */
    private int jump;
    /**
     * 高度 contentType==3使用 可选 高度设计，以设计图高度为准，客户端宽度为屏幕宽度，高度换算为机型高度
     */
    private int height;
    /**
     * 播放时间 单位：秒
     */
    private int duration;
    /**
     * 循环播放次数, 0-无限循环(勿填0)
     */
    private int loops = 1;

    /**
     * svga点击按钮key name
     */
    private String clickLayerName;
    /**
     * 不带围观按钮的横幅svga
     */
    private String svgaURL;

    /**
     * 带围观按钮的横幅svga
     */
    private String jumpSvgaURL;

    /**
     * 不带围观的礼物滚屏svga
     */
    private String miniURL;

    /**
     * 带围观按钮的礼物滚屏svga
     */
    private String jumpMiniURL;


    /**
     * <pre>
     * 注意：一个Map&lt;String, AppBannerSvgaText&gt;只能包含一个key，比如layerKey1。若需要多个key，则另起一个Map
     * </pre>
     */
    private List<Map<String, AppBannerSvgaText>> contentLayers;

    private List<Map<String, String>> imgLayers;


    /**
     * 可选 播放动效等候队列的优先级：优先级越高，从等候队列到播放的时机越早；值越小，优先级越高 目前默认全屏礼物特效的优先级为999，如果优先级低于全屏礼物特效优先级，需要该值大于999
     */
    private int level;


    private AppBannerLayout layout;

    /**
     * contentType==6使用 必填 宽高比 客户端默认宽为全屏，svga高度根据宽高比计算
     */
    private String whRatio;

}
