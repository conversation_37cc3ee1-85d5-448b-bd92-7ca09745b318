package com.yy.gameecology.activity.service.layer.itembuilder;

import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-07-14 16:34
 **/
@Service
public class LayerItemBuilderManager implements BeanPostProcessor {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private final Map<String, LayerItemBuilder> layerItemBuilderMap = Maps.newConcurrentMap();

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        Class<?> targetClass = AopProxyUtils.ultimateTargetClass(bean);
        if (LayerItemBuilder.class.isAssignableFrom(targetClass)) {
            LayerItemBuilder itemBuilder = (LayerItemBuilder) bean;
            for (String itemKey : itemBuilder.getItemKeys()) {
                log.info("added layer item builder itemKey:{} builder:{}", itemKey, itemBuilder);
                layerItemBuilderMap.put(itemKey, itemBuilder);
            }
        }
        return BeanPostProcessor.super.postProcessAfterInitialization(bean, beanName);
    }

    public LayerItemBuilder getBuilder(String itemKey) {
        return layerItemBuilderMap.get(itemKey);
    }
}
