package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.config.proto.ProtoCodec;
import com.yy.gameecology.activity.worker.server.SvcSDKServer;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.protocol.pb.GameecologyActivity.GameEcologyMsg;
import com.yy.service.SvcSDK;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.turnover.TFamilySsid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> 2020/7/10
 */
@Service
public class SvcSDKServiceImpl implements SvcSDKService {

    private static final Logger log = LoggerFactory.getLogger(SvcSDKServiceImpl.class);

    @Autowired
    private ProtoCodec protoCodec;

    @Autowired
    private CommonService commonService;

    @Autowired
    private SvcSDKServer svcSDKServer;

    @Autowired
    private WhiteListService whiteListService;

    @Override
    public void sdkBcUsergroup(GameEcologyMsg message) {
        throw new RuntimeException("not support");
    }

    @Override
    public void unicastUid(long uid, GameEcologyMsg message) {
        Assert.notNull(message, "message is null");
        if (SysEvHelper.isLocal() || SysEvHelper.isYuFa() || SysEvHelper.skipSvcsdkInit()) {
            return;
        }
        checkStatus();
        if (isClose(message.getUri())) {
            return;
        }

        try {
            byte[] data = protoCodec.encode(message).array();
            boolean ret = SvcSDK.getInstance().sdkUcUidMsg(uid, data);
            log.info("unicastUid uid:{} uri:{} ret:{}", uid, message.getUri(), ret);
        } catch (Exception e) {
            log.error("unicastUid uid:{} msg:{} err:{}", uid, message, e.getMessage(), e);
        }
    }

    @Override
    public void broadcastSub(long sid, long ssid, GameEcologyMsg message) {
        Assert.notNull(message, "message is null");
        if (SysEvHelper.isLocal() || SysEvHelper.isYuFa() || SysEvHelper.skipSvcsdkInit()) {
            return;
        }
        checkStatus();
        if (isClose(message.getUri())) {
            return;
        }

        try {
            byte[] data = protoCodec.encode(message).array();
            boolean ret = SvcSDK.getInstance().sdkBcSid((int) sid, (int) ssid, data);
            if (SysEvHelper.isDev()) {
                log.info("broadcastSub sid:{} ssid:{} uri:{} ret:{}", sid, ssid, message.getUri(), ret);
            }
        } catch (Exception e) {
            log.error("broadcastSub sid:{} ssid:{} msg:{} err:{}", sid, ssid, message, e.getMessage(), e);
        }
    }

    @Override
    public void broadcastTop(long sid, GameEcologyMsg message) {
        Assert.notNull(message, "message is null");
        if (SysEvHelper.isLocal() || SysEvHelper.isYuFa() || SysEvHelper.skipSvcsdkInit()) {
            return;
        }
        checkStatus();
        if (isClose(message.getUri())) {
            return;
        }

        try {
            byte[] data = protoCodec.encode(message).array();
            boolean ret = SvcSDK.getInstance().sdkBcTid((int) sid, data);
            if (SysEvHelper.isDev()) {
                log.info("broadcastTop sid:{} uri:{} ret:{}", sid, message.getUri(), ret);
            }
        } catch (Exception e) {
            log.error("broadcastTop sid:{} msg:{} err:{}", sid, message, e.getMessage(), e);
        }
    }

    @Override
    public void broadcastTemplate(Template template, GameEcologyMsg message) {
        Assert.notNull(template, "template is null");
        Assert.notNull(message, "message is null");
        if (SysEvHelper.isLocal() || SysEvHelper.isYuFa() || SysEvHelper.skipSvcsdkInit()) {
            return;
        }
        checkStatus();
        if (isClose(message.getUri())) {
            return;
        }

        try {
            byte[] data = protoCodec.encode(message).array();
            List<ChannelInfo> channelInfos = commonService.queryOnlineChannel(template);
            for (ChannelInfo channel : channelInfos) {
                boolean ret = SvcSDK.getInstance().sdkBcSid((int) channel.getSid(), (int) channel.getSsid(), data);
                log.info("broadcastTemplate template:{} sid:{} ssid:{} uri:{} ret:{}", template,
                        channel.getSid(), channel.getSsid(), message.getUri(), ret);
            }
        } catch (Exception e) {
            log.error("broadcastTemplate template:{} msg:{} err:{}", template, message, e.getMessage(), e);
        }
    }

    @Override
    public void broadcastTemplate(long actId, Template template, GameEcologyMsg message) {
        Assert.notNull(template, "template is null");
        Assert.notNull(message, "message is null");
        if (SysEvHelper.isLocal() || SysEvHelper.isYuFa() || SysEvHelper.skipSvcsdkInit()) {
            return;
        }
        checkStatus();
        if (isClose(message.getUri())) {
            return;
        }

        try {
            byte[] data = protoCodec.encode(message).array();
            List<ChannelInfo> channelInfos = commonService.queryBroChannels(actId, template);
            for (ChannelInfo channel : channelInfos) {
                boolean ret = SvcSDK.getInstance().sdkBcSid((int) channel.getSid(), (int) channel.getSsid(), data);
                log.info("broadcastTemplate template:{} sid:{} ssid:{} uri:{} ret:{}", template,
                        channel.getSid(), channel.getSsid(), message.getUri(), ret);
            }
        } catch (Exception e) {
            log.error("broadcastTemplate template:{} msg:{} err:{}", template, message, e.getMessage(), e);
        }
    }

    /**
     * 默认打开
     *
     * @param uri
     * @return
     */
    private boolean isClose(int uri) {
        boolean close = Const.GEPM.getParamValueToInt(GeParamName.SVCSDK_BROADCAST_SWITCH + uri, 1) == 0;
        if (close) {
            log.info("svcsdk isClose uri:{} close:{}", uri, close);
        }
        return close;
    }

    @Override
    public void broadcastAllChanelsInPW(long actId, GameEcologyMsg message) {
        List<ChannelInfo> channelInfos = whiteListService.getPwAllChannel(actId);

        log.info("begin bro,actId:{},size:{},uri:{}", actId, channelInfos.size(), message.getUri());
        for (ChannelInfo channelInfo : channelInfos) {
            broadcastTop(channelInfo.getSid(), message);
        }
        log.info("bro end,actId:{},channel size:{},uri:{}", actId, channelInfos.size(), message.getUri());
    }

    @Override
    public void broadcastAllChanelsInSkillCard(GameEcologyMsg message) {
        List<ChannelInfo> channelInfos = commonService.getSkillCardAllChannel();

        log.info("begin broadcastAllChanelsInSkillCard,size:{},uri:{}", channelInfos.size(), message.getUri());
        for (ChannelInfo channelInfo : channelInfos) {
            broadcastTop(channelInfo.getSid(), message);
        }
        log.info("bro broadcastAllChanelsInSkillCard,channel size:{},uri:{}", channelInfos.size(), message.getUri());
    }

    @Override
    public void broadcastFamilyInSkillCard(long familyId, GameEcologyMsg message) {
        List<ChannelInfo> familySsids = commonService.listFamilySsid2(familyId);

        log.info("begin broadcastFamilyInSkillCard,size:{},uri:{}", familySsids.size(), message.getUri());
        for (ChannelInfo ting : familySsids) {
            broadcastSub(ting.getSid(), ting.getSsid(), message);
        }
        log.info("bro broadcastFamilyInSkillCard done,ting size:{},uri:{}", familySsids.size(), message.getUri());
    }


    /**
     * @param template
     * @param message
     * @param sid      0全频道 传Temple
     * @param exssid
     */
    @Override
    public void broadcastTemplate(Template template, GameEcologyMsg message, long sid, long exssid) {
        Assert.notNull(template, "template is null");
        Assert.notNull(message, "message is null");
        if (SysEvHelper.isLocal() || SysEvHelper.isYuFa() || SysEvHelper.skipSvcsdkInit()) {
            return;
        }
        checkStatus();
        if (isClose(message.getUri())) {
            return;
        }
        try {
            byte[] data = protoCodec.encode(message).array();
            List<ChannelInfo> channelInfos = commonService.queryOnlineChannel(template);
            for (ChannelInfo channel : channelInfos) {
                long sid1 = channel.getSid();
                long ssid = channel.getSsid();
                final boolean sidMatch = sid != sid1 && sid != 0;
                if (exssid == ssid || sidMatch) {
                    continue;
                }
                boolean ret = SvcSDK.getInstance().sdkBcSid((int) channel.getSid(), (int) channel.getSsid(), data);
                log.info("broadcastTemplate template:{} sid:{} ssid:{} uri:{} ret:{}", template,
                        channel.getSid(), channel.getSsid(), message.getUri(), ret);
            }
        } catch (Exception e) {
            log.error("broadcastTemplate template:{} msg:{} err:{}", template, message, e.getMessage(), e);
        }
    }

    private void checkStatus() {
        while (!svcSDKServer.isStarted()) {
            try {
                String stack = Stream.of(Thread.currentThread().getStackTrace())
                        .map(StackTraceElement::toString)
                        .collect(Collectors.joining("\n\t"));
                log.info("SvcSDKService used at startup! {}", stack);
                TimeUnit.SECONDS.sleep(1);
            } catch (Exception e) {
                log.warn("SvcSDKServer checkStatus err:{}", e.getMessage(), e);
            }
        }
    }

}
