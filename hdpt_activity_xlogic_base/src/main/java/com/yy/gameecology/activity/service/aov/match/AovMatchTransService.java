package com.yy.gameecology.activity.service.aov.match;

import com.yy.gameecology.activity.bean.mq.HdzkAovRoundSettledEvent;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.aov.game.AovGameTransService;
import com.yy.gameecology.common.bean.UpdateMatchNode;
import com.yy.gameecology.common.consts.aov.AovConst;
import com.yy.gameecology.common.db.mapper.aov.AovMatchNodeExtMapper;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseMapper;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseMatchExtMapper;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseRoundExtMapper;
import com.yy.gameecology.common.db.model.gameecology.aov.*;
import com.yy.gameecology.common.utils.AovUtils;
import com.yy.gameecology.hdzj.element.component.attr.AovMatchComponentAttr;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AovMatchTransService {

    @Resource
    private AovPhaseMapper aovPhaseMapper;

    @Resource
    private AovMatchNodeExtMapper aovMatchNodeExtMapper;

    @Resource
    private AovPhaseRoundExtMapper aovPhaseRoundExtMapper;

    @Resource
    private AovPhaseMatchExtMapper aovPhaseMatchExtMapper;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private AovGameTransService aovGameTransService;

    @Transactional(rollbackFor = Throwable.class)
    public void saveMatchNodes(AovPhase phase, List<AovPhaseRound> rounds, List<AovPhaseTeam> teams) {
        int rs = aovPhaseMapper.updatePhaseState(phase.getId(), AovConst.PhaseState.SUCC, AovConst.PhaseState.INITIALIZED);
        if (rs <= 0) {
            return;
        }

        log.info("saveMatchNodes update phase state with phaseId:{} rs:{}", phase.getId(), rs);

        final int teamSize = teams.size();

        // 需要比拼的轮次数
        final int roundCount = AovUtils.getRoundCount(teamSize);

        final int firstRoundNum = roundCount + 1, firstRoundNodeSize = 1 << roundCount;

        final int byeSize = firstRoundNodeSize - teamSize;
        Assert.isTrue(byeSize >= 0, "bye size must not negative");
        Map<Long, Long> teamUidMap = new HashMap<>(teamSize);

        // 轮空队伍
        List<Long> byeTeamIds = new ArrayList<>(firstRoundNodeSize);
        // 需要battle的队伍
        List<Long> battleTeamIds = new ArrayList<>(teamSize);
        for (int i = 0; i < teamSize; i++) {
            AovPhaseTeam team = teams.get(i);
            long teamId = team.getId();
            teamUidMap.put(teamId, team.getCreator());
            // 分高的进入轮空
            if (i < byeSize) {
                byeTeamIds.add(teamId);
                byeTeamIds.add(-1L);
                continue;
            }

            battleTeamIds.add(teamId);
        }

        log.info("saveMatchNodes processing byeTeamIds:{} battleTeamIds:{}", byeTeamIds, battleTeamIds);
        Assert.isTrue(battleTeamIds.size() % 2 == 0, "battle team's size must be even number");

        // 先打乱需要battle的队伍，再加入总队伍的尾部
        Collections.shuffle(battleTeamIds);
        byeTeamIds.addAll(battleTeamIds);

        for (AovPhaseRound round : rounds) {
            if (round.getRoundNum() > firstRoundNum) {
                continue;
            }

            final boolean firstRound = round.getRoundNum() == firstRoundNum;
            int startNodeIndex = 1 << (round.getRoundNum() - 1), roundNodeSize = startNodeIndex;
//            int endNodeIndex = (1 << round.getRoundNum()) - 1;
            List<AovMatchNode> nodes = new ArrayList<>(roundNodeSize);
            for (int i = 0; i < roundNodeSize; i++) {
                int nodeIndex = startNodeIndex + i;
                AovMatchNode node = new AovMatchNode();
                node.setPhaseId(round.getPhaseId());
                node.setRoundId(round.getId());
                node.setNodeIndex(nodeIndex);

                // 判断是否是第一轮
                if (firstRound) {
                    long teamId = byeTeamIds.get(i);
                    node.setTeamId(teamId);
                    node.setUid(teamUidMap.getOrDefault(teamId, -1L));
                    node.setState(AovConst.MatchNodeState.ADJUSTING);
                } else {
                    node.setState(AovConst.MatchNodeState.INIT);
                }

                nodes.add(node);
            }

            // 批量插入
            rs = aovMatchNodeExtMapper.batchInsertMatchNodes(nodes);
            log.info("saveMatchNodes batch insert nodes with roundNum:{} rs:{}", round.getRoundNum(), rs);

            if (firstRound) {
                rs = aovPhaseRoundExtMapper.updateRoundState(round.getId(), AovConst.RoundState.INIT, AovConst.RoundState.ADJUSTING);
                log.info("saveMatchNodes update first round round state rs:{}", rs);
            }
        }

        // 插入一个nodeIndex = 1的冠军节点
        AovMatchNode node = new AovMatchNode();
        node.setPhaseId(phase.getId());
        node.setRoundId(-1L);
        node.setNodeIndex(1);
        node.setState(AovConst.MatchNodeState.INIT);

        rs = aovMatchNodeExtMapper.insertSelective(node);
        log.info("saveMatchNodes insert no.1 node rs:{}", rs);
    }

    /**
     *
     * @param attr
     * @param round
     * @param nodes
     * @param now
     */
    @Transactional(rollbackFor = Throwable.class)
    public void saveCloseAdjustingRound(AovMatchComponentAttr attr, AovPhaseRound round, List<AovMatchNode> nodes, Date now) {
        int rs = aovPhaseRoundExtMapper.updateRoundState(round.getId(), AovConst.RoundState.ADJUSTING, AovConst.RoundState.GAME_CREATABLE);
        if (rs <= 0) {
            return;
        }

        List<Long> nodeIds = nodes.stream().map(AovMatchNode::getId).toList();
        rs = aovMatchNodeExtMapper.updateMatchNodesState(nodeIds, AovConst.MatchNodeState.ADJUSTING, AovConst.MatchNodeState.GAME_CREATABLE);
        log.info("saveCloseAdjustingRound update nodeIds:{} rs:{}", nodeIds, rs);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void saveCreateAutoGames(AovMatchComponentAttr attr, AovPhaseRound round, List<AovMatchNode> nodes, Date now) {
        int rs = aovPhaseRoundExtMapper.updateRoundState(round.getId(), AovConst.RoundState.GAME_CREATABLE, AovConst.RoundState.GAME_CREATED);
        log.info("saveCreateNodeGames update with roundId:{} roundNum:{} rs:{}", round.getId(), round.getRoundNum(), rs);
        if (rs <= 0) {
            return;
        }

        Map<Integer, AovMatchNode> nodeMap = nodes.stream().collect(Collectors.toMap(AovMatchNode::getNodeIndex, Function.identity()));
        for (Map.Entry<Integer, AovMatchNode> entry : nodeMap.entrySet()) {
            int nodeIndex = entry.getKey();
            if (nodeIndex % 2 == 1) {
                continue;
            }

            AovMatchNode node1 = entry.getValue();
            AovMatchNode node2 = nodeMap.get(nodeIndex + 1);
            Assert.notNull(node2, "opponent cannot be null");
            Assert.isTrue(node1.getTeamId() > 0, "camp1 team must not bye");

            // 轮空局
            if (node2.getTeamId() < 0L) {
                createByeGame(node1, node2);
                continue;
            }

            createAutoNormalGame(round, node1, node2);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    public void saveCreateManualGames(AovMatchComponentAttr attr, AovPhaseRound round, List<AovMatchNode> nodes, Date now) {
        Map<Integer, AovMatchNode> nodeMap = nodes.stream().collect(Collectors.toMap(AovMatchNode::getNodeIndex, Function.identity()));
        for (Map.Entry<Integer, AovMatchNode> entry : nodeMap.entrySet()) {
            int nodeIndex = entry.getKey();
            if (nodeIndex % 2 == 1) {
                continue;
            }

            AovMatchNode node1 = entry.getValue();
            AovMatchNode node2 = nodeMap.get(nodeIndex + 1);
            Assert.notNull(node2, "opponent cannot be null");
            Assert.isTrue(node1.getTeamId() > 0, "camp1 team must not bye");

            // 轮空局
            if (node2.getTeamId() < 0L) {
                createByeGame(node1, node2);
                continue;
            }

            createManualNormalGame(round, node1, node2);
        }
    }

    /**
     * 轮空的对局（直接更新node的状态即可）
     * @param node1
     * @param node2
     */
    public void createByeGame(AovMatchNode node1, AovMatchNode node2) {
        int rs = aovMatchNodeExtMapper.updateMatchNode(new UpdateMatchNode(node1.getId(), AovConst.MatchNodeState.GAME_CREATABLE, AovConst.MatchNodeState.RESULTED, null, null, null, AovConst.GameTeamState.RESULTED_WIN, 1, null));
        log.info("createByeGame update node state with nodeIndex1:{} teamId:{} rs:{}", node1.getNodeIndex(), node1.getTeamId(), rs);
        rs = aovMatchNodeExtMapper.updateMatchNode(new UpdateMatchNode(node2.getId(), AovConst.MatchNodeState.GAME_CREATABLE, AovConst.MatchNodeState.RESULTED, null, null, null, AovConst.GameTeamState.CANCEL_NOT_READY, 0, null));
        log.info("createByeGame update node state with nodeIndex2:{} teamId:{} rs:{}", node2.getNodeIndex(), node2.getTeamId(), rs);
    }

    /**
     * 根据事先的配置自动创建的比赛
     * @param round
     * @param node1
     * @param node2
     */
    public void createAutoNormalGame(AovPhaseRound round, AovMatchNode node1, AovMatchNode node2) {
        int bo = round.getBo(), battleMode = round.getBattleMode();
        String roundName = round.getRoundName();
        Date startTime = round.getStartTime();
        AovPhaseMatch match = aovPhaseMatchExtMapper.selectByMatchUniq(round.getPhaseId(), round.getRoundNum(), node1.getNodeIndex(), node2.getNodeIndex());
        if (match != null) {
            bo = match.getBo();
            battleMode = match.getBattleMode();
            roundName = match.getRoundName();
            startTime = match.getStartTime();
        }
        long gameId = aovGameTransService.createNextGameAndTeam(node1, node2, round.getRoundNum(), roundName, bo, battleMode, 1, startTime);
        log.info("createAutoNormalGame create game and team request with nodeIndex1:{} nodeIndex2:{} gameId:{}", node1.getNodeIndex(), node2.getNodeIndex(), gameId);
        if (gameId <= 0) {
            throw new RuntimeException("create game fail gameId:" + gameId);
        }
        int rs = aovMatchNodeExtMapper.updateMatchNode(new UpdateMatchNode(node1.getId(), AovConst.MatchNodeState.GAME_CREATABLE, AovConst.MatchNodeState.GAME_CREATED, null, null, null, null, null, null));
        log.info("createAutoNormalGame update node state with nodeIndex:{} teamId:{} rs:{}", node1.getNodeIndex(), node1.getTeamId(), rs);
        rs = aovMatchNodeExtMapper.updateMatchNode(new UpdateMatchNode(node2.getId(), AovConst.MatchNodeState.GAME_CREATABLE, AovConst.MatchNodeState.GAME_CREATED, null, null, null, null, null, null));
        log.info("createAutoNormalGame update node state with nodeIndex:{} teamId:{} rs:{}", node2.getNodeIndex(), node2.getTeamId(), rs);
    }

    /**
     * 根据手动设置的比赛信息创建比赛
     * @param round
     * @param node1
     * @param node2
     */
    public void createManualNormalGame(AovPhaseRound round, AovMatchNode node1, AovMatchNode node2) {
        AovPhaseMatch match = aovPhaseMatchExtMapper.selectByMatchUniq(round.getPhaseId(), round.getRoundNum(), node1.getNodeIndex(), node2.getNodeIndex());
        if (match == null) {
            log.warn("createManualNormalGame with nodeIndex:{} {} match not exist", node1.getNodeIndex(), node2.getNodeIndex());
            return;
        }

        int bo = match.getBo(), battleMode = match.getBattleMode();
        String roundName = match.getRoundName();
        Date startTime = match.getStartTime();

        long gameId = aovGameTransService.createNextGameAndTeam(node1, node2, round.getRoundNum(), roundName, bo, battleMode, 1, startTime);
        log.info("createManualNormalGame create game and team request with nodeIndex1:{} nodeIndex2:{} gameId:{}", node1.getNodeIndex(), node2.getNodeIndex(), gameId);
        if (gameId <= 0) {
            throw new RuntimeException("create game fail gameId:" + gameId);
        }
        int rs = aovMatchNodeExtMapper.updateMatchNode(new UpdateMatchNode(node1.getId(), AovConst.MatchNodeState.GAME_CREATABLE, AovConst.MatchNodeState.GAME_CREATED, null, null, null, null, null, null));
        log.info("createManualNormalGame update node state with nodeIndex:{} teamId:{} rs:{}", node1.getNodeIndex(), node1.getTeamId(), rs);
        rs = aovMatchNodeExtMapper.updateMatchNode(new UpdateMatchNode(node2.getId(), AovConst.MatchNodeState.GAME_CREATABLE, AovConst.MatchNodeState.GAME_CREATED, null, null, null, null, null, null));
        log.info("createManualNormalGame update node state with nodeIndex:{} teamId:{} rs:{}", node2.getNodeIndex(), node2.getTeamId(), rs);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void settleRound(AovMatchComponentAttr attr, AovPhaseRound round, AovPhaseRound nextRound, Map<Integer, AovMatchNode> nodeMap, Date now) {
        int rs = aovPhaseRoundExtMapper.updateRoundState(round.getId(), AovConst.RoundState.GAME_CREATED, AovConst.RoundState.ADVANCED);
        log.info("settleRound update round state with roundId:{} roundNum:{} rs:{}", round.getId(), round.getRoundNum(), rs);
        if (rs <= 0) {
            return;
        }
        // 记录未晋级的节点
        Map<Integer, AovMatchNode> candidates = new HashMap<>(nodeMap);
        // 记录未能晋级的目标节点
        List<Integer> lackAdvanceIndexes = new ArrayList<>(nodeMap.size());

        List<Long> advancedTeamIds = new ArrayList<>(nodeMap.size());
        List<Long> substituteTeamIds = new ArrayList<>(nodeMap.size());
        List<Long> eliminatedTeamIds = new ArrayList<>(nodeMap.size());

        // 第一轮直接取胜负关系
        for (Map.Entry<Integer, AovMatchNode> entry : nodeMap.entrySet()) {
            int nodeIndex = entry.getKey();
            if (nodeIndex % 2 == 1) {
                continue;
            }

            AovMatchNode node1 = entry.getValue();
            AovMatchNode node2 = nodeMap.get(nodeIndex + 1);

            AovMatchNode advancedNode = tryAdvance(round, node1, node2);

            if (advancedNode != null) {
                candidates.remove(advancedNode.getNodeIndex());
                advancedTeamIds.add(advancedNode.getTeamId());
            } else {
                int advanceNodeIndex = nodeIndex / 2;

                // 决赛无队伍晋级，直接结束就好
                if (advanceNodeIndex == 1) {
                    // 决赛无队伍晋级则都更新为被淘汰
                    rs = aovMatchNodeExtMapper.updateMatchNodesState(List.of(node1.getId(), node2.getId()), AovConst.MatchNodeState.RESULTED, AovConst.MatchNodeState.ELIMINATED);
                    log.error("final game updateMatchNodesState rs:{}", rs);

                    return;
                }

                lackAdvanceIndexes.add(advanceNodeIndex);
            }
        }

        log.info("settleRound after settle lackAdvanceIndexes:{} candidates:{}", lackAdvanceIndexes, candidates);

        // 处理尚未完成晋级的node
        if (!lackAdvanceIndexes.isEmpty()) {
            // 通过补位晋级
            List<AovMatchNode> sortedCandidates = candidates.values().stream()
                    .filter(node -> node.getTeamId() > 0)
                    .sorted((n1, n2) -> n2.getAliveScore() - n1.getAliveScore())
                    .toList();

            for (int i = 0; i < lackAdvanceIndexes.size(); i++) {
                int advanceNodeIndex = lackAdvanceIndexes.get(i);
                AovMatchNode substitute = sortedCandidates.get(i);
                substituteTeamIds.add(substitute.getTeamId());
                saveSubstituteAdvanceNode(round, advanceNodeIndex, substitute);
                candidates.remove(substitute.getNodeIndex());
            }
        }

        // 剩余的candidate状态更新为被淘汰（ELIMINATED）
        if (!candidates.isEmpty()) {
            Set<Long> nodeIds = new HashSet<>(candidates.size());
            for (AovMatchNode node : candidates.values()) {
                nodeIds.add(node.getId());
                if (node.getTeamId() > 0) {
                    eliminatedTeamIds.add(node.getTeamId());
                }
            }
            rs = aovMatchNodeExtMapper.updateMatchNodesState(nodeIds, AovConst.MatchNodeState.RESULTED, AovConst.MatchNodeState.ELIMINATED);
            if (rs < nodeIds.size()) {
                throw new RuntimeException("update eliminated state fail expected size:" + nodeIds.size() + " actual size:" + rs);
            }
        }

        // 完成晋级，将下一轮状态修改为 可创建游戏
        if (nextRound != null) {
            rs = aovPhaseRoundExtMapper.updateRoundState(nextRound.getId(), AovConst.RoundState.INIT, AovConst.RoundState.GAME_CREATABLE);
            log.info("settleRound update next round game creatable with roundNum:{} rs:{}", nextRound.getRoundNum(), rs);
        } else { // 没有下一轮，说明已经是决赛，再发布一个结算roundNum=1的轮次结算事件
            HdzkAovRoundSettledEvent event = new HdzkAovRoundSettledEvent();
            event.setSeq(String.format("round_settle:%d_%d", round.getPhaseId(), round.getRoundNum() - 1));
            event.setRoundNum(round.getRoundNum() - 1);
            event.setRoundName("决赛");
            event.setActId(attr.getActId());
            event.setPhaseId(round.getPhaseId());
            event.setAdvancedTeamIds(Collections.emptyList());
            event.setSubstituteTeamIds(Collections.emptyList());
            event.setEliminatedTeamIds(advancedTeamIds);
            event.setSettleTime(now);

            kafkaService.sendHdzkCommonEvent(event);
            log.info("without nextRound send round 1 kafka");
        }

        // 发布本轮次晋级完成事件，用于奖励
        HdzkAovRoundSettledEvent event = new HdzkAovRoundSettledEvent();
        event.setSeq(String.format("round_settle:%d_%d", round.getPhaseId(), round.getRoundNum()));
        event.setRoundNum(round.getRoundNum());
        event.setRoundName(round.getRoundName());
        event.setActId(attr.getActId());
        event.setPhaseId(round.getPhaseId());
        event.setAdvancedTeamIds(advancedTeamIds);
        event.setSubstituteTeamIds(substituteTeamIds);
        event.setEliminatedTeamIds(eliminatedTeamIds);
        event.setSettleTime(now);

        kafkaService.sendHdzkCommonEvent(event);
    }

    /**
     * 尝试通过对决的胜负关系晋级
     * @param round
     * @param node1
     * @param node2
     * @return
     */
    private AovMatchNode tryAdvance(AovPhaseRound round, AovMatchNode node1, AovMatchNode node2) {
        int advanceNodeIndex = node1.getNodeIndex() / 2;

        int advanceRequiredScore = round.getBo() / 2 + 1;
        if (node1.getScore() >= advanceRequiredScore) {
            saveWinAdvanceNode(round, advanceNodeIndex, node1);
            return node1;
        } else if (node2.getScore() >= advanceRequiredScore) {
            saveWinAdvanceNode(round, advanceNodeIndex, node2);
            return node2;
        }

        // 平分
        return null;
    }

    /**
     * 保存获胜直接晋级的node
     * @param round
     * @param advanceNodeIndex
     * @param win
     */
    private void saveWinAdvanceNode(AovPhaseRound round, int advanceNodeIndex, AovMatchNode win) {
        AovMatchNode advanceNode = aovMatchNodeExtMapper.selectNode(round.getPhaseId(), advanceNodeIndex);
        Assert.notNull(advanceNode, "win advance node cannot be null");

        int rs = aovMatchNodeExtMapper.updateMatchNode(new UpdateMatchNode(win.getId(), AovConst.MatchNodeState.RESULTED, AovConst.MatchNodeState.ADVANCED, null, null, null, null, null, null));
        log.info("update win state with nodeIndex:{} rs:{}", win.getNodeIndex(), rs);
        if (rs <= 0) {
            throw new RuntimeException("win node update fail");
        }

        int advanceTargetState = advanceNodeIndex == 1 ? AovConst.MatchNodeState.ELIMINATED : AovConst.MatchNodeState.GAME_CREATABLE;

        rs = aovMatchNodeExtMapper.updateMatchNode(new UpdateMatchNode(advanceNode.getId(), AovConst.MatchNodeState.INIT, advanceTargetState, win.getTeamId(), win.getUid(), AovConst.AdvanceType.WIN, null, null, null));
        log.info("update win advance node with nodeIndex:{} win nodeIndex:{} rs:{}", advanceNodeIndex, win.getNodeIndex(), rs);
        if (rs <= 0) {
            throw new RuntimeException("win advance node update fail");
        }
    }

    private void saveSubstituteAdvanceNode(AovPhaseRound round, int advanceNodeIndex, AovMatchNode substitute) {
        AovMatchNode advanceNode = aovMatchNodeExtMapper.selectNode(round.getPhaseId(), advanceNodeIndex);
        Assert.notNull(advanceNode, "substitute advance node cannot be null");

        int rs = aovMatchNodeExtMapper.updateMatchNode(new UpdateMatchNode(substitute.getId(), AovConst.MatchNodeState.RESULTED, AovConst.MatchNodeState.ADVANCED, null, null, null, null, null, null));
        log.info("update substitute state with nodeIndex:{} rs:{}", substitute.getNodeIndex(), rs);
        if (rs <= 0) {
            throw new RuntimeException("substitute node update fail");
        }
        rs = aovMatchNodeExtMapper.updateMatchNode(new UpdateMatchNode(advanceNode.getId(), AovConst.MatchNodeState.INIT, AovConst.MatchNodeState.GAME_CREATABLE, substitute.getTeamId(), substitute.getUid(), AovConst.AdvanceType.SUBSTITUTE, null, null, null));
        log.info("update substitute advance node with nodeIndex:{} substitute nodeIndex:{} rs:{}", advanceNode.getNodeIndex(), substitute.getNodeIndex(), rs);
        if (rs <= 0) {
            throw new RuntimeException("substitute advance node update fail");
        }
    }

    public static int getAliveScore(int teamState) {
        return switch (teamState) {
            case AovConst.GameTeamState.RESULTED_WIN -> 1000;
            case AovConst.GameTeamState.RESULTED_LOSE -> 995;
            case AovConst.GameTeamState.NO_RESULT -> 100;
            case AovConst.GameTeamState.CANCEL_READY -> 10;
            case AovConst.GameTeamState.CANCEL_NOT_READY -> -1000;
            default -> 0;
        };
    }
}
