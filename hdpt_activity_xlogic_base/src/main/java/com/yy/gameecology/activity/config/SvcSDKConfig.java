package com.yy.gameecology.activity.config;

import com.yy.gameecology.activity.config.proto.ProtoCodec;
import com.yy.gameecology.activity.config.proto.ProtoHandlerBeanPostProcessor;
import com.yy.gameecology.activity.config.proto.ProtoHandlerMapping;
import com.yy.gameecology.activity.config.svcsdk.MessageInvoker;
import com.yy.gameecology.activity.config.svcsdk.SvcSDKCallback;
import com.yy.gameecology.common.support.SysEvHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.LoggingErrorHandler;

/**
 * <AUTHOR> 2020/7/22
 */
@Slf4j
@Configuration
public class SvcSDKConfig {

    @Bean
    public ProtoHandlerMapping protoHandlerMapping() {
        return new ProtoHandlerMapping();
    }

    @Bean
    public ProtoHandlerBeanPostProcessor protoHandlerBeanPostProcessor(ProtoHandlerMapping protoHandlerMapping) {
        return new ProtoHandlerBeanPostProcessor(protoHandlerMapping);
    }

    @Bean
    public ProtoCodec protoCodec() {
        return new ProtoCodec();
    }

    @Bean
    public MessageInvoker messageInvoker(ProtoHandlerMapping protoHandlerMapping, ProtoCodec protoCodec) {
        return new MessageInvoker(protoHandlerMapping, protoCodec);
    }

    @Bean
    public SvcSDKCallback svcSDKCallback(MessageInvoker messageInvoker) {
        return new SvcSDKCallback(messageInvoker);
    }

    @Bean("svcGatewayWxKafkaProperties")
    @ConfigurationProperties("yy.kafka.svc.gateway.wx")
    public KafkaProperties svcGatewayWxKafkaProperties() {
        return new KafkaProperties();
    }

    @Bean(name = "svcGatewayWxContainerFactory")
    public KafkaListenerContainerFactory svcGatewayWxContainerFactory(
            @Qualifier("svcGatewayWxKafkaProperties") KafkaProperties kafkaProperties) {
        ConcurrentKafkaListenerContainerFactory containerFactory = new ConcurrentKafkaListenerContainerFactory();
        if (SysEvHelper.isLocal() || SysEvHelper.isYuFa() || SysEvHelper.isHistory()) {
            log.warn("当前环境默认不启动Consumer.");
            containerFactory.setAutoStartup(false);
        }
        containerFactory.setConcurrency(2);
        kafkaProperties.getConsumer().setEnableAutoCommit(false);;
        containerFactory.setErrorHandler(new LoggingErrorHandler() {
            @Override
            public boolean isAckAfterHandle() {
                return false;
            }
        });
        containerFactory.setConsumerFactory(
                new DefaultKafkaConsumerFactory(kafkaProperties.buildConsumerProperties()));
        return containerFactory;
    }

}
