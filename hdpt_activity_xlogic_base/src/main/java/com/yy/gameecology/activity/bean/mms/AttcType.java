package com.yy.gameecology.activity.bean.mms;

public enum  AttcType {
    /**图片*/
    IMG("IMG"),
    /**文本*/
    TEXT("TEXT"),
    /**视频文件*/
    VIDEO_FILE("VIDEO_FILE"),
    /**视频 暂时没有用*/
    VIDEO("VIDEO"),
    /**音频*/
    AUDIO("AUDIO"),
    /**JSON 暂时没有用*/
    JSON("JSON"),
    /**富媒体混合类型*/
    RICH_MEDIA("RICH_MEDIA"),
    /**自定义音视频流类型*/
    MEDIA_STREAM("MEDIA_STREAM"),
    /** 富媒体 */
    EMI("EMI");
    private String type;
    AttcType(String type){
        this.type = type;
    }

    public String getType() {
        return type;
    }
}
