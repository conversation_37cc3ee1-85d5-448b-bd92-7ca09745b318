package com.yy.gameecology.activity.bean.anchorwelfare;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

@Data
public class LoginTaskAward {

    @ComponentAttrField(labelText = "新用户奖励", remark = "单位厘")
    private int newUserAward;

    @ComponentAttrField(labelText = "老用户奖励", remark = "单位厘")
    private int oldUserAward;

    @ComponentAttrField(labelText = "任务名称")
    private String taskName;
}
