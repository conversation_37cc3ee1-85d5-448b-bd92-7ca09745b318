package com.yy.gameecology.activity.bean.rank;

/**
 * desc: cp榜元素
 *
 * @createBy 曾文帜
 * @create 2020-07-21 16:14
 **/
public class RankItemUserAnchor extends RankItemBase {
    //主播
    private BabyRankItem babyRankItem;
    //用户
    private UserRankItem userRankItem;

    //分值
    private Long score;

    private String cpMember;

    @Override
    public void setKey(String key) {
        this.cpMember = key;
    }

    @Override
    public String getKey() {
        return this.cpMember;
    }

    public Long getScore() {
        return score;
    }

    public void setScore(Long score) {
        this.score = score;
    }

    public BabyRankItem getBabyRankItem() {
        return babyRankItem;
    }

    public void setBabyRankItem(BabyRankItem babyRankItem) {
        this.babyRankItem = babyRankItem;
    }

    public UserRankItem getUserRankItem() {
        return userRankItem;
    }

    public void setUserRankItem(UserRankItem userRankItem) {
        this.userRankItem = userRankItem;
    }




}
