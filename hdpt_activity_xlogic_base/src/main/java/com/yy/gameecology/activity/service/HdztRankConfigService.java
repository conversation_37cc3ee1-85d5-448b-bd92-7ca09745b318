package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.actlayer.PhaseInfo;
import com.yy.gameecology.activity.bean.hdzt.RankingConfigResponse;
import com.yy.gameecology.activity.bean.hdzt.RankingConfigVo;
import com.yy.gameecology.activity.bean.hdzt.RankingPhaseGroupVo;
import com.yy.gameecology.activity.bean.hdzt.RankingPhaseVo;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.common.consts.PhaseInfoExtField;
import com.yy.gameecology.common.consts.RankingConfigExt;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.hdztranking.RankingInfo;
import com.yy.thrift.hdztranking.RankingPhaseInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-23 12:29
 **/
@Service
public class HdztRankConfigService {
    private static final Logger log = LoggerFactory.getLogger(HdztRankConfigService.class);

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private CommonService commonService;

    /**
     * 获取活动所有榜单配置
     *
     * @param actId 活动id
     * @return 榜单配置
     */
    public RankingConfigResponse getRankConfig(Long actId) {
        RankingConfigResponse response = new RankingConfigResponse();
        Map<Long, RankingInfo> rankingInfoMap = hdztRankingThriftClient.queryRankConfig(actId, commonService.getNow(actId));
        if (MapUtils.isEmpty(rankingInfoMap)) {
            return response;
        }
        List<RankingConfigVo> rankVos = Lists.newArrayList();
        //所有榜单
        for (Long rankId : rankingInfoMap.keySet()) {
            RankingInfo rankingInfo = rankingInfoMap.get(rankId);
            if (rankingInfo == null) {
                continue;
            }
            RankingConfigVo vo = toRankVo(rankingInfo);
            rankVos.add(vo);
        }

        response.setRanks(rankVos);
        return response;
    }

    public RankingConfigVo getSingleRankConfig(Long actId, Long rankId) {
        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, rankId);
        return toRankVo(rankingInfo);
    }

    private RankingConfigVo toRankVo(RankingInfo rankingInfo) {
        RankingConfigVo vo = new RankingConfigVo();
        vo.setRankingId(rankingInfo.getRankingId());
        vo.setRankingName(rankingInfo.getRankingName());
        //1代表天榜 2代表小时榜
        vo.setTimeKey(rankingInfo.getTimeKey() + "");
        vo.setCalBeginTime(rankingInfo.getCalBeginTime() / 1000 + "");
        vo.setCalEndTime(rankingInfo.getCalEndTime() / 1000 + "");
        vo.setTimeKey(rankingInfo.getTimeKey() + "");
        vo.setTimeKeyBegin(rankingInfo.getTimeKeyBegin());
        vo.setTimeKeyEnd(rankingInfo.getTimeKeyEnd());


        //当前阶段
        if (rankingInfo.getCurrentPhase() != null) {
            RankingPhaseVo curRankingPhaseVo = new RankingPhaseVo();
            BeanUtils.copyProperties(rankingInfo.getCurrentPhase(), curRankingPhaseVo);
            vo.setCurrentPhase(curRankingPhaseVo);
        }
        //所有阶段
        if (MapUtils.isNotEmpty(rankingInfo.getPhasesMap())) {
            List<RankingPhaseVo> rankingPhaseVos = Lists.newArrayList();
            for (Long phaseId : rankingInfo.getPhasesMap().keySet()) {
                RankingPhaseVo rankingPhaseVo = new RankingPhaseVo();
                BeanUtils.copyProperties(rankingInfo.getPhasesMap().get(phaseId), rankingPhaseVo);
                rankingPhaseVos.add(rankingPhaseVo);
            }
            vo.setPhases(rankingPhaseVos);
        }

        return vo;

    }


    //先7夕活动定制查询，后面改中台阶段配置信息实现
    public List<RankingPhaseGroupVo> getQiXiRankingPhase(Long actId) {
        log.info("getQiXiRankingPhase here, actId:{}", actId);
        return getRankingPhaseGroup(actId, Lists.newArrayList("ZBFQ,ZBHQ", "GHFQ,GHDJS,GHHQ", "JYTT"));
    }

    /**
     * 是否需要分组，1需要，0不需要
     * @param rankingInfo
     * @return
     */
    public int getRankNeedGroup(RankingInfo rankingInfo) {
        return getRankExtValue(rankingInfo, RankingConfigExt.NEED_GROUP_KEY);
    }

    /**
     * @see com.yy.gameecology.common.consts.GeRankType
     */
    public int getRankType(RankingInfo rankingInfo) {
        return getRankExtValue(rankingInfo, RankingConfigExt.RANK_TYPE_KEY);
    }

    /**
     * @see com.yy.gameecology.common.consts.RankMemberType
     */
    public int getMemberType(RankingInfo rankingInfo) {
        return getRankExtValue(rankingInfo, RankingConfigExt.MEMBER_TYPE_KEY);
    }


    /**
     * @see com.yy.gameecology.common.bean.Template
     */
    public int getTemplateType(RankingInfo rankingInfo) {
        return getRankExtValue(rankingInfo, RankingConfigExt.TEMPLATE_TYPE_KEY);
    }


    /**
     * 标识榜单扩展信息数据来源
     *
     * @param rankingInfo 榜单配置
     * @param groupId     分组id
     * @return 返回0的时候代表不用去业务读取，直接用udb的数据
     */
    public int getRankDataSource(RankingInfo rankingInfo, String groupId) {
        return getRankExtValue(rankingInfo, RankingConfigExt.RANK_DATA_SOURCE + groupId);
    }

    public List<Integer> getPkItemShowOrder(RankingInfo rankingInfo, long phaseId) {
        Map<String, String> ext = rankingInfo.getExtData();
        String showOrderMapStr = MapUtils.getString(ext, RankingConfigExt.PK_ITEM_SHOW_ORDER, "{}");
        if (!StringUtils.startsWith(showOrderMapStr, StringUtil.OPEN_BRACE)) {
            return Collections.emptyList();
        }

        Map<Long, Long> showOrderMap = JSON.parseObject(showOrderMapStr, new TypeReference<HashMap<Long, Long>>(){});

        long showOrder = MapUtils.getLongValue(showOrderMap, phaseId, 0);
        if (showOrder <= 0) {
            return Collections.emptyList();
        }

        List<Integer> rs = new ArrayList<>(16);
        while (showOrder != 0) {
            int temp = (int) (showOrder & 0xf);
            rs.add(temp);
            showOrder >>= 4;
        }
        return rs;
    }

    /**
     * 是否是第一阶段
     */
    public boolean isFirstPhase(PhaseInfo phaseInfo) {
        if (phaseInfo == null) {
            return false;
        }
        if (StringUtil.isEmpty(phaseInfo.getExtJson())) {
            return false;
        }
        JSONValidator jsonValidator = JSONValidator.from(phaseInfo.getExtJson());
        if (!jsonValidator.validate()) {
            return false;
        }
        try {
            JSONObject jsonObject = JSON.parseObject(phaseInfo.getExtJson());
            return Convert.toInt(jsonObject.getInteger(PhaseInfoExtField.FIRST_PHASE), 0) == 1;
        } catch (Exception e) {
            log.warn("isFirstPhase error,e:{}", e.getMessage(), e);
            return false;
        }
    }


    /**
     * 是否替换基础元素，如果替换，则不用调用udb接口读取榜单基础信息
     */
    public boolean isReplaceBaseField(RankingInfo rankingInfo, String groupId) {
        return getRankExtValue(rankingInfo, RankingConfigExt.REPLACE_BASE_FIELD_KEY + groupId) == 1;
    }


    private int getRankExtValue(RankingInfo rankingInfo, String key) {
        Map<String, String> ext = rankingInfo.getExtData();
        if (ext == null || !ext.containsKey(key)) {
            log.warn("getRankType not config,actId:{},rankId:{},key:{}", rankingInfo.getActId(), rankingInfo.getRankingId(), key);
            return 0;
        }
        return Convert.toInt(ext.get(key));
    }

    /**
     * 是否需要参赛资格检查
     */
    public boolean isRaceQualification(RankingInfo rankingInfo){
        Map<String, String> ext = rankingInfo.getExtData();
        if (ext == null || !ext.containsKey(RankingConfigExt.RACE_QUALIFICATION_FLAG)) {
            return false;
        }
        return "1".equals(ext.get(RankingConfigExt.RACE_QUALIFICATION_FLAG));
    }


    /**
     * 获取赛程信息
     * 对阶段组中时间有断层并且当前时间在断层内的，当前阶段是在断层的上一个阶段，这个接口代码最好不要使用，只给前端用
     * @param actId
     * @param groupCodes
     * @return
     */
    public List<RankingPhaseGroupVo> getRankingPhaseGroup(Long actId, List<String> groupCodes) {
        List<RankingPhaseGroupVo> res = Lists.newArrayList();
        Long now = commonService.getNow(actId).getTime();
        List<RankingPhaseInfo> rankingPhaseInfos = hdztRankingThriftClient.queryRankingPhaseInfo(actId);
        Map<String, List<RankingPhaseInfo>> phaseInfosMap = rankingPhaseInfos.stream()
                .collect(Collectors.groupingBy(RankingPhaseInfo::getPhaseGroupCode));

        for (String groupCode : groupCodes) {
            RankingPhaseGroupVo groupVo = new RankingPhaseGroupVo();
            groupVo.setGroupCode(groupCode);
            groupVo.setGroupName("---");

            List<RankingPhaseInfo> phaseInfos = Stream.of(groupCode.split(",")).map(code -> phaseInfosMap.get(code))
                    .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
                    .sorted(Comparator.comparing(RankingPhaseInfo::getBeginTime))
                    .collect(Collectors.toList());

            List<RankingPhaseVo> babyPhases = Lists.newArrayList();
            for (int i = 0; i < phaseInfos.size(); i++) {
                RankingPhaseInfo phaseInfo = phaseInfos.get(i);
                RankingPhaseVo vo = new RankingPhaseVo();
                BeanUtils.copyProperties(phaseInfo, vo);
                babyPhases.add(vo);
                //当前时间在活动阶段内或者是在阶段的断层内
                boolean isCurrent = (now >= phaseInfo.getBeginTime() && now < phaseInfo.getEndTime())
                        || (now >= phaseInfo.getEndTime() && i + 1 < phaseInfos.size() && now < phaseInfos.get(i + 1).getBeginTime());

                if (isCurrent) {
                    RankingPhaseVo current = new RankingPhaseVo();
                    BeanUtils.copyProperties(vo, current);
                    groupVo.setCurrentPhase(current);
                }

            }

            groupVo.setPhases(babyPhases);

            res.add(groupVo);
        }

        return res;
    }
}
