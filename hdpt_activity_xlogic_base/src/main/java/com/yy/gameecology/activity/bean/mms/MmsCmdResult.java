package com.yy.gameecology.activity.bean.mms;

import org.apache.commons.lang3.StringUtils;

public enum  MmsCmdResult {
    /**等待审核**/
    WAIT_AUDIT("0"),
    /**不违规**/
    ACCESS("1"),
    /**违规**/
    VIOLATION("2"),
    /**下载失败**/
    DOWNLOAD_FAIL("3"),
    /**已接收**/
    ACCEPTED("-1");

    private String type;
    MmsCmdResult(String type){
        this.type = type;
    }

    public static MmsCmdResult fromValue(String value){
        if(StringUtils.isBlank(value)){
            return null;
        }
        for(MmsCmdResult e : MmsCmdResult.values()){
            if(e.type.equals(value)){
                return e;
            }
        }
        return null;
    }

    public String getType() {
        return type;
    }
}

