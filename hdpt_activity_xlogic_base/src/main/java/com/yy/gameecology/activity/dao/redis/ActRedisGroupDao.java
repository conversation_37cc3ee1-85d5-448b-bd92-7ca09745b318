package com.yy.gameecology.activity.dao.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.KeyCopyResult;
import com.yy.gameecology.activity.bean.UpdateTaskReq;
import com.yy.gameecology.activity.bean.UpdateTaskResult;
import com.yy.gameecology.activity.commons.RedisSupport;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.service.datatransfer.RedisDataCopy;
import com.yy.gameecology.activity.service.datatransfer.RedisDataCopyFactory;
import com.yy.gameecology.common.bean.HashIncParameter;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import io.lettuce.core.KeyScanCursor;
import io.lettuce.core.ScanArgs;
import io.lettuce.core.api.async.RedisKeyAsyncCommands;
import lombok.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;


@Repository
public class ActRedisGroupDao {


    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Autowired
    private RedisDataCopyFactory redisDataCopyFactory;

    /**
     * @param groupCode 通过com.yy.gameecology.activity.config.redis.RedisConfigManager#getGroupCode(java.lang.Long)获得 ，如果没有活动id使用 com.yy.gameecology.activity.config.redis.RedisConfigManager#default_group_code
     * @return 根据redis分组id获得
     */
    public StringRedisTemplate getRedisTemplate(String groupCode) {
        return redisConfigManager.getRedisTemplate(groupCode);
    }

    /**
     * 获取redis服务器的时间（毫秒数）
     */
    public long time(String groupCode) {
        List<Object> list = getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            connection.time();
            return null;
        });
        return (long) list.get(0);
    }

    public <T> List<Object> zBatchIncrDouble(String groupCode, List<String> keys, List<T> vals, List<Double> incs) {
        return getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            for (int index = 0; index < keys.size(); index++) {
                connection.zIncrBy(keys.get(index).getBytes(), incs.get(index), vals.get(index).toString().getBytes());
            }
            return null;
        });
    }

    public <T> List<Object> zBatchIncr(String groupCode, List<String> keys, List<T> vals, List<Long> incs) {
        return getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            for (int index = 0; index < keys.size(); index++) {
                connection.zIncrBy(keys.get(index).getBytes(), incs.get(index), vals.get(index).toString().getBytes());
            }
            return null;
        });
    }

    public <T> List<Object> zBatchAdd(String groupCode, List<String> keys, List<T> vals, List<Long> scores) {
        return getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            for (int index = 0; index < keys.size(); index++) {
                connection.zAdd(keys.get(index).getBytes(), scores.get(index), vals.get(index).toString().getBytes());
            }
            return null;
        });
    }

    public <T> List<Object> zBatchAddDouble(String groupCode, List<String> keys, List<T> vals, List<Double> scores) {
        return getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            for (int index = 0; index < keys.size(); index++) {
                connection.zAdd(keys.get(index).getBytes(), scores.get(index), vals.get(index).toString().getBytes());
            }
            return null;
        });
    }



    public String get(String groupCode, final String key) {
        return getRedisTemplate(groupCode).opsForValue().get(key);
    }

    public long incrValue(String groupCode, final String key, final long step) {
        Long rs = getRedisTemplate(groupCode).opsForValue().increment(key, step);
        return rs == null ? 0 : rs;
    }

    public void set(String groupCode, final String key, final String value) {
        getRedisTemplate(groupCode).opsForValue().set(key, value);
    }

    public void set(String groupCode, final String key, final String value, long expireSec) {
        getRedisTemplate(groupCode).opsForValue().set(key, value, expireSec, TimeUnit.SECONDS);
    }

    public boolean setNX(String groupCode, final String key, String val) {
        Boolean succ = getRedisTemplate(groupCode).opsForValue().setIfAbsent(key, val);
        return succ != null && succ;
    }

    public boolean setNX(String groupCode, final String key, String val, final long sec) {
        Boolean succ = getRedisTemplate(groupCode).execute((RedisCallback<Boolean>) connection -> {
            Boolean setNX = connection.setNX(key.getBytes(), val.getBytes());
            if (Boolean.TRUE.equals(setNX)) {
                connection.expire(key.getBytes(), sec);
            }
            return setNX;
        });
        return succ != null && succ;
    }

    public boolean hasKey(String groupCode, String key) {
        return getRedisTemplate(groupCode).hasKey(key);
    }

    public void del(String groupCode, final String key) {
        getRedisTemplate(groupCode).delete(key);
    }

    public void delKeys(String groupCode, Set<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }
        getRedisTemplate(groupCode).delete(keys);
    }

    public String hget(String groupCode, final String key, final String hashKey) {
        Object obj = getRedisTemplate(groupCode).opsForHash().get(key, hashKey);
        if (obj == null) {
            return null;
        } else {
            return obj.toString();
        }
    }

    public Map<Object, Object> hGetAll(String groupCode, final String key) {
        return getRedisTemplate(groupCode).opsForHash().entries(key);
    }

    public Map<Object, Object> hScan(String groupCode, String key, int batchSize) {
        Map<Object, Object> reuslt = Maps.newHashMap();
        //get
        Cursor<Map.Entry<Object, Object>> cursor =
                getRedisTemplate(groupCode).opsForHash().scan(key, ScanOptions.scanOptions().count(batchSize).match("*").build());
        while (cursor.hasNext()) {
            Map.Entry<Object, Object> data = cursor.next();
            reuslt.put(data.getKey(), data.getValue());
        }
        try {
            cursor.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return reuslt;
    }

    public List<Object> hmGet(String groupCode, final String key, List<Object> hashKeys) {
        return getRedisTemplate(groupCode).opsForHash().multiGet(key, hashKeys);
    }

    public void hset(String groupCode, final String key, final String hashKey, final String value) {
        getRedisTemplate(groupCode).opsForHash().put(key, hashKey, value);
    }

    public List<Object> hset(String groupCode, final String key, final String hashKey, final String value,
                             long expireSecond) {
        List<Object> list = getRedisTemplate(groupCode).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                connection.hSet(key.getBytes(), hashKey.getBytes(), value.getBytes());
                if (expireSecond > 0) {
                    connection.expire(key.getBytes(), expireSecond);
                }
                return connection.closePipeline();
            }
        });
        return list;
    }

    public List<Object> hsetBatchKey(String group, final String key, final List<String> hashKeys, final List<String> values,
                                     Long expireSecond) {
        List<Object> list = getRedisTemplate(group).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                for (int i = 0; i < hashKeys.size(); ++i) {
                    //String key = keys.get(i);
                    String hashKey = hashKeys.get(i);
                    String value = values.get(i);
                    //long expireSecond = expireSeconds.get(i);
                    connection.hSet(key.getBytes(), hashKey.getBytes(), value.getBytes());
                }
                if (expireSecond > 0) {
                    connection.expire(key.getBytes(), expireSecond);
                }
                return connection.closePipeline();
            }
        });
        return list;
    }

    /**
     * scan满足条件的key
     **/
    public Set<String> scanKey(String groupCode, String keyPattern, int count) {
        return getRedisTemplate(groupCode).execute((RedisCallback<Set<String>>) connection -> {
            Set<String> keys = Sets.newHashSet();
            // 最多一次取1000进行匹配
            int limit = Math.min(count, 1000);

            RedisKeyAsyncCommands multiKeyCommands = (RedisKeyAsyncCommands) connection.getNativeConnection();

            ScanArgs scanParams = new ScanArgs();
            scanParams.match(keyPattern);
            scanParams.limit(limit);

            try {
                KeyScanCursor<byte[]> cursor = (KeyScanCursor<byte[]>) multiKeyCommands.scan(scanParams).get();
                addKeys(keys, cursor);
                while (!cursor.isFinished()) {
                    cursor = (KeyScanCursor<byte[]>) multiKeyCommands.scan(cursor, scanParams).get();
                    addKeys(keys, cursor);
                }
            } catch (Exception ex) {
                log.error("scanKey error,groupCode={},keyPattern={},count={},e:{}", groupCode, keyPattern, count, ex.getMessage(), ex);
            }
            return keys;
        });
    }

    private void addKeys(Set<String> keys, KeyScanCursor<byte[]> cursor) {
        cursor.getKeys().forEach(key -> keys.add(new String(key)));
    }

    public void hmset(String groupCode, final String key, final Map<String, String> map) {
        getRedisTemplate(groupCode).opsForHash().putAll(key, map);
    }

    public void hmset(String groupCode, final String key, final Map<String, String> map, long expireSecond) {
        getRedisTemplate(groupCode).opsForHash().putAll(key, map);
        getRedisTemplate(groupCode).expire(key, expireSecond, TimeUnit.SECONDS);
    }

    public long hIncrByKey(String groupCode, final String key, final String hashKey, final long value) {
        return getRedisTemplate(groupCode).boundHashOps(key).increment(hashKey, value);
    }

    public long hIncrByKey(String groupCode, final String key, final String hashKey, final long value, long expireSecond) {
        List<Object> list = getRedisTemplate(groupCode).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                connection.hIncrBy(key.getBytes(), hashKey.getBytes(), value);
                if (expireSecond > 0) {
                    connection.expire(key.getBytes(), expireSecond);
                }
                return connection.closePipeline();
            }
        });
        return Convert.toLong(list.get(0));
    }

    public long hlen(String groupCode, final String key) {
        return getRedisTemplate(groupCode).boundHashOps(key).size();
    }

    public long hsize(String groupCode, final String key) {
        Long size = getRedisTemplate(groupCode).boundHashOps(key).size();
        return size==null ? 0l : size;
    }


    public boolean hsetnx(String groupCode, final String key, final String hashKey, final String value) {
        return getRedisTemplate(groupCode).boundHashOps(key).putIfAbsent(hashKey, value);
    }

    public List<Object> hdelKey(String groupCode, final String key, final List<String> hashKey) {
        List<Object> list = getRedisTemplate(groupCode).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                for (int i = 0; i < hashKey.size(); ++i) {
                    connection.hDel(key.getBytes(), hashKey.get(i).getBytes());
                }
                return connection.closePipeline();
            }
        });
        return list;
    }

    public void hdel(String groupCode, final String key, String hashKey) {
        getRedisTemplate(groupCode).opsForHash().delete(key, hashKey);
    }


    public Double zScore(String groupCode, String key, String obj) {
        return getRedisTemplate(groupCode).opsForZSet().score(key, obj);
    }

    public long zscore(String groupCode, String key, String row) {
        return Convert.toLong(getRedisTemplate(groupCode).opsForZSet().score(key, row));
    }

    /**
     * 只支持分数为正整数的zset
     */
    public List<Long> zScoreAndRank(String groupCode, String key, String obj) {
        List<Object> list = getRedisTemplate(groupCode).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                connection.zScore(key.getBytes(), obj.getBytes());
                connection.zRevRank(key.getBytes(), obj.getBytes());
                return connection.closePipeline();
            }
        });

        // 表示 zset key 或 member 不存在
        if (list.get(0) == null) {
            return Arrays.asList(0L, 0L);
        } else {
            long score = Convert.toLong(list.get(0));
            long rank = Convert.toLong(list.get(1)) + 1;
            return Arrays.asList(score, rank);
        }
    }

    public Long zRevRank(String groupCode, String key, String obj) {
        return getRedisTemplate(groupCode).opsForZSet().reverseRank(key, obj);
    }

    public boolean zAdd(String groupCode, String key, String obj, double score) {
        return getRedisTemplate(groupCode).opsForZSet().add(key, obj, score);
    }

    public long zIncr(String groupCode, String key, String obj, long score) {
        return getRedisTemplate(groupCode).opsForZSet().incrementScore(key, obj, score).longValue();
    }

    public Set<ZSetOperations.TypedTuple<String>> zrevRange(String groupCode, String key, long num) {
        return getRedisTemplate(groupCode).opsForZSet().reverseRangeWithScores(key, 0, num - 1);
    }

    public Set<ZSetOperations.TypedTuple<String>> zrevRange(String groupCode, String key, long start, long end) {
        return getRedisTemplate(groupCode).opsForZSet().reverseRangeWithScores(key, start, end);
    }

    public Set<String> zrevRangeByScore(String groupCode, String key, long min, long max) {
        return getRedisTemplate(groupCode).opsForZSet().reverseRangeByScore(key, min, max);
    }

    public Set<ZSetOperations.TypedTuple<String>> zrevRangeByScoreWithScore(String groupCode, String key, long min, long max) {
        return getRedisTemplate(groupCode).opsForZSet().reverseRangeByScoreWithScores(key, min, max);
    }

    public Set<ZSetOperations.TypedTuple<String>> zrevRangeByScoreWithScore(String groupCode, String key, long min, long max, int count) {
        return getRedisTemplate(groupCode).opsForZSet().reverseRangeByScoreWithScores(key, min, max, 0, count);
    }

    public Set<String> zrevRangeNoScores(String groupCode, String key, long num) {
        return getRedisTemplate(groupCode).opsForZSet().reverseRange(key, 0, num - 1);
    }

    public List<Set<String>> zBatchRevRangeNoScores(String groupCode, List<String> keys, long num) {
        List<Object> list = getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            for (String key : keys) {
                connection.zRevRange(key.getBytes(), 0, num - 1);
            }
            return null;
        });
        List<Set<String>> result = Lists.newArrayList();
        for (Object obj : list) {
            if (obj == null) {
                result.add(Sets.newLinkedHashSet());
            } else {
                Set<String> set = (Set<String>) obj;
                result.add(set);
            }
        }
        return result;
    }

    public Set<ZSetOperations.TypedTuple<String>> zrange(String groupCode, String key, long num) {
        return getRedisTemplate(groupCode).opsForZSet().rangeWithScores(key, 0, num - 1);
    }

    public long zcard(String groupCode, String key) {
        return getRedisTemplate(groupCode).opsForZSet().size(key);
    }

    public long zcount(String groupCode, String key, double min, double max) {
        return getRedisTemplate(groupCode).opsForZSet().count(key, min, max);
    }

    public void zDel(String groupCode, final String key, final String obj) {
        getRedisTemplate(groupCode).opsForZSet().remove(key, obj);
    }

    public Long getExpire(String groupCode, String key) {
        return getRedisTemplate(groupCode).getExpire(key);
    }

    public boolean sIsMember(String groupCode, String key, String member) {
        return getRedisTemplate(groupCode).opsForSet().isMember(key, member);
    }

    public <T> T execute(@NonNull ComponentAttr attr, @NonNull Function<StringRedisTemplate, T> fun) {
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        StringRedisTemplate redisTemplate = getRedisTemplate(groupCode);
        return fun.apply(redisTemplate);
    }

    public <T> T executeLua(String groupCode, String scriptLuaName, Class<T> returnClz, List<String> keys, List<String> argv) {
        DefaultRedisScript<T> script = new DefaultRedisScript<>();
        script.setScriptText(RedisSupport.getScript(scriptLuaName));
        script.setResultType(returnClz);
        return getRedisTemplate(groupCode).execute(script, keys, argv.toArray());
    }

    public boolean hExists(String groupCode, String key, String row) {
        return getRedisTemplate(groupCode).opsForHash().hasKey(key, row);
    }

    public Set<String> sMembers(String groupCode, String key) {
        return getRedisTemplate(groupCode).opsForSet().members(key);
    }

    public long sAdd(String groupCode, String key, String member) {
        return getRedisTemplate(groupCode).opsForSet().add(key, member);
    }

    public Long sadd(String group, String key, String... values) {
        return getRedisTemplate(group).opsForSet().add(key, values);
    }

    public long sBatchAdd(String groupCode, String key, String... member) {
        return getRedisTemplate(groupCode).opsForSet().add(key, member);
    }

    public Long zAddSets(String group, String key, Set<ZSetOperations.TypedTuple<String>> tuples) {
        return getRedisTemplate(group).opsForZSet().add(key, tuples);
    }

    public Long sRem(String groupCode, String key, String... values) {
        return getRedisTemplate(groupCode).opsForSet().remove(key, values);
    }



    /**
     * FIXME: 本代码支持到 2034/11/19 01:27:27
     *
     * @return
     */
    public String getTime() {
        return "0." + (Integer.MAX_VALUE - System.currentTimeMillis() / 1000);
    }

    /**
     * @param pattern
     * @return
     */
    public Set<String> keys(String groupCode, String pattern) {
        return getRedisTemplate(groupCode).keys(pattern);
    }


    public boolean setExpire(String groupCode, String key, long seconds) {
        return getRedisTemplate(groupCode).expire(key, seconds, TimeUnit.SECONDS);
    }


    /**
     * hash 累积 step，当 step>=0 时不能超过 limit， 当 step<0时不能小于limit
     *
     * @return 返回2个元素的list， 第一个元素是结果码， 第二个元素是操作后的分值
     * 1: 累积 step 后不违反 limit 限定，返回累积后的新值
     * -1：step>=0时递增失败（递增后会大于limit），返回旧值
     * -2：step<0时递减失败 （递减后会小于limit），返回旧值
     */
    public List<Long> hIncrWithLimit(String groupCode, final String key, final String field, final long step, long limit) {
        if (true) {
            throw new RuntimeException("hash_incr_with_limit.lua,lua forbid");
        }
        return Lists.newArrayList();
    }




    /**
     * 返回老值，并且当新值大于老值时更新否则不更新
     */
    public long hSetGrownReturnOld(String groupCode, String hashKey, String receiver, long score) {
        if (true) {
            throw new RuntimeException("hset_grown_return_old.lua,lua forbid");
        }
        return -1;
    }





    /**
     * 读取 list 指定位置的元素
     */
    public String lindex(String groupCode, String key, long index) {
        return getRedisTemplate(groupCode).opsForList().index(key, index);
    }

    /**
     * 移除表中所有与 value 相等的值
     */
    public long lrem(String groupCode, String key, String value) {
        return getRedisTemplate(groupCode).opsForList().remove(key, 0, value);
    }

    /**
     * 从队首弹出一个元素
     */
    public String lpop(String groupCode, String key) {
        return getRedisTemplate(groupCode).opsForList().leftPop(key);
    }

    public String rpop(String groupCode, String key) {
        return getRedisTemplate(groupCode).opsForList().rightPop(key);
    }

    public long rpush(String groupCode, String key, String value) {
        return getRedisTemplate(groupCode).opsForList().rightPush(key, value);
    }

    public long lpush(String groupCode, String key, String value) {
        return getRedisTemplate(groupCode).opsForList().leftPush(key, value);
    }

    public List<String> lrange(String groupCode, String key, long start, long end) {
        return getRedisTemplate(groupCode).opsForList().range(key, start, end);
    }


    public long rPush(String group, String key, String... values) {
        return getRedisTemplate(group).opsForList().rightPushAll(key, values);
    }


    public long llen(String groupCode, String key) {
        return getRedisTemplate(groupCode).opsForList().size(key);
    }

    public void rename(String group, final String old_key, final String new_key) {
        getRedisTemplate(group).rename(old_key, new_key);
    }

    public boolean expire(String group, final String key, final long time) {
        return getRedisTemplate(group).expire(key, time, TimeUnit.SECONDS);
    }










    /**
     * 复制redis 数据
     *
     * @param oldRedisGroup 源头redis
     * @param newRedisGroup 目标 redis
     * @param key           redis key
     */
    public KeyCopyResult copy(String oldRedisGroup, String newRedisGroup, String key) {
        DataType dataType = this.getRedisTemplate(oldRedisGroup).type(key);
        RedisDataCopy redisDataCopy = redisDataCopyFactory.getInstance(dataType, key);
        return redisDataCopy.copy(oldRedisGroup, newRedisGroup, key);
    }

    public void unlink(String group, final List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }
        this.getRedisTemplate(group).executePipelined((RedisCallback<Object>) connection -> {
            keys.forEach(key -> connection.unlink(key.getBytes()));
            return null;
        });
    }

}
