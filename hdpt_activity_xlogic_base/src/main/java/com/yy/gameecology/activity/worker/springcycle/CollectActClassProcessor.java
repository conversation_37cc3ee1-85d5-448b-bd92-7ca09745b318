package com.yy.gameecology.activity.worker.springcycle;

import com.yy.gameecology.activity.service.ActOfflineService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-02-17 14:49
 **/
@Component
public class CollectActClassProcessor implements BeanPostProcessor {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ActOfflineService actOfflineService;


    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        Class<?> targetClass = AopUtils.getTargetClass(bean);
        actOfflineService.putActCustomizedClass(targetClass.getName());
        return bean;
    }

}
