package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.client.yrpc.OnlineChannelClient;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.client.WebdbSinfoClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.utils.*;
import com.yy.java.webdb.WebdbSubChannelInfo;
import com.yy.protocol.pb.online.channel.OnlineChannel;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-08-05 10:29
 **/
@Service
public class OnlineSubChannelCacheService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private OnlineChannelService onlineChannelService;

    @Autowired
    private ActRedisGroupDao actRedisGroupDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Autowired
    private WebdbSinfoClient webdbSinfoClient;

    @Autowired
    private OnlineChannelClient onlineChannelClient;

    private static final int MAX_COUNTER = 5000;

    private static final String ONLINE_SUB_CHANNEL =  Const.addActTempPrefix("ssid_cache:online_sub_channel:");

    private static final String ONLINE_SUB_CHANNEL_INFO =  Const.addActTempPrefix("ssid_cache:online_sub_channel_info");

    /**
     * 保存数据的key
     */
    private static final String ONLINE_SUB_CHANNEL_POSITION = Const.addActTempPrefix("ssid_cache:online_sub_channel_position");


    public void load2Redis() {
        Clock clock = new Clock();

        List<String> allSubChannel = getAllSubChannelSid();

        clock.tag();

        Map<String, Map<String, String>> subChannelInfoResult = Maps.newHashMap();

        List<List<String>> subIdList = MyListUtils.subList(allSubChannel, 200);
        for (List<String> subChannels : subIdList) {
            Map<String, WebdbSubChannelInfo> subResult = webdbSinfoClient.batchGetSubChannelInfoNoCache(subChannels);
            if (MapUtils.isNotEmpty(subResult)) {
                for (Map.Entry<String, WebdbSubChannelInfo> entry : subResult.entrySet()) {
                    Map<String, String> subChannelMap = WebdbSinfoClient.toSubChannelMap(entry.getValue());
                    subChannelInfoResult.put(entry.getKey(), subChannelMap);
                }
            }
        }
        clock.tag();

        //存数据的key,由于离线环境 pika rename 命令，用这种方式支持原子替换
        String newKey = ONLINE_SUB_CHANNEL + DateUtil.format(DateUtil.PATTERN_TYPE1);
        String oldKey = getSubChannelRealKey();

        List<Object> pr = actRedisGroupDao.getRedisTemplate(redisConfigManager.getDefaultGroupCode()).executePipelined((RedisConnection connection) -> {
            for (String hashField : subChannelInfoResult.keySet()) {
                connection.hSet(newKey.getBytes(), hashField.getBytes(), JSON.toJSONString(subChannelInfoResult.get(hashField)).getBytes());
            }
            // 登记更新信息
            connection.set(ONLINE_SUB_CHANNEL_INFO.getBytes(), (DateUtil.today() + "|size:" + subChannelInfoResult.size() + "|" + SystemUtil.getWorkerInfo()).getBytes());
            return null;
        });
        clock.tag();

        //指示到新的位置读取，起到原子替换的目的
        setSubChannelRealKey(newKey);
        //删除老数据(采用过期key方式，防止load2Memory因为时序问题，读取了个过期的position key,导致读不到数据，虽然这样有可能读到过期数据，但是缓存更新本来就有时效性，这个认为是可以接受)
        if (StringUtil.isNotBlank(oldKey)) {
            actRedisGroupDao.expire(redisConfigManager.getDefaultGroupCode(), oldKey, DateUtil.THREE_MIN_SECONDS);
        }

        log.info("load2Redis,get sub channel size:{},clock:{}", subChannelInfoResult.size(), clock.tag());
    }


    public void load2Memory() {
        Clock clock = new Clock();
        List<String> allSidSSid = getAllSubChannelSid();
        Map<String, Map<String, String>> subChannelMap = Maps.newHashMap();

        List<List<String>> allSidSSidBatch = MyListUtils.subList(allSidSSid, 50);
        for (List<String> sidssids : allSidSSidBatch) {
            Map<String, Map<String, String>> subChannelInfo = getSubChannelInfoNew(sidssids);
            if (MapUtils.isNotEmpty(subChannelInfo)) {
                subChannelMap.putAll(subChannelInfo);
            }
        }
        log.info("load2Memory done,size:{},clock:{}", allSidSSid.size(), clock.tag());
        Const.cacheAllSubChannelInfo(subChannelMap);
    }

    private long counter_total = 0;
    private long counter_memory = 0;
    private long counter_redis = 0;
    private long counter_source = 0;

    @Report
    public Map<String, String> getSubChannelInfo(String sidssid) {
        if (counter_total > MAX_COUNTER) {
            log.info("getSubChannelInfo counter,total:{},memory:{},redis:{},source:{}", counter_total, counter_memory, counter_redis, counter_source);
            counter_total = 0;
            counter_memory = 0;
            counter_redis = 0;
            counter_source = 0;
        }
        counter_total++;
        //尝试内存中获取
        Map<String, String> subChannelInfo = Const.getSubChannelInfoCache(sidssid);
        if (MapUtils.isNotEmpty(subChannelInfo)) {
            counter_memory++;
            return subChannelInfo;
        }
        //尝试redis获取
        subChannelInfo = getSubChannelInfoFromRedis(sidssid);
        if (MapUtils.isNotEmpty(subChannelInfo)) {
            counter_redis++;
            return subChannelInfo;
        }
        //源头接口获取
        counter_source++;

        final String getSubChInfoSourceLogKey = "get_sub_channel_info_source_log";
        if (Const.GEPM.getParamValueToBoolean(getSubChInfoSourceLogKey, true)) {
            log.info("getSubChannelInfo form source,sidssid:{}", sidssid);
        }

        return WebdbSinfoClient.toSubChannelMap(webdbSinfoClient.getSubChannelInfo(sidssid));
    }

    public Map<String, Map<String, String>> getSubChannelInfoFromRedis(List<String> sidssid) {
        Map<String, Map<String, String>> result = Maps.newHashMap();

        List<Object> sids = Lists.newArrayList();
        sidssid.forEach(x -> {
            sids.add(x);
        });
        String ssidKey = getSubChannelRealKey();
        List<Object> results = actRedisGroupDao.hmGet(redisConfigManager.getDefaultGroupCode(), ssidKey, sids);
        if (results == null) {
            return result;
        }
        for (int i = 0; i < sids.size(); i++) {
            String key = sids.get(i).toString();
            Object channelInfoObject = results.get(i);
            if (channelInfoObject != null) {
                Map<String, String> channelInfo = JSON.parseObject(Convert.toString(channelInfoObject), Map.class);
                if (MapUtils.isNotEmpty(channelInfo)) {
                    result.put(key, channelInfo);
                }

            }

        }
        return result;
    }

    public Map<String, Map<String, String>> getSubChannelInfoNew(List<String> sidssid) {
        log.info("use online ser data");
        Map<String, Map<String, String>> result = Maps.newHashMap();
        try {
            OnlineChannel.SubChannelInfoReq req = OnlineChannel.SubChannelInfoReq.newBuilder().addAllSidssids(sidssid).build();
            OnlineChannel.SubChannelInfoRsp rsp = onlineChannelClient.getProxy().getSubChannelInfo(req);
            if (rsp != null && rsp.getCode() == 0) {
                Map<String, OnlineChannel.SubChannelSaInfoMap> rspMap = rsp.getSubChannelInfoMapMap();
                rspMap.forEach((key, subChannelSaInfo)->{
                    Map<String, String> subChannelSaInfoMap = subChannelSaInfo.getSubChannelSaInfoMapMap();
                    result.put(key, subChannelSaInfoMap);
                });
            } else {
                log.error("load online data error, rsp:{}", JsonUtil.toJson(rsp));
            }
        }  catch (Exception e) {
            log.error("load online data error, e:{}", ExceptionUtils.getStackTrace(e));
        }
        return result;
    }

    public Map<String, String> getSubChannelInfoFromRedis(String sidssid) {
        String positionKey = getSubChannelRealKey();
        String result = actRedisGroupDao.hget(redisConfigManager.getDefaultGroupCode(), positionKey, sidssid);
        if (StringUtil.isEmpty(result)) {
            return null;
        }
        return JSON.parseObject(result, Map.class);
    }

    private List<String> getAllSubChannelSid() {
        List<String> allSubChannel = Lists.newArrayList();
        Map<Long, Map<Long, OnlineChannelInfo>> map = onlineChannelService.getAllOnlineChannelMap();
        for (Long sid : map.keySet()) {
            for (Long ssid : map.get(sid).keySet()) {
                allSubChannel.add(sid + "_" + ssid);
            }
        }

        return allSubChannel;
    }


    /**
     * 设置存放子频道信息key
     */
    private void setSubChannelRealKey(String newKey) {
        actRedisGroupDao.set(redisConfigManager.getDefaultGroupCode(), ONLINE_SUB_CHANNEL_POSITION, newKey);
    }

    /**
     * 获取存储子频道信息真实key
     */
    private String getSubChannelRealKey() {
        String realKey = actRedisGroupDao.get(redisConfigManager.getDefaultGroupCode(), ONLINE_SUB_CHANNEL_POSITION);
        return StringUtil.isBlank(realKey) ? ONLINE_SUB_CHANNEL : realKey;
    }


}
