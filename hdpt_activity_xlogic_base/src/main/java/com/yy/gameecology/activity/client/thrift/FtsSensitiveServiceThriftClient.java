package com.yy.gameecology.activity.client.thrift;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.utils.Clock;
import com.yy.thrift.fts_sensitive_word.FtsSensitiveService;
import com.yy.thrift.fts_sensitive_word.SensitiveWord;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 敏感词服务
 */
@Component
public class FtsSensitiveServiceThriftClient {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Reference(protocol = "nythrift", owner = "${thrift_fts_sensitive_word_client_s2s_name}", registry = "consumer-reg", timeout = 30000, parameters = {"threads", "10"})
    private FtsSensitiveService.Iface proxy = null;

    @Reference(protocol = "nythrift", owner = "${thrift_fts_sensitive_word_client_s2s_name}", registry = "consumer-reg", timeout = 30000, parameters = {"threads", "10"}
            , retries = 2 , cluster = "failover")
    private FtsSensitiveService.Iface readProxy = null;

    public FtsSensitiveService.Iface getProxy() {
        return proxy;
    }

    public FtsSensitiveService.Iface getReadProxy() {
        return readProxy;
    }

    @Cached(timeToLiveMillis = 10 * 60 * 1000)
    public List<String> getSensitiveWordsConfigCache(Integer module) {
        return getSensitiveWordsConfig(module);
    }

    /**
     * @param module
     * @return
     */
    public List<String> getSensitiveWordsConfig(Integer module) {
        Clock clock = new Clock();
        try {

            List<SensitiveWord> sensitiveWordList = getReadProxy().GetSensitiveWordsConfig(Lists.newArrayList(module));
            if (CollectionUtils.isEmpty(sensitiveWordList)) {
                log.warn("getSensitiveWordsConfig fail. module:{} resp:{}", module, JSON.toJSONString(sensitiveWordList));
                return Lists.newArrayList();
            }
            log.info("getSensitiveWordsConfig module:{},resp:{}", module, JSON.toJSONString(sensitiveWordList));

            String words = sensitiveWordList.get(0).getWords();
            if (StringUtils.isNotBlank(words)) {
                return Lists.newArrayList(words.split(","));
            }

        } catch (Throwable t) {
            log.error("getSensitiveWordsConfig exception@module:{}, err:{} {}", module, t.getMessage(), clock.tag());
        }
        return Lists.newArrayList();
    }
}
