package com.yy.gameecology.activity.bean;

import com.yy.gameecology.activity.bean.gamebaby.Transaction;
import com.yy.gameecology.activity.bean.jiaoyou.PropsUseInfo;

import java.util.Optional;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/11/10 18:15
 * @Modified: 礼物来源渠道
 */
public enum GiftSourceChannel {
    UNKNOWN(0),
    PC(1),
    ZHUIWAN(2),
    YOMI(3);

  private final int value;

  private GiftSourceChannel(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  public static GiftSourceChannel findByValue(int value) {
    switch (value) {
      case 1:
        return PC;
      case 77:
      case 78:
        return ZHUIWAN;
      default:
        return UNKNOWN;
    }
  }

  /**
   * 营收来源的礼物时间渠道分类
   * @param channel
   * @return
   */
  public static GiftSourceChannel findByTurnover(Integer channel) {

    if (channel == null) {
      return UNKNOWN;
    }
    switch (channel) {
      case 0:
        return PC;
      case 77:
      case 78:
        return ZHUIWAN;
      default:
        return UNKNOWN;
    }
  }

  public static GiftSourceChannel findByValue(PropsUseInfo gift) {
    int channel = Optional.ofNullable(gift).map(PropsUseInfo::getChannel).map(Long::intValue)
            .orElse(-1);
    return findByTurnover(channel);
  }


    public static GiftSourceChannel findByValue(Transaction gift) {

        String channel = Optional.ofNullable(gift).map(Transaction::getMac)
                .orElse("-1");

        switch (channel) {
            case "12":
                return PC;
            case "05":
            case "06":
                return ZHUIWAN;
            case "07":
            case "08":
                return YOMI;
            default:
                return UNKNOWN;
        }

  }
}
