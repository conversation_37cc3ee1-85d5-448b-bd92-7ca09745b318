package com.yy.gameecology.hdzj.utils;

import java.util.List;
import java.util.Random;

public class RandomUtil {

    // 构造一个全局随机数发生器
    public static Random RD = new Random(System.currentTimeMillis());

    /**
     * 随机选出1个下标索引，选中概率和段值成正比（值小于1的段总是会被忽略，永远不会被选中！）
     * 若没有找到合适的段，则返回 -1，随机抽奖失败
     */
    public static int randomIndex(List<Long> indexs) {
        // 对大于0的段累计所有数量
        long total = 0;
        for (long num : indexs) {
            total += num > 0 ? num : 0;
        }

        // 从总量中随机一个数字，遍历查找符合的下标
        long rand = (long) (RD.nextDouble() * total);
        for (int index = 0; index < indexs.size(); index++) {
            long prize = indexs.get(index);
            if(prize > 0) {
                if (rand < prize) {
                    return index;
                }
                rand -= prize;
            }
        }

        return -1;
    }

    /**
     * 制作一个 szie 长度的 随机份额 数组
     */
    public static int[] makeRandomQuotas(int size, int bound) {
        int[] indexs = new int[size];
        for(int i=0; i<size; i++) {
            indexs[i] = Math.abs(RD.nextInt(bound));
        }
        return indexs;
    }

    /**
     * 将所有份数累加，返回总值
     */
    public static long sum(int[] quotas) {
        long total = 0;
        for(int i=0; i<quotas.length; i++) {
            total += quotas[i];
        }
        return total;
    }

    /**
     * 按 quotas 指示的份额瓜分 amount
     * @param quotas - 份额数
     * @param amount - 要瓜分的数量
     * @return 瓜分好的数值，所有份数之和等于amount
     */
    public static long[] divide(int[] quotas, long amount) {
        // 剩余待瓜分值
        long left = amount;

        // 份额总数
        long total = sum(quotas);

        // 跳过第一个元素，按份额比例瓜分 amount
        long[] values = new long[quotas.length];
        for(int i=1; i<quotas.length; i++) {
            values[i] = amount * quotas[i] / total;
            left -= values[i];
        }

        // 最后的剩余值，全部给第一个元素，保证完整瓜分，不留零头
        values[0] = left;

        return values;
    }
}
