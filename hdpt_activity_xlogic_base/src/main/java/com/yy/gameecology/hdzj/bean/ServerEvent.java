package com.yy.gameecology.hdzj.bean;

import com.yy.gameecology.common.support.SysEvHelper;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2023/4/24 17:54
 **/
@Data
public class ServerEvent {
    private String seq = UUID.randomUUID().toString();
    /**
     * 事件， EXPOSE=曝光事件
     **/
    private String event = "EXPOSE";
    private Long timestamp = System.currentTimeMillis();
    /**
     * 生产者服务名
     **/
    private String server = "hdzk" + SysEvHelper.getGroup();
    /**
     * 用户uid
     **/
    private Long uid;
    /**
     * 用户hdid
     **/
    private String hdid;
    /**
     * app
     **/
    private String app;
    /**
     * 事件相关属性
     * 曝光事件（url:曝光地址，channelId：渠道id）
     */
    Map<String, String> data;

    public ServerEvent() {

    }

    public ServerEvent(long uid, String app, String channelId) {
        this.uid = uid;
        this.app = app;
        data = new HashMap<>();
        data.put("channelId", channelId);
    }
}
