package com.yy.gameecology.hdzj.element.history.attr;

import com.google.common.collect.Lists;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021.06.21 11:18
 */
public class MilestoneV2ComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "等级配置", subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = LevelConfig.class)})
    private List<LevelConfig> levelConfigs = Lists.newArrayList();
    @ComponentAttrField(labelText = "奖池id")
    private long taskId;
    @ComponentAttrField(labelText = "团贡献榜id")
    private long tuanContributionRankId = 0L;
    /**
     * 发奖重试次数
     */
    @ComponentAttrField(labelText = "重试次数")
    private int retry = 2;

    public LevelConfig getLevelConfig(long rankId, long phaseId) {
        return levelConfigs.stream()
                .filter(item -> item.getMainRankId() == rankId && item.getPhaseId() == phaseId)
                .findFirst().orElse(null);
    }

    public List<LevelConfig> getLevelConfigs() {
        return levelConfigs;
    }

    public void setLevelConfigs(List<LevelConfig> levelConfigs) {
        this.levelConfigs = levelConfigs;
    }

    public long getTaskId() {
        return taskId;
    }

    public void setTaskId(long taskId) {
        this.taskId = taskId;
    }

    public int getRetry() {
        return retry;
    }

    public void setRetry(int retry) {
        this.retry = retry;
    }

    public long getTuanContributionRankId() {
        return tuanContributionRankId;
    }

    public void setTuanContributionRankId(long tuanContributionRankId) {
        this.tuanContributionRankId = tuanContributionRankId;
    }

    public static class LevelConfig {
        /**
         * 主榜ID
         */
        @ComponentAttrField(labelText = "主榜ID")
        private long mainRankId;

        /**
         * 用户贡献榜ID
         */
        @ComponentAttrField(labelText = "用户贡献榜ID")
        private long playerRankId;

        /**
         * 主播贡献榜ID
         */
        @ComponentAttrField(labelText = "主播贡献榜ID")
        private long anchorRankId;

        /**
         * 阶段
         */
        @ComponentAttrField(labelText = "阶段Id")
        private long phaseId;

        /**
         * 给用户top n 发奖
         */
        @ComponentAttrField(labelText = "topN用户", remark = "给用户top n 发奖")
        private long playerTopN;
        /**
         * 给主播top n 发奖
         */
        @ComponentAttrField(labelText = "topN主播", remark = "给主播top n 发奖")
        private long anchorTopN;

        /**
         * 贡献神豪奖励
         */
        @ComponentAttrField(labelText = "贡献神豪奖励奖包id")
        private long playerAwardPackageId;

        /**
         * 贡献陪陪奖励
         */
        @ComponentAttrField(labelText = "贡献陪陪奖励奖包id")
        private long anchorAwardPackageId;

        /**
         * 团的限制分数，只有符合分数才会
         */
        @ComponentAttrField(labelText = "团的限制分数", remark = "只有符合分数才会")
        private long thresholdScore;
        /**
         * 里程碑等级
         */
        @ComponentAttrField(labelText = "里程碑等级")
        private int level;

        /**
         * 是否全业务广播
         */
        @ComponentAttrField(labelText = "是否全业务广播")
        private boolean isBroadcast = false;

        public long getMainRankId() {
            return mainRankId;
        }

        public void setMainRankId(long mainRankId) {
            this.mainRankId = mainRankId;
        }

        public long getPlayerRankId() {
            return playerRankId;
        }

        public void setPlayerRankId(long playerRankId) {
            this.playerRankId = playerRankId;
        }

        public long getAnchorRankId() {
            return anchorRankId;
        }

        public void setAnchorRankId(long anchorRankId) {
            this.anchorRankId = anchorRankId;
        }

        public long getPhaseId() {
            return phaseId;
        }

        public void setPhaseId(long phaseId) {
            this.phaseId = phaseId;
        }

        public long getPlayerTopN() {
            return playerTopN;
        }

        public void setPlayerTopN(long playerTopN) {
            this.playerTopN = playerTopN;
        }

        public long getAnchorTopN() {
            return anchorTopN;
        }

        public void setAnchorTopN(long anchorTopN) {
            this.anchorTopN = anchorTopN;
        }

        public long getPlayerAwardPackageId() {
            return playerAwardPackageId;
        }

        public void setPlayerAwardPackageId(long playerAwardPackageId) {
            this.playerAwardPackageId = playerAwardPackageId;
        }

        public long getAnchorAwardPackageId() {
            return anchorAwardPackageId;
        }

        public void setAnchorAwardPackageId(long anchorAwardPackageId) {
            this.anchorAwardPackageId = anchorAwardPackageId;
        }

        public long getThresholdScore() {
            return thresholdScore;
        }

        public void setThresholdScore(long thresholdScore) {
            this.thresholdScore = thresholdScore;
        }

        public int getLevel() {
            return level;
        }

        public void setLevel(int level) {
            this.level = level;
        }

        public boolean isBroadcast() {
            return isBroadcast;
        }

        public void setBroadcast(boolean broadcast) {
            isBroadcast = broadcast;
        }
    }
}
