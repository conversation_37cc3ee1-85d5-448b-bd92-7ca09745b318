package com.yy.gameecology.hdzj.element.history.attr;

import com.yy.gameecology.hdzj.annotation.SkipCheck;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 组件属性类
 *
 * <AUTHOR>
 * @date 2021/11/02 14:25
 */
@Getter
@Setter
@SkipCheck
public class NianshouComponentAttr extends ComponentAttr {

    /**
     * 此组件的模版
     */
    private int template;

    /**
     * 奖励概率 key:奖励值，  value：奖励的占比值
     * {level:{awardCount:possible,level:{awardCount:possible}}
     */
    private Map<Integer, Map<Long, Long>> playerShuijingProbabilities;

    /**
     * 奖励 奖池ID 水晶
     */
    private long playerTaskId;

    /**
     * 奖励 礼包ID 水晶
     */
    private long playerPackageId;

    //todo 用户模拟奖池实现
    /**
     * 用户奖池概率
     * packageId 概率
     * packageId 60
     * packageId 35
     * packageId 20
     * packageId 10
     * {level,{package:proterties}}
     * 普通奖励的
     */
    private Map<Integer, Map<Long, Long>> playerNormalProbabilities;

    /**
     * 用户普通奖池ID
     */
    private Map<Integer, Long> playerNormalTaskId;

    private Map<Long, Long> packageDayLimit;

    //用户总奖池数量
    private long playerTotalAwardCount;

    //主播队友金奖池 taskId
    private long anchorTaskId;

    //主播队友金奖池 packageId
    private long anchorPackageId;

    //主播总奖池数量
    private long anchorTotalAwardCount;
    /**
     * 主播奖池概率
     * 2880 60
     * 3880 35
     * 5000 20
     * 10000 10
     */
    private Map<Long, Long> anchorAwardProbabilities;



    /**
     * 参与抽奖时是否检查手机, 0-不检查（默认）， 1-检查
     */
    private int checkMobileFlag;

    /**
     * 严格检查手机时，只有明确绑了手机的才通过， 没绑或自身系统异常未知的也不通过
     * 0-非严格模式（默认）， 1-严格模式
     */
    private int strickCheckMobile = 0;

    /**
     * 年兽显示的时长, 22秒展示+8秒打年兽+5秒结果展示
     */
    //private int boxShowSeconds = 35;

    /**
     * 做多取前 xx 名用户可实际中奖
     */
    private int maxHitUserNum = 1000;

    /**
     * 奖励最多的前 X 名用户（用于特别展示） 手气王
     */
    private int topHitUserNum = 1;

    /**
     * 奖励发放最长重试秒数，超过后丢弃
     */
    private int issueRetrySeconds = 1800;

    /**
     * 开奖时，随机数取值范围, 得到的随机数取了绝对值
     */
    private int randomBound = 10000;

    /**
     * 开奖时均分部分对总奖励的占比百分数，取值范围 0 ~ 100（表示 0% ~ 100%）
     */
    private int basePercentage = 50;

    //年兽等级对应详情
    private Map<Integer, String> nsLevelMap;

    //集合阶段
    private int preTime = 22;

    //战斗时间
    private int fightTime = 8;

    //结果展示时间
    private int showResultTime = 5;

    //当前业务ID
    private int busiId;

    //主播首次开播tips
    private String anchorFirstTip = "召唤年兽可获得队友金红包奖励!";

  /*  public static int getBusiId(int template) {
        if(template == Template.Gamebaby.getValue()) return 400;
        if(template == Template.Jiaoyou.getValue()) return 500;
        if(template == Template.Yuezhan.getValue()) return 600;
        return -1;
    }*/

    /**
     * 用户紫水晶,紫宝石,红贝下发奖励 {busiId:{img:"",unit:xxx}}
     */
    private Map<Integer, Map<String, Object>> busi2AwardUnitImg;

    public static String getRetMsg(int ret) {
        final int ret0 = 0, ret1 = 1, ret2 = 2, ret3 = 3;
        if (ret == ret0) {
            return "成功参加";
        }
        if (ret == ret1) {
            return "已参加";
        }
        if (ret == ret2) {
            return "不存在的宝箱";
        }
        if (ret == ret3) {
            return "未绑定手机";
        }
        return "系统繁忙，请稍后再试！";
    }

    public String getAnchorFirstTip() {
        return anchorFirstTip;
    }

    public void setAnchorFirstTip(String anchorFirstTip) {
        this.anchorFirstTip = anchorFirstTip;
    }
}
