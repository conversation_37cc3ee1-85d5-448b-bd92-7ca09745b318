package com.yy.gameecology.hdzj.delay;

import com.yy.gameecology.hdzj.element.ComponentAttr;

/**
 *
 * @param <T> 活动组件属性ComponentAttr对象
 * @param <E> 自定义的延迟事件对象，尽量不要使用pb、thrift生成的对象，使用自定义的类型或基础类型都可以
 */
public interface DelayQueueEventPublisher<T extends ComponentAttr, E> {

    /**
     * 延迟事件发布入口
     * @param attr 组件属性
     * @param key 同一个组件中唯一
     * @param event 延迟事件对象，尽量不要使用pb、thrift生成的对象，使用自定义的类型或基础类型都可以
     * @param expiredMills 希望在什么时间戳到期处理，注意不是in是at
     */
    void publishDelayEvent(T attr, String key, E event, long expiredMills);
}
