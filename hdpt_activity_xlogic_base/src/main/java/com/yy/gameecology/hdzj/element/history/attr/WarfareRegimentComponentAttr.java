package com.yy.gameecology.hdzj.element.history.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.List;
import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption: 战力团组件属性
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
public class WarfareRegimentComponentAttr extends ComponentAttr {
    /**
     * 三个key
     * userAddTask：用户加入战力团任务
     * userAllCompleteTask：用户每日全部任务
     * anchorTask：主播任务
     **/
    @ComponentAttrField(labelText = "任务配置", subFields = {
            @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "任务类型", remark = "userAddTask：用户加入战力团任务;userAllCompleteTask：用户每日全部任务;anchorTask：主播任务"),
            @SubField(fieldName = Constant.VALUE, type = TaskInfo.class)
    })
    private Map<String, TaskInfo> taskInfoMap;

    /**
     * 用户加入战力团任务
     */
    // private TaskInfo userAddTask;
    /**
     * 用户每日全部任务完成任务
     */
    // private TaskInfo userAllCompleteTask;
    /**
     * 每日任务
     */
    @ComponentAttrField(labelText = "用户每日任务", subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = TaskInfo.class)})
    private List<TaskInfo> userDailyTasks;
    /**
     * 用户每日登陆榜单id
     */
    @ComponentAttrField(labelText = "用户每日登陆榜单id")
    private long userLoginRankId;
    /**
     * 用户每日登陆阶段id
     */
    @ComponentAttrField(labelText = "用户每日登陆阶段id")
    private long userLoginPhaseId;
    /**
     * 主播任务配置
     */
    // private TaskInfo anchorTask;

    /**
     * 用户加入战力团奖励
     */
    @ComponentAttrField(labelText = "用户加入战力团奖励")
    private long addAward = 10;

    /**
     * 主播人气值对应的奖励
     */
    @ComponentAttrField(labelText = "主播人气值对应的奖励",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "人气值"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "奖励值")
            })
    private Map<Integer, Integer> anchorScoreAwardMap;
    /**
     * 用户停留直播间 tip文案
     */
    @ComponentAttrField(labelText = "用户停留直播间tip")
    private String userRemainTip;
    /**
     * 主播战力团生成tip文案
     */
    @ComponentAttrField(labelText = "主播战力团生成tip")
    private String anchorFirstTip;
    /**
     * 用户加入主播战力团子频道广播文案
     */
    @ComponentAttrField(labelText = "用户加入主播战力团子频道广播文案")
    private String userAddAnchorTip;
    /**
     * 引导类型对应的push文案
     */
    @ComponentAttrField(labelText = "引导类型对应的push文案",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "类型", remark = "0:加入战力团"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "tip文案")
            })
    private Map<Integer, String> guideTypeTipMap;
    /**
     * 用户奖池转换-taskId
     */
    @ComponentAttrField(labelText = "用户转换奖池id")
    private long changeTaskId;
    /**
     * 用户奖池转换-业务packageMap
     */
    @ComponentAttrField(labelText = "业务奖包转换Map",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "业务id_原奖包id"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "新奖包id")
            })
    private Map<String, Long> busiIdPackageChangeMap = Maps.newHashMap();

    public Map<String, TaskInfo> getTaskInfoMap() {
        return taskInfoMap;
    }

    public void setTaskInfoMap(Map<String, TaskInfo> taskInfoMap) {
        this.taskInfoMap = taskInfoMap;
    }

    public TaskInfo getUserAddTask() {
        return taskInfoMap.get("userAddTask");
    }

//    public void setUserAddTask(TaskInfo userAddTask) {
//        this.userAddTask = userAddTask;
//    }

    public TaskInfo getUserAllCompleteTask() {
        return taskInfoMap.get("userAllCompleteTask");
    }

//    public void setUserAllCompleteTask(TaskInfo userAllCompleteTask) {
//        this.userAllCompleteTask = userAllCompleteTask;
//    }

    public List<TaskInfo> getUserDailyTasks() {
        return userDailyTasks;
    }

    public void setUserDailyTasks(List<TaskInfo> userDailyTasks) {
        this.userDailyTasks = userDailyTasks;
    }

    public long getUserLoginRankId() {
        return userLoginRankId;
    }

    public void setUserLoginRankId(long userLoginRankId) {
        this.userLoginRankId = userLoginRankId;
    }

    public long getUserLoginPhaseId() {
        return userLoginPhaseId;
    }

    public void setUserLoginPhaseId(long userLoginPhaseId) {
        this.userLoginPhaseId = userLoginPhaseId;
    }

    public TaskInfo getAnchorTask() {
        return taskInfoMap.get("anchorTask");
    }

//    public void setAnchorTask(TaskInfo anchorTask) {
//        this.anchorTask = anchorTask;
//    }

    public long getAddAward() {
        return addAward;
    }

    public void setAddAward(long addAward) {
        this.addAward = addAward;
    }

    public Map<Integer, Integer> getAnchorScoreAwardMap() {
        return anchorScoreAwardMap;
    }

    public void setAnchorScoreAwardMap(Map<Integer, Integer> anchorScoreAwardMap) {
        this.anchorScoreAwardMap = anchorScoreAwardMap;
    }

    public String getUserRemainTip() {
        return userRemainTip;
    }

    public void setUserRemainTip(String userRemainTip) {
        this.userRemainTip = userRemainTip;
    }

    public String getAnchorFirstTip() {
        return anchorFirstTip;
    }

    public void setAnchorFirstTip(String anchorFirstTip) {
        this.anchorFirstTip = anchorFirstTip;
    }

    public String getUserAddAnchorTip() {
        return userAddAnchorTip;
    }

    public void setUserAddAnchorTip(String userAddAnchorTip) {
        this.userAddAnchorTip = userAddAnchorTip;
    }

    public Map<Integer, String> getGuideTypeTipMap() {
        return guideTypeTipMap;
    }

    public void setGuideTypeTipMap(Map<Integer, String> guideTypeTipMap) {
        this.guideTypeTipMap = guideTypeTipMap;
    }

    public long getChangeTaskId() {
        return changeTaskId;
    }

    public void setChangeTaskId(long changeTaskId) {
        this.changeTaskId = changeTaskId;
    }

    public Map<String, Long> getBusiIdPackageChangeMap() {
        return busiIdPackageChangeMap;
    }

    public void setBusiIdPackageChangeMap(Map<String, Long> busiIdPackageChangeMap) {
        this.busiIdPackageChangeMap = busiIdPackageChangeMap;
    }

    public static class TaskInfo {
        @ComponentAttrField(labelText = "任务id")
        private String taskId;
        @ComponentAttrField(labelText = "任务名称")
        private String taskName;
        @ComponentAttrField(labelText = "榜单id")
        private long rankId;
        @ComponentAttrField(labelText = "阶段id")
        private long phaseId;
        @ComponentAttrField(labelText = "时间格式")
        private String dateFormat = "";
        @ComponentAttrField(labelText = "奖池id")
        private long awardTaskId;


        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public String getTaskName() {
            return taskName;
        }

        public void setTaskName(String taskName) {
            this.taskName = taskName;
        }

        public long getRankId() {
            return rankId;
        }

        public void setRankId(long rankId) {
            this.rankId = rankId;
        }

        public long getPhaseId() {
            return phaseId;
        }

        public void setPhaseId(long phaseId) {
            this.phaseId = phaseId;
        }

        public String getDateFormat() {
            return dateFormat;
        }

        public void setDateFormat(String dateFormat) {
            this.dateFormat = dateFormat;
        }

        public long getAwardTaskId() {
            return awardTaskId;
        }

        public void setAwardTaskId(long awardTaskId) {
            this.awardTaskId = awardTaskId;
        }
    }
}
