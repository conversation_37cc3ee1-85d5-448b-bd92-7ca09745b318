package com.yy.gameecology.hdzj.bean.aov;

import com.yy.gameecology.common.db.model.gameecology.aov.AovWhitelist;

public class AovWhitelistInfo extends AovWhitelist {

    protected long yy;

    protected String nick;

    public AovWhitelistInfo() {
    }

    public AovWhitelistInfo(AovWhitelist aovWhitelist) {
        this.setId(aovWhitelist.getId());
        this.setActId(aovWhitelist.getActId());
        this.setPhaseId(aovWhitelist.getPhaseId());
        this.setUid(aovWhitelist.getUid());
        this.setInviter(aovWhitelist.getInviter());
        this.setCreateTime(aovWhitelist.getCreateTime());
    }

    public long getYy() {
        return yy;
    }

    public void setYy(long yy) {
        this.yy = yy;
    }

    public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick;
    }
}
