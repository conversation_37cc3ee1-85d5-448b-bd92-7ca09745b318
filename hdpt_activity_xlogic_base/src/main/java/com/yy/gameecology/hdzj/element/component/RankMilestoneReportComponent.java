package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSortedSet;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.activity.service.SignedService;
import com.yy.gameecology.activity.service.ext.RankMilestoneReportExtHandler;
import com.yy.gameecology.common.bean.ChannelBaseInfo;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.HdztRankType;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.RankMilestoneReportComponentAttr;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import freemarker.template.Template;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * 榜单节点报告
 */
@Component
public class RankMilestoneReportComponent extends BaseActComponent<RankMilestoneReportComponentAttr> implements BeanFactoryAware {

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private SignedService signedService;

    private BeanFactory beanFactory;

    @Override
    public Long getComponentId() {
        return ComponentId.ACTIVITY_MILESTONE_REPORT;
    }


    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void onRankingScoreChanged(RankingScoreChanged event, RankMilestoneReportComponentAttr attr) {
        log.info("ActivityMilestoneReportComponent handle event:{}, attr:{}", event, attr);
        if (StringUtils.isEmpty(attr.getGloryLink())) {
            return;
        }

        if (CollectionUtils.isEmpty(attr.getMilestoneScores())) {
            return;
        }

        final long rankId = event.getRankId(), phaseId = event.getPhaseId();
        if (rankId != attr.getRankId() || phaseId != attr.getPhaseId()) {
            return;
        }
        String memberId = event.getMember();
        long actId = attr.getActId();

        ImmutableSortedSet<Long> sortedSet = ImmutableSortedSet.copyOf(attr.getMilestoneScores());

        //当前分数
        long score = attr.getPhaseId() > 0 ? event.getPhaseScore() : event.getRankScore();
        //本次变动之前的分数
        long lastScore = score - event.getItemScore();

        Long currentThreshold = sortedSet.floor(score);
        Long lastThreshold = sortedSet.floor(lastScore);

        if (currentThreshold == null || Objects.equals(currentThreshold, lastThreshold)) {
            log.info("onRankingScoreChanged ignore");
            return;
        }

        RoleType roleType = RoleType.findByValue(attr.getRoleType());
        Assert.notNull(roleType, "角色配置错误");
        MemberInfo memberInfo = memberInfoService.getMemberInfo((long) attr.getBusiId(), roleType, memberId);
        MilestoneInfo info = new MilestoneInfo(
                event.getRankId(),
                event.getPhaseId(),
                memberId,
                memberInfo.getHdLogo(),
                memberInfo.getName(),
                Convert.toLong(memberInfo.getAsid()),
                Convert.toLong(memberInfo.getSid()),
                score, event.getItemScore(),
                currentThreshold,
                DateUtil.getNowYyyyMMddHHmmss());

        //如果是主播，把签约信息加上
        if (roleType == RoleType.ANCHOR && attr.isSignedGuild()) {
            long signedSid = signedService.getSignedSidByBusiId(Convert.toLong(memberId), attr.getBusiId());
            if (signedSid != 0) {
                info.setSignedSid(signedSid);
                ChannelBaseInfo channelBaseInfo = commonService.getChannelInfo(signedSid, false);
                if (channelBaseInfo != null) {
                    info.setSignedAsid(channelBaseInfo.getAsid());
                    info.setSignedGuildName(channelBaseInfo.getName());
                    info.setSignedGuildLogo(channelBaseInfo.getLogo());
                }
            }
        }

        //总榜榜单ID不为0，查总榜榜单积分
        if (attr.getTotalRankId() > 0) {
            Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(attr.getActId(), attr.getTotalRankId(), attr.getPhaseId(), StringUtils.EMPTY, event.getMember(), ImmutableMap.of(RankExtParaKey.RANK_SCORE_SOURCE, HdztRankType.SRC));
            long totalScore = rank == null ? 0 : rank.getScore();
            info.setTotalScore(totalScore);
        }

        if (StringUtils.isNotEmpty(attr.getExtHandler())) {
            try {
                RankMilestoneReportExtHandler extHandler = beanFactory.getBean(attr.getExtHandler(), RankMilestoneReportExtHandler.class);
                info.setExt(extHandler.buildExt(event, attr));
            } catch (Exception e) {
                log.warn("onRankingScoreChanged handle ext exception:", e);
            }
        }

        String groupCode = redisConfigManager.getGroupCode(actId);
        String key = RandomStringUtils.random(31, true, true);
        actRedisDao.set(groupCode, key, JSON.toJSONString(info), 20 * 24 * 60 * 60);
        String link = attr.getGloryLink();
        if (StringUtils.isNotEmpty(link)) {
            if (StringUtils.contains(link, StringUtil.QUESTION_MARK) && !StringUtils.endsWith(link, StringUtil.AND)) {
                link += "&";
            }

            if (!StringUtils.contains(link, StringUtil.QUESTION_MARK)) {
                link += "?";
            }

            link += ("actId=" + actId + "&seq=" + key);
            info.setLink(link);
        }

        log.info("ActivityMilestoneReportComponent gen info:{}", info);
        String msg;
        try {
            String templateName = makeKey(attr, "TPL_NAME");
            Template template = new Template(templateName, attr.getTemplate(), null);
            msg = FreeMarkerTemplateUtils.processTemplateIntoString(template, info);
        } catch (Exception e) {
            log.warn("processTemplate exception:", e);
            return;
        }

        //baiduInfoFlowRobotService.sendNotifyByActAttrKey(attr.getActId(), "activity_report_notice", msg, Collections.emptyList());
        baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT, msg, Collections.emptyList());
    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }


    @Data
    public static class MilestoneInfo {

        protected long rankId;

        protected long phaseId;

        protected String memberId;

        protected String logo;

        protected String name;

        protected long asid;

        protected long sid;

        /**
         * roleType为主播时添加签约信息
         */
        protected long signedSid;

        protected long signedAsid;

        protected String signedGuildName;

        protected String signedGuildLogo;

        /**
         * 当前分值
         */
        protected long score;

        /**
         * 本次增加的分值
         */
        protected long addScore;

        /**
         * 突破的分值大关
         */
        protected long scoreThreshold;

        /**
         * 总榜分值
         */
        protected long totalScore;

        /**
         * 发生时间，yyyy-MM-dd HH:mm:ss
         */
        protected String date;

        @JSONField(serialize = false, deserialize = false)
        protected String link = StringUtils.EMPTY;

        protected Map<String, String> ext = Collections.emptyMap();

        public MilestoneInfo() {
        }

        public MilestoneInfo(long rankId, long phaseId, String memberId, String logo, String name, long asid, long sid, long score, long addScore, long scoreThreshold, String date) {
            this.rankId = rankId;
            this.phaseId = phaseId;
            this.memberId = memberId;
            this.logo = logo;
            this.name = name;
            this.asid = asid;
            this.sid = sid;
            this.score = score;
            this.addScore = addScore;
            this.scoreThreshold = scoreThreshold;
            this.date = date;
        }
    }
}
