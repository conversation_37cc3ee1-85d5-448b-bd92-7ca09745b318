package com.yy.gameecology.hdzj.element.attrconfig;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/23 18:00
 **/
@Component
public class BoxTypeSource implements DropDownSource {
    @Override
    public List<DropDownVo> listDropDown() {
        // [{"code":"1","desc":"金宝箱(1)"},{"code":"2","desc":"银宝箱(2)"}]
        return Arrays.asList(
                new DropDownVo("1", "金宝箱(1)"),
                new DropDownVo("2", "银宝箱(2)")
        );
    }
}
