package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-11-16 18:20
 **/
@Data
public class AwardAttrConfig {
    @ComponentAttrField(labelText = "奖励奖池Id")
    protected Long tAwardTskId;

    @ComponentAttrField(labelText = "奖励奖包Id",remark = "填0，则是抽奖")
    protected Long tAwardPkgId;

    @ComponentAttrField(labelText = "奖励发放数量")
    protected Integer num;

    /**
     * 使用中台的奖励名
     */
    @Deprecated
    @ComponentAttrField(labelText = "奖励名称")
    protected String awardName;

    /**
     * 使用中台的奖励单位
     */
    @Deprecated
    @ComponentAttrField(labelText = "单位")
    protected String unit;

    /**
     * 使用中台的奖励图
     */
    @Deprecated
    @ComponentAttrField(labelText = "奖励图标")
    protected String awardIcon;

    @ComponentAttrField(labelText = "奖励金额")
    protected long awardAmount;

    @ComponentAttrField(labelText = "备注")
    protected String remark;

    @ComponentAttrField(labelText = "扩展信息")
    protected String extJson;
}
