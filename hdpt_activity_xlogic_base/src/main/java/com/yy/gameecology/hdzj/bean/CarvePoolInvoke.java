package com.yy.gameecology.hdzj.bean;

import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.hdzj.element.component.attr.CarvePoolComponentAttr;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-11-03 15:22
 **/
public class CarvePoolInvoke {
    private PhaseTimeEnd event;
    private CarvePoolComponentAttr attr;

    public CarvePoolInvoke(){}

    public CarvePoolInvoke(PhaseTimeEnd event, CarvePoolComponentAttr attr) {
        this.event = event;
        this.attr = attr;
    }

    public PhaseTimeEnd getEvent() {
        return event;
    }

    public void setEvent(PhaseTimeEnd event) {
        this.event = event;
    }

    public CarvePoolComponentAttr getAttr() {
        return attr;
    }

    public void setAttr(CarvePoolComponentAttr attr) {
        this.attr = attr;
    }
}
