package com.yy.gameecology.hdzj.element.history.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ActResultTypeSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021.09.22 10:06
 * 阶段结束,从榜单rankId的第N位startIndex开始获取count个,排名从rankIndex开始,记录在redisKey中
 */
public class HonorHallV1ComponentAttr extends ComponentAttr {

    /**
     * 一个荣耀榜涉及的榜单ID,用于过滤事件
     */
    @ComponentAttrField(labelText = "榜单id列表", remark = "多个时逗号分隔", subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private List<Long> rankIds;

    /**
     * 具体的配置
     */
    @ComponentAttrField(labelText = "具体的配置", subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = HonorConfig.class)})
    private List<HonorConfig> configs;

    /**
     * 分组 对应数据库表act_result groupId
     */
    @ComponentAttrField(labelText = "分组")
    private String groupId;

    /**
     * 对应数据库表act_result type  1-> 交友 2->约战 3-> 宝贝 4->陪玩
     */
    @ComponentAttrField(labelText = "类型", dropDownSourceBeanClass = ActResultTypeSource.class)
    private int type = 1;

    public class HonorConfig {
        @ComponentAttrField(labelText = "榜单id")
        private long rankId;
        @ComponentAttrField(labelText = "阶段id")
        private long phaseId;
        @ComponentAttrField(labelText = "开始下标")
        private int startIndex;
        @ComponentAttrField(labelText = "数量")
        private long count;

        /**
         * 榜单类型,有些是从pk榜结算
         * normal:常规榜单, pk:PK榜
         */
        @ComponentAttrField(labelText = "榜单类型", remark = "normal:常规榜单, pk:PK榜")
        private String rankType;

        /**
         * 当pk时,读取胜方还是败方
         */
        @ComponentAttrField(labelText = "pk胜方", remark = "当pk时,true:胜方,false:败方")
        private boolean winner;
        /**
         * 结算排名
         */
        @ComponentAttrField(labelText = "结算排名")
        private int rankIndex;

        public long getRankId() {
            return rankId;
        }

        public void setRankId(long rankId) {
            this.rankId = rankId;
        }

        public long getPhaseId() {
            return phaseId;
        }

        public void setPhaseId(long phaseId) {
            this.phaseId = phaseId;
        }

        public int getStartIndex() {
            return startIndex;
        }

        public void setStartIndex(int startIndex) {
            this.startIndex = startIndex;
        }

        public long getCount() {
            return count;
        }

        public void setCount(long count) {
            this.count = count;
        }

        public String getRankType() {
            return rankType;
        }

        public void setRankType(String rankType) {
            this.rankType = rankType;
        }

        public int getRankIndex() {
            return rankIndex;
        }

        public void setRankIndex(int rankIndex) {
            this.rankIndex = rankIndex;
        }

        public boolean isWinner() {
            return winner;
        }

        public void setWinner(boolean winner) {
            this.winner = winner;
        }
    }

    public List<Long> getRankIds() {
        return rankIds;
    }

    public void setRankIds(List<Long> rankIds) {
        this.rankIds = rankIds;
    }

    public List<HonorConfig> getConfigs() {
        return configs;
    }

    public void setConfigs(List<HonorConfig> configs) {
        this.configs = configs;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
