package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.List;

/**
 * @Author: CXZ
 * @Desciption: 桃花还愿屋组件属性
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
public class VotiveHouseComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "商店下标")
    private long shopIndex;
    @ComponentAttrField(labelText = "任务信息", subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = TaskInfo.class)})
    private List<TaskInfo> taskInfos;

    public List<TaskInfo> getTaskInfos() {
        return taskInfos;
    }

    public void setTaskInfos(List<TaskInfo> taskInfos) {
        this.taskInfos = taskInfos;
    }

    public long getShopIndex() {
        return shopIndex;
    }

    public void setShopIndex(long shopIndex) {
        this.shopIndex = shopIndex;
    }

    public static class TaskInfo {
        @ComponentAttrField(labelText = "任务名称")
        private String taskName;
        @ComponentAttrField(labelText = "榜单id")
        private long rankId;
        @ComponentAttrField(labelText = "阶段id")
        private long phaseId;
        @ComponentAttrField(labelText = "日期格式")
        private String dateFormat;
        /**
         * 完成任务需要的总数
         */
        @ComponentAttrField(labelText = "完成任务的总数")
        private int totalCount;
        /**
         * 是否是循环任务
         */
        @ComponentAttrField(labelText = "是否循环")
        private boolean isLoop;
        /**
         * 任务的礼包名称，展示作用
         */
        @ComponentAttrField(labelText = "礼包名称")
        private String packageName;
        /**
         * 任务的礼包类型，给pc区分横幅
         */
        @ComponentAttrField(labelText = "礼包类型")
        private String packageType;
        /**
         * 奖励签的数量
         */
        @ComponentAttrField(labelText = "奖励数量")
        private int award;
        /**
         * 单播tip文案
         */
        @ComponentAttrField(labelText = "单播文案")
        private String tipContest;
        /**
         * web前端展示图片
         */
        @ComponentAttrField(labelText = "展示图片")
        private String showImage;
        /**
         * 任务单位
         */
        @ComponentAttrField(labelText = "任务单位")
        private String unit;

        public String getTaskName() {
            return taskName;
        }

        public void setTaskName(String taskName) {
            this.taskName = taskName;
        }

        public long getRankId() {
            return rankId;
        }

        public void setRankId(long rankId) {
            this.rankId = rankId;
        }

        public long getPhaseId() {
            return phaseId;
        }

        public void setPhaseId(long phaseId) {
            this.phaseId = phaseId;
        }

        public String getDateFormat() {
            return dateFormat;
        }

        public void setDateFormat(String dateFormat) {
            this.dateFormat = dateFormat;
        }

        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }

        public boolean isLoop() {
            return isLoop;
        }

        public void setLoop(boolean loop) {
            isLoop = loop;
        }

        public String getPackageName() {
            return packageName;
        }

        public void setPackageName(String packageName) {
            this.packageName = packageName;
        }

        public String getPackageType() {
            return packageType;
        }

        public void setPackageType(String packageType) {
            this.packageType = packageType;
        }

        public int getAward() {
            return award;
        }

        public void setAward(int award) {
            this.award = award;
        }

        public String getTipContest() {
            return tipContest;
        }

        public void setTipContest(String tipContest) {
            this.tipContest = tipContest;
        }

        public String getShowImage() {
            return showImage;
        }

        public void setShowImage(String showImage) {
            this.showImage = showImage;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }
    }
}
