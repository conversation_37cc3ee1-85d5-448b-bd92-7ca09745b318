package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class AovAwardComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    protected long busiId;

    @ComponentAttrField(labelText = "领取奖励风控策略")
    protected String riskStrategyKey;

    @ComponentAttrField(labelText = "奖励所需报名队伍", remark = "对应roundNum的奖励所需的报名队伍数", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "roundNum", remark = "奖励的roundNum"),
            @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "报名队伍数", remark = "所需的报名队伍数")
    })
    protected Map<Integer, Integer> requireTeamCnt;
}
