package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrCollector;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalTime;
import java.util.List;

@Getter
@Setter
public class KaiHeiSkinPortalComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "入口业务标识", remark = "基础频道入口白名单业务标识")
    protected String biz = "yo_kaihei_act";

    @ComponentAttrField(labelText = "导流App", remark = "多个使用逗号分隔开")
    protected String apps;

    @ComponentAttrField(labelText = "白名单组件",remark = "参与活动UID白名单组件索引")
    protected int whitelistCmptIndex;

    @ComponentAttrField(labelText = "登录YO白名单组件", remark = "登录了YO的uid所在的白名单组件索引")
    protected int loginCmptIndex;

    @ComponentAttrField(labelText = "皮肤配置", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = SkinConfig.class))
    protected List<SkinConfig> skinConfigs;

    @ComponentAttrField(labelText = "提前关闭入口", remark = "针对没有进YO的uid，提前多少天关闭入口")
    protected int shutPortalAheadDays;

    @ComponentAttrField(labelText = "触发推送的未打卡天数", remark = "连续多少天未打卡触发推送")
    protected int pushDays;

    @ComponentAttrField(labelText = "触发推送后静默时间", remark = "触发完时间后静默多少天继续推送")
    protected int silenceDays;

    @ComponentAttrField(labelText = "最多推送次数", remark = "最多推送多少次后不再推送")
    protected int pushTimes;

    @ComponentAttrField(labelText = "推送开始时间", remark = "触发推送的开始时间")
    protected LocalTime pushStartTime;

    @ComponentAttrField(labelText = "推送结束时间", remark = "触发推送的结束时间")
    protected LocalTime pushEndTime;

    @ComponentAttrField(labelText = "中台推送Appid", remark = "推送中台消息的appid")
    protected String pushAppid = "Yomi";

    @ComponentAttrField(labelText = "中台推送标题")
    protected String pushTitle;

    @ComponentAttrField(labelText = "中台推送跳转链接")
    protected String pushLink;

    @Getter
    @Setter
    public static class SkinConfig {

        @ComponentAttrField(labelText = "皮肤ID")
        protected int skinId;

        @ComponentAttrField(labelText = "皮肤名称")
        protected String skinName;

        @ComponentAttrField(labelText = "皮肤图片地址", propType = ComponentAttrCollector.PropType.IMAGE)
        protected String skinIcon;
    }
}
