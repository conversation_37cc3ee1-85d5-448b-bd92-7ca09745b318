package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.bean.HonorConfig;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ActivityGloryReportComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "战报链接")
    protected String gloryLink;

    /**
     * 一个荣耀榜涉及的榜单ID和阶段ID,用于过滤事件  {rankId_phaseId:groupId}
     */
    @ComponentAttrField(labelText = "分组映射配置", remark = "一个荣耀榜涉及的榜单ID和阶段ID,用于过滤事件  {rankId_phaseId:groupId}",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "榜单ID和阶段ID", remark = "rankId_phaseId"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "分组id")
            })
    private Map<String, String> rankGroupMap;

    /**
     * 具体的配置  {grouId:[{config1},{config2}]
     */
    @ComponentAttrField(labelText = "具体的配置"
            , subFields = {
            @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "分组id"),
            @SubField(fieldName = Constant.VALUE, type = List.class, skip = true),
            @SubField(fieldName = Constant.MAP_LIST_VALUE, type = HonorConfig.class)
    })
    private Map<String, List<HonorConfig>> configsMap;

    @ComponentAttrField(labelText = "分组配置", subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = GroupDefine.class)})
    protected List<GroupDefine> groupDefines;

    @ComponentAttrField(labelText = "结果配置", subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = ActResult.class)})
    protected List<ActResult> actResults;

    @ComponentAttrField(labelText = "freemarker文本模板")
    protected String template;

    @Data
    public static class GroupDefine {
        /**
         * 分组id
         **/
        @ComponentAttrField(labelText = "分组id")
        private String groupId;
        /**
         * 分组名称
         **/
        @ComponentAttrField(labelText = "分组名称")
        private String groupName;
        /**
         * 业务类型：1-交友 2-约战 3-宝贝 4-互动全品类 5-陪玩
         **/
        @ComponentAttrField(labelText = "业务类型", remark = "1-交友 2-约战 3-宝贝 4-互动全品类 5-陪玩")
        private Integer type;
    }

    @Data
    public static class ActResult {
        /**
         * 分组id
         **/
        @ComponentAttrField(labelText = "分组id")
        private String groupId;
        @ComponentAttrField(labelText = "业务类型", remark = "1-交友 2-约战 3-宝贝 4-互动全品类 5-陪玩")
        private String type;

        @ComponentAttrField(labelText = "角色类型")
        private Integer roleType;
        /**
         * 排名，从1开始算起
         **/
        @ComponentAttrField(labelText = "榜单排名", remark = "从1开始算起")
        private Integer rank;
        /**
         * 显示顺序
         **/
        @ComponentAttrField(labelText = "显示顺序")
        private Integer showOrder;

        /**
         * 称号
         **/
        @ComponentAttrField(labelText = "称号")
        private String title;
    }
}
