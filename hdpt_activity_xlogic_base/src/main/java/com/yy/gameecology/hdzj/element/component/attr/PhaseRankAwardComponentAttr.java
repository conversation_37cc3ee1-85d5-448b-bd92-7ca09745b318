package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;

import java.util.Map;

/**
 * 功能描述:按阶段所在名次做奖励的组件
 *
 * <AUTHOR>
 * @date 2021/4/8 22:27
 */
public class PhaseRankAwardComponentAttr extends ComponentAttr {

    // 奖品发放错误尝试的次数，总共最多调用 1 + retry 次， 让发放尽量成功
    @ComponentAttrField(labelText = "重试次数", remark = "奖品发放错误尝试的次数，总共最多调用 1 + retry 次， 让发放尽量成功")
    private int retry = 2;

    // 1-当前比拼的榜 2-阶段晋级过来的初始名单（此时dateStr不生效） 3- 榜单贡献来源， 还有其它值。。。
    @ComponentAttrField(labelText = "榜单类型", remark = "1-当前比拼的榜 2-阶段晋级过来的初始名单（此时dateStr不生效） 3- 榜单贡献来源",
            dropDownSourceBeanClass = RankTypeSource.class)
    private String rankType = "1";

    //榜单成员有可能是用 | 分隔的组合值，用来指示取哪个组合部分，默认为0（取第一个）
    @ComponentAttrField(labelText = "成员下标", remark = "榜单成员有可能是用 | 分隔的组合值，用来指示取哪个组合部分，默认为0（取第一个）")
    private int receiverInx = 0;

    // 筛选榜单ID，输入榜单ID和此处不同不处理
    @ComponentAttrField(labelText = "榜单ID", remark = "榜单ID，输入榜单ID和此处不同不处理")
    private long rankId = 0;

    // 筛选阶段ID，输入阶段ID和此处不同不处理
    @ComponentAttrField(labelText = "阶段ID", remark = "筛选阶段ID，输入阶段ID和此处不同不处理")
    private long phaseId = 0;

    // 主榜单ID， 若大于0，则根据 primaryRankPos 位置找出成员，用于 rankId 形成贡献榜完整 key
    @ComponentAttrField(labelText = "主榜单ID", remark = "主榜单ID， 若大于0，则根据主榜成员位置 位置找出成员，用于 和榜单id 形成贡献榜完整 key")
    private long primaryRankId = 0;

    // 当 primaryRankId>0 时， 表示主榜的名次位置
    @ComponentAttrField(labelText = "主榜的名次", remark = "表示主榜的名次位置")
    private int primaryRankPos = 1;

    // 当 primaryRankId>0 时， 用于指示主榜要查询的榜单类型
    @ComponentAttrField(labelText = "主榜榜单类型", remark = "当 primaryRankId>0 时， 用于指示主榜要查询的榜单类型",
            dropDownSourceBeanClass = RankTypeSource.class)
    private String primaryRankType = "1";

    /*
        名次奖励配置

        第一层key：位置说明，第一层value：指定位置发放的奖励， 第二层key：taskId，第三层key：奖包ID， 第三层值：奖包ID发放的数量
        第一层key内容组合示例：1,3,8,9-20,31 ，每个值用 , 分开，连续值用 - 指示，同一个位置不能出现多次奖励，否则报错
     */
    @ComponentAttrField(labelText = "名次奖励配置", remark = "第一层key:位置说明;第二层key:奖池id;第三层key:奖包ID;第三层值:奖包ID发放的数量,第一层key内容组合示例：1,3,8,9-20,31;每个值用 , 分开,连续值用 - 指示,同一个位置不能出现多次奖励,否则报错",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "榜单名次"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "奖池id"),
                    @SubField(fieldName = Constant.KEY3, type = Long.class, labelText = "奖包id"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "发放奖包数量")
            })
    Map<String, Map<Long, Map<Long, Integer>>> rankAwardConfig = Maps.newHashMap();

    /*
        名次插入语句配置

        key：要执行的sql，只支持 insert 语句， 上面有占位符，
        示例: INSERT INTO `ge_award_record`(`seq`,`act_id`,`rank_id`,`user_uid`,`receive_uid`,`score`,`rank`,`c_time`,`u_time`) VALUES ('##seq##', ##actId##, ##rankId##, '##member[1]##', '##member[2]##', ##score##, ##rank##, now(),now());

        value：位置(名次)说明, 每个值用 , 分开，连续值用 - 指示，同一个位置不能出现多次奖励，否则报错
        示例：1,3,8,9-20,31
    */
    @ComponentAttrField(labelText = "名次插入语句", remark = "key:要执行的sql,只支持 insert 语句,上面有占位符,示例: INSERT INTO `ge_award_record`(`seq`,`act_id`,`rank_id`,`user_uid`,`receive_uid`,`score`,`rank`,`c_time`,`u_time`) VALUES ('##seq##', ##actId##, ##rankId##, '##member[1]##', '##member[2]##', ##score##, ##rank##, now(),now()); value：位置(名次)说明, 每个值用 , 分开，连续值用 - 指示，同一个位置不能出现多次奖励，否则报错;示例：1,3,8,9-20,31",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "插入sql"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "榜单名次")
            })
    Map<String, String> rankMysqlConfig = Maps.newHashMap();

    /**
     * 是否发送如流消息, 默认不发送 1->发送
     * 若要发送需配置
     * INSERT INTO gameecology.ge_act_attr (act_id, attr_name, attr_value, attr_desc) VALUES (2022035002, 'act_notice', '{"groupId":5611212,"baiduWebhook":"http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dd54387406ec799763cd62b4c40219dd8","userIds":""}', '活动重要消息通知');
     */
    @ComponentAttrField(labelText = "发送如流消息", remark = "是否发送如流消息, 默认不发送 1->发送,若要发送需配置,INSERT INTO gameecology.ge_act_attr (act_id, attr_name, attr_value, attr_desc) VALUES (2022035002, 'act_notice', '{\"groupId\":5611212,\"baiduWebhook\":\"http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dd54387406ec799763cd62b4c40219dd8\",\"userIds\":\"\"}', '活动重要消息通知');",
            dropDownSourceBeanClass = YesNoSource.class)
    private int sendFlowNotice = 0;

    public int getSendFlowNotice() {
        return sendFlowNotice;
    }

    public void setSendFlowNotice(int sendFlowNotice) {
        this.sendFlowNotice = sendFlowNotice;
    }

    public int getRetry() {
        return retry;
    }

    public void setRetry(int retry) {
        this.retry = retry;
    }

    public String getRankType() {
        return rankType;
    }

    public void setRankType(String rankType) {
        this.rankType = rankType;
    }

    public int getReceiverInx() {
        return receiverInx;
    }

    public void setReceiverInx(int receiverInx) {
        this.receiverInx = receiverInx;
    }

    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(long phaseId) {
        this.phaseId = phaseId;
    }

    public long getPrimaryRankId() {
        return primaryRankId;
    }

    public void setPrimaryRankId(long primaryRankId) {
        this.primaryRankId = primaryRankId;
    }

    public int getPrimaryRankPos() {
        return primaryRankPos;
    }

    public void setPrimaryRankPos(int primaryRankPos) {
        this.primaryRankPos = primaryRankPos;
    }

    public String getPrimaryRankType() {
        return primaryRankType;
    }

    public void setPrimaryRankType(String primaryRankType) {
        this.primaryRankType = primaryRankType;
    }

    public Map<String, Map<Long, Map<Long, Integer>>> getRankAwardConfig() {
        return rankAwardConfig;
    }

    public void setRankAwardConfig(Map<String, Map<Long, Map<Long, Integer>>> rankAwardConfig) {
        this.rankAwardConfig = rankAwardConfig;
    }

    public Map<String, String> getRankMysqlConfig() {
        return rankMysqlConfig;
    }

    public void setRankMysqlConfig(Map<String, String> rankMysqlConfig) {
        this.rankMysqlConfig = rankMysqlConfig;
    }

    public boolean isMyDuty(long rankId, long phaseId) {
        return (rankId < 1 || phaseId < 1) ? false : (this.rankId == rankId && this.phaseId == phaseId);
    }
}
