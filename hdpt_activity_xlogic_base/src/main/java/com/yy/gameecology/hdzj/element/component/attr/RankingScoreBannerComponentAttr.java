package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.hdzj.annotation.SkipCheck;
import com.yy.gameecology.hdzj.bean.BroadcastConfig;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.RoleTypeSource;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-11-20 10:58
 **/
@SkipCheck
public class RankingScoreBannerComponentAttr extends ComponentAttr {


    // 筛选榜单ID，输入榜单ID和此处不同不处理
    @ComponentAttrField(labelText = "榜单id列表", remark = "多个时逗号分隔,后端去重",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private Set<Long> rankIds = Sets.newHashSet();

    @ComponentAttrField(labelText = "阶段ID", remark = "限定监听榜单所在阶段，可配置0或不配置，则不限制阶段")
    private long phaseId = 0;


    //榜单成员有可能是用 | 分隔的组合值，用来指示取哪个组合部分，默认为0（取第一个）
    @ComponentAttrField(labelText = "成员下标", remark = "榜单成员有可能是用 | 分隔的组合值，用来指示取哪个组合部分，默认为0（取第一个）")
    private int receiverInx = 0;

    @ComponentAttrField(labelText = "一次送礼多级任务，只广播最高分值阶梯")
    private boolean selectHighestLevel = false;

    /**
     * 广播对象的角色类型
     */
    @ComponentAttrField(labelText = "角色类型", dropDownSourceBeanClass = RoleTypeSource.class)
    private int roleType = 200;

    /**
     * 默认横幅url
     */
    @ComponentAttrField(labelText = "默认横幅url")
    private String defaultBannerUrl;


    /**
     * 默认横幅广播配置，优先用
     * key需要达到的分值 value广播横幅配置
     */
    @ComponentAttrField(labelText = "默认横幅广播配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "分值"),
                    @SubField(fieldName = Constant.VALUE, type = List.class, skip = true),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = BroadcastConfig.class)
            })
    private TreeMap<Long, List<BroadcastConfig>> defaultScoreBannerConfig = Maps.newTreeMap();


    /**
     * 根据角色id配置横幅广播配置 第一次key，角色id,多个的时候用逗号隔开 第二key需要达到的分值 value广播横幅配置
     */
    @ComponentAttrField(labelText = "角色id配置横幅广播",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "角色id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "达到的分值"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = BroadcastConfig.class),
                    @SubField(fieldName = Constant.VALUE, type = List.class, skip = true)
            })
    private TreeMap<String, TreeMap<Long, List<BroadcastConfig>>> roleScoreBannerConfig = Maps.newTreeMap();



    public boolean isMyDuty(long rankId, long phaseId) {
        return rankIds.contains(rankId) && (this.phaseId == 0 || this.phaseId == phaseId);
    }


    public Set<Long> getRankIds() {
        return rankIds;
    }

    public void setRankIds(Set<Long> rankIds) {
        this.rankIds = rankIds;
    }

    public long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(long phaseId) {
        this.phaseId = phaseId;
    }

    public int getReceiverInx() {
        return receiverInx;
    }

    public void setReceiverInx(int receiverInx) {
        this.receiverInx = receiverInx;
    }

    public TreeMap<Long, List<BroadcastConfig>> getDefaultScoreBannerConfig() {
        return defaultScoreBannerConfig;
    }

    public void setDefaultScoreBannerConfig(TreeMap<Long, List<BroadcastConfig>> defaultScoreBannerConfig) {
        this.defaultScoreBannerConfig = defaultScoreBannerConfig;
    }

    public TreeMap<String, TreeMap<Long, List<BroadcastConfig>>> getRoleScoreBannerConfig() {
        return roleScoreBannerConfig;
    }

    public void setRoleScoreBannerConfig(TreeMap<String, TreeMap<Long, List<BroadcastConfig>>> roleScoreBannerConfig) {
        this.roleScoreBannerConfig = roleScoreBannerConfig;
    }

    public String getDefaultBannerUrl() {
        return defaultBannerUrl;
    }

    public void setDefaultBannerUrl(String defaultBannerUrl) {
        this.defaultBannerUrl = defaultBannerUrl;
    }

    public int getRoleType() {
        return roleType;
    }

    public void setRoleType(int roleType) {
        this.roleType = roleType;
    }

    public boolean isSelectHighestLevel() {
        return selectHighestLevel;
    }

    public void setSelectHighestLevel(boolean selectHighestLevel) {
        this.selectHighestLevel = selectHighestLevel;
    }
}
