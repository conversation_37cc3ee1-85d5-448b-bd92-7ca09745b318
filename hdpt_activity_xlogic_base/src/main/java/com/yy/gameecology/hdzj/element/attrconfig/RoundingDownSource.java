package com.yy.gameecology.hdzj.element.attrconfig;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/25 14:07
 **/
@Component
public class RoundingDownSource implements DropDownSource {
    // [{"code":"10","desc":"舍弃个位(10)"},{"code":"100","desc":"舍弃百位和个位(100)"}]
    @Override
    public List<DropDownVo> listDropDown() {
        return Arrays.asList(
                new DropDownVo("1", "舍弃小数位(1)"),
                new DropDownVo("10", "舍弃个位(10)"),
                new DropDownVo("100", "舍弃百位和个位(100)")
        );
    }
}
