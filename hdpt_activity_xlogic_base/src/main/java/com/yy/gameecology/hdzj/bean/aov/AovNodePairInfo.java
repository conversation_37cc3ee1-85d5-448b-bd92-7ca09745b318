package com.yy.gameecology.hdzj.bean.aov;

import com.yy.gameecology.common.consts.aov.AovConst;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AovNodePairInfo {

    protected int advanceNodeIndex;

    protected AovMatchNodeInfo node1;

    protected AovMatchNodeInfo node2;

    /**
     * 标识是否为轮空局
     */
    protected int bye = 1;

    /**
     * 参考{@link AovConst.MatchState}
     */
    protected int state;
}
