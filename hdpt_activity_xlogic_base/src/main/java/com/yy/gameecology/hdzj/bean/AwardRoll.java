package com.yy.gameecology.hdzj.bean;

import com.yy.gameecology.activity.bean.UserInfoVo;
import lombok.Data;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-07-14 16:26
 **/
@Data
public class AwardRoll {
   private String time;
   private String prize;
   private int type;
   private long packageId;
   /**
    * 奖品图片
    */
   private String logo;
   private List<UserInfoVo> user;

   private String extJson;

   private long sid;

   private long ssid;

   private String giftName;

   private long giftCount;

   private String giftUnit;

   private String giftIcon;

   private String unit;
}
