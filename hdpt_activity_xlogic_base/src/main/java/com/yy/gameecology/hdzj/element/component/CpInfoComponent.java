package com.yy.gameecology.hdzj.element.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.bean.CpUid;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.mapper.gameecology.cmpt.Cmpt2064CpInfoMapper;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2064CpInfo;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CpInfoComponentAttr;
import com.yy.thrift.hdztranking.ActorInfoItem;
import com.yy.thrift.hdztranking.ActorQueryItem;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-23 15:37
 **/
@Slf4j
@RestController
@RequestMapping("/2064")
public class CpInfoComponent extends BaseActComponent<CpInfoComponentAttr> {

    @Autowired
    private Cmpt2064CpInfoMapper cmpt2064CpInfoMapper;

    @Autowired
    private UserInfoService userInfoService;

    @Override
    public Long getComponentId() {
        return ComponentId.CP_INFO;
    }

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, CpInfoComponentAttr attr) {
        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        Date time = DateUtil.getDate(event.getOccurTime());
        String dateCode = TimeKeyHelper.getTimeCode(attr.getTimeKey(), time);

        //已经保存过了的cp对，不再操作mysql
        String redisGroup = getRedisGroupCode(attr.getActId());
        String key = makeKey(attr, "cpDupInsertCache:" + dateCode + ":" + event.getMember());
        String exist = actRedisDao.get(redisGroup, key);
        if (StringUtil.isNotBlank(exist)) {
            log.info("already exist actId:{},index:{},member:{},dateCode:{}", attr.getActId(), attr.getCmptUseInx(), event.getMember(), dateCode);
            return;
        }

        //cp对入库
        CpUid cpUid = Const.splitCpMember(event.getMember());
        int res = cmpt2064CpInfoMapper.insertCpInfo(attr.getActId(), attr.getCmptUseInx(), dateCode, cpUid.getUserUid(), cpUid.getAnchorUid());

        actRedisDao.set(redisGroup, key, event.getOccurTime(), DateUtil.ONE_DAY_SECONDS);

        log.info("insertCpInfo actId:{},index:{},member:{},dateCode:{},res:{}", attr.getActId(), attr.getCmptUseInx(), event.getMember(), dateCode, res);

    }


    /**
     * 查询与此uid组成的任意cp对
     */
    public List<CpUid> queryUserCpUid(long actId, long cmptUserIndex, String dateCode, long uid) {
        List<Cmpt2064CpInfo> cmpt2064CpInfos = cmpt2064CpInfoMapper.selectCpInfo(actId, cmptUserIndex, dateCode, uid);
        if (CollectionUtils.isEmpty(cmpt2064CpInfos)) {
            return Lists.newArrayList();
        }

        return cmpt2064CpInfos.stream().map(p -> {
            CpUid cpUid = new CpUid();
            cpUid.setUserUid(p.getUserUid());
            cpUid.setAnchorUid(p.getAnchorUid());
            cpUid.setMember(p.getUserUid() + "|" + p.getAnchorUid());
            return cpUid;
        }).collect(Collectors.toList());
    }

    @RequestMapping("/getCpList")
    public Response<CpListRsp> getCpList(@RequestParam(name = "actId") int actId,
                                         @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                         @RequestParam(name = "dateCode", required = false) String dateCode) {
        long uid = getLoginYYUid();
        if (uid <= 0L) {
            return Response.fail(400, "未登陆");
        }
        if (SysEvHelper.isHistory()) {
            return Response.fail(400, "活动已结束!");
        }

        CpInfoComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未配置");
        }

        if (StringUtils.isEmpty(dateCode)) {
            dateCode = TimeKeyHelper.getTimeCode(attr.getTimeKey(), commonService.getNow(actId));
        }

        String rankDateCode = StringUtil.EMPTY;
        if (StringUtils.isEmpty(dateCode)) {
            rankDateCode = TimeKeyHelper.getTimeCode(attr.getRankShowScoreTimeKey(), commonService.getNow(actId));
        }

        CpListRsp rsp = new CpListRsp();

        //用户资料
        Set<Long> uids = Sets.newHashSet();
        uids.add(uid);
        List<CpUid> cpUids = queryUserCpUid(actId, cmptInx, dateCode, uid);
        if(!CollectionUtils.isEmpty(cpUids)){
            Set<Long> userUid = cpUids.stream().map(CpUid::getUserUid).collect(Collectors.toSet());
            Set<Long> anchorUid = cpUids.stream().map(CpUid::getAnchorUid).collect(Collectors.toSet());
            uids.addAll(userUid);
            uids.addAll(anchorUid);
        }
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids), multiNickUsers, false, Template.all.getCode());

        //当前用户信息
        rsp.setUid(uid);
        var curUser = userInfos.get(uid);
        if (curUser != null) {
            rsp.setAvatar(curUser.getAvatarUrl());
            rsp.setNick(curUser.getNick());
        }
        //多昵称
//        rsp.setNickExtUsers(multiNickUsers);


        if (CollectionUtils.isEmpty(cpUids)) {
            return Response.success(rsp);
        }

        //分数
        List<String> memberIds = cpUids.stream().map(CpUid::getMember).toList();
        List<ActorQueryItem> paras = Lists.newArrayList();
        for (String memberId : memberIds) {
            ActorQueryItem queryItem = new ActorQueryItem();
            queryItem.setActorId(memberId);
            queryItem.setRankingId(attr.getRankId());
            queryItem.setPhaseId(attr.getPhaseId());
            queryItem.setWithStatus(false);
            queryItem.setDateStr(rankDateCode);
            paras.add(queryItem);
        }
        Map<String, ActorInfoItem> actorInfoItemMap = hdztRankingThriftClient.queryActorRankingInfoMap(actId, paras);

        //cp信息
        List<CpMember> outPutCpMembers = Lists.newArrayList();
        buildCpMembers(cpUids, actorInfoItemMap, userInfos, outPutCpMembers);

        //按分数从高到低倒序
        outPutCpMembers = outPutCpMembers.stream().sorted(Comparator.comparing(CpMember::getScore).reversed()).collect(Collectors.toList());

        rsp.setCpMembers(outPutCpMembers);

        return Response.success(rsp, multiNickUsers);
    }

    private static void buildCpMembers(List<CpUid> cpUids, Map<String, ActorInfoItem> actorInfoItemMap, Map<Long, UserInfoVo> userInfos, List<CpMember> cpMembers) {
        for (CpUid cpUid : cpUids) {
            CpMember cpMember = new CpMember();
            cpMember.setCpMember(cpUid.getMember());

            ActorInfoItem score = actorInfoItemMap.get(cpUid.getMember());
            if (score != null) {
                cpMember.setScore(score.getScore());
            }

            //主播信息
            MemInfo anchorMember = new MemInfo();
            anchorMember.setUid(cpUid.getAnchorUid());
            UserInfoVo anchorUserInfo = userInfos.get(cpUid.getAnchorUid());
            if (anchorUserInfo != null) {
                anchorMember.setName(anchorUserInfo.getNick());
                anchorMember.setAvatar(anchorUserInfo.getAvatarUrl());
            }
            cpMember.setAnchor(anchorMember);

            //用户信息
            MemInfo userMember = new MemInfo();
            userMember.setUid(cpUid.getUserUid());
            UserInfoVo userInfo = userInfos.get(cpUid.getUserUid());
            if (userInfo != null) {
                userMember.setName(userInfo.getNick());
                userMember.setAvatar(userInfo.getAvatarUrl());
            }
            cpMember.setUser(userMember);

            cpMembers.add(cpMember);
        }
    }


    @Data
    public static class CpListRsp {
        List<CpMember> cpMembers;

        Map<String, Map<String, MultiNickItem>> nickExtUsers;

        private long uid;

        private String avatar;

        private String nick;
    }

    @Data
    public static class CpMember {
        private String cpMember;

        private MemInfo anchor;

        private MemInfo user;

        private long score;
    }

    @Data
    public static class MemInfo {
        private long uid;

        private String name;

        private String avatar;
    }


}
