package com.yy.gameecology.hdzj.element.component.attr;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Map;

@Data
public class MultiYONovicePackageComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "风控策略key")
    private String riskStrategyKey = "ACT_STRATEGY:2023061001:NOVICE";

    @ComponentAttrField(labelText = "通用弹窗类型")
    private String noticeType = "newUserPackage";

    @ComponentAttrField(labelText = "新用户弹窗类型值")
    private String noticeValue = "PcPopUp";

    @ComponentAttrField(labelText = "模板遮罩弹窗类型值")
    private String maskNoticeValue = "mask";

    @ComponentAttrField(labelText = "礼包配置", useDialog = 1, subFields = {
            @SubField(labelText = "业务ID", type = Long.class, fieldName = Constant.KEY1),
            @SubField(labelText = "个数", type = NoviceConfig.class, fieldName = Constant.VALUE)
    })
    private Map<Long, NoviceConfig> noviceConfigs;

    @ComponentAttrField(labelText = "礼物过期提醒倒计时长", remark = "距离礼物过期（活动结束时间）还有多久（毫秒）需要提醒")
    private long expiredMsgCountdown;

    @ComponentAttrField(labelText = "YY消息提醒的AppId")
    private int msgAppid;

    @ComponentAttrField(labelText = "YY消息提醒发送人Uid")
    private long msgSenderUid;

    @ComponentAttrField(labelText = "追玩进场秀装扮ID", remark = "用来判断是否自动佩戴")
    private int decorationId;

    @Data
    public static class NoviceConfig {

        @ComponentAttrField(labelText = "活动开始时间")
        private Date startTime;

        @ComponentAttrField(labelText = "活动结束时间")
        private Date endTime;

        @ComponentAttrField(labelText = "导流App", remark = "多个使用逗号分隔开")
        private String apps;

        @ComponentAttrField(labelText = "白名单组件索引", remark = "用户白名单存储的白名单组件的useIndex")
        private long whitelistCmptInx;

        @ComponentAttrField(labelText = "渠道ID")
        private String channelId;

        @ComponentAttrField(labelText = "奖池ID")
        private long taskId;

        @ComponentAttrField(labelText = "PC奖包ID")
        private long pcPackageId;

        @ComponentAttrField(labelText = "PC发放礼物数", remark = "只用做限额用，实际发放数量在奖包里")
        private int pcGiftCount;

        @ComponentAttrField(labelText = "免费礼物奖包ID", remark = "针对非白名单用户，发免费礼物的奖包ID")
        private long freePackageId;

        @ComponentAttrField(labelText = "App全额奖包ID", remark = "针对新设备发全额奖励")
        private long appFullPackageId;

        @ComponentAttrField(labelText = "App发放礼物数", remark = "只用做限额用，实际发放数量在奖包里")
        private int appGiftCount;

        @ComponentAttrField(labelText = "App部分奖包ID", remark = "针对非新设备只发部分奖励")
        private long appPartPackageId;

        @ComponentAttrField(labelText = "限制弹窗领取比例", remark = "今日已领取数量超过总限额的x(0.9)时，不弹窗")
        private float stopPopupRate = 0.9f;

        @ComponentAttrField(labelText = "每日限额", remark = "超过该限额后领奖失败")
        private long dailyLimit;

        @ComponentAttrField(labelText = "日期对应的每日限额")
        private String dayLimitConfig;

        @ComponentAttrField(labelText = "营收的礼物ID")
        private int propId;

        @ComponentAttrField(labelText = "营收的appid")
        private int propAppid;

        @ComponentAttrField(labelText = "营收的活动ID")
        private int propActivityId;

        @ComponentAttrField(labelText = "礼物过期提醒IM文案")
        private String giftExpiredMsg;

        @ComponentAttrField(labelText = "礼物过期提醒SMS文案")
        private String giftExpiredSmsMsg;

        public long getDayLimit(String day) {
            if (!StringUtils.startsWith(dayLimitConfig, StringUtil.OPEN_BRACE)) {
                return dailyLimit;
            }

            JSONObject json = JSON.parseObject(dayLimitConfig);

            if (json.containsKey(day)) {
                return json.getLongValue(day);
            }

            return dailyLimit;
        }
    }
}
