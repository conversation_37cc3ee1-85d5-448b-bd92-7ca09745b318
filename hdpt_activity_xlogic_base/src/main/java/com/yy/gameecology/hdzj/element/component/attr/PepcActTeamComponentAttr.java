package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrCollector;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.Set;

@Data
public class PepcActTeamComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "官方队长群二维码", remark = "官方队长群二维码", propType = ComponentAttrCollector.PropType.IMAGE)
    private String teamLeadGroupQr;

    @ComponentAttrField(labelText = "队长群群号", remark = "官方队长群群号")
    private String teamLeadGroupId;

    @ComponentAttrField(labelText = "官方队员群二维码", remark = "官方队员群二维码", propType = ComponentAttrCollector.PropType.IMAGE)
    private String teammatesGroupQr;

    @ComponentAttrField(labelText = "官方群群号", remark = "官方大群群号")
    private String wxNum;

    @ComponentAttrField(labelText = "默认队伍宣言")
    private String teamDeclaration;

    @ComponentAttrField(labelText = "队伍队员数下限", remark = "单个队伍内队伍人数（包括队长）人数下限")
    private Integer teamMemberMin;

    @ComponentAttrField(labelText = "队伍队员数上限", remark = "单个队伍内队伍人数（包括队长）人数上限")
    private Integer teamMemberMax;

    @ComponentAttrField(labelText = "每期队伍数下限", remark = "每期需要完整报名的队伍数量，少于队伍下限，本期比赛将被取消")
    private Integer teamNumMin;

    @ComponentAttrField(labelText = "每期队伍数上限", remark = "每期可完整报名队伍数量上限，超过此队伍数，后面报名的将被舍弃")
    private Integer teamNumMax;

    @ComponentAttrField(labelText = "同手机uid限制", remark = "绑定了相同手机号的参赛uid限制")
    private int mobileUidLimit = 1;

    @ComponentAttrField(labelText = "黑名单组件索引", remark = "参赛黑名单组件索引，黑名单用户不允许报名（创建队伍&加入队伍）")
    private int blacklistComponentIndex;

    @ComponentAttrField(labelText = "命中黑名单提示", remark = "命中黑名单后的提示")
    private String blacklistTips = "检测到您有多次违规行为，请1天后重试或联系赛事专属客服：YY80698";

    @ComponentAttrField(labelText = "进入游戏房间时间",remark = "从游戏开始时间往前倒推N分钟可以进入游戏H5房间")
    protected  int jumpGameMin;

//    @ComponentAttrField(labelText = "启用参赛白名单", remark = "是否启用参赛白名单")
//    private boolean useWhitelist;
//
//    @ComponentAttrField(labelText = "未在白名单提示", remark = "启用白名单后，如果未命中白名单，返回给前端你的提示")
//    private String whitelistTips;
//
//    @ComponentAttrField(labelText = "白名单管理者", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class), remark = "参赛白名单管理者uid，多个使用英文逗号分隔")
//    private Set<Long> whitelistManagerUids;

    @ComponentAttrField(labelText = "风控策略key", remark = "参赛风控策略")
    private String riskStrategyKey;

    @ComponentAttrField(labelText = "秩序appid", remark = "秩序送审通道的AppId")
    private String mmsAppid;

    @ComponentAttrField(labelText = "秩序appSecret", remark = "秩序送审通道的AppSecret")
    private String mmsAppSecret;

    @ComponentAttrField(labelText = "秩序appSecretId", remark = "秩序送审通道的AppSecretId")
    private String mmsAppSecretId;

}
