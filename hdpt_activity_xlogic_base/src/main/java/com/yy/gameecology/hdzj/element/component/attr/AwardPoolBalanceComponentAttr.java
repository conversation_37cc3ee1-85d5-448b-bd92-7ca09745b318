package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2022-10-29 15:26
 **/
@Data
public class AwardPoolBalanceComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "奖池数量")
    private long awardPoolConfig;

    @ComponentAttrField(labelText = "任务消息重试时限", remark = "超过这个时间，榜单更新更新事件 redis seq失效不可安全重试")
    private Integer seqExpireSeconds = DateUtil.ONE_DAY_SECONDS;
}
