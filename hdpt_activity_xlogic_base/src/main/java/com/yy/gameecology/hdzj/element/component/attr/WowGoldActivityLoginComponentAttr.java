package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import lombok.Data;

import java.util.Map;

@Data
public class WowGoldActivityLoginComponentAttr  extends ComponentAttr {

    @ComponentAttrField(labelText = "白名单组件",remark = "参与活动UID白名单组件索引")
    protected int whitelistCmptIndex;

    @ComponentAttrField(labelText = "新旧用户白名单组件",remark = "新旧用户列表组件索引")
    protected int userTypeListCmptIndex;

    @ComponentAttrField(labelText = "hdid用户白名单组件",remark = "hdid列表组件索引")
    protected int hidListCmptIndex;

    @ComponentAttrField(labelText = "任务组件",remark = "任务组件索引")
    protected int taskCmptIndex;

    @ComponentAttrField(labelText = "风控key",remark = "风控拦截使用")
    protected String riskStrategyKey;

    @ComponentAttrField(labelText = "奖池限额组件",remark = "")
    protected int poolLimitIndex;

    @ComponentAttrField(labelText = "活动名单控制组件id",remark = "")
    protected int controlEnterIndex;

//    @ComponentAttrField(labelText = "奖池限额配置id",remark = "")
//    protected int poolLimitConfigId;

    @ComponentAttrField(labelText = "活动奖包id",remark = "")
    protected int packageId;

    @ComponentAttrField(labelText = "参与活动app类型",remark = "")
    protected String appName;

//    @ComponentAttrField(labelText = "最小限额",remark = "")
//    protected int minPoolLimit;

    @ComponentAttrField(labelText = "老用户弹窗",remark = "弹窗队列弹出")
    protected String oldUserPop;

    @ComponentAttrField(labelText = "新用户弹窗",remark = "弹窗队列弹出")
    protected String newUserPop;

    @ComponentAttrField(labelText = "默认弹窗",remark = "弹窗队列弹出")
    protected String defaultPop;

    @ComponentAttrField(labelText = "魔兽世界发奖", remark = "",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "奖励类型"),
                    @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class, labelText = "奖励配置")
            })
    private Map<String, AwardAttrConfig> rewardConfig = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "老用户获奖title",remark = "")
    protected String oldRewardPopTitle;

    @ComponentAttrField(labelText = "老用户获奖message",remark = "")
    protected String oldRewardPopMessage;

    @ComponentAttrField(labelText = "老用户获奖link",remark = "")
    protected String oldRewardPopLink;
}
