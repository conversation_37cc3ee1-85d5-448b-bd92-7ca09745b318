package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

/**
 * @Author: CXZ
 * @Desciption: 抽奖记录组件
 * @Date: 2021/4/15 19:19
 * @Modified:
 */
public class LotteryRecordComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    private Long busiId;

    @ComponentAttrField(labelText = "奖池id", remark = "多个用逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private long[] taskIds;

    public long getBusiId() {
        return busiId;
    }

    public void setBusiId(long busiId) {
        this.busiId = busiId;
    }

    public long[] getTaskIds() {
        return taskIds;
    }

    public void setTaskIds(long[] taskIds) {
        this.taskIds = taskIds;
    }
}
