package com.yy.gameecology.hdzj.element.attrconfig;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/23 17:42
 **/
@Component
public class BroadcastTypeSource implements DropDownSource {
    @Override
    public List<DropDownVo> listDropDown() {
        // [{"code":"4","desc":"单业务广播(4)"},{"code":"5","desc":"多业务广播(5)"}]
        // 2=子频道（这个没有），3=顶级频道（这个没有）
        return Arrays.asList(
                new DropDownVo("-1", "不需要广播"),
                new DropDownVo("2", "子频道(2)"),
                new DropDownVo("3", "顶级频道（家族）(3)"),
                new DropDownVo("4", "单业务广播(4)"),
                new DropDownVo("5", "多业务广播(5)")
        );
    }
}
