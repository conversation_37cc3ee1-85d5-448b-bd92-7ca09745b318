package com.yy.gameecology.hdzj;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yy.gameecology.hdzj.element.ActComponent;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 活动组件事件分发处理类
 *
 * @author: 郭立平[<EMAIL>]
 * @date: 2021/2/4 10:50
 **/
@Component
public class HdzjComponentCollaborator {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private final static Map<Class<?>, List<Class<?>>> BASE_CLASS_CHANGE_MAP = new ImmutableMap.Builder<Class<?>, List<Class<?>>>()
            .put(boolean.class, Lists.newArrayList(Boolean.class))
            .put(byte.class, Lists.newArrayList(Byte.class))
            .put(char.class, Lists.newArrayList(Character.class))
            .put(double.class, Lists.newArrayList(Double.class, Float.class, Long.class, Integer.class, Short.class))
            .put(float.class, Lists.newArrayList(Float.class, Integer.class, Short.class))
            .put(int.class, Lists.newArrayList(Integer.class, Short.class))
            .put(long.class, Lists.newArrayList(Long.class, Integer.class, Short.class))
            .put(short.class, Lists.newArrayList(Short.class))
            .build();


    @Lazy
    @Autowired
    private ElementManager elementManager;


    /**
     * 出现多放个参数类型符合的方法不能精确匹配
     * @param actId
     * @param cmptId
     * @param methodName
     * @param args
     * @return
     * @throws Exception
     */

    public Object callComponent(Long actId, long cmptId, String methodName, Object... args) throws Exception {
        ActComponent actComponent = elementManager.getActComponent(actId, cmptId);
        Assert.notNull(actComponent, "actId=" + actId + ",cmptId=" + cmptId + " cat not find.");
        Class<?>[] parameterTypes = Stream.of(args).map(Object::getClass).toArray(Class<?>[]::new);
        //只找类自定义的方法，不查找父类的方法
        List<Method> methods = Stream.of(actComponent.getClass().getDeclaredMethods())
                .filter(method -> method.getName().equals(methodName))
                .collect(Collectors.toList());
        Assert.isTrue(!methods.isEmpty(), "actId=" + actId + ",cmptId=" + cmptId + " cat not find.");

        Optional<Method> method = methods.stream().filter(m -> checkParamType(m.getParameterTypes(), parameterTypes)).findFirst();
        if (!method.isPresent()) {
            throw new NoSuchMethodException(actComponent.getClass().getName() + "." + methodName + argumentTypesToString(parameterTypes));
        }

        return method.get().invoke(actComponent, args);
    }

    private String argumentTypesToString(Class<?>[] argTypes) {
        StringBuilder buf = new StringBuilder();
        buf.append("(");
        if (argTypes != null) {
            for (int i = 0; i < argTypes.length; i++) {
                if (i > 0) {
                    buf.append(", ");
                }
                Class<?> c = argTypes[i];
                buf.append((c == null) ? "null" : c.getName());
            }
        }
        buf.append(")");
        return buf.toString();
    }

    /**
     * 比对调用方法的参数类型
     * @param a1
     * @param a2
     * @return
     */
    private boolean checkParamType(Class<?>[] a1, Class<?>[] a2) {
        if (a1 == null) {
            return a2 == null || a2.length == 0;
        }

        if (a2 == null) {
            return a1.length == 0;
        }

        if (a1.length != a2.length) {
            return false;
        }


        for (int i = 0; i < a1.length; i++) {
            //基本类型
            List<Class<?>> convertibleList = BASE_CLASS_CHANGE_MAP.get(a1[i]);
            if (CollectionUtils.isNotEmpty(convertibleList)) {
                if (!convertibleList.contains(a2[i])) {
                    return false;
                }
                continue;
            }
            if (a1[i] != a2[i] && !a1[i].isAssignableFrom(a2[i])) {
                return false;
            }

        }

        return true;
    }
}
