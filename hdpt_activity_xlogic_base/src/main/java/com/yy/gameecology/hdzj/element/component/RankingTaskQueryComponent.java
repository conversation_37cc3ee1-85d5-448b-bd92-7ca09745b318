package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.BabyMissionItem;
import com.yy.gameecology.activity.bean.actlayer.PhaseInfo;
import com.yy.gameecology.activity.bean.actlayer.TaskItem;
import com.yy.gameecology.activity.bean.hdzt.RankPhasePair;
import com.yy.gameecology.activity.bean.hdzt.TaskUserInfoVo;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.service.HdztTaskService;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.bean.UserTaskInfo;
import com.yy.gameecology.hdzj.bean.UserTaskVo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.RankingTaskQueryComponentAttr;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/7 10:19
 **/
@Component
public class RankingTaskQueryComponent extends BaseActComponent<RankingTaskQueryComponentAttr> {
    private static Logger logger = LoggerFactory.getLogger(RankingTaskQueryComponent.class);

    @Autowired
    private HdztTaskService hdztTaskService;
    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Override
    public Long getComponentId() {
        return ComponentId.RANKING_TASK_INFO_QUERY;
    }

    /**
     * type : 0 查询所有,1 查询用户任务, 2查询主播任务
     **/
    public Map<String, List<UserTaskVo>> queryUserTask(long actId, int cmptUseInx, long uid, int type, String dateStr) {
        Map<String, List<UserTaskVo>> result = Maps.newHashMap();
        if (uid <= 0) {
            return result;
        }
        RankingTaskQueryComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            logger.warn("not exist attr config,actId={},cmptUseInx={}", actId, cmptUseInx);
            return result;
        }

        // 需要查询的任务信息没有配置
        if (!checkAttr(attr, type)) {
            return result;
        }
        final int type0 = 0, type1 = 1, type2 = 2;
        if (type == type0 || type == type1) {
            result.put("userTaskVos", queryTask(uid, actId, attr.getUserTasks(), dateStr));
        }
        if (type == type0 || type == type2) {
            result.put("anchorTasks", queryTask(uid, actId, attr.getAnchorTasks(), dateStr));
        }

        return result;
    }

    /**
     * 获取当前正在进行的任务
     **/
    public Map<String, UserTaskVo> queryUserCurrentTask(long actId, int cmptUseInx, long uid, int type, String dateStr) {
        Map<String, List<UserTaskVo>> taskMap = queryUserTask(actId, cmptUseInx, uid, type, dateStr);
        Map<String, UserTaskVo> result = Maps.newHashMap();
        for (Map.Entry<String, List<UserTaskVo>> entry : taskMap.entrySet()) {
            List<UserTaskVo> userTaskVos = entry.getValue();
            if (CollectionUtils.isEmpty(userTaskVos)) {
                continue;
            }
            UserTaskVo currentTask = getCurrentTask(userTaskVos);
            result.put(entry.getKey(), currentTask);
        }

        return result;
    }

    public Map<String, UserTaskInfo> queryUserTaskV2(long actId, int cmptUseInx, long uid, int type, String dateStr) {
        Map<String, List<UserTaskVo>> data = queryUserTask(actId, cmptUseInx, uid, type, dateStr);

        Map<String, UserTaskInfo> result = Maps.newHashMap();
        for (Map.Entry<String, List<UserTaskVo>> listEntry : data.entrySet()) {
            List<UserTaskVo> list = listEntry.getValue();
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            UserTaskInfo userTaskInfo = new UserTaskInfo();
            userTaskInfo.setTaskValues(list.stream().map(UserTaskVo::getPassValue).sorted().collect(Collectors.toList()));
            userTaskInfo.setCurrentValue(list.get(0).getCompleteValue());

            int currentTaskIndex = userTaskInfo.getTaskValues().size();
            for (int index = 0; index < userTaskInfo.getTaskValues().size(); index++) {
                if (userTaskInfo.getCurrentValue() <= userTaskInfo.getTaskValues().get(index)) {
                    currentTaskIndex = index + 1;
                    break;
                }
            }
            userTaskInfo.setCurrentTaskIndex(currentTaskIndex);

            result.put(listEntry.getKey(), userTaskInfo);
        }

        return result;
    }

    private UserTaskVo getCurrentTask(List<UserTaskVo> userTaskVos) {
        if (CollectionUtils.isEmpty(userTaskVos)) {
            return null;
        }

        int size = userTaskVos.size();
        for (int index = 0; index < size; index++) {
            if (userTaskVos.get(index).getStatus() != 1) {
                return userTaskVos.get(index);
            }
        }

        return userTaskVos.get(size - 1);
    }

    private boolean checkAttr(RankingTaskQueryComponentAttr attr, int type) {
        final int type0 = 0, type1 = 1, type2 = 2;
        if (type == type0 && CollectionUtils.isEmpty(attr.getUserTasks()) && CollectionUtils.isEmpty(attr.getAnchorTasks())) {
            return false;
        }

        if (type == type1 && CollectionUtils.isEmpty(attr.getUserTasks())) {
            return false;
        }

        if (type == type2 && CollectionUtils.isEmpty(attr.getAnchorTasks())) {
            return false;
        }
        return true;
    }

    private List<UserTaskVo> queryTask(long uid, long actId, List<RankPhasePair> rankPhasePairList, String dateStr) {
        List<UserTaskVo> taskVos = new ArrayList<>();
        if (CollectionUtils.isEmpty(rankPhasePairList)) {
            return taskVos;
        }
        List<TaskUserInfoVo> taskUserInfoVos = hdztTaskService.getUserTaskInfos(uid, actId, rankPhasePairList, dateStr);
        Map<Long, PhaseInfo> actPhaseMap = hdztRankingThriftClient.queryRankingPhaseMap(actId);
        for (TaskUserInfoVo taskUserInfoVo : taskUserInfoVos) {
            List<BabyMissionItem> missionItems = taskUserInfoVo.getMissions();
            if (CollectionUtils.isEmpty(missionItems)) {
                continue;
            }
            // 当前在进行的任务id
            long curTaskId = taskUserInfoVo.getCurTaskId();
            long phaseId = taskUserInfoVo.getPhaseId();
            PhaseInfo phaseInfo = actPhaseMap.get(phaseId);

            // 次数限制对应于表ranking_phase的recycle_count
            int finishCountLimit = 1;
            if (phaseInfo != null) {
                finishCountLimit = phaseInfo.getRecycleCount() == 0 ? 1 : phaseInfo.getRecycleCount();
            }

            BabyMissionItem missionItem = missionItems.get(0);
            List<TaskItem> taskItems = missionItem.getTaskItems();

            // 当前进行到第几轮
            long curRound = missionItem.getCurRound();

            Long passValue = 0L;
            for (TaskItem taskItem : taskItems) {
                String extJson = taskItem.getExtjson();

                int taskLevel = taskItem.getTaskId().intValue();

                // 系数,解决如下场景：心跳上报60秒1次,界面展示要1分钟
                int coefficient = 1;
                try {
                    if (StringUtil.isNotBlank(extJson)) {
                        JSONObject jsonObject = JSON.parseObject(extJson);
                        coefficient = Convert.toInt(jsonObject.getString("coefficient"), 1);
                        taskLevel = Convert.toInt(jsonObject.getString("awardCount"), taskLevel);
                    }
                } catch (Exception ex) {
                    logger.error("get coefficient error,extJson={}", extJson, ex);
                }
                int status = taskItem.getTaskId() < curTaskId ? 1 : 0;
                passValue += taskItem.getPassValue();
                if (finishCountLimit != -1 && finishCountLimit < curRound) {
                    status = 1;
                }
                if (curTaskId == taskItem.getTaskId() && passValue <= missionItem.getCompletedCount()) {
                    status = 1;
                }
                int currentFinishCount = status == 1 ? finishCountLimit : (int) curRound - 1;

                Long currentValue = status == 1 ? passValue : missionItem.getCompletedCount();
                UserTaskVo userTaskVo = new UserTaskVo(taskItem.getRemark(), finishCountLimit, currentFinishCount
                        , currentValue.intValue() / coefficient
                        , passValue.intValue() / coefficient
                        , status, taskLevel, taskLevel == taskItems.size(), missionItem.getCompletedCount());

                if (taskItem.getTaskId() == curTaskId) {
                    userTaskVo.setTaskName(missionItem.getTaskName());
                }

                taskVos.add(userTaskVo);
            }
        }

        return taskVos;
    }

    private List<UserTaskVo> queryChannelTask(long actId, int cmptUseInx, long sid, String dateStr) {
        List<UserTaskVo> result = new ArrayList<>();
        if (sid <= 0) {
            return result;
        }
        RankingTaskQueryComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            logger.warn("not exist attr config,actId={},cmptUseInx={}", actId, cmptUseInx);
            return result;
        }

        result = queryTask(sid, actId, attr.getChannelTasks(), dateStr);

        return result;
    }

    public UserTaskVo queryChannelCurrentTask(long actId, int cmptUseInx, long sid, String dateStr) {
        List<UserTaskVo> userTaskVos = queryChannelTask(actId, cmptUseInx, sid, dateStr);

        return getCurrentTask(userTaskVos);
    }
}
