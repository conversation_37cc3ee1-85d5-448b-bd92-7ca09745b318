package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.mq.hdzk.DayTaskCompleteEvent;
import com.yy.gameecology.activity.bean.mq.hdzk.DayTaskPollOutEvent;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.common.bean.PairTemplateBean;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.MD5SHAUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.bean.daytask.CurDayTask;
import com.yy.gameecology.hdzj.bean.daytask.CurDayTaskItem;
import com.yy.gameecology.hdzj.bean.daytask.UpdateDayTaskReq;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.WhitelistComponent;
import com.yy.gameecology.hdzj.element.component.attr.DayTaskComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardLimitConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.DayTaskConfig;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-09-18 17:06
 **/

/**
 * 由DayTaskComponent2 mysql实现代替
 */
@Deprecated
@RestController
@RequestMapping("/cmpt/dayTaskCmpt")
@Component
public class DayTaskComponent extends BaseActComponent<DayTaskComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private WhitelistComponent whitelistComponent;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private CommonService commonService;

    @Autowired
    private KafkaService kafkaService;


    @Override
    public Long getComponentId() {
        return ComponentId.DAY_TASK;
    }

    /**
     * 用户最近过的任务日期
     * 注意：此标记在所有写操作完成后再打上，确保可失败重试！！！
     * task:{uid}:last_complete_day
     */
    private static final String LAST_COMPLETE_DAY_CODE = "task:%s:last_complete_day";
    /**
     * 用户当前正在过的任务
     * task:{uid}:cur_task_day
     */
    private static final String CUR_TASK_DAY_INDEX = "task:%s:cur_task_day_index";

    /**
     * hash
     * 用户当前子任务的值
     * task:{uid}:{dayIndex}
     */
    private static final String DAY_ITEM_TASK = "task:%s:%s";

    /**
     * 限额奖包已发放数量
     */
    private static final String AWARD_SEND = "award_send:%s";

    private static final String AWARD_SEND_DAY_SEND_STATIC =  "award_day_send:static:%s:%s";

    private static final long DUP_SECONDS = DateUtil.ONE_DAY_SECONDS;

    @RequestMapping("/queryCurDayTaskInfo")
    public CurDayTask queryCurDayTaskInfo(long actId, long index, String member) {
        return queryCurDayTask(actId, index, member);
    }


    /**
     * 更新任务
     * 接口幂等、可重复调用，有问题抛出异常
     */
    public void updateTask(UpdateDayTaskReq req) {
        log.info("updateTask begin,req={}", JSON.toJSONString(req));
        Assert.hasLength(req.getSeq(), "seq不为空");
        Assert.hasLength(req.getItem(), "item 不为空");
        Assert.hasLength(req.getDayCode(), "dayCode 不为空");
        Assert.isTrue(req.getActId() > 0, "actId 0");

        var attr = req.getCmptIndex() > 0 ? getComponentAttr(req.getActId(), req.getCmptIndex()) : getUniqueComponentAttr(req.getActId());
        //白名单判断
        if (!checkWhiteList(attr, req)) {
            return;
        }

        //活动中台白名单判断
        if (SysEvHelper.isDeploy() && commonService.isGrey(attr.getActId())) {
            boolean inWhiteList = hdztRankingThriftClient.checkWhiteList(attr.getActId(), RoleType.USER, req.getMember());
            if (!inWhiteList) {
                log.info("updateTask grey not in whitelist uid:{},actId:{}", req.getMember(), attr.getActId());
                return;
            }
        }

        String redisCode = getRedisGroupCode(req.getActId());
        //判断是否完成了当日所有任务
        if (alreadyCompleteDayTask(redisCode, attr, req)) {
            return;
        }
        //获取正在过的任务
        int curTaskDayIndex = queryCurTaskDayIndex(redisCode, attr, req.getMember());
        if (!hasTaskItem(attr, req, curTaskDayIndex)) {
            return;
        }

        //奖池判断
        if (!checkAwardPoolLimit(attr, curTaskDayIndex)) {
            log.warn("award pool limit");
            return;
        }

        //增加过子任务数值
        String taskItemKey = buildDayItemTaskKey(attr, req.getMember(), curTaskDayIndex);
        String itemSeq = makeKey(attr, "seq:task_value:" + req.getItem() + ":" + req.getSeq());

        //actRedisDao.hIncrByKeyWithSeq(redisCode, itemSeq, taskItemKey, req.getItem(), req.getValue(), DUP_SECONDS);
        //TODO 如果次组件要重新启用，则需要改造此处
        if (true) {
            throw new RuntimeException("hIncrByKeyWithSeq,lua forbid");
        }

        //判断当前是否完成了任务，如已完成，则保存完任务数据
        boolean completeTask = isCompleteCurDayTask(redisCode, attr, req.getMember(), curTaskDayIndex);
        if (!completeTask) {
            log.info("not pass task,member:{},taskItemKey:{},itemSeq:{},curTaskDayIndex:{}", req.getMember(), taskItemKey, itemSeq, completeTask);
            return;
        }

        //保存数据
        int newTaskDayIndex = getNextDayIndex(attr.getDayTaskConfig(), curTaskDayIndex);
        saveTaskData(redisCode, attr, req, newTaskDayIndex);

        //发奖
        releaseAward(redisCode, attr, req, curTaskDayIndex);

        //发布完成任务事件
        completeTaskEventNotify(attr, req, curTaskDayIndex);

        //写最终完成标记。 注意：这一步必须放到最后，确保失败重试的时候前面步骤可以执行到
        saveCompleteDayTaskTag(redisCode, attr, req);

        log.info("updateTask done,req={}", JSON.toJSONString(req));
    }

    public CurDayTask queryCurDayTask(DayTaskComponentAttr attr, String member) {
        CurDayTask curDayTask = new CurDayTask();
        long actId = attr.getActId();
        String redisCode = getRedisGroupCode(actId);
        String nowDayCode = DateUtil.format(commonService.getNow(actId), DateUtil.PATTERN_TYPE2);

        int curDayIndex = queryCurTaskDayIndex(redisCode, attr, member);
        int dayShowIndex = curDayIndex;
        //如果是当天完成的任务，进度要停留在当天
        boolean isCompleteDayTask = isCompleteCurDayTask(redisCode, attr, member, curDayIndex);
        String completeDayCode = queryCompleteDayTaskTag(redisCode, attr, member);
        int maxIndex = getMaxDayIndex(attr);
        boolean completeLastTask = maxIndex == curDayIndex && isCompleteDayTask;
        if (StringUtil.isNotBlank(completeDayCode) && completeDayCode.equals(nowDayCode) && !completeLastTask) {
            dayShowIndex = curDayIndex - 1;
        }

        curDayTask.setDayShowIndex(dayShowIndex);
        curDayTask.setCompleteDayCode(completeDayCode);

        //已完成打卡天数
        int signed = isCompleteDayTask ? curDayIndex : curDayIndex - 1;
        curDayTask.setSignedDay(signed);

        int finalCurDayIndex = dayShowIndex;
        List<DayTaskConfig> configs = attr.getDayTaskConfig().stream().filter(p -> p.getTaskDayIndex() == finalCurDayIndex).toList();
        String taskItemKey = buildDayItemTaskKey(attr, member, finalCurDayIndex);
        Map<Object, Object> completeTaskItem = actRedisDao.hGetAll(redisCode, taskItemKey);
        List<CurDayTaskItem> itemResult = Lists.newArrayList();
        for (DayTaskConfig dayTaskConfig : configs) {
            CurDayTaskItem curDayTaskItem = new CurDayTaskItem();
            curDayTaskItem.setTaskName(dayTaskConfig.getTaskName());
            curDayTaskItem.setPassItem(dayTaskConfig.getPassItem());
            long value = Convert.toLong(completeTaskItem.getOrDefault(dayTaskConfig.getPassItem(), 0), 0);
            int state = value > 0 ? 1 : 0;
            curDayTaskItem.setState(state);

            itemResult.add(curDayTaskItem);
        }
        curDayTask.setCurTaskItem(itemResult);


        return curDayTask;
    }

    /**
     * 用户当前任务信息
     */
    public CurDayTask queryCurDayTask(long actId, long cmptIndex, String member) {

        var attr = getComponentAttr(actId, cmptIndex);
        return queryCurDayTask(attr, member);
    }

    public PairTemplateBean<Integer, AwardAttrConfig> getNearAwardConfig(long actId, long cmptIndex, int curDayTask) {
        var attr = getComponentAttr(actId, cmptIndex);
        int maxDayIndex = getMaxDayIndex(attr);
        for (int i = curDayTask; i <= maxDayIndex; i++) {
            if (attr.getDayAward().containsKey(i)) {
                return new PairTemplateBean<>(curDayTask, attr.getDayAward().get(i));
            }
        }
        return null;
    }


    /**
     * 用户当前正在过的任务配置
     */
    public List<DayTaskConfig> queryDayTaskConfig(long actId, long cmptIndex, String member) {
        String redisCode = getRedisGroupCode(actId);
        var attr = getComponentAttr(actId, cmptIndex);
        int curTaskDayIndex = queryCurTaskDayIndex(redisCode, attr, member);
        return attr.getDayTaskConfig().stream().filter(p -> p.getTaskDayIndex() == curTaskDayIndex).collect(Collectors.toList());
    }

    public long queryPoolBalance(DayTaskComponentAttr attr, long packageId) {
        String redisCode = getRedisGroupCode(attr.getActId());
        AwardLimitConfig config = attr.getAwardTotalLimit().get(packageId);
        if (config == null || config.getLimit() <= 0) {
            return -1;
        }
        String sendKey = buildAwardSendKey(attr, packageId);
        long alreadySend = Convert.toLong(actRedisDao.get(redisCode, sendKey), 0);
        long left = config.getLimit() - alreadySend;
        if (left <= config.getMinimumAmount()) {
            return 0;
        }
        return left;
    }

    public boolean checkAwardPoolLimit(DayTaskComponentAttr attr, int curTaskDayIndex) {
        AwardAttrConfig awardConfig = attr.getDayAward().get(curTaskDayIndex);
        if (awardConfig == null) {
            return true;
        }

        long balance = queryPoolBalance(attr, awardConfig.getTAwardPkgId());
        return balance == -1 || balance > awardConfig.getNum();
    }


    private void releaseAward(String redisCode, DayTaskComponentAttr attr, UpdateDayTaskReq req, int curTaskDayIndex) {
        AwardAttrConfig awardConfig = attr.getDayAward().get(curTaskDayIndex);
        if (awardConfig != null) {
            AwardLimitConfig limitConfig = attr.getAwardTotalLimit().get(awardConfig.getTAwardPkgId());
            //奖池扣除金额
            long reduceAmount = awardConfig.getAwardAmount();
            if (limitConfig != null && limitConfig.getLimit() > 0 && reduceAmount > 0) {
                String limitSeq = makeKey(attr, "seq:award_limit:" + req.getItem() + ":" + req.getSeq());
                List<Long> addResult = reduceAwardPool(attr, req.getMember(), awardConfig.getTAwardTskId(), awardConfig.getTAwardPkgId(), limitSeq, reduceAmount);
                if (addResult.get(0) <= 0) {
                    log.info("releaseAward pool out,req:{},curTaskDayIndex:{},awardAmount():{},limit:{},addResult:{}", JSON.toJSONString(req), curTaskDayIndex, reduceAmount, limitSeq, JSON.toJSONString(addResult));
                    return;
                }
            }

            String awardSeq = makeKey(attr, "award:" + req.getSeq());
            String awardSeqMd5 = MD5SHAUtil.getMD5(awardSeq);
            String time = DateUtil.format(commonService.getNow(attr.getActId()));
            long userUid = Convert.toLong(req.getMember());
            Map<Long, Integer> packageIdAmount = ImmutableMap.of(awardConfig.getTAwardPkgId(), awardConfig.getNum());
            //异步发奖
            hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.GAME_ECOLOGY.getValue(), userUid, awardConfig.getTAwardTskId(), ImmutableMap.of(awardConfig.getTAwardTskId(), packageIdAmount), awardSeqMd5, Maps.newHashMap());
            log.info("award done,actId:{},uid:{},awardSeq:{},awardSeqMd5:{},awardConfig:{}", attr.getActId(), userUid, awardSeq, awardSeqMd5, JSON.toJSONString(awardConfig));

        }
    }

    /**
     * 奖池扣减
     */
    public List<Long> reduceAwardPool(DayTaskComponentAttr attr,String member, long taskId, long packageId, String limitSeq, long amount) {
        log.info("reduceAwardPool,member:{},packageId:{},seq:{},amount:{}",member, packageId, limitSeq, amount);
        String sendKey = buildAwardSendKey(attr, packageId);
        AwardLimitConfig limitConfig = attr.getAwardTotalLimit().get(packageId);
        long limit = limitConfig == null ? 0 : limitConfig.getLimit();
        List<Long> addResult;
        //List<Long> addResult = actRedisDao.incrValueWithLimitSeq(getRedisGroupCode(attr.getActId()), limitSeq, sendKey, amount, limit, false, DUP_SECONDS);
        //TODO 如果次组件要重新启用，则需要改造此处
        if (true) {
            throw new RuntimeException("string_incr_with_limit_seq.lua,lua forbid");
        }
        long balance = queryPoolBalance(attr, packageId);
        if (addResult.get(0) <= 0 || balance <= 0) {
            //发布奖池消耗完成事件
            DayTaskPollOutEvent event = new DayTaskPollOutEvent();
            event.setActId(attr.getActId());
            event.setSeq(makeKey(attr, "poll_out:" + limitSeq));
            event.setTaskId(taskId);
            event.setPackageId(packageId);
            kafkaService.sendHdzkCommonEvent(event);
            log.info("reduceAwardPool out,member:{},packageId:{},seq:{},amount:{}",member, packageId, limitSeq, amount);
        }
        //日消耗
        if (addResult.get(0) > 0) {
            String dayCode = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
            String dayKey = buildAwardDaySendStaticKey(attr, dayCode, packageId);
            String daySeq = limitSeq + ":daystatic";
//            actRedisDao.incrValueWithSeq(getRedisGroupCode(attr.getActId()), daySeq, dayKey, amount, DUP_SECONDS);
            //TODO 如果次组件要重新启用，则需要改造此处
            if (true) {
                throw new RuntimeException("incrValueWithSeq.lua,lua forbid");
            }
        }

        log.info("reduceAwardPool done,member:{},packageId:{},seq:{},amount:{},addResult:{}",member, packageId, limitSeq, amount, JSON.toJSONString(addResult));
        return addResult;
    }

    public long getAwardDaySend(DayTaskComponentAttr attr, Date now,long packageId) {
        String dayCode = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        String dayKey = buildAwardDaySendStaticKey(attr, dayCode, packageId);
        String value = actRedisDao.get(getRedisGroupCode(attr.getActId()),dayKey);
        return Convert.toLong(value,0);

    }
    /**
     * 任务通知
     */
    private void completeTaskEventNotify(DayTaskComponentAttr attr, UpdateDayTaskReq req, int curTaskDayIndex) {
        List<Long> taskId = attr.getDayTaskConfig()
                .stream()
                .filter(p -> p.getTaskDayIndex() == curTaskDayIndex)
                .map(DayTaskConfig::getTaskId).toList();
        DayTaskCompleteEvent event = new DayTaskCompleteEvent();
        event.setActId(attr.getActId());
        event.setSeq(makeKey(attr, "complete:" + curTaskDayIndex + ":" + req.getMember()));
        event.setTaskId(taskId);
        event.setDayIndex(curTaskDayIndex);
        event.setMemberId(req.getMember());
        kafkaService.sendHdzkCommonEvent(event);
    }

    private boolean checkWhiteList(DayTaskComponentAttr attr, UpdateDayTaskReq req) {
        if (attr.getWhiteListIndex() > 0) {
            boolean inWhiteList = whitelistComponent.inWhitelist(req.getActId(), attr.getCmptUseInx(), req.getMember());
            if (!inWhiteList) {
                log.info("updateTask not in white list,seq:{},actId:{},memberId:{},item:{}", req.getSeq(), req.getActId(), req.getMember(), req.getItem());
                return false;
            }
        }

        return true;
    }

    private boolean alreadyCompleteDayTask(String redisCode, DayTaskComponentAttr attr, UpdateDayTaskReq req) {
        String lastDay = queryCompleteDayTaskTag(redisCode, attr, req.getMember());
        if (req.getDayCode().equals(lastDay)) {
            log.info("today complete task,seq:{},actId:{},memberId:{},item:{},dayCode:{}", req.getSeq(), req.getActId(), req.getMember(), req.getItem(), req.getDayCode());
            return true;
        }
        return false;
    }

    private String queryCompleteDayTaskTag(String redisCode, DayTaskComponentAttr attr, String member) {
        String lastDayKey = buildLastCompleteDayKey(attr, member);
        return actRedisDao.get(redisCode, lastDayKey);
    }

    private void saveCompleteDayTaskTag(String redisCode, DayTaskComponentAttr attr, UpdateDayTaskReq req) {
        String lastDayKey = buildLastCompleteDayKey(attr, req.getMember());
        actRedisDao.set(redisCode, lastDayKey, req.getDayCode());
        log.info("saveCompleteDayTaskTag redisCode:{},lastDayKey:{},dayCode:{}", redisCode, lastDayKey, req.getDayCode());
    }

    public int queryCurTaskDayIndex(String redisCode, DayTaskComponentAttr attr, String member) {
        //获取正在过的任务
        String curTaskDayKey = buildCurTaskDay(attr, member);
        return Convert.toInt(actRedisDao.get(redisCode, curTaskDayKey), Const.ONE);
    }

    private boolean hasTaskItem(DayTaskComponentAttr attr, UpdateDayTaskReq req, int curTaskDayIndex) {
        //设置当天子任务状态
        List<DayTaskConfig> curDayTaskConfig = attr.getDayTaskConfig()
                .stream()
                .filter(p -> p.getTaskDayIndex() == curTaskDayIndex)
                .toList();
        boolean noneItem = curDayTaskConfig.stream().noneMatch(p -> p.getPassItem().equals(req.getItem()));
        if (noneItem) {
            log.info("none item,actId:{},seq:{},dayIndex:{},item:{}", req.getActId(), req.getSeq(), curTaskDayIndex, req.getItem());
            return false;
        }
        return true;
    }

    private void saveTaskData(String redisCode, DayTaskComponentAttr attr, UpdateDayTaskReq req, int newDayIndex) {
        saveCurTaskDayIndex(redisCode, attr, req, newDayIndex);
    }

    private void saveCurTaskDayIndex(String redisCode, DayTaskComponentAttr attr, UpdateDayTaskReq req, int newDayIndex) {
        String curTaskDayKey = buildCurTaskDay(attr, req.getMember());
        actRedisDao.set(redisCode, curTaskDayKey, Convert.toString(newDayIndex));
    }

    private boolean isCompleteCurDayTask(String redisCode, DayTaskComponentAttr attr, String member, int dayIndex) {
        String taskItemKey = buildDayItemTaskKey(attr, member, dayIndex);
        List<DayTaskConfig> curDayTaskConfig = attr.getDayTaskConfig()
                .stream()
                .filter(p -> p.getTaskDayIndex() == dayIndex).toList();
        Map<Object, Object> completeTaskItem = actRedisDao.hGetAll(redisCode, taskItemKey);
        for (DayTaskConfig config : curDayTaskConfig) {
            long value = Convert.toLong(completeTaskItem.get(config.getPassItem()), 0L);
            if (value < config.getPassValue()) {
                return false;
            }
        }

        return true;
    }


    private int getNextDayIndex(List<DayTaskConfig> dayTaskConfigs, int curDayIndex) {
        int maxIndex = dayTaskConfigs.stream().mapToInt(DayTaskConfig::getTaskDayIndex).max().orElse(0);
        return Math.min(maxIndex, curDayIndex + 1);
    }

    private int getMaxDayIndex(DayTaskComponentAttr attr) {
        return attr.getDayTaskConfig().stream().mapToInt(DayTaskConfig::getTaskDayIndex).max().orElse(0);
    }

    private String buildCurTaskDay(DayTaskComponentAttr attr, String member) {
        return makeKey(attr, String.format(CUR_TASK_DAY_INDEX, member));
    }

    private String buildLastCompleteDayKey(DayTaskComponentAttr attr, String member) {
        return makeKey(attr, String.format(LAST_COMPLETE_DAY_CODE, member));
    }

    private String buildDayItemTaskKey(DayTaskComponentAttr attr, String member, int dayIndex) {
        return makeKey(attr, String.format(DAY_ITEM_TASK, member, dayIndex));
    }

    private String buildAwardSendKey(DayTaskComponentAttr attr, long packageId) {
        return makeKey(attr, String.format(AWARD_SEND, packageId));
    }

    private String buildAwardDaySendStaticKey(DayTaskComponentAttr attr, String dayCode, long packageId) {
        return makeKey(attr, String.format(AWARD_SEND_DAY_SEND_STATIC, dayCode, packageId));
    }
}
