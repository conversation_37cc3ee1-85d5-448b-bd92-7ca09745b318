package com.yy.gameecology.hdzj.bean.wzry;

import lombok.Data;

/**
 * desc:我的参赛历史记录
 *
 * <AUTHOR>
 * @date 2024-01-12 11:27
 **/
@Data
public class WzryMyGameRecordVo {
    /**
     * 赛事id
     */
    private Long gameId;

    /**
     * 列表左边图片地址
     */
    private String gameIcon;

    /**
     * 比赛名称
     */
    private String gameName;

    /**
     * 赛事类型 0=5V5 1=1V1 3=3V3
     */
    private int battleMode;

    /**
     * 开始比赛时间
     */
    private String startTime;

    /**
     * 比赛状态 1-比赛胜利 2-比赛失败 3-比赛异常
     */
    private int state;

    /**
     * 奖金
     */
    private long award;
}
