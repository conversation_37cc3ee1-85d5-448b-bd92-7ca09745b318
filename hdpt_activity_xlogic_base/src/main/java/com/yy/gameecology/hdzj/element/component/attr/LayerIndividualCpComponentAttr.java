package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-24 10:59
 **/
@Data
public class LayerIndividualCpComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "广播模板", remark = "3--交友 5--聊天室")
    private int broTemplate;

    @ComponentAttrField(labelText = "榜单Id")
    private long rankId;

    @ComponentAttrField(labelText = "阶段Id")
    private long phaseId;

    @ComponentAttrField(labelText = "时间分榜日期格式", remark = "如：yyyyMMdd")
    private String dateFormat;

    @ComponentAttrField(labelText = "每分钟定时广播更新的成员数量")
    private long minRefreshMemberCount = 1000;

    @ComponentAttrField(labelText = "每3秒定时广播更新的成员数量")
    private long highFrequencyRefreshMemberCount = 10;


    @ComponentAttrField(labelText = "挂件tab key")
    private String itemType = "cp_latest";
}
