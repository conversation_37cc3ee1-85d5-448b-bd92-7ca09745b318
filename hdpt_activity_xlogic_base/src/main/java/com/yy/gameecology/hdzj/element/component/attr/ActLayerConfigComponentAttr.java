package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.actlayer.ShowTopTitleConfig;
import com.yy.gameecology.hdzj.annotation.SkipCheck;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.LayerTimeViewStatusConfig;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2022-07-01 16:49
 **/
@SkipCheck
@Data
public class ActLayerConfigComponentAttr extends ComponentAttr {


    /**
     * 重构来源 act_layer_view_attr where attr_name = customer_convert_state_%s_%s
     */
    @ComponentAttrField(labelText = "榜单状态转换",remark = "榜单级别界面状态个性化映射,优先级，榜单>tab>全局",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "榜单id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "阶段id"),
                    @SubField(fieldName = Constant.KEY3, type = Integer.class, labelText = "来源状态"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "目标状态")
            })
    private Map<Long, Map<Long, Map<Integer, Integer>>> customerRankConvertState = Maps.newHashMap();

    /**
     * 重构来源 act_layer_view_attr where attr_name = customer_convert_state_%s
     */
    @ComponentAttrField(labelText = "tab状态转换",remark = "tab级别界面状态个性化映射",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "itemTypeKey"),
                    @SubField(fieldName = Constant.KEY2, type = Integer.class, labelText = "来源状态"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "目标状态")
            })
    private Map<String, Map<Integer, Integer>> customerTabConvertState = Maps.newHashMap();


    @ComponentAttrField(labelText = "tab时间状态转换",remark = "tab+时间级别界面状态个性化映射",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "itemTypeKey"),
                    @SubField(fieldName = Constant.KEY2, type = Integer.class, labelText = "来源状态"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = LayerTimeViewStatusConfig.class, labelText = "状态设置")
            })
    private Map<String, Map<Integer, List<LayerTimeViewStatusConfig>>> customerTabTimeConvertState = Maps.newHashMap();

    /**
     * 重构来源 act_layer_view_attr where attr_name = customer_item_type_convert_state_all
     */
    @ComponentAttrField(labelText = "全局状态转换",remark = "全局界面状态个性化映射",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "来源状态"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "目标状态")
            })
    private Map<Integer, Integer> customerOverallConvertState = Maps.newHashMap();

    @ComponentAttrField(labelText = "tab状态设置",remark = "优先级低于状态转换",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "itemTypeKey"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = LayerTimeViewStatusConfig.class, labelText = "状态设置")
            })
    private Map<String, List<LayerTimeViewStatusConfig>> overallTimeState = Maps.newHashMap();


    /**
     * 重构来源 act_layer_view_attr where attr_name = anchor_task_rank_id_
     */
    @ComponentAttrField(labelText = "主播任务榜单id配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "业务ID"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "榜单Id")
            })
    private Map<Long, Long> anchorTaskRankId = Maps.newHashMap();

    /**
     * 重构来源 act_layer_view_attr where attr_name = anchor_task_phase_id_
     */
    @ComponentAttrField(labelText = "主播任务阶段id配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "业务ID"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "阶段Id")
            })
    private Map<Long, Long> anchorTaskPhaseId = Maps.newHashMap();

    @ComponentAttrField(labelText = "主播任务榜单分榜配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "业务ID"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "分榜配置")
            })
    private Map<Long, Long> anchorTaskTimeKey = Maps.newHashMap();


    /**
     * 重构来源 act_layer_view_attr where attr_name = bro_debug_sid_
     */
    @ComponentAttrField(labelText = "log配置",remark = "输出频道广播日志频道Id",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)}
    )
    private Set<Long> broDebugSid = Sets.newHashSet();


    /**
     * 重构来源 act_layer_view_attr where attr_name = user_info_source_{roleType}
     */
    @ComponentAttrField(labelText = "用户模板",remark = "获取用户信息模板类型",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "roleType 角色类型"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "模板类型")
            })
    private Map<Integer, Integer> userInfoSourceMap = Maps.newHashMap();


    /**
     * 重构来源 表  act_layer_view_attr where attr_name =  show_pre_day_top_info
     */
    @ComponentAttrField(labelText = "日榜 top n", remark = "日榜次日展示前一天top N title 配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "榜单Id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "阶段Id"),
                    @SubField(fieldName = Constant.VALUE, type = ShowTopTitleConfig.class, labelText = "展示配置")
            })
    private Map<Long, Map<Long, ShowTopTitleConfig>> prePhaseTopNTitle = Maps.newHashMap();

    /**
     * 重构来源  ranking_config_attr where  "tb_ranking_config:race_pass_desc:" + phaseId
     */
    @ComponentAttrField(labelText = "自定义N进X展示",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "榜单Id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "阶段Id"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "N进X描述")
            })
    private Map<Long, Map<Long, String>> racePassDesc = Maps.newHashMap();

    /**
     * 重构来源  ranking_config_attr where  "tb_ranking_config:group_pass_desc_flag:" + phaseId
     */
    @ComponentAttrField(labelText = "展示分组的N进X",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "榜单Id"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = Long.class, labelText = "阶段Id")
            })
    private Map<Long, Set<Long>> useGroupPassDesc = Maps.newHashMap();


    /**
     * 重构来源  ranking_config_attr where "tb_ranking_config:last_phase_top_title:" + phaseId + ":" + rank
     */
    @ComponentAttrField(labelText = "最后一阶段展示top n 配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "榜单Id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "阶段Id"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "topN")
            })
    private Map<Long, Map<Long, Integer>> lastPhaseTopN = Maps.newHashMap();

    /**
     * 重构来源  ranking_config_attr where "tb_ranking_config:last_phase_top_title:" + phaseId + ":" + rank;
     */
    @ComponentAttrField(labelText = "名次top n title",remark = "按榜单+阶段+名次配置最后一阶段展示top n展示title,优先级大于lastPhaseTopTitle",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "榜单Id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "阶段Id"),
                    @SubField(fieldName = Constant.KEY3, type = Long.class, labelText = "名次"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "top n的title")
            })
    private Map<Long, Map<Long, Map<Long, String>>> lastPhaseRankTopTitle = Maps.newHashMap();


    /**
     * 重构来源  ranking_config_attr where "tb_ranking_config:last_phase_top_title:" + phaseId
     */
    @ComponentAttrField(labelText = "阶段top n title",remark = "按榜单+阶段设置最后一阶段展示top n展示title",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "榜单Id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "阶段Id"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "top n的title")
            })
    private Map<Long, Map<Long, String>> lastPhaseTopTitle = Maps.newHashMap();


    @ComponentAttrField(labelText = "静态配置",remark = "按照榜单阶段输出给前端的静态配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "榜单Id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "阶段Id"),
                    @SubField(fieldName = Constant.KEY3, type = String.class, labelText = "配置名"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "配置内容")
            })
    private Map<Long, Map<Long, Map<String, String>>> outPutTabStaticConfig = Maps.newHashMap();

    @ComponentAttrField(labelText = "扩展信息组件id",remark = "为加载扩展信息的组件id",
            subFields = {
                    @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
            })
    private Set<Long> extCmptIds = Sets.newHashSet();
}
