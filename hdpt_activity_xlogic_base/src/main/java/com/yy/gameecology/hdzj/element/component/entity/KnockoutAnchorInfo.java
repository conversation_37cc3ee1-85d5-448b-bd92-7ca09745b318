package com.yy.gameecology.hdzj.element.component.entity;

import com.yy.gameecology.common.db.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * 淘汰赛主播信息表
 * 用于存储所有参与淘汰赛的主播信息，替代Redis的ALL_ANCHOR集合
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@TableColumn(underline = true)
public class KnockoutAnchorInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5159_anchor_info_";

    public static String getTableName(long actId) {
        return TABLE_NAME + actId;
    }

    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<KnockoutAnchorInfo> ROW_MAPPER = (rs, rowNum) -> {
        KnockoutAnchorInfo result = new KnockoutAnchorInfo();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getLong("cmpt_use_inx"));
        result.setMemberId(rs.getString("member_id"));
        result.setPhaseId(rs.getLong("phase_id"));
        result.setStatus(rs.getInt("status"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        result.setUpdateTime(rs.getTimestamp("update_time"));
        return result;
    };

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 组件使用索引
     */
    private Long cmptUseInx;

    /**
     * 主播ID
     */
    private String memberId;

    /**
     * 阶段ID
     */
    private Long phaseId;

    /**
     * 状态：1-活跃，2-已淘汰，3-已晋级
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    // 状态常量
    public static final int STATUS_ACTIVE = 1;
    public static final int STATUS_ELIMINATED = 2;
    public static final int STATUS_PROMOTED = 3;
}
