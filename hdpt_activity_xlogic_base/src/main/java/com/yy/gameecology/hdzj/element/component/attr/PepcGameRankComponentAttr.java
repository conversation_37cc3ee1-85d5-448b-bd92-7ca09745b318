package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-10 15:36
 **/
@Data
public class PepcGameRankComponentAttr  extends ComponentAttr {

    @ComponentAttrField(labelText = "业务id")
    private int busiId;
    /**
     * 击杀榜单上报item
     */
    @ComponentAttrField(labelText = "击杀榜单上报item")
    private String killItemId;

    /**
     * 胜利傍上报item
     */
    @ComponentAttrField(labelText = "胜利傍上报item")
    private String winItemId;

    @ComponentAttrField(labelText = "角色id")
    private Long actorId;
}
