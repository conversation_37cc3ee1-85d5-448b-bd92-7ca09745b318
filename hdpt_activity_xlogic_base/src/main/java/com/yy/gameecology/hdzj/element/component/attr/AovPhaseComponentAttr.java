package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.Duration;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class AovPhaseComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "上一活动ID", remark = "上一个活动ID")
    private long prevActId;

    @ComponentAttrField(labelText = "开始时间", remark = "第一期比赛开始报名时间，若为空则使用活动开始时间")
    private Date beginTime;

    @ComponentAttrField(labelText = "挂起开始时间", remark = "开始挂起时间，为空表示不挂起")
    private Date suspendBeginTime;

    @ComponentAttrField(labelText = "挂起结束时间", remark = "结束挂起时间，挂起开始时间不为空，结束时间为空表示，从开始时间后无限挂起")
    private Date suspendEndTime;

    @ComponentAttrField(labelText = "报名时长", remark = "报名时长（距离报名开始）")
    private Duration signUpDuration;

    @ComponentAttrField(labelText = "调整时长", remark = "调整时间长度（距离报名结束）")
    private Duration adjustDuration = Duration.ZERO;

    @ComponentAttrField(labelText = "默认皮肤", remark = "默认皮肤设置")
    private String defaultSkin = "default";

    @ComponentAttrField(labelText = "皮肤配置", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = ActSkinConfig.class))
    private List<ActSkinConfig> actSkinConfigs = Lists.newArrayList();

    @ComponentAttrField(labelText = "轮次分组配置", subFields = {
            @SubField(fieldName = Constant.KEY1, labelText = "分组ID", remark = "轮次ID，唯一，不能配置0", type = Integer.class),
            @SubField(fieldName = Constant.VALUE, type = ActRoundGroupConfig.class)
    }, remark = "轮次分组配置")
    private Map<Integer, ActRoundGroupConfig> roundGroupConfigs;

    @ComponentAttrField(labelText = "轮次配置", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = ActRoundConfig.class))
    private List<ActRoundConfig> roundConfigs;

    @ComponentAttrField(labelText = "对局配置", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = ActMatchConfig.class), remark = "特殊配置那些同一轮次不同配置的对局")
    private List<ActMatchConfig> matchConfigs;

    @ComponentAttrField(labelText = "奖励配置", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = PhaseAwardConfig.class))
    private List<PhaseAwardConfig> phaseAwardConfigs;

    @ComponentAttrField(labelText = "奖励图片")
    private String awardInfo;

    @Data
    public static class ActSkinConfig {
        @ComponentAttrField(labelText = "开始时段", remark = "时间戳，毫秒")
        private long startTime;

        @ComponentAttrField(labelText = "结束时段", remark = "时间戳，毫秒")
        private long endTime;

        @ComponentAttrField(labelText = "皮肤编码")
        private String skinCode;

        @ComponentAttrField(labelText = "备注")
        private String remark;
    }

    @Getter
    @Setter
    public static class ActRoundGroupConfig {

        @ComponentAttrField(labelText = "组名称")
        protected String groupName;

        @ComponentAttrField(labelText = "开始时段", remark = "HH:mm:ss")
        protected LocalTime startTime;

        @ComponentAttrField(labelText = "结束时段", remark = "HH:mm:ss")
        protected LocalTime endTime;
    }

    @Getter
    @Setter
    public static class ActRoundConfig {

        @ComponentAttrField(labelText = "所属分组", remark = "轮次所属的分组")
        protected int groupId;

        @ComponentAttrField(labelText = "轮次", remark = "逻辑round，2进1->2，4进2->3，8进4->4……")
        protected int roundNum;

        @ComponentAttrField(labelText = "所属日期", remark = "所属日期是离报名最后一天间隔天数。即报名最后一天 + x天的日期。结合后续的时间配置即可得到具体的时间")
        protected int signupEndDayOffset;

        @ComponentAttrField(labelText = "开始时段", remark = "HH:mm:ss")
        protected LocalTime startTime;

        @ComponentAttrField(labelText = "结束时段", remark = "HH:mm:ss")
        protected LocalTime endTime;

        @ComponentAttrField(labelText = "BO", remark = "BO数")
        protected int bo;

        @ComponentAttrField(labelText = "模式", remark = "比赛模式：0-5V5,1-1V1,3-3V3")
        protected int battleMode;

        @ComponentAttrField(labelText = "轮次名称", remark = "8进4、半决赛")
        protected String name;

        @ComponentAttrField(labelText = "自动建比赛", remark = "晋级成功后是否自动创建本轮次的比赛，默认：是")
        protected Boolean createGame = true;
    }

    @Getter
    @Setter
    public static class ActMatchConfig {
        @ComponentAttrField(labelText = "轮次", remark = "逻辑round，2进1->2，4进2->3，8进4->4……")
        protected int roundNum;

        @ComponentAttrField(labelText = "节点一", remark = "节点一索引号，必须为偶数")
        protected int nodeIndex1;

        @ComponentAttrField(labelText = "节点二", remark = "节点二索引号，必须为节点一 + 1")
        protected int nodeIndex2;

        @ComponentAttrField(labelText = "开始时段", remark = "HH:mm:ss")
        protected LocalTime startTime;

        @ComponentAttrField(labelText = "BO", remark = "BO数")
        protected int bo;

        @ComponentAttrField(labelText = "模式", remark = "比赛模式：0-5V5,1-1V1,3-3V3")
        protected int battleMode;

        @ComponentAttrField(labelText = "轮次名称", remark = "8进4、半决赛")
        protected String name;
    }

    @Getter
    @Setter
    public static class PhaseAwardConfig {
        @ComponentAttrField(labelText = "结算的轮次", remark = "在本轮次被淘汰可获得奖励, 特殊：0-首胜奖励")
        protected int roundNum;

        @ComponentAttrField(labelText = "平分人数", remark = "队伍平分人数")
        protected int memberSize;

        @ComponentAttrField(labelText = "奖励金额", remark = "单位（点券）")
        protected long awardAmount;

        @ComponentAttrField(labelText = "奖池ID")
        protected long tAwardTskId;

        @ComponentAttrField(labelText = "奖包ID")
        protected long tAwardPkgId;

        @ComponentAttrField(labelText = "奖励说明")
        protected String awardDesc;

        @ComponentAttrField(labelText = "额外奖励金额", remark = "单位（点券）")
        protected long extraAwardAmount;

        @ComponentAttrField(labelText = "额外奖励奖池ID")
        protected long tAwardTskExtraId;

        @ComponentAttrField(labelText = "额外奖励奖包ID")
        protected long tAwardPkgExtraId;

        @ComponentAttrField(labelText = "额外奖励说明")
        protected String extraAwardDesc;

    }
}
