package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import lombok.Data;

import java.util.Collections;
import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption: 过任务提醒组件属性
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
@Data
public class AnchorTaskTipComponentAttr extends ComponentAttr {

    /**
     * 任务的榜单id
     */
    @ComponentAttrField(labelText = "任务的榜单id")
    private Long rankId;
    /**
     * 任务的阶段id
     */
    @ComponentAttrField(labelText = "任务的阶段id")
    private Long phaseId;

    /**
     * 用于多任务区分
     */
    @ComponentAttrField(labelText = "任务类型", remark = "用于多任务区分,默认值为1")
    private Integer taskType = 1;

    @ComponentAttrField(labelText = "任务名称", remark = "按照任务等级排序,用逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class)})
    private String[] taskNames;

    @ComponentAttrField(labelText = "任务奖励文案", remark = "按照任务等级排序,用逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class)})
    private String[] taskAwards;

    @ComponentAttrField(labelText = "任务奖励ICON", remark = "按照任务等级排序,用逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class)})
    private String[] taskAwardIcons;

    @ComponentAttrField(labelText = "任务奖励数量", remark = "按照任务等级排序,用逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Integer.class)})
    private Integer[] taskAwardCounts;

    /**
     * 任务名称展示模式，1=展示最高级，2=合并展示，需要连接符
     */
    @ComponentAttrField(labelText = "任务名称展示模式", dropDownSourceBeanClass = TaskNameShowTypeSource.class)
    private int taskNameShowType = 1;
    /**
     * 任务名称合并展示链接符
     */
    @ComponentAttrField(labelText = "任务名称展示链接符")
    private String taskNameConnector = ",";
    /**
     * 任务奖励展示模式，0 = 不展示，1=展示最高级，2=合并展示，需要连接符
     */
    @ComponentAttrField(labelText = "任务奖励展示模式", dropDownSourceBeanClass = TaskAwardShowTypeSource.class)
    private int taskAwardShowType = 1;
    /**
     * 任务奖励合并展示链接符
     */
    @ComponentAttrField(labelText = "任务奖励展示链接符")
    private String taskAwardConnector = "+";

    /**
     * 是否单播
     */
    @ComponentAttrField(labelText = "是否单播")
    private boolean isUnicast = true;

    /**
     * 一次完成多个任务的时候，底部广播间隔时间
     */
    @ComponentAttrField(labelText = "广播间隔时间", remark = "一次完成多个任务的时候，底部广播间隔时间")
    private int completeBroWaitMills = 600;

    /**
     * 任务等级需要广播的类型 广播类型 2=子频道，3=顶级频道，4=单业务广播；5=多业务广播
     */
    @ComponentAttrField(labelText = "广播的类型",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "任务等级"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "广播的类型", remark = "2=子频道，3=顶级频道，4=单业务广播；5=多业务广播")
            })
    private Map<Integer, Integer> levelBroTypeMap = Collections.emptyMap();


    @ComponentAttrField(labelText = "任务完成抽奖",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "任务等级"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "抽奖奖池ID")
            })
    private Map<Integer, Long> levelLotteryMap = Collections.emptyMap();
}
