package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableSortedMap;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardLotteryMsg;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.BroadCastHelpService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.SvcSDKService;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.MyListUtils;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.redis.MedalTaskComponent;
import com.yy.gameecology.hdzj.element.history.attr.MedalTaskV1ComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 已废弃,功能已被 5008 组件替代
 * 勋章组件
 *
 * <AUTHOR>
 * @date 2021/15 14:25
 * @see MedalTaskComponent
 */
@Component
@Deprecated
public class MedalTaskV1Component extends BaseActComponent<MedalTaskV1ComponentAttr> {


    @Autowired
    private SvcSDKService svcSDKService;
    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Autowired
    private BroadCastHelpService broadCastHelpService;
    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;
    @Autowired
    private CommonService commonService;

    /**
     * 用户勋章等级
     */
    public static final String USER_GIFT_MEDAL = "%s_user_gift_medal";
    /**
     * 素材小图尺寸，用于更新公屏勋章 长x高
     **/
    public static final String MINI_MATERIAL_SIZE = "68x16";
    /**
     * 素材大图尺寸 用于过勋章任务提示 长x高
     **/
    public static final String MATERIAL_SIZE = "120x48";


    @Override
    public Long getComponentId() {
        return ComponentId.MEDAL_TASK;
    }

    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void onTaskProgressChanged(TaskProgressChanged event, MedalTaskV1ComponentAttr attr) {
        if (!ArrayUtils.contains(attr.getBusiIds(), event.getBusiId()) || event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }
        BusiId busiId = BusiId.findByValue((int) event.getBusiId());
        long uid = Convert.toLong(event.getMember());
        long score = 0;
        for (String giftItem : attr.getGiftItems()) {
            score += Convert.toLong(event.getItemCurrNumMap().get(giftItem));
        }
        //通过分数获取等级
        Map.Entry<Long, Integer> entry = ImmutableSortedMap.copyOf(reverseMap(attr.getLevelScoreMap())).floorEntry(score);

        Assert.notNull(entry, String.format("config error,not cat level by score,actId:{},cmptId:{},cmptUseInx:{},score:{}"
                , attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), score));

        int curLevel = entry.getValue();

        //查询下一级勋章，null说明本身是最高级了
        Long nextLevelScore = attr.getLevelScoreMap().get(curLevel + 1);
        long diff = nextLevelScore != null ? nextLevelScore - score : 0L;

        tipMedalUpdate(attr, uid, curLevel, diff, busiId);
        log.info("onTaskProgressChanged done -> event:{}, attr:{}", event, attr);
    }

    @HdzjEventHandler(value = HdztAwardLotteryMsg.class, canRetry = false)
    public void onHdztAwardLotteryMsg(HdztAwardLotteryMsg event, MedalTaskV1ComponentAttr attr) {
        long uid = event.getUid();
        Map<String, Integer> taskPackageLevelMap = attr.getTaskPackageLevelMap();
        if (CollectionUtils.isEmpty(taskPackageLevelMap) || !ArrayUtils.contains(attr.getBusiIds(), event.getBusiId())) {
            return;
        }
        BusiId busiId = BusiId.findByValue((int) event.getBusiId());
        List<Integer> levels = event.getData().stream()
                .map(award -> taskPackageLevelMap.get(event.getTaskId() + "_" + award.getPackageId()))
                .filter(Objects::nonNull)
                .distinct().sorted().collect(Collectors.toList());
        if (levels.isEmpty()) {
            return;
        }

        int curLevel = MyListUtils.last(levels);

        Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(attr.getActId(), attr.getRankId(), attr.getPhaseId(), "", uid + "", Maps.newHashMap());

        long score = Math.max(rank.getScore(), 0L);

        //查询下一级勋章，null说明本身是最高级了
        Map.Entry<Integer, Long> entry = ImmutableSortedMap.copyOf(attr.getLevelScoreMap()).higherEntry(curLevel);
        //这次没有对0分数的进行过滤
        long diff = entry != null ? entry.getValue() - score : 0L;
        tipMedalUpdate(attr, uid, curLevel, diff, busiId);
        log.info("onhdztAwardLotteryMsg done -> event:{}, attr:{}", JSON.toJSONString(event), attr);
    }


    /**
     * 更新用户勋章等级
     *
     * @param attr
     * @param uid
     * @param level
     * @return
     */
    public long updateUserMedalLevelAndReturnOld(MedalTaskV1ComponentAttr attr, long uid, int level) {
        BusiId busiId = attr.getBusiIds().length == 1 ? BusiId.findByValue((attr.getBusiIds()[0]).intValue()) : BusiId.GAME_ECOLOGY;
        String key = makeKey(attr, String.format(USER_GIFT_MEDAL, busiId));
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        //return actRedisDao.hSetGrownReturnOld(groupCode, key, String.valueOf(uid), level);
        if(true){
            throw new RuntimeException("已废弃,功能已被 5008 组件替代");
        }
        return -1;
    }

    /**
     * 查询用户勋章等级
     *
     * @param attr
     * @param uid
     * @return
     */
    public int queryUserMedalLevel(MedalTaskV1ComponentAttr attr, long uid) {
        BusiId busiId = attr.getBusiIds().length == 1 ? BusiId.findByValue((attr.getBusiIds()[0]).intValue()) : BusiId.GAME_ECOLOGY;
        String key = makeKey(attr, String.format(USER_GIFT_MEDAL, busiId));
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String value = actRedisDao.hget(groupCode, key, String.valueOf(uid));
        return StringUtils.isNumeric(value) ? Integer.parseInt(value) : 0;
    }

    /**
     * 勋章升级单播和广播
     *
     * @param attr
     * @param uid
     * @param curLevel
     * @param diff
     */
    private void tipMedalUpdate(MedalTaskV1ComponentAttr attr, long uid, int curLevel, long diff, BusiId busiId) {

        Long actId = attr.getActId();
        //上次的等级（勋章可能有多个发放途径）
        long lastLevel = updateUserMedalLevelAndReturnOld(attr, uid, curLevel);
        long plus = curLevel - lastLevel;
        //被别的发放渠道广播了
        if (plus <= 0) {
            log.info("tipMedalUpdate ignore@uid:{},curLevel:{},diff:{}", uid, curLevel, diff);
            return;
        }

        //大图
        GameecologyActivity.MedalInfo medalInfo = getMedalInfo(attr, curLevel, MATERIAL_SIZE);
        if (medalInfo == null) {
            log.error("tipMedalUpdate error! medal material not find actId:{} material size:{}",
                    actId, MATERIAL_SIZE);
            return;
        }

        //向用户单播升级信息
        GameecologyActivity.Act202008_MedalLevelUpTips medalTips = GameecologyActivity.Act202008_MedalLevelUpTips.newBuilder()
                .setActId(actId).setPlus((int) plus).setDiff((int) diff).setMedal(medalInfo).build();

        GameecologyActivity.GameEcologyMsg medalTipsMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kAct202008_MedalLevelUpTips_VALUE)
                .setAct202008MedalLevelUpTips(medalTips).build();
        log.info("tipMedalUpdate medal levelUp actId:{} uid:{} level:{} plus:{} diff:{}",
                actId, uid, curLevel, plus, diff);
        svcSDKService.unicastUid(uid, medalTipsMsg);


        //广播用户勋章升级横幅
        Integer broType = attr.getLevelBroTypeMap().get(curLevel);

        if (broType != null) {
            GameecologyActivity.Act202008_UserHonorBanner.Builder userHonorBanner = GameecologyActivity.Act202008_UserHonorBanner.newBuilder()
                    .setActId(actId).setUser(broadCastHelpService.getUserInfo(uid, busiId)).setLevel(curLevel).setName(medalInfo.getName());
            GameecologyActivity.GameEcologyMsg userHonorBannerMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.kAct202008_UserHonorBanner_VALUE)
                    .setAct202008UserHonorBanner(userHonorBanner).build();

            broadCastHelpService.broadcast(actId, busiId, broType, 0L, 0L, userHonorBannerMsg);
        }
    }

    /**
     * 在配置中获取素材
     *
     * @param attr
     * @param materialSize
     * @return
     */
    private List<GameecologyActivity.MedalInfo> getMedalInfos(MedalTaskV1ComponentAttr attr, String materialSize) {
        return attr.getMedalMaterialMap().keySet().stream().distinct()
                .map(level -> getMedalInfo(attr, level, materialSize)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 在配置中获取素材
     *
     * @param attr
     * @param level
     * @param materialSize
     * @return
     */
    private GameecologyActivity.MedalInfo getMedalInfo(MedalTaskV1ComponentAttr attr, int level, String materialSize) {
        MedalTaskV1ComponentAttr.MedalMaterial medalMaterial = attr.getMedalMaterialMap().get(level);
        String url = getIconUrl(medalMaterial, materialSize);
        if (medalMaterial == null || StringUtils.isEmpty(url)) {
            return null;
        }
        String[] size = materialSize.split("x");
        GameecologyActivity.MedalInfo medalInfo = GameecologyActivity.MedalInfo.newBuilder()
                //id是和前端协商好的
                .setId(120).setName(medalMaterial.getName()).setLevel(level).setUrl(url)
                .setWidth(Integer.parseInt(size[0])).setHeight(Integer.parseInt(size[1])).build();
        return medalInfo;
    }

    private String getIconUrl(MedalTaskV1ComponentAttr.MedalMaterial medalMaterial, String materialSize) {
        if (MINI_MATERIAL_SIZE.equals(materialSize)) {
            return medalMaterial.getSmallIcon();
        }
        if (MATERIAL_SIZE.equals(materialSize)) {
            return medalMaterial.getLargeIcon();
        }

        return "";
    }


    public JSONObject getMedalInfo(MedalTaskV1ComponentAttr attr, long uid, String materialSize) {

        Long actId = attr.getActId();

        JSONObject result = new JSONObject();
        //灰度白名单控制
        if (!commonService.checkWhiteList(actId, RoleType.USER, String.valueOf(uid))) {
            return result;
        }

        int curLevel = queryUserMedalLevel(attr, uid);
        Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(attr.getActId(), attr.getRankId(), attr.getPhaseId(), "", uid + "", Maps.newHashMap());
        long score = Math.max(rank.getScore(), 0L);

        //查询下一级勋章，null说明本身是最高级了
        Map.Entry<Integer, Long> entry = ImmutableSortedMap.copyOf(attr.getLevelScoreMap()).higherEntry(curLevel);
        //这次没有对0分数的进行过滤
        long lastScore = entry != null ? entry.getValue() : attr.getLevelScoreMap().getOrDefault(curLevel, 0L);

        result.put("score", score);
        result.put("level", curLevel);
        result.put("lastScore", lastScore);

        if (MINI_MATERIAL_SIZE.equals(materialSize) || MATERIAL_SIZE.equals(materialSize)) {
            List<GameecologyActivity.MedalInfo> medalInfos = getMedalInfos(attr, materialSize);
            if (medalInfos.size() != attr.getMedalMaterialMap().keySet().size()) {
                log.error("getMedalInfo error! medal config not exist,not find medal info actId:{} ", actId);
                return result;
            }
            result.put("medalInfos", medalInfos);
            result.put("medal", getMedalInfo(attr, curLevel, materialSize));
        }

        return result;
    }

    public JSONObject getMedalInfo(long actId, long busiId, long uid, String materialSize) {

        List<MedalTaskV1ComponentAttr> attrs = getAllComponentAttrs(actId);
        List<MedalTaskV1ComponentAttr> busiIdAttrs = attrs.stream().filter(attr -> ArrayUtils.contains(attr.getBusiIds(), busiId)).collect(Collectors.toList());
        if (busiIdAttrs.size() != 1) {
            log.error("getMedalInfo error,not find config ,actId:{},busiId:{}", actId, busiId);
            return new JSONObject();
        }
        return getMedalInfo(busiIdAttrs.get(0), uid, materialSize);
    }

    /**
     * 逆转map的 key-value
     *
     * @param map
     * @param <K>
     * @param <V>
     * @return
     */
    private <K, V> Map<V, K> reverseMap(Map<K, V> map) {
        return map.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));
    }

}
