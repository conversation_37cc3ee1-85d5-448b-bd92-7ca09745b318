package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-08-29 17:00
 **/
@Data
public class TaskContributeLotteryComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "榜单id", remark = "监听榜单变化事件，用于中控组件过任务")
    private long rankId;


    @ComponentAttrField(labelText = "阶段id", remark = "监听榜单变化事件，用于中控组件过任务")
    private long phaseId;

    @ComponentAttrField(labelText = "取用户信息业务id")
    private long userInfoBusiId;

    @ComponentAttrField(labelText = "任务分榜key",remark = "为空，任务不按时间分榜，yyyyMMdd，按日分任务，次日清零")
    private String taskTimeFormat;


    @ComponentAttrField(labelText = "去重key过期时间(秒)", remark = "0==key不过期  ; >0设置过期时间为相应秒 ;  <0不做key去重检查")
    private long repeatedCheckExpire;


    @ComponentAttrField(labelText = "任务贡献成员类型",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class, remark = "role type用string 兼容lua反序列化")
            },remark = "抽奖受益者，多个用逗号隔开，用于从榜单变化事件中actor提取对象")
    private Set<String> contributeRoleType = Sets.newHashSet();

    /**
     *
     */
    @ComponentAttrField(labelText = "任务分值配置",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class, remark = "过任务配置,累加型配置，非区间端")
            })
    private List<Long> taskConfig = Lists.newArrayList();


    @ComponentAttrField(labelText = "任务可循环次数", remark = "0：不重置，大于0：重置的次数， 小于0：不限次数无限循环")
    private long recycleCount = 0;

    @ComponentAttrField(labelText = "贡献前N可参与抽奖", remark = "参考TOP N贡献值，TOP N内有多少个用户超过lotteryScore，就在这几个用户里面抽1个，若没有用户高于lotteryScore，则随机抽")
    private long lotteryTopNContribute;

    @ComponentAttrField(labelText = "优先抽奖分数", remark = "达到这个分数，可优先参与抽奖")
    private long lotteryFirstScore;

    @ComponentAttrField(labelText = "抽奖任务Id")
    private long lotteryTaskId;

    @ComponentAttrField(labelText = "大奖广播奖包Id",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class, remark = "")
            })
    private Set<Long> bigAwardPackageIds;

    @ComponentAttrField(labelText = "全模板广播模板id", remark = "2==宝贝  3==交友 5==语音房")
    private int broTemplate;

    @ComponentAttrField(labelText = "中奖广播横幅id",remark = "与客户端协商")
    private long awardBroBannerId;

    @ComponentAttrField(labelText = "中奖广播横幅类型",remark = "与客户端协商")
    private long awardBroBannerType;

    @ComponentAttrField(labelText = "挂件itemType",remark = "挂件中展示玩法内容的itemType")
    private String layerExtInfoItemType;

    @ComponentAttrField(labelText = "中奖信息挂件itemType",remark = "中奖信息展示在挂件中的哪个itemType")
    private String noticeLayerItemType;

    @ComponentAttrField(labelText = "中奖信息广播roleId",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
            }, remark = "从榜单变化事件中提取厅角色出来广播，依赖stream事件上报的厅id,多个用逗号隔开")
    private Set<Long> noticeLayerFilterRoleId = Sets.newHashSet();

    @ComponentAttrField(labelText = "累榜上报Item", remark = "过任务累中台榜礼物item，如果不配置，则不会上报")
    private String updateHdztRankItem;

}
