package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.List;

/**
 * 能量站属性类
 * <AUTHOR>
 * @date 2022/07/11
 */
@Data
public class EnergyStationComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "主播首次开播tips")
    private String anchorFirstTip = "!";

    @ComponentAttrField(labelText = "任务分和奖励换算率", remark = "浮点数，任务分:奖励 的值，比如 2.5 就是指 1任务分 = 2.5个奖励")
    private double scoreAwardRatio = 0;

    @ComponentAttrField(labelText = "主持能量站任务详情", subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = TaskLevelInfo.class)})
    private List<TaskLevelInfo> taskLevelInfos = Lists.newArrayList();

    @ComponentAttrField(labelText = "能量站榜单ID", remark = "中台配置的能量站采榜ID，中台订阅该榜单的 RankingScoreChanged 事件驱动能量站任务进度")
    private long rankId;

    @ComponentAttrField(labelText = "能量站贡献榜单ID", remark = "中台配置的能量站贡献榜单ID，用于能量站CP广播时提取贡献最高的用户")
    private long cpRankId;

    @ComponentAttrField(labelText = "阶段ID", remark = "能量站榜单ID（rankId） / 能量站贡献榜单ID（cpRankId） 共用的阶段")
    private long phaseId;

    @ComponentAttrField(labelText = "成员类型", remark = "自定义的一个编码，前端需要")
    private String memberType = "";

    @ComponentAttrField(labelText = "实例分组", remark = "实例分组值相同 的 实例的【奖励限额、奖池ID、奖包ID】务必相同，否则不能正常工作！！！")
    private int useInxGroup = 1;

    @ComponentAttrField(labelText = "奖励限额", remark = "有相同 【实例分组】 值的实例的 【奖励限额】 必须都相同，否则不能正常工作！！！")
    private long awardLimit = 0;

    @ComponentAttrField(labelText = "奖池ID", remark = "有相同 【实例分组】 值的实例的 【奖池ID】 必须都相同，否则不能正常工作！！！")
    private long awardTaskId = 0;

    @ComponentAttrField(labelText = "奖包ID", remark = "有相同 【实例分组】 值的实例的 【奖包ID】 必须都相同，否则不能正常工作！！！")
    private long awardPackageId = 0;

    @ComponentAttrField(labelText = "奖励名称", remark = "前端展示需要，若为空，则应自动取奖包的名称")
    private String awardName = "";

    @Data
    public static class TaskLevelInfo {
        @ComponentAttrField(labelText = "任务级别名称")
        public String name;

        @ComponentAttrField(labelText = "任务级别总分值", remark = "注意是总分值，不是阶梯值")
        public long score;

        @ComponentAttrField(labelText = "任务级别总奖励", remark = "注意是总奖励值，不是阶梯奖励值")
        public long award;

        @ComponentAttrField(labelText = "任务分转奖励比例", remark = "浮点数，0表示不转换，比如 0.0223 = 2.23% 转换比例，注意 rate是用来和总分值相乘得到总奖励的")
        public double rate;
    }

    /**
     * 给定分值是否达到最高任务级别
     */
    public boolean isTopScoreLevel(long score) {
        return score >= getTotalScore();
    }

    /**
     * 获取总任务值
     */
    public long getTotalScore() {
        int size = taskLevelInfos.size();
        return size==0 ? 0 : taskLevelInfos.get(size-1).getScore();
    }

    /**
     * 给定分值是否达到最高任务级别
     */
    public boolean isTopAwardLevel(long award) {
        return award >= getTotalAward();
    }

    /**
     * 获取总奖励
     */
    public long getTotalAward() {
        int size = taskLevelInfos.size();
        return size==0 ? 0 : taskLevelInfos.get(size-1).getAward();
    }

    /**
     * 计算分值所在任务级别, 级别从 1 开始计数, 当返回0时，表示还没有过任何级别
     */
    public int calcScoreLevel(long score) {
        int size = taskLevelInfos.size();
        for(int i=size-1; i>=0; i--) {
            TaskLevelInfo taskLevelInfo = taskLevelInfos.get(i);
            if(score >=  taskLevelInfo.getScore()) {
                return i + 1;
            }
        }
        return 0;
    }

    /**
     * 计算奖励所在级别, 级别从 1 开始计数, 当返回0时，表示还没有过任何级别
     */
    public int calcAwardLevel(long award) {
        int size = taskLevelInfos.size();
        for(int i=size-1; i>=0; i--) {
            TaskLevelInfo taskLevelInfo = taskLevelInfos.get(i);
            if(award >= taskLevelInfo.getAward()) {
                return i + 1;
            }
        }
        return 0;
    }

    /**
     * 通过任务分值计算任务奖励
     */
    public long calcScoreLevelAward(long score) {
        int scoreLevel = this.calcScoreLevel(score);
        if(scoreLevel == 0) {
            return 0;
        }

        TaskLevelInfo taskLevelInfo = taskLevelInfos.get(scoreLevel - 1);
        double rate = taskLevelInfo.getRate();
        if(isZero(rate)) {
            return taskLevelInfo.getAward();
        } else if(score == taskLevelInfo.getScore()) {
            return taskLevelInfo.getAward();
        } else {
            return (long) (score * scoreAwardRatio * rate);
        }
    }

    /**
     * 计算到下一级 还差的分值
     */
    public long getNextLevelLeftScore(long currScore) {
        int level = calcScoreLevel(currScore);
        int size = taskLevelInfos.size();
        return level>=size ? 0 : taskLevelInfos.get(level).getScore() - currScore;
    }

    /**
     * 判浮点数为0，这里用一个极小浮点数做判零比较
     */
    public boolean isZero(double d) {
        return d <  0.00000000001;
    }

}
