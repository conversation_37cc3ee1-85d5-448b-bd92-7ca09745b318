package com.yy.gameecology.hdzj.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * 战队信息
 *
 * <AUTHOR>
 * @since 2022/9/5 14:33
 **/
@Data
public class TeamDTO {
    /**
     * 公会id
     **/
    @ComponentAttrField(labelText = "公会id")
    private Long sid;
    /**
     * 战队id
     **/
    @ComponentAttrField(labelText = "战队id")
    private Long teamId;
    /**
     * 战队名称
     **/
    @ComponentAttrField(labelText = "战队名称")
    private String teamName;
    /**
     * 战队成员
     **/
    @ComponentAttrField(labelText = "战队成员", remark = "逗号分隔")
    private String memberIdList;
}
