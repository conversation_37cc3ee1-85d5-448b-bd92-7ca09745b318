package com.yy.gameecology.hdzj.element.redis;

import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.HdzjHelper;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.RankingScoreAwardComponentQuaAttr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * desc:根据榜单分值奖励领奖资格组件
 *
 * @createBy 曾文帜
 * @create 2021-08-11 21:17
 **/
@UseRedisStore
@Component
public class RankingScoreAwardQuaComponent extends BaseActComponent<RankingScoreAwardComponentQuaAttr> {

    @Autowired
    private ReceiveAwardComponent receiveAwardComponent;


    @Override
    public Long getComponentId() {
        return ComponentId.RANKING_SCORE_AWARD_QUA;
    }

    /**
     * 响应榜单变化事件，按所达分值区间发放奖励， 本函数做了防重检查，每个分值区间只能执行一次
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void onRankingScoreChanged(RankingScoreChanged event, RankingScoreAwardComponentQuaAttr attr) {
        // 先检查是否要处理的
        long rankId = event.getRankId();
        if (!attr.isMyDuty(rankId)) {
            return;
        }

        // 查找当前分值对应的等级分值
        log.info("onRankingScoreChanged done@event:{}, attr:{}", event, attr);
        long newLevelScore = attr.findAwardLevelScore(event.getRankScore());
        if (newLevelScore <= 0) {
            return;
        }

        // 提取奖励接收者
        int receiverInx = attr.getReceiverInx();
        long receiver = Long.parseLong(event.getMember().split("\\|")[receiverInx]);

        // 若设置操作返回的老值大于或等于新值，则设置失败，直接返回
        String subName = HdzjHelper.getRankingScoreChangedSubKey(event, false);
        String hashKey = makeKey(attr, subName + ":CurrLevelScore");
        String groupCode = redisConfigManager.getGroupCode(event.getActId());
        //long oldLevelScore = actRedisDao.hSetGrownReturnOld(groupCode, hashKey, String.valueOf(receiver), newLevelScore);
        long oldLevelScore = 0;
        //TODO 如果次组件要重新启用，则需要改造此处
        if (true) {
            throw new RuntimeException("hset_grown_return_old.lua,lua forbid");
        }
        if (oldLevelScore >= newLevelScore) {
            return;
        }

        // 对在 (oldLevelScore, newLevelScore] 这个区间的所有阶梯奖励进行发放操作
        Date now = commonService.getNow(attr.getActId());
        String time = DateUtil.format(now);
        Map<Long, Map<String, Integer>> awardConfigMap = attr.getSubScoreAwardConfig(oldLevelScore, newLevelScore);
        for (Long levelScore : awardConfigMap.keySet()) {
            String checkKey = makeKey(attr, subName + ":" + levelScore);
            String checkField = receiver + ":" + receiverInx;
            String checkValue = event.getTimestamp() + ", " + event.getSeq();

            if (actRedisDao.hsetnx(groupCode, checkKey, checkField, checkValue)) {
                Map<String, Integer> awardCodeMap = awardConfigMap.get(levelScore);
                long uid = Convert.toLong(receiver, 0);
                long index = attr.getReceiveAwardComponentIndex();
                for (String awardCode : awardCodeMap.keySet()) {
                    int amount = awardCodeMap.get(awardCode);
                    //只能尽可能保证成功
                    String seq = "award_qua_component:" + checkField + ":" + awardCode;
                    log.info("begin addAwardQualificationWithRetry,index:{},seq:{},actId:{},uid:{},awardCode:{},amount:{}", index, seq, attr.getActId(), uid, awardCode, amount);
                    receiveAwardComponent.addAwardQualificationWithRetry(index, seq, attr.getActId(), uid, awardCode, amount, attr.getRetry());
                }
            }
        }
    }


}
