package com.yy.gameecology.hdzj.element.history.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021.06.21 11:18
 */
public class MilestoneComponentAttr extends ComponentAttr {
    /**
     * 主榜ID
     */
    @ComponentAttrField(labelText = "主榜ID")
    private long mainRankId;
    /**
     * 用户贡献榜ID
     */
    @ComponentAttrField(labelText = "用户贡献榜ID")
    private long playerRankId;
    /**
     * 主播贡献榜ID
     */
    @ComponentAttrField(labelText = "主播贡献榜ID")
    private long anchorRankId;

    @ComponentAttrField(labelText = "阶段id")
    private long phaseId;


    /**
     * 给用户top n 发奖
     */
    @ComponentAttrField(labelText = "topN用户", remark = "给用户top n 发奖")
    private long playerTopN;
    /**
     * 给主播top n 发奖
     */
    @ComponentAttrField(labelText = "TopN主播", remark = "给主播top n 发奖")
    private long anchorTopN;

    /**
     * 用户奖包
     */
    @ComponentAttrField(labelText = "用户奖包",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "奖池id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "奖包id"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "奖品数量")
            })
    private Map<Long, Map<Long, Integer>> playerAwardPackageIds = new HashMap<>();
    /**
     * 主播奖包
     */
    @ComponentAttrField(labelText = "主播奖包",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "奖池id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "奖包id"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "奖品数量")
            })
    private Map<Long, Map<Long, Integer>> anchorAwardPackageIds = new HashMap<>();

    /**
     * 里程碑等级
     */
    @ComponentAttrField(labelText = "里程碑等级")
    private long level;

    @ComponentAttrField(labelText = "分数")
    private long score;

    @ComponentAttrField(labelText = "重试次数")
    private int retry = 2;

    /**
     * 是否全业务广播
     */
    @ComponentAttrField(labelText = "是否全业务广播")
    private boolean isBroadcast = false;

    public int getRetry() {
        return retry;
    }

    public void setRetry(int retry) {
        this.retry = retry;
    }

    public long getMainRankId() {
        return mainRankId;
    }

    public void setMainRankId(long mainRankId) {
        this.mainRankId = mainRankId;
    }

    public long getPlayerRankId() {
        return playerRankId;
    }

    public void setPlayerRankId(long playerRankId) {
        this.playerRankId = playerRankId;
    }

    public long getAnchorRankId() {
        return anchorRankId;
    }

    public void setAnchorRankId(long anchorRankId) {
        this.anchorRankId = anchorRankId;
    }

    public long getPlayerTopN() {
        return playerTopN;
    }

    public void setPlayerTopN(long playerTopN) {
        this.playerTopN = playerTopN;
    }

    public long getAnchorTopN() {
        return anchorTopN;
    }

    public void setAnchorTopN(long anchorTopN) {
        this.anchorTopN = anchorTopN;
    }

    public Map<Long, Map<Long, Integer>> getPlayerAwardPackageIds() {
        return playerAwardPackageIds;
    }

    public void setPlayerAwardPackageIds(Map<Long, Map<Long, Integer>> playerAwardPackageIds) {
        this.playerAwardPackageIds = playerAwardPackageIds;
    }

    public Map<Long, Map<Long, Integer>> getAnchorAwardPackageIds() {
        return anchorAwardPackageIds;
    }

    public void setAnchorAwardPackageIds(Map<Long, Map<Long, Integer>> anchorAwardPackageIds) {
        this.anchorAwardPackageIds = anchorAwardPackageIds;
    }

    public long getLevel() {
        return level;
    }

    public void setLevel(long level) {
        this.level = level;
    }

    public long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(long phaseId) {
        this.phaseId = phaseId;
    }

    public long getScore() {
        return score;
    }

    public void setScore(long score) {
        this.score = score;
    }

    public boolean isBroadcast() {
        return isBroadcast;
    }

    public void setBroadcast(boolean broadcast) {
        isBroadcast = broadcast;
    }
}
