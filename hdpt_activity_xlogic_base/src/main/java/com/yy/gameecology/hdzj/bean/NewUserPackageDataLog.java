package com.yy.gameecology.hdzj.bean;

import lombok.Data;

@Data
public class NewUserPackageDataLog {

    private int popUpNum;

    private int quietPopUpNum;

    private int obtainNum;

    private int obtainNaNum;

    private int appObtainNum;

    private int appObtainPartNum;

    private int appObtainFreeNum;

    private int yomiLoginNum;

    private int yomiLoginNaNum;

    private int zhuiwanLoginNum;

    private int zhuiwanLoginNaNum;

    private int yomiHdidLoginNum;

    private int yomiHdidLoginNaNum;

    private int zhuiwanHdidLoginNum;

    private int zhuiwanHdidLoginNaNum;

    private long totalIssueCount;

    private long totalIssueAmount;

    private long totalUsedCount;

    private long totalUsedUser;

    private long zhuiwanUsedCount;

    private long zhuiwanUsedUser;

    private long yomiUsedCount;

    private long yomiUsedUser;

}

