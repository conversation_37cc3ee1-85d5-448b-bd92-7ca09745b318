package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Sets;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

import java.util.Set;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-11 15:48
 **/
@Data
public class WzryGameComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "创建赛事策略：提前创建多少天赛事")
    private int createGamePreDay;

    @ComponentAttrField(labelText = "创建1V1赛事策略：每日首场比赛时间 HH:mm:ss")
    public String create1V1GameStartTime;

    @ComponentAttrField(labelText = "创建1V1赛事策略：每场比赛时间间隔(分钟)")
    public int create1V1GameTimeSpanMin;

    @ComponentAttrField(labelText = "创建1V1赛事策略：每日赛事视图数量")
    private int create1V1GameDayAmount;

    @ComponentAttrField(labelText = "创建1V1赛事策略：1个赛事视图对应实际比赛数量")
    private int create1V1GameViewAmount;

    @ComponentAttrField(labelText = "创建5V5赛事策略：每日首场比赛时间 HH:mm:ss")
    public String create5V5GameStartTime;

    @ComponentAttrField(labelText = "创建5V5赛事策略：每场比赛时间间隔")
    public int create5V5GameTimeSpanMin;

    @ComponentAttrField(labelText = "创建5V5赛事策略：每日赛事数量")
    private int create5V5GameDayAmount;

    @ComponentAttrField(labelText = "创建5V5赛事策略：1个赛事视图对应实际比赛数量")
    private int create5V5GameViewAmount;

    @ComponentAttrField(labelText = "1v1赛事列表游戏图片")
    private String gameIcon1V1;

    @ComponentAttrField(labelText = "5v5赛事列表游戏图片")
    private String gameIcon5V5;

    @ComponentAttrField(labelText = "参与赛事风控策略")
    private String riskStrategyKey;

    @ComponentAttrField(labelText = "营收appid")
    private int turnoverAppId;

    @ComponentAttrField(labelText = "营收参赛门票产品id")
    private int piaoProductId;

    @ComponentAttrField(labelText = "营收参赛门票货币类型")
    private int piaoCurrentType;

    @ComponentAttrField(labelText = "营收参赛门票产品产品类型")
    private int piaoProductType;

    @ComponentAttrField(labelText = "1V1参赛消耗门票")
    private int price1V1;

    @ComponentAttrField(labelText = "5V5参赛消耗门票")
    private int price5V5;

    @ComponentAttrField(labelText = "1V1得比赛每个队员发的奖金")
    private long award1V1;

    @ComponentAttrField(labelText = "5V5得比赛每个队员发的奖金")
    private long award5V5;


    @ComponentAttrField(labelText = "预约推送的消息发送者", remark = "系统消息:1234567，活动中心:10000，20000:Yo语音小助手")
    private int fromId = 20000;
    @ComponentAttrField(labelText = "预约推送的消息标题")
    private String title;
    @ComponentAttrField(labelText = "预约推送的消息内容")
    private String content;
    @ComponentAttrField(labelText = "预约推送的消息图片")
    private String image;
    @ComponentAttrField(labelText = "预约推送的消息跳转链接")
    private String link;

    @ComponentAttrField(labelText = "赛事取消退款通知弹窗")
    private String gameCancelPopLink;

    @ComponentAttrField(labelText = "IM消息提醒的AppId")
    private int msgAppid;

    @ComponentAttrField(labelText = "IM消息提醒发送人Uid")
    private long msgSenderUid;


    @ComponentAttrField(labelText = "赛事取消退款IM消息")
    private String gameCancelRefundImMsg;

    @ComponentAttrField(labelText = "赛事取消不退款通知弹窗")
    private String gameCancelImMsg;

    @ComponentAttrField(labelText = "赛事赏金任务Id")
    private long awardTaskId;

    @ComponentAttrField(labelText = "赛事赏金包裹Id")
    private long awardPackageId;

    @ComponentAttrField(labelText = "赛事手工初始化权限uid")
    private Set<Long> initGameAuth = Sets.newHashSet();

    @ComponentAttrField(labelText = "用户成功提现告警金额(分)")
    private long userWithDrawWarn;

    @ComponentAttrField(labelText = "总成功提现告警金额(分)")
    private long totalWithDrawWarn;
}
