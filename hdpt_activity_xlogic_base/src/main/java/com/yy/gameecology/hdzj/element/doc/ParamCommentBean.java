package com.yy.gameecology.hdzj.element.doc;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/15 15:39
 **/
@Data
public class ParamCommentBean {
    private String commentText;
    private String name;
    private String typeName;
    private String fullTypeName;
    private List<FieldCommentBean> fieldList = new ArrayList<>();

    public void addField(FieldCommentBean fieldCommentBean) {
        fieldList.add(fieldCommentBean);
    }
}
