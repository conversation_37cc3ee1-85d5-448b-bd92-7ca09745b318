package com.yy.gameecology.hdzj.element.history.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.RoleTypeSource;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.List;
import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption: 淘汰成员角色修改组件属性
 * @Date: 2021/11/01 16:39
 * @Modified:
 */
public class MemberRoleChangeV1ComponentAttr extends ComponentAttr {

    /**
     * 监听处理的榜单id
     */
    @ComponentAttrField(labelText = "榜单id", remark = "监听处理的榜单id,多个逗号分隔", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
    })
    private List<Long> rankIds;
    /**
     * 监听处理的阶段id
     */
    @ComponentAttrField(labelText = "阶段id", remark = "监听处理的阶段id,多个逗号分隔", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
    })
    private List<Long> phaseIds;

    /**
     * 《rankId-phaseId，前阶段榜单列表》-本阶段的榜单和上个阶段的榜单的map，不一致是需要配置
     */
    @ComponentAttrField(labelText = "阶段榜单", remark = "《rankId-phaseId，前阶段榜单列表》-本阶段的榜单和上个阶段的榜单的map，不一致是需要配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, labelText = "本阶段的榜单", type = String.class, remark = "格式：rankId-phaseId"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, labelText = "前阶段榜单列表", type = Long.class, remark = "多个时逗号分离")
            })
    private Map<String, List<Long>> rankPhasePreRankListMap = Maps.newHashMap();

    /**
     * 角色转换map《旧角色，新角色》
     */
    @ComponentAttrField(labelText = "角色转换", subFields = {
            @SubField(fieldName = Constant.KEY1, labelText = "旧角色", type = Long.class),
            @SubField(fieldName = Constant.VALUE, labelText = "新角色", type = Long.class)
    })
    private Map<Long, Long> roleChangeMap;
    /**
     * 主榜的角色类型，200主播
     */
    @ComponentAttrField(labelText = "主榜的角色类型", dropDownSourceBeanClass = RoleTypeSource.class)
    private int memberRoleType;

    /**
     * 数据上报的item
     */
    @ComponentAttrField(labelText = "数据上报的item")
    private String updatingItem;

    /**
     * 贡献榜单的角色id
     */
    @ComponentAttrField(labelText = "贡献榜单的角色id", dropDownSourceBeanClass = RoleTypeSource.class)
    private long contributeMemberRole;
    /**
     * 主榜和贡献榜需要增加
     */
    @ComponentAttrField(labelText = "主榜和贡献榜需要增加")
    private long contributeRankIdAdd;

    /**
     * 失败重试次数
     */
    @ComponentAttrField(labelText = "失败重试次数")
    private int reTryCount = 3;

    /**
     * 管理员
     */
    @ComponentAttrField(labelText = "管理员")
    private String[] admins = {};

    public List<Long> getRankIds() {
        return rankIds;
    }

    public void setRankIds(List<Long> rankIds) {
        this.rankIds = rankIds;
    }

    public List<Long> getPhaseIds() {
        return phaseIds;
    }

    public void setPhaseIds(List<Long> phaseIds) {
        this.phaseIds = phaseIds;
    }

    public Map<String, List<Long>> getRankPhasePreRankListMap() {
        return rankPhasePreRankListMap;
    }

    public void setRankPhasePreRankListMap(Map<String, List<Long>> rankPhasePreRankListMap) {
        this.rankPhasePreRankListMap = rankPhasePreRankListMap;
    }

    public Map<Long, Long> getRoleChangeMap() {
        return roleChangeMap;
    }

    public void setRoleChangeMap(Map<Long, Long> roleChangeMap) {
        this.roleChangeMap = roleChangeMap;
    }

    public int getMemberRoleType() {
        return memberRoleType;
    }

    public void setMemberRoleType(int memberRoleType) {
        this.memberRoleType = memberRoleType;
    }

    public String getUpdatingItem() {
        return updatingItem;
    }

    public void setUpdatingItem(String updatingItem) {
        this.updatingItem = updatingItem;
    }

    public long getContributeMemberRole() {
        return contributeMemberRole;
    }

    public void setContributeMemberRole(long contributeMemberRole) {
        this.contributeMemberRole = contributeMemberRole;
    }

    public long getContributeRankIdAdd() {
        return contributeRankIdAdd;
    }

    public void setContributeRankIdAdd(long contributeRankIdAdd) {
        this.contributeRankIdAdd = contributeRankIdAdd;
    }

    public int getReTryCount() {
        return reTryCount;
    }

    public void setReTryCount(int reTryCount) {
        this.reTryCount = reTryCount;
    }

    public String[] getAdmins() {
        return admins;
    }

    public void setAdmins(String[] admins) {
        this.admins = admins;
    }
}
