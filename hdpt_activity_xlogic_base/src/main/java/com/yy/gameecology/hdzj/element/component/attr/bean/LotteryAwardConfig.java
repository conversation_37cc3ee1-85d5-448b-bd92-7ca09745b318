package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;


@Data
public class LotteryAwardConfig {

    @ComponentAttrField(labelText = "抽奖奖池ID")
    private long lotteryTaskId;

    @ComponentAttrField(labelText = "抽奖奖包ID")
    private long lotteryPackageId;

    @ComponentAttrField(labelText = "发奖奖池ID")
    private long taskId;

    @ComponentAttrField(labelText = "发奖奖包ID")
    private long packageId;

    @ComponentAttrField(labelText = "礼物金额-单位厘")
    private long giftAmount;

    @ComponentAttrField(labelText = "奖励发放数量")
    protected Integer num;

    @ComponentAttrField(labelText = "奖励名称")
    protected String awardName;

    @ComponentAttrField(labelText = "奖励图标")
    protected String awardIcon;

    @ComponentAttrField(labelText = "是否大奖",remark = "0 非 1 是")
    private int bigAward;

    @ComponentAttrField(labelText = "挂件奖励图标",remark = "没有配置时用奖励图标")
    protected String layerAwardIcon;


}
