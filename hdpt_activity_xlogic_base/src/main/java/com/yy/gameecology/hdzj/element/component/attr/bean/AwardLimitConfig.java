package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-10-08 18:22
 **/
@Data
public class AwardLimitConfig {
    @ComponentAttrField(labelText = "总限额")
    protected long limit;

    @ComponentAttrField(labelText = "前置清0", remark = "单奖池限额小于这个金额的时候，提前让用户感知奖池不足，否则会出现奖池剩余金额较小的时候难以被清0")
    protected long  minimumAmount;

    @ComponentAttrField(labelText = "扣到0为止", remark = "及时余额不足，但只要余额还大于0依然可以扣。直到余额不大于0")
    protected boolean reduceToZero;

    @ComponentAttrField(labelText = "备注")
    protected String desc;
}
