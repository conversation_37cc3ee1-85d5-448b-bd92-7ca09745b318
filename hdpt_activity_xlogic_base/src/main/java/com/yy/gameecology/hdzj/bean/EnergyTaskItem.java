package com.yy.gameecology.hdzj.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022.04.15 15:09
 * 能量站任务
 */
@Data
public class EnergyTaskItem {
    /**
     * 等级
     */
    private long level;

    /**
     * 任务项名称
     */
    private String name;

    /**
     * 任务分值要求
     */
    private long req;

    /**
     * 总奖励,所有完成的任务的奖励加起来
     */
    private long award;

    /**
     * 超过关卡按比例返现
     */
    private double rate;

    /**
     * 单一任务分值(当前任务级别的阶梯值)
     */
    private long singleTaskScore;

    /**
     * 单一任务奖励(当前任务奖励)
     */
    private long singleTaskAward;
}
