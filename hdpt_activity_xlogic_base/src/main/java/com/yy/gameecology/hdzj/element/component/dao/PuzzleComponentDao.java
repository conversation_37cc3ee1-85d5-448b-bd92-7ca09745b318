package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.common.bean.CpUid;
import com.yy.gameecology.common.db.mapper.gameecology.cmpt.Cmpt5150UserPuzzleMapper;
import com.yy.gameecology.common.db.mapper.gameecology.cmpt.Cmpt5150UserPuzzleStateMapper;
import com.yy.gameecology.common.utils.MD5SHAUtil;
import com.yy.gameecology.hdzj.element.component.attr.PuzzleComponentAttr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-24 20:52
 **/
@Repository
public class PuzzleComponentDao {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private Cmpt5150UserPuzzleMapper cmpt5150UserPuzzleMapper;

    @Autowired
    private Cmpt5150UserPuzzleStateMapper cmpt5150UserPuzzleStateMapper;

    @Transactional(rollbackFor = Exception.class)
    public void addPuzzleBalance(String seq, TaskProgressChanged event, CpUid cpUid, PuzzleComponentAttr attr, String userPieceCode) {
        log.info("addPuzzleBalance begin, actId:{},userUid:{},userPieceCode:{}", attr.getActId(), event.getMember(), userPieceCode);
        String addPuzzleSeq = MD5SHAUtil.getMD5("add_puzzle:" + seq);
        boolean addPuzzleResult = commonDataDao.hashValueSetNX(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), "add_puzzle", addPuzzleSeq, event.getMember());
        if (!addPuzzleResult) {
            log.warn("add puzzle already add? actId:{},userUid:{},userPieceCode:{},anchorUid:{}", attr.getActId(), cpUid.getUserUid(), userPieceCode, cpUid.getAnchorUid());
            return;
        }
        int userRes = cmpt5150UserPuzzleMapper.insertOrUpdateUserPuzzle(attr.getActId(), attr.getCmptUseInx(), cpUid.getUserUid(), cpUid.getAnchorUid(), userPieceCode, 1L);
        log.info("addPuzzleBalance done, actId:{},userUid:{},userPieceCode:{},userRes:{}", attr.getActId(), event.getMember(), userPieceCode, userRes);

    }

    @Transactional(rollbackFor = Exception.class)
    public void clearPuzzle(CpUid cpUid, PuzzleComponentAttr attr, int addRound) {
        int clearRet = cmpt5150UserPuzzleMapper.clearBalance(attr.getActId(), attr.getCmptUseInx(), cpUid.getUserUid(), cpUid.getAnchorUid());
        int addRoundRet = cmpt5150UserPuzzleStateMapper.insertOrUpdateUserPuzzleState(attr.getActId(), attr.getCmptUseInx(), cpUid.getUserUid(), cpUid.getAnchorUid(), addRound);
        log.info("clearBalance done,uid:{},anchorUid:{},round:{},clearRet:{},addRoundRet:{}", cpUid.getUserUid(), cpUid.getAnchorUid(), addRound, clearRet, addRoundRet);
    }
}
