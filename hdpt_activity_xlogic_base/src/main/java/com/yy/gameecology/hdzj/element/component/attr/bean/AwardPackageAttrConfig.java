package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-11-16 18:20
 **/
@Data
public class AwardPackageAttrConfig {

    @ComponentAttrField(labelText = "奖励奖包Id",remark = "填0，则是抽奖")
    protected Long tAwardPkgId;

    @ComponentAttrField(labelText = "奖励发放数量")
    protected Integer num;

    @ComponentAttrField(labelText = "奖励名称")
    protected String awardName;

    @ComponentAttrField(labelText = "单位")
    protected String unit;

    @ComponentAttrField(labelText = "奖励图标")
    protected String awardIcon;

    @ComponentAttrField(labelText = "奖励金额",remark = "")
    protected long awardAmount;

    @ComponentAttrField(labelText = "备注")
    protected String remark;

    @ComponentAttrField(labelText = "扩展信息")
    protected String extJson;
}
