package com.yy.gameecology.hdzj.element.history;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.TurnoverServiceClient;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.exception.BadRequestException;
import com.yy.gameecology.activity.exception.ParameterException;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.GiftPackageYBPayComponentAttr;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import com.yy.thrift.turnover.TAppId;
import com.yy.thrift.turnover.TConsumeProductRequest;
import com.yy.thrift.turnover.TConsumeProductResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.UUID;

/**
 * A. 获取礼包购买状态
 * 1. redis存有一批uid,用set存储,有个属性配置项指定这批uid是白名单还是黑名单
 * 2. 白名单时,只有当前用户在名单中才有资格购买;黑名单时,只有当前用户不在黑名单中才有资格购买
 * 3. 当用户有购买资格时,判断当前用户的购买次数是否超过限制,超过了则不允许购买
 * <p>
 * B. 购买礼包
 * 1. 检查用户的购买资格
 * 2. 判断当前IP的操作频率
 * 3. 分布式锁锁定当前用户
 * 4. 调用营收接口进行扣费(需要营收提前配置扣费标识)
 * 5. 购买成功后调用中台接口发放礼包
 *
 * @Author: CXZ
 * @Desciption: 礼包组件:YB支付
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
@Deprecated
@Component
public class GiftPackageYBPayComponent extends BaseActComponent<GiftPackageYBPayComponentAttr> {
    @Autowired
    private TurnoverServiceClient turnoverServiceClient;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Autowired
    private ActInfoService actInfoService;

    @Autowired
    private Locker locker;

    private static final String BUY_GIFT_PACKAGE_IP_LIMIT_KEY = "buyGiftPackageIpLimit:%s";

    private static final String BUY_RECORD = "buyRecord";

    private static final String BUY_LOCK = "GiftPackage_lock_%s";

    @Override
    public Long getComponentId() {
        return ComponentId.GIFT_PACKAGE_BUY_YB_PAY;
    }


    public Response<JSONObject> giftPackageStatus(GiftPackageYBPayComponentAttr attr, long uid) {


        boolean hasQualification = true;
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        if (attr.isLimitQualification()) {
            boolean contains = actRedisDao.sIsMember(groupCode, attr.getLimitUidRedisKey(), String.valueOf(uid));
            hasQualification = (contains && attr.isAllow()) || (!contains && !attr.isAllow());
        }
        JSONObject data = new JSONObject();
        if (hasQualification) {
            String redisKey = makeKey(attr, BUY_RECORD);
            boolean canBuy = Convert.toInt(actRedisDao.zscore(groupCode, redisKey, String.valueOf(uid)), 0) < attr.getLimitBugCount();
            data.put("canBuy", canBuy ? 1 : 0);
        } else {
            data.put("canBuy", -1);
        }

        return Response.success(data);
    }


    public Response<String> buyGiftPackage(GiftPackageYBPayComponentAttr attr, long uid, String ip) {
        Clock clock = new Clock();

        long actId = attr.getActId();
        if (!actInfoService.inActTime(actId)) {
            return Response.fail(2, "活动已结束");
        }
        if (!commonService.checkWhiteList(attr.getActId(), RoleType.USER, String.valueOf(uid))) {
            return Response.fail(2, "活动暂未开始");
        }

        String groupCode = redisConfigManager.getGroupCode(actId);

        if (attr.isLimitQualification()) {
            boolean contains = actRedisDao.sIsMember(groupCode, attr.getLimitUidRedisKey(), String.valueOf(uid));
            boolean hasQualification = (contains && attr.isAllow()) || (!contains && !attr.isAllow());
            if (!hasQualification) {
                return Response.fail(2, attr.getLimitQualificationTip());
            }
        }
        clock.tag();
        String ipKey = makeKey(attr, String.format(BUY_GIFT_PACKAGE_IP_LIMIT_KEY, ip));
        String oldUid = actRedisDao.get(groupCode, ipKey);
        if (StringUtils.hasLength(oldUid)) {
            log.info("buyGiftPackage ip repeat. uid:{} ip:{} old:{}", uid, ip, oldUid);
            return Response.fail(2, "当前购买礼包人数较多，请稍后再试");
        }
        //营收那边的appId
        TAppId appId = changeToAppId(BusiId.findByValue((int) attr.getBusiId()));
        if (appId == null) {
            log.error("buyGiftPackage bussId config error，attr：{}", JSON.toJSONString(attr));
            return Response.fail(2, "当前购买礼包人数较多，请稍后再试");
        }
        clock.tag();
        String lockName = makeKey(attr, String.format(BUY_LOCK, uid));
        int second = 30;
        Secret lock = null;
        try {
            lock = locker.lock(lockName, second);
            if (lock != null) {
                String userGiftPackageKey = makeKey(attr, BUY_RECORD);
                //达到限制购买次数
//                if (attr.getLimitBugCount() > 0
//                        && actRedisDao.zIncrWithLimit(groupCode, userGiftPackageKey, String.valueOf(uid), 1, attr.getLimitBugCount()).get(0) != 1L) {
//                    return Response.fail(2, attr.getLimitBugCountTip());
//                }

                //TODO 如果次组件要重新启用，则需要改造此处
                if (true) {
                    throw new RuntimeException("zset_incr_with_limit.lua,lua forbid");
                }

                TConsumeProductRequest req = new TConsumeProductRequest();
                String seq = UUID.randomUUID().toString();
                clock.tag();
                int pId = 0;
                req.setUid(uid);
                req.setAppid(appId);
                req.setProductType(attr.getpType());
                // productId随便，没有填0
                req.setProductId(pId);
                // amount 以紫宝石为单位
                req.setAmount(attr.getPrice());
                req.setSeqId(seq);
                req.setDescription(attr.getGiftDes());
                TConsumeProductResult result = turnoverServiceClient.consumeProductNew(req);
                clock.tag();
                log.info("buyGiftPackage consumeProductNew uid:{} pType:{} amount:{} seq:{} result:{}", uid, attr.getpType(), attr.getPrice(), seq, result);
                //购买成功
                if (result != null && result.getCode() == 1) {
                    BatchWelfareResult res = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                            uid, attr.getTaskId(), attr.getCount(), attr.getPackageId(), seq);
                    log.info("buyGiftPackage doWelfare uid:{} seq:{} res:{}", uid, seq, res);
                    actRedisDao.set(groupCode, ipKey, String.valueOf(uid), attr.getIpLimitSec());
                    return new Response<>(0, "恭喜你购买成功，快去体验吧！");
                }
                clock.tag();
                //购买失败
                actRedisDao.zIncr(groupCode, userGiftPackageKey, String.valueOf(uid), -1L);
                final int codem23 = -23;
                if (result != null && result.getCode() == codem23) {
                    return Response.fail(1, "Y币余额不足");
                }

            }
        } catch (Exception e) {
            log.info("buyGiftPackage error {}", e.getMessage(), e);
        } finally {
            if (lock != null) {
                locker.unlock(lockName, lock);
            }
        }
        return Response.fail(2, "当前购买礼包人数较多，请稍后再试");
    }

    public Response<JSONObject> giftPackageStatus(long actId, long index, long uid) {
        if (!isMyDuty(actId)) {
            throw new BadRequestException("无效活动礼包");
        }

        GiftPackageYBPayComponentAttr attr = getComponentAttr(actId, index);
        if (attr != null) {
            return giftPackageStatus(attr, uid);
        }
        throw new ParameterException("index error.");
    }

    public Response<String> buyGiftPackage(long actId, long index, long uid, String ip) {
        if (!isMyDuty(actId)) {
            throw new BadRequestException("无效活动礼包");
        }
        GiftPackageYBPayComponentAttr attr = getComponentAttr(actId, index);
        if (attr != null) {
            return buyGiftPackage(attr, uid, ip);
        }
        throw new ParameterException("index error.");
    }

    private TAppId changeToAppId(BusiId busiId) {
        switch (busiId) {
            case YUE_ZHAN:
                return TAppId.VipPk;
            case GAME_BABY:
                return TAppId.Baby;
            default:
                return null;
        }
    }

}
