package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardLimitConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.DayTaskConfig;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-09-18 17:06
 **/
@Data
public class DayTaskComponent2Attr extends ComponentAttr {

    @ComponentAttrField(labelText = "日任务配置",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = DayTaskConfig.class)})
    private List<DayTaskConfig> dayTaskConfig = Lists.newLinkedList();

    @ComponentAttrField(labelText = "日任务发奖", remark = "",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "任务日"),
                    @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class, labelText = "奖励配置")
            })
    private Map<Integer, AwardAttrConfig> dayAward = Maps.newLinkedHashMap();


    @ComponentAttrField(labelText = "任务白名单组件索引")
    private long whiteListIndex;

    @ComponentAttrField(labelText = "限额控制组件索引")
    private long limitControlIndex;

}
