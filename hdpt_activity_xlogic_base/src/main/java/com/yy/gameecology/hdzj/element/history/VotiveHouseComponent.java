package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.service.DelayQueueService;
import com.yy.gameecology.activity.service.SvcSDKService;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.HdzjComponentCollaborator;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.VotiveHouseComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.QueryRankingRequest;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 桃花还愿屋
 *
 * <AUTHOR>
 * @date 2021/15 14:25
 */
@Deprecated
@Component
public class VotiveHouseComponent extends BaseActComponent<VotiveHouseComponentAttr> {
    @Autowired
    private SvcSDKService svcSDKService;
    @Autowired
    private HdzjComponentCollaborator componentCollaborator;
    @Autowired
    private DelayQueueService delayQueueService;

    @Override
    public Long getComponentId() {
        return ComponentId.VOTIVE_HOUSE;
    }

    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void votiveHouseAccomplishTask(TaskProgressChanged event, VotiveHouseComponentAttr attr) {

        long actId = attr.getActId();
        Optional<VotiveHouseComponentAttr.TaskInfo> taskInfoOp =
                attr.getTaskInfos().stream()
                        .filter(taskInfo -> taskInfo.getRankId() == event.getRankId() && taskInfo.getPhaseId() == event.getPhaseId())
                        .findFirst();

        if (!taskInfoOp.isPresent()) {
            return;
        }


        VotiveHouseComponentAttr.TaskInfo taskInfo = taskInfoOp.get();
        int roundComplete = taskInfo.isLoop() ? (int) event.getRoundComplete() : 1;
        long uid = Convert.toLong(event.getMember());
        int award = taskInfo.getAward() * roundComplete;

        //发奖
        try {
            componentCollaborator.callComponent(actId, ComponentId.SHOP, "addUserCurrency", actId, attr.getShopIndex(), uid, "HUANG_YUAN", award);
        } catch (Exception e) {
            log.error("votiveHouseAccomplishTask award by callComponent error attr :{} event:{} {}"
                    , JSON.toJSONString(attr), JSON.toJSONString(event), e.getMessage(), e);
        }
        String tipContest = taskInfo.getTipContest().replace("##num##", roundComplete + "");

        Map<String, String> tipInfo = ImmutableMap.of("award", "获得还愿签x" + award);
        GameecologyActivity.CommonNoticeResponse.Builder tip = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(event.getActId())
                .setNoticeType("votiveHouseAccomplishTaskTip")
                .setNoticeValue(taskInfo.getPackageType() + "")
                .setNoticeMsg(tipContest)
                .setExtJson(JSON.toJSONString(tipInfo));

        GameecologyActivity.GameEcologyMsg userTipsMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(tip).build();

        String nowDate = DateUtil.getNowYyyyMMddHHmmss();
        //同个用户排队单播，前端的展示时间是5秒
        if(true){
            throw new RuntimeException("lua forbid");
        }
//        delayQueueService.lineUpLocal("votiveHouseAccomplishTask-" + uid, 6, () -> {
//            svcSDKService.unicastUid(uid, userTipsMsg);
//            log.info("votiveHouseAccomplishTask daily unicastUid done@uid:{},cTime:{},data:{}"
//                    , uid, nowDate, JsonFormat.printToString(userTipsMsg));
//        });

        log.info("votiveHouseAccomplishTask done@attr :{} event:{}", JSON.toJSONString(attr), JSON.toJSONString(event));

        Date date = DateUtil.getDate(event.getOccurTime(), DateUtil.DEFAULT_PATTERN);
        int index = attr.getTaskInfos().indexOf(taskInfo);
        bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.findByValue((int) event.getBusiId()), date.getTime(), uid + "", RoleType.USER, index
                , 11, taskInfo.getTaskName());
    }

    public List<JSONObject> queryUserTaskInfo(long actId, long cmptUseInx, long uid) {
        VotiveHouseComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + cmptUseInx);

        Date now = commonService.getNow(attr.getActId());
        //批量查询榜单
        List<QueryRankingRequest> rankingRequests = Lists.newArrayList();
        for (VotiveHouseComponentAttr.TaskInfo taskInfo : attr.getTaskInfos()) {
            String dateStr = StringUtils.isBlank(taskInfo.getDateFormat()) ? "" : DateUtil.format(now, taskInfo.getDateFormat());
            QueryRankingRequest rankingRequest = new QueryRankingRequest();
            rankingRequest.setActId(actId);
            rankingRequest.setRankingId(taskInfo.getRankId());
            rankingRequest.setPhaseId(taskInfo.getPhaseId());
            rankingRequest.setRankingCount(0);
            rankingRequest.setDateStr(dateStr);
            rankingRequest.setPointedMember(uid + "");
            rankingRequest.setExtData(Maps.newHashMap());
            rankingRequests.add(rankingRequest);
        }
        List<List<Rank>> ranksList = hdztRankingThriftClient.queryBatchRanking(rankingRequests, Maps.newHashMap());

        //组织任务数据
        List<JSONObject> userTaskInfos = Lists.newArrayList();
        for (int i = 0; i < attr.getTaskInfos().size(); i++) {
            VotiveHouseComponentAttr.TaskInfo taskInfo = attr.getTaskInfos().get(i);
            Rank rank = ranksList.get(i).get(0);
            long score = Math.max(rank.getScore(), 0L);
            long showScore = score;
            long totalCount = taskInfo.getTotalCount();
            if (score >= totalCount) {
                showScore = taskInfo.isLoop() ? score % totalCount : score;
            }
            JSONObject userTaskInfo = new JSONObject();

            userTaskInfo.put("taskName", taskInfo.getTaskName());
            userTaskInfo.put("completedCount", showScore);
            userTaskInfo.put("totalCount", taskInfo.getTotalCount());
            userTaskInfo.put("isLoop", taskInfo.isLoop());

            userTaskInfo.put("icon", taskInfo.getShowImage());
            userTaskInfo.put("unit", taskInfo.getUnit());
            userTaskInfo.put("award", taskInfo.getAward());
            userTaskInfo.put("packageName", taskInfo.getPackageName());

            userTaskInfos.add(userTaskInfo);
        }

        return userTaskInfos;
    }


}
