package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class DragonTreasureComponentAttr extends ComponentAttr {
    /**
     * 成员是否为房间 - 1:是语音房间，非1：是子频道
     */
    @ComponentAttrField(labelText = "成员是否为房间", remark = "1:是语音房间，非1：是子频道")
    private int roomFlag = 0;

    /**
     * 业务ID， 见 com.yy.thrift.hdztranking.BusiId
     */
    @ComponentAttrField(labelText = "业务ID", remark = "见 com.yy.thrift.hdztranking.BusiId")
    private int busiId;

    /**
     * 推送移动端横幅业务ID - 需要推送的业务类型 1 -语音房 2 -交友房 4 -其他 8 -宝贝 位域表示 支持组合：交友and宝贝：2+8
     */
    @ComponentAttrField(labelText = "推送移动端横幅业务ID", remark = "需要推送的业务类型 1 -语音房 2 -交友房 4 -其他 8 -宝贝 位域表示 支持组合：交友and宝贝：2+8")
    private int appBannerBusiId = 1;

    @ComponentAttrField(labelText = "推送移动端横幅 svga url")
    private String appBannerSvgaUrl = "";

    @ComponentAttrField(labelText = "推送移动端横幅内容")
    private String appBannerContent = "";

    @ComponentAttrField(labelText = "推送移动端横幅SvgaTextKey", remark = "询问美术设计获取")
    private String appBannerSvgaTextKey = "";

    /**
     * 业务模板ID，见  com.yy.thrift.broadcast.Template
     */
    @ComponentAttrField(labelText = "业务模板ID", remark = "见  com.yy.thrift.broadcast.Template")
    private int template;

    /**
     * 广播 banner id
     */
    @ComponentAttrField(labelText = "广播 banner id")
    private int bannerId = 1;

    /**
     * 房间秘宝广播 banner type
     */
    @ComponentAttrField(labelText = "房间秘宝广播 banner type")
    private int bannerTypeForRoom = 347100;

    /**
     * 全服秘宝广播 banner type
     */
    @ComponentAttrField(labelText = "全服秘宝广播 banner type")
    private int bannerTypeForAll = 347101;

    /**
     * 赐福广播 banner type
     */
    @ComponentAttrField(labelText = "赐福广播 banner type")
    private int bannerTypeForBlessing = 347102;

    /**
     * 虚拟货币任务:  key：${rankId}|${phaseId}, value: List元素为 ${currencyId}|${amount}|${summary}
     */
    @ComponentAttrField(labelText = "虚拟货币任务", remark = "记录任务收入用", subFields = {
            @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "榜单|阶段", remark = "${rankId}|${phaseId}"),
            @SubField(fieldName = Constant.VALUE, type = List.class, labelText = "任务列表", remark = "出现顺序对应任务级别"),
            @SubField(fieldName = Constant.MAP_LIST_VALUE, type = String.class, labelText = "货币ID|数量|摘要", remark = "多个元素之间用 , 分开")
    })
    private Map<String, List<String>> currencyTaskMap;

    /**
     * 秘宝资格榜单id
     */
    @ComponentAttrField(labelText = "秘宝资格榜单id")
    private long treasureQualificationRankId;

    /**
     * 秘宝资格阶段id
     */
    @ComponentAttrField(labelText = "秘宝资格阶段id")
    private long treasureQualificationPhaseId;

    /**
     * 秘宝级别人数，越前越高级， 元素内容为： ${level}|${amount}, 比如 S|4
     */
    @ComponentAttrField(labelText = "秘宝级别人数", remark = "越前越高级， 元素内容为： ${level}|${amount}, 比如 S|4，多个元素之间用 , 分开"
            , subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class)})
    private List<String> treasureLevelAmounts;

    /**
     * 赐福榜单id
     */
    @ComponentAttrField(labelText = "赐福榜单id")
    private long blessingRankId;

    /**
     * 赐福阶段id， 若<1, 则忽略阶段
     */
    @ComponentAttrField(labelText = "赐福阶段id", remark = "若<1, 则忽略阶段")
    private long blessingPhaseId;

    /**
     * 赐福角色id， 若<1, 则自动取 送礼人 角色
     */
    @ComponentAttrField(labelText = "赐福角色id", remark = "若<1, 则自动取 送礼人 角色")
    private long blessingActorId;

    /**
     * 赐福抽奖奖池ID, Key:任务级别（从0开始为第一级）， val：奖池ID
     */
    @ComponentAttrField(labelText = "赐福抽奖奖池ID",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务级别", remark = "从0开始为第一级"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "奖池ID")
            })
    private Map<Long, Long> blessingAwardTaskIds;

    /**
     * 秘宝级别对应的抽奖奖池ID， key：秘宝级别（例如 S/A， 同 treasureLevelAmounts 中的 ${level}), val：奖池ID
     */
    @ComponentAttrField(labelText = "秘宝级别对应的抽奖奖池ID",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "秘宝级别", remark = "例如 S/A， 同 秘宝级别人数 中的 ${level}"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "奖池ID")
            })
    private Map<String, Long> treasureLevelAwardTaskIds;

    /**
     * 解秘支付货币ID
     */
    @ComponentAttrField(labelText = "解秘支付货币ID")
    private String unlockCurrencyId;

    /**
     * 解秘支付货币名称
     */
    @ComponentAttrField(labelText = "解秘支付货币名称")
    private String unlockCurrencyName;

    /**
     * 每次解秘支付货币数量
     */
    @ComponentAttrField(labelText = "每次解秘支付货币数量")
    private long unlockCurrencyNum;

    /**
     * 解秘截止时间，格式： yyyy-MM-dd HH:mm:ss
     */
    @ComponentAttrField(labelText = "解秘截止时间", remark = "格式： yyyy-MM-dd HH:mm:ss")
    private String unlockEndTime = "";

    /**
     * 解秘宝总结词（用于交易记录）
     */
    @ComponentAttrField(labelText = "解秘宝总结词", remark = "用于交易记录")
    private String unlockSummary = "解XX秘宝";

    /**
     * 兑换支付货币ID
     */
    @ComponentAttrField(labelText = "兑换支付货币ID")
    private String exchangeCurrencyId;

    /**
     * 兑换奖池ID
     */
    @ComponentAttrField(labelText = "兑换奖池ID")
    private long exchangeAwardTaskId;

    /**
     * 秘宝资格开始小时
     */
    @ComponentAttrField(labelText = "秘宝资格开始小时", remark = "0~23，在给定值开始有小时秘宝")
    private long qualificationBeginHour = 17;

    /**
     * 秘宝资格结束小时
     */
    @ComponentAttrField(labelText = "秘宝资格结束小时", remark = "0~23，在给定值之后无小时秘宝")
    private long qualificationEndHour = 23;

    /**
     * 商品价格, key: packageId,  value: exchangeCurrencyId 的数量
     */
    @ComponentAttrField(labelText = "商品价格",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "奖包ID", remark = "packageId"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "兑换支付货币数量", remark = "exchangeCurrencyId 的数量")
            })
    private Map<Long, Long> exchangePriceMap;

    /**
     * 兑换截止时间，格式： yyyy-MM-dd HH:mm:ss
     */
    @ComponentAttrField(labelText = "兑换截止时间", remark = "格式： yyyy-MM-dd HH:mm:ss")
    private String exchangeEndTime = "";

    /**
     * 秘宝问题文件名(注意，不需要路径，只要名字）
     */
    @ComponentAttrField(labelText = "秘宝问题文件名", remark = "注意，不需要路径，只要名字")
    private String fileName = "DragonTreasureQuestion-001.txt.shuffle";

    /**
     * 抽、发奖 错误重试次数 - 确保抽发操作尽量成功完成
     */
    @ComponentAttrField(labelText = "抽、发奖 错误重试次数", remark = "确保抽发操作尽量成功完成")
    private int retry = 0;

    /**
     * 用户只能兑换一次的奖包
     */
    private Map<Long, Integer> userUniqExchPackageIds = ImmutableMap.of(92035L, 1, 92030L, 3, 92029L, 1, 92028L, 1);
}
