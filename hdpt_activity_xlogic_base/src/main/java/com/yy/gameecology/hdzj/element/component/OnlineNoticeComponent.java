package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.service.DelaySvcSDKServiceV2;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.OnlineNoticeComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.OnlineNoticeDao;
import com.yy.protocol.pb.GameecologyActivity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;

/**
 * 发送单播通知，如果用户当前不在线，则等到首次进入频道后再发
 */
@Slf4j
@Component
public class OnlineNoticeComponent extends BaseActComponent<OnlineNoticeComponentAttr> {

    private static final String ONLINE_NOTICE = "online_notice:%d";

    @Autowired
    private OnlineNoticeDao onlineNoticeDao;

    @Autowired
    private DelaySvcSDKServiceV2 delaySvcSDKServiceV2;
    @Autowired
    private TransactionTemplate transactionTemplate;

    @Override
    public Long getComponentId() {
        return ComponentId.ONLINE_NOTICE;
    }

    public void sendOnlineNotice(long actId, long cmptUseInx, long uid, String seq, String noticeType, String noticeValue) {
        sendOnlineNotice(actId, cmptUseInx, uid, seq, noticeType, noticeValue, 0);
    }

    /**
     * 发送在线通知
     * @param actId
     * @param cmptUseInx
     * @param uid
     * @param noticeType
     * @param noticeValue
     * @param delay 如果在线，需要延迟x秒后再发（单位：秒）
     */
    public void sendOnlineNotice(long actId, long cmptUseInx, long uid, String seq, String noticeType, String noticeValue, long delay) {
        var attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            log.error("sendOnlineNotice fail component not exist");
            throw new IllegalArgumentException("component not exist");
        }

        UserCurrentChannel channel = commonService.getNoCacheUserCurrentChannel(uid, 1);
        if (channel != null) {
            var msg = buildOnlineNoticeMsg(actId, noticeType, noticeValue);
            if (delay > 0) {
                delaySvcSDKServiceV2.unicastUid(uid, msg, delay);
            } else {
                svcSDKService.unicastUid(uid, msg);
            }
            log.info("sendOnlineNotice success with noticeValue:{}", noticeValue);
            return;
        }

        // 不在线（不在频道内，则保存）
        Date now = commonService.getNow(actId);
        var expiredSecond = attr.getExpireDuration().toSeconds();
        final Date expiredTime;
        if (expiredSecond > 0) {
            expiredTime = DateUtils.addSeconds(now, (int) expiredSecond);
        } else {
            var actInfo = actInfoService.queryActivityInfo(actId);
            if (actInfo == null) {
                throw new IllegalArgumentException("actInfo is null");
            }

            expiredTime = new Date(actInfo.getEndTimeShow());
        }

        int rs = onlineNoticeDao.addOnlineNotice(actId, attr.getCmptUseInx(), uid, seq, noticeType, noticeValue, expiredTime);
        log.info("sendOnlineNotice save online notice success with uid:{} rs:{}", uid, rs);
    }

    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = true)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, OnlineNoticeComponentAttr attr) {
        final long uid = event.getUid();

        if (CollectionUtils.isNotEmpty(attr.getBusinessIds())) {
            if (!attr.getBusinessIds().contains(event.getBusiId())) {
                log.warn("onUserEnterTemplate skip unsatisfied uid:{} busiId:{}", uid, event.getBusiId());
                return;
            }
        }

        if (CollectionUtils.isNotEmpty(attr.getNoticeHosts())) {
            if (!attr.getNoticeHosts().contains(event.getHost())) {
                log.warn("onUserEnterTemplate skip unsatisfied uid:{} host:{}", uid, event.getHost());
                return;
            }
        }

        if (CollectionUtils.isNotEmpty(attr.getNoticeHostIds())) {
            if (!attr.getNoticeHostIds().contains(event.getHostName().getHostId())) {
                log.warn("onUserEnterTemplate skip unsatisfied uid:{} hostId:{}", uid, event.getHostName().getHostId());
                return;
            }
        }

        Date now = commonService.getNow(uid);

        var notices = onlineNoticeDao.selectOnlineNotices(attr.getActId(), attr.getCmptUseInx(), uid, now);
        if (CollectionUtils.isEmpty(notices)) {
            return;
        }

        long delay = attr.getInitDelay().toSeconds();
        for (var notice : notices) {
            transactionTemplate.execute(status -> {
                int rs = onlineNoticeDao.deleteOnlineNotice(notice.getId());
                if (rs <= 0) {
                    return 0;
                }

                doSendDelayOnlineNotice(attr.getActId(), uid, notice.getNoticeType(), notice.getNoticeValue(), delay);

                return 1;
            });
        }
    }

    private GameecologyActivity.GameEcologyMsg buildOnlineNoticeMsg(long actId, String noticeType, String noticeValue) {
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType(noticeType)
                .setNoticeValue(noticeValue);

        return GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
    }

    private void doSendDelayOnlineNotice(long actId, long uid, String noticeType, String noticeValue, long delay) {
        var msg = buildOnlineNoticeMsg(actId, noticeType, noticeValue);
        delaySvcSDKServiceV2.unicastUid(uid, msg, delay);
        log.info("doSendDelayOnlineNotice with uid:{} noticeValue:{}", uid, noticeValue);
    }
}
