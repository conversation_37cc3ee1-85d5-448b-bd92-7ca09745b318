package com.yy.gameecology.hdzj.element.attrconfig;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/28 10:24
 **/
@Component
public class AwardTypeSource implements DropDownSource {

    // [{"code":"1","desc":"按照排名奖励"},{"code":"2","desc":"按照分值奖励"}]
    @Override
    public List<DropDownVo> listDropDown() {
        return Arrays.asList(
                new DropDownVo("1", "按照排名奖励(1)"),
                new DropDownVo("2", "按照分值奖励(2)")
        );
    }
}
