package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

@Data
public class TravelDiaryMissionConfig {
    @ComponentAttrField(labelText = "任务等级")
    private int level;

    @ComponentAttrField(labelText = "任务名称")
    private String levelName;

    @ComponentAttrField(labelText = "任务阈值")
    private long score;

    @ComponentAttrField(labelText = "广播类型，累计任务需配置",remark = "2-子频道广播 3-顶级频道下所有子厅广播/家族下面所有子厅 4-全模板")
    private Long broType;

}
