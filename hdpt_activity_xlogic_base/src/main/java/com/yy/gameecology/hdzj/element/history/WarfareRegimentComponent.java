package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSortedMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.bean.AnchorStartShowEvent;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserRemainEvent;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.client.thrift.CulClient;
import com.yy.gameecology.activity.exception.LotteryException;
import com.yy.gameecology.activity.exception.ParameterException;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.MyListUtils;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.WarfareRegimentComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztaward.PackageLeftQuota;
import com.yy.thrift.hdztranking.*;
import com.yy.thrift.zhuiwan.BaseRsp;
import com.yy.thrift.zhuiwan.PopupMsg;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.util.HtmlUtils;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 战力团
 *
 * <AUTHOR>
 * @date 2021/15 14:25
 */
@Deprecated
@Component
public class WarfareRegimentComponent extends BaseActComponent<WarfareRegimentComponentAttr> {
    @Autowired
    private SvcSDKService svcSDKService;

    @Autowired
    private BroadCastHelpService broadCastHelpService;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private CulClient culClient;

    @Autowired
    private DelayQueueService delayQueueService;

    /**
     * 主播的战力团创建数据
     */
    private final String ANCHOR_WARFARE_REGIMENT_KEY = "anchor_warfare_regiment";

    /**
     * 用户的战力团主播
     */
    private final String USER_WARFARE_REGIMENT_STATUS = "user_warfare_regiment_status";

    /**
     * 用户的任务完成数据，null是为完成，1是可抽奖，0已完成
     */
    private final String USER_TASK_INFO_KEY = "user_task_info";
    /**
     * 用户追玩频道的引导数据
     */
    private final String USER_GOTO_INFO_KEY = "user_goto_info";
    /**
     * 主播的抽奖此次数
     */
    private final String ANCHOR_LOTTERY_COUNT_KEY = "anchor_lottery_count";


    @Override
    public Long getComponentId() {
        return ComponentId.WARFARE_RAGIMENT;
    }

    @Autowired
    private OnlineChannelService onlineChannelService;

    @Autowired
    private AntiCheatGnService antiCheatGnService;

    /**
     * 首次上麦-为主播创建战力团,并发单播提醒
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = AnchorStartShowEvent.class, canRetry = true)
    public void createAnchorRegiment(AnchorStartShowEvent event, WarfareRegimentComponentAttr attr) {
        long uid = event.getUid();

        String key = makeKey(attr, ANCHOR_WARFARE_REGIMENT_KEY);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
        if (first) {
            RetryTool.withRetryCheck(attr.getActId(), event.getSeq(), () -> {
                svcSDKService.unicastUid(uid, getNoticeMsg(attr.getActId(), "zw_anchor_first_tip", attr.getAnchorFirstTip()));
            });
        }
        log.info("createAnchorRegiment done@uid:{} first:{} tip:{}", uid, first, attr.getAnchorFirstTip());
    }

    /**
     * 用户停留10分钟-发tip
     *
     * @param userRemainEvent
     * @param attr
     */
    @HdzjEventHandler(value = UserRemainEvent.class, canRetry = true)
    public void doHandleUserRemainEvent(UserRemainEvent userRemainEvent, WarfareRegimentComponentAttr attr) {
        long uid = userRemainEvent.getUid();
        long sid = userRemainEvent.getSid();
        long ssid = userRemainEvent.getSsid();

        long warfareRegimentAnchor = getUserWarfareRegimentAnchor(uid, attr);
        if (warfareRegimentAnchor > 0) {
            log.info("doHandleUserRemainEvent ignore add@ uid:{} sid:{} ssid:{} ", uid, sid, ssid);
            return;
        }
        OnlineChannelInfo onlineChannelInfo = onlineChannelService.get(sid, ssid);
        //没有主播在线
        if (onlineChannelInfo == null) {
            log.info("doHandleUserRemainEvent ignore not find anchor@ uid:{} sid:{} ssid:{} ", uid, sid, ssid);
            return;
        }
        //本房间的主播不tip
        boolean isAnchor = onlineChannelInfo.getEffectAnchorId().contains(uid);
        if (isAnchor) {
            log.info("doHandleUserRemainEvent ignore is anchor@ uid:{} sid:{} ssid:{} ", uid, sid, ssid);
            return;
        }
        RetryTool.withRetryCheck(userRemainEvent.getActId(), userRemainEvent.getSeq(), () -> {
            svcSDKService.unicastUid(uid, getNoticeMsg(attr.getActId(), "zw_520_user_remain_tip", attr.getUserRemainTip()));
            log.info("doHandleUserRemainEvent done@ uid:{} sid:{} ssid:{} tip:{} ", uid, sid, ssid, attr.getUserRemainTip());
        });
    }

    /**
     * 用户登录的时候push引导信息（引导到直播间）
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void userLoginPush(RankingScoreChanged event, WarfareRegimentComponentAttr attr) {
        if (event.getRankId() != attr.getUserLoginRankId() || event.getPhaseId() != attr.getUserLoginPhaseId()) {
            return;
        }

        Long uid = Long.parseLong(event.getMember().trim());
        //是否要登录记录

        //获取删除
        String groupCode = redisConfigManager.getGroupCode(event.getActId());
        String gotoInfo = actRedisDao.hget(groupCode, makeKey(attr, USER_GOTO_INFO_KEY), uid + "");
        if (StringUtils.isBlank(gotoInfo)) {
            return;
        }
        actRedisDao.hdel(groupCode, makeKey(attr, USER_GOTO_INFO_KEY), uid + "");
        String[] gotoInfoA = gotoInfo.split("_");
        int type = Convert.toInt(gotoInfoA[0]);
        long anchorUid = Convert.toLong(gotoInfoA[1]);
        long sid = Convert.toLong(gotoInfoA[2]);
        long ssid = Convert.toLong(gotoInfoA[3]);
        long ctime = Convert.toLong(gotoInfoA[4]);
        Date now = commonService.getNow(attr.getActId());

        //24小时失效
        if (ctime < DateUtil.add(now, -1).getTime()) {
            log.info("userLoginPush ignore ctime@ uid:{} sid:{} ssid:{} ctime:{} now:{}"
                    , uid, sid, ssid, DateUtil.getPattenStrFromTime(ctime, DateUtil.DEFAULT_PATTERN)
                    , DateUtil.format(now, DateUtil.DEFAULT_PATTERN));
            return;
        }
        //0是加入战力团，已经加入的不弹
        if (type == 0) {
            long warfareRegimentAnchor = getUserWarfareRegimentAnchor(uid, attr);
            if (warfareRegimentAnchor > 0) {
                log.info("userLoginPush ignore add@ uid:{} sid:{} ssid:{} ", uid, sid, ssid);
                return;
            }
        }
        String contest = attr.getGuideTypeTipMap().get(type);
        final String placeholder = "##anchorName##";
        if (contest.contains(placeholder)) {
            Long busiId = webdbThriftClient.getBusiId(sid, ssid);
            UserBaseInfo userBaseInfo = broadCastHelpService.getUserBaseInfo(anchorUid, BusiId.findByValue(busiId.intValue()));
            String anchorName = userBaseInfo.getNick();
            final boolean suc = anchorName.contains("<") && anchorName.contains(">");
            if (suc) {
                anchorName = HtmlUtils.htmlEscape(anchorName);
            }
            contest = contest.replace(placeholder, anchorName);

        }
        pushToZw(attr.getActId(), Long.valueOf(uid), contest, sid, ssid);
        log.info("userLoginPush done@ uid:{} sid:{} ssid:{} contest:{} type:{}", uid, sid, ssid, contest, type);
    }

    /**
     * 主播的战力团人气达到任务梯度
     *
     * @param event
     * @param attr
     */
   /* @HdzjEventHandler(TaskProgressChanged.class)
    public void anchorAccomplishTask(TaskProgressChanged event, WarfareRegimentComponentAttr attr) {
        WarfareRegimentComponentAttr.TaskInfo taskInfo = attr.getAnchorTask();
        if (event.getRankId() != taskInfo.getRankId() || event.getPhaseId() != taskInfo.getPhaseId()) {
            return;
        }
        List<Integer> levels = LongStream.range(event.getStartTaskIndex(), event.getCurrTaskIndex())
                .mapToObj(i -> (int) i + 1).collect(Collectors.toList());

        long awards = levels.stream()
                .map(level -> attr.getAnchorLevelAwardMap().get(level))
                .mapToLong(Long::new)
                .sum();

        String anchorUid = event.getMember().trim();
        actRedisDao.hIncrByKey(makeKey(attr, ANCHOR_LOTTERY_COUNT_KEY), anchorUid, awards);
        log.info("anchorAccomplishTask done@anchorUid:{} levels:{} awards:{}", anchorUid, JSON.toJSONString(levels), awards);
    }*/
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void anchorAccomplishTask(RankingScoreChanged event, WarfareRegimentComponentAttr attr) {
        WarfareRegimentComponentAttr.TaskInfo taskInfo = attr.getAnchorTask();
        if (event.getRankId() != taskInfo.getRankId() || event.getPhaseId() != taskInfo.getPhaseId()) {
            return;
        }
        long nowScore = event.getRankScore();
        long lastScore = nowScore - event.getItemScore();
        long awards = attr.getAnchorScoreAwardMap().entrySet().stream()
                .filter(entry -> entry.getKey() > lastScore && entry.getKey() <= nowScore)
                .mapToInt(Map.Entry::getValue).sum();

        String anchorUid = event.getMember().trim();
        if (awards > 0) {
            String groupCode = redisConfigManager.getGroupCode(event.getActId());
            actRedisDao.hIncrByKey(groupCode, makeKey(attr, ANCHOR_LOTTERY_COUNT_KEY), anchorUid, awards);
        }
        log.info("anchorAccomplishTask done@anchorUid:{} lastScore:{} nowScore:{} awards:{} seq:{}"
                , anchorUid, lastScore, nowScore, awards, event.getSeq());
    }

    /**
     * 用户日任务完成事件
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void userAccomplishTask(TaskProgressChanged event, WarfareRegimentComponentAttr attr) {
        Optional<WarfareRegimentComponentAttr.TaskInfo> taskInfoOptional =
                attr.getUserDailyTasks().stream()
                        .filter(taskInfo -> event.getRankId() == taskInfo.getRankId() && event.getPhaseId() == taskInfo.getPhaseId())
                        .findFirst();
        if (!taskInfoOptional.isPresent()) {
            return;
        }
        long uid = Long.valueOf(event.getMember().trim());
        Date date = DateUtil.getDate(event.getOccurTime(), DateUtil.DEFAULT_PATTERN);
        WarfareRegimentComponentAttr.TaskInfo taskInfo = taskInfoOptional.get();
        String taskKey = getTaskKey(taskInfo, uid, date);
        String groupCode = redisConfigManager.getGroupCode(event.getActId());
        actRedisDao.hset(groupCode, makeKey(attr, USER_TASK_INFO_KEY), taskKey, "1");
        GameecologyActivity.GameEcologyMsg userTipsMsg = getNoticeMsg(attr.getActId(), "zw_520_task_tip", taskInfo.getTaskName());

        String nowDate = DateUtil.getNowYyyyMMddHHmmss();
        //同个用户排队单播，前端的展示时间是5秒
        if(true){
            throw new RuntimeException("lua forbid");
        }
//        delayQueueService.lineUpLocal("userAccomplishTask-" + uid, 6, () -> {
//            svcSDKService.unicastUid(uid, userTipsMsg);
//            log.info("userAccomplishTask daily unicastUid done@uid:{},cTime:{} executeTime:{},data:{}"
//                    , uid, nowDate, DateUtil.getNowYyyyMMddHHmmss(), JsonFormat.printToString(userTipsMsg));
//        });

        int index = attr.getUserDailyTasks().indexOf(taskInfo);
        bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.GAME_ECOLOGY, date.getTime(), uid + "", RoleType.USER, index + 1,
                9, taskInfo.getTaskId());

        log.info("userAccomplishTask daily task done@uid:{} taskInfo:{} taskKey:{}", uid, JSON.toJSONString(taskInfo), taskKey);

        //检查是否完成全部任务
        List<String> taskKeys = attr.getUserDailyTasks().stream().map(task -> getTaskKey(task, uid, date))
                .collect(Collectors.toList());
        taskKeys.add(1, getTaskKey(attr.getUserAddTask(), uid, date));

        List<Object> taskResults = actRedisDao.hmGet(groupCode, makeKey(attr, USER_TASK_INFO_KEY), MyListUtils.toObjectList(taskKeys));

        long unfinishedCount = taskResults.stream().filter(Objects::isNull).count();
        boolean allFinished = false;
        if (unfinishedCount == 0L) {
            String allCompleteTask = getTaskKey(attr.getUserAllCompleteTask(), uid, date);
            allFinished = actRedisDao.hsetnx(groupCode, makeKey(attr, USER_TASK_INFO_KEY), allCompleteTask, "1");

            bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.GAME_ECOLOGY, date.getTime(), uid + "", RoleType.USER, 0
                    , 9, attr.getUserAllCompleteTask().getTaskId());

        }

        log.info("userAccomplishTask allTask done@uid:{} taskInfo:{} unfinishedCount:{} allFinished:{}",
                uid, JSON.toJSONString(taskInfo), unfinishedCount, allFinished);


    }


    /**
     * 查询用户加入战力团的状态
     *
     * @param actId
     * @param uid
     * @return
     */
    public Response queryUserStatus(long actId, long uid) {
        WarfareRegimentComponentAttr attr = getUniqueComponentAttr(actId);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + 1);
        long warfareRegimentAnchor = getUserWarfareRegimentAnchor(uid, attr);
        String anchorNick = "";
        if (warfareRegimentAnchor > 0) {
            anchorNick = broadCastHelpService.getUserBaseInfo(warfareRegimentAnchor, null).getNick();
        }
        return Response.success(ImmutableMap.of(
                "status", warfareRegimentAnchor > 0 ? 1 : 0,
                "anchorUid", warfareRegimentAnchor,
                "anchorNick", anchorNick));
    }

    /**
     * 获取用户加入的战力团，没加返回0
     *
     * @param uid
     * @param attr
     * @return
     */
    private long getUserWarfareRegimentAnchor(long uid, WarfareRegimentComponentAttr attr) {
        String key = makeKey(attr, USER_WARFARE_REGIMENT_STATUS);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String string = actRedisDao.hget(groupCode, key, String.valueOf(uid));
        if (StringUtils.isBlank(string)) {
            return 0L;
        }
        return Long.parseLong(string.split("-")[0]);
    }

    /**
     * 查询用户完成任务信息
     *
     * @param actId
     * @param uid
     * @return
     */
    public Response queryUserTaskInfo(long actId, long uid) {
        WarfareRegimentComponentAttr attr = getUniqueComponentAttr(actId);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + 1);
        Date now = commonService.getNow(actId);

        List<String> taskKeys = attr.getUserDailyTasks().stream().map(task -> getTaskKey(task, uid, now))
                .collect(Collectors.toList());

        taskKeys.add(0, getTaskKey(attr.getUserAddTask(), uid, now));
        taskKeys.add(1, getTaskKey(attr.getUserAllCompleteTask(), uid, now));

        String groupCode = redisConfigManager.getGroupCode(actId);
        List<Object> taskResults = actRedisDao.hmGet(groupCode, makeKey(attr, USER_TASK_INFO_KEY), MyListUtils.toObjectList(taskKeys));

        Map<String, Object> addTaskItem = toTaskItem(attr.getUserAddTask(), taskResults.get(0));
        Map<String, Object> allTaskItem = toTaskItem(attr.getUserAllCompleteTask(), taskResults.get(1));

        List<Map<String, Object>> dailyTaskItem = IntStream.range(0, attr.getUserDailyTasks().size())
                .mapToObj(i -> toTaskItem(attr.getUserDailyTasks().get(i), taskResults.get(i + 2)))
                .collect(Collectors.toList());

        long dailyAllAwardRemain = queryTaskDailyLeft(attr.getUserAllCompleteTask().getAwardTaskId(), now);

        Map<String, Object> userTaskInfo = ImmutableMap.of("dailyTasks", dailyTaskItem, "addTask", addTaskItem,
                "dailyAllTask", allTaskItem, "dailyAllAwardRemain", dailyAllAwardRemain);
        return Response.success(userTaskInfo);

    }

    /**
     * 用户抽奖
     *
     * @param actId
     * @param uid
     * @param taskId
     * @param ip
     * @param hdid
     * @return
     */
    public Response userDrawLottery(long actId, long uid, String taskId, String ip, String hdid) {
        WarfareRegimentComponentAttr attr = getUniqueComponentAttr(actId);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + 1);
        if (!actInfoService.inActTime(actId)) {
            return Response.fail(2, "活动已经结束");
        }
        if (StringUtils.isBlank(hdid)) {
            return Response.fail(2, "操作异常，请重新操作");
        }
        //检验登录
       /* if (!isLogin(uid, attr)) {
            return Response.fail(2, "操作异常，请退出APP重进");
        }*/

        //组织所有任务
        List<WarfareRegimentComponentAttr.TaskInfo> allTaskInfos = Lists.newArrayList(attr.getUserAddTask(), attr.getUserAllCompleteTask());
        allTaskInfos.addAll(attr.getUserDailyTasks());

        //
        Optional<WarfareRegimentComponentAttr.TaskInfo> taskInfoOptional = allTaskInfos.stream()
                .filter(info -> info.getTaskId().equals(taskId.trim()))
                .findFirst();
        if (!taskInfoOptional.isPresent()) {
            throw new ParameterException("taskId error");
        }

        long busiId = 0;
        //每日全部任务完成需要按业务抽奖，要查出业务id
        if (attr.getUserAllCompleteTask().getTaskId().equals(taskId.trim())) {
            busiId = queryUserInTemplate(uid);
            if (busiId == 0 || busiId == BusiId.PEI_WAN.getValue()) {
                log.error("userDrawLottery ignore user busiId not find@,uid:{} taskId:{}", uid, taskId);
                return Response.fail(2, "请到频道内抽取");

            }
        }

        log.info("userDrawLottery info@ taskId:{} uid:{} busiId:{},ip:{} hdid:{}", taskId, uid, busiId, ip, hdid);

        Date now = commonService.getNow(actId);
        String taskKey = getTaskKey(taskInfoOptional.get(), uid, now);
        final boolean suc = checkRisk(uid, 0L, hdid, ip, 2);
        if (!suc) {
            return Response.fail(1, "账号异常，可前往10频道咨询");
        }
        long awardTaskId = taskInfoOptional.get().getAwardTaskId();
        String seq = UUID.randomUUID().toString();
        String userLotteryKey = makeKey(attr, USER_TASK_INFO_KEY);
        long lotteryBusiId = BusiId.GAME_ECOLOGY.getValue();

        Response response = drawTaskLotteryNum(actId, uid, lotteryBusiId, now, awardTaskId, userLotteryKey, taskKey, seq);
        log.info("userDrawLottery done@ uid:{} taskInfo:{}, response:{} seq:{}"
                , uid, JSON.toJSONString(taskInfoOptional.get()), JSON.toJSONString(response), seq);

        long awardPackageId = 0L;
        //抽到奖品
        if (response.getResult() == 0) {
            //判断是否需要分业务在发奖
            awardPackageId = (Long) ((Map<String, Object>) response.getData()).get("packageId");
            Long busiIdPackageId = attr.getBusiIdPackageChangeMap().get(busiId + "-" + awardPackageId);
            if (busiIdPackageId == null) {
                log.warn("userDrawLottery not find change package,uid:{} taskId:{},awardTaskId:{} packageId:{} "
                        , uid, taskId, awardTaskId, awardPackageId);
            } else {
                hdztAwardServiceClient.doWelfare(DateUtil.format(now, DateUtil.DEFAULT_PATTERN), busiId, uid, attr.getChangeTaskId(), 1, busiIdPackageId, seq + "-again");
            }
        }

        int index = allTaskInfos.indexOf(taskInfoOptional.get());
        bigDataService.saveNoRankDataToFile(actId, BusiId.findByValue((int) busiId), now.getTime(), uid + "", RoleType.USER, index
                , 10, hdid, 0, awardPackageId);

        final int code3 = 3;
        if (response.getResult() == code3) {
            return Response.fail(3, "手慢了，奖励已被领完");
        }
        return response;
    }

    /**
     * 判断用户近两天是否在追玩登录过
     *
     * @param uid
     * @param attr
     * @return
     */
    private boolean isLogin(long uid, WarfareRegimentComponentAttr attr) {
        List<QueryRankingRequest> rankingRequests = Lists.newArrayList();

        Date now = commonService.getNow(attr.getActId());
        Date lastDay = DateUtil.add(now, -1);

        for (String day : Lists.newArrayList(DateUtil.format(now, DateUtil.PATTERN_TYPE2), DateUtil.format(lastDay, DateUtil.PATTERN_TYPE2))) {

            QueryRankingRequest rankingRequest = new QueryRankingRequest();
            rankingRequest.setActId(attr.getActId());
            rankingRequest.setRankingId(attr.getUserLoginRankId());
            rankingRequest.setPhaseId(attr.getUserLoginPhaseId());
            rankingRequest.setRankingCount(0);
            rankingRequest.setDateStr(day);
            rankingRequest.setPointedMember(uid + "");
            rankingRequest.setExtData(Maps.newHashMap());
            rankingRequests.add(rankingRequest);
        }
        List<List<Rank>> ranksList = hdztRankingThriftClient.queryBatchRanking(rankingRequests, Maps.newHashMap());
        long loginScore = ranksList.stream().flatMap(Collection::stream).map(Rank::getScore).mapToLong(Long::new).max().getAsLong();
        return loginScore > 0;
    }

    /**
     * 引导用户去追玩等级
     *
     * @param actId
     * @param uid
     * @param anchorUid
     * @param sid
     * @param ssid
     * @param type
     * @return
     */
    public Response gotoZw(long actId, long uid, @NotNull Long anchorUid, Long sid, Long ssid, Integer type) {
        WarfareRegimentComponentAttr attr = getUniqueComponentAttr(actId);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + 1);
        if (!actInfoService.inActTime(actId)) {
            return Response.success("");
        }
        if (sid <= 0 || ssid <= 0) {
            throw new ParameterException("sid or ssid error");
        }

        if (type == 0 && anchorUid == 0L) {
            throw new ParameterException("anchorUid error");
        }
        String tip = attr.getGuideTypeTipMap().get(type);
        if (tip == null) {
            throw new ParameterException("type error");
        }
        Date now = commonService.getNow(actId);
        String userGotoInfo = String.format("%s_%s_%s_%s_%s", type, anchorUid, sid, ssid, now.getTime());
        String groupCode = redisConfigManager.getGroupCode(actId);
        actRedisDao.hset(groupCode, makeKey(attr, USER_GOTO_INFO_KEY), uid + "", userGotoInfo);
        return Response.success("");
    }

    /**
     * 查询主播任务
     *
     * @param actId
     * @param uid
     * @param anchorUid
     * @return
     */
    public Response queryAnchorTask(long actId, long uid, Long anchorUid) {
        WarfareRegimentComponentAttr attr = getUniqueComponentAttr(actId);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + 1);

        WarfareRegimentComponentAttr.TaskInfo anchorTaskInfo = attr.getAnchorTask();
        //查询主播战力团人气
        ActorQueryItem actorQueryItem = new ActorQueryItem();
        actorQueryItem.setActorId(anchorUid + "");
        actorQueryItem.setRoleType(RoleType.ANCHOR.getValue());
        actorQueryItem.setRankingId(anchorTaskInfo.getRankId());
        actorQueryItem.setPhaseId(anchorTaskInfo.getPhaseId());
        actorQueryItem.setWithStatus(false);
        ActorInfoItem actorInfoItem = hdztRankingThriftClient.queryActorRankingInfo(actId, actorQueryItem);

        Assert.notNull(actorInfoItem, "anchor rank info is null,actorQueryItem=" + actorQueryItem.toString());

        long myScore = Math.max(actorInfoItem.getScore(), 0);

        long distance = 0;
        if (actorInfoItem.getRank() != 1) {
            long preScore = Math.max(actorInfoItem.getPreScore(), 0);
            distance = preScore - myScore;
        }
        distance = Math.max(distance, 0);

        List<Map<String, Integer>> targetAwards = ImmutableSortedMap.copyOf(attr.getAnchorScoreAwardMap()).entrySet().stream()
                .map(entry -> ImmutableSortedMap.of("target", entry.getKey(), "award", entry.getValue()))
                .collect(Collectors.toList());

     /*   OnlineChannelInfo onlineChannelInfo = onlineChannelService.get(sid, ssid);
        List<Long> effectAnchorIds = Optional.ofNullable(onlineChannelInfo).map(OnlineChannelInfo::getEffectAnchorId).orElse(Lists.newArrayList());*/
        int status = 0;
        int lotteryNum = 0;
        //是都是主播
        if (anchorUid.equals(uid)) {
            status = 1;
            String groupCode = redisConfigManager.getGroupCode(actId);
            String anchorLotteryNumString = actRedisDao.hget(groupCode, makeKey(attr, ANCHOR_LOTTERY_COUNT_KEY), anchorUid + "");
            lotteryNum = Convert.toInt(anchorLotteryNumString);
        }

        //总抽奖次数
        int totalLotteryNum = attr.getAnchorScoreAwardMap().entrySet().stream()
                .filter(entry -> entry.getKey() <= myScore).mapToInt(Map.Entry::getValue)
                .sum();

        Date now = commonService.getNow(actId);

        long lotteryNumRemain = queryTaskTotalLeft(attr.getAnchorTask().getAwardTaskId(), now);

        JSONObject anchorTask = new JSONObject();
        anchorTask.put("rank", Math.max(actorInfoItem.getRank(), 0));
        anchorTask.put("score", myScore);
        anchorTask.put("distance", distance);
        anchorTask.put("status", status);
        anchorTask.put("lotteryNum", lotteryNum);
        anchorTask.put("totalLotteryNum", totalLotteryNum);
        anchorTask.put("lotteryNumRemain", lotteryNumRemain);
        anchorTask.put("targetAwards", targetAwards);


        return Response.success(anchorTask);
    }

    /**
     * 主播抽奖
     *
     * @param actId
     * @param uid
     * @return
     */
    public Response anchorDrawLottery(long actId, long uid) {

        WarfareRegimentComponentAttr attr = getUniqueComponentAttr(actId);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + 1);
        if (!actInfoService.inActTime(actId)) {
            return Response.fail(2, "活动已经结束");
        }

        Date now = commonService.getNow(actId);
        long lotteryNumRemain = queryTaskTotalLeft(attr.getAnchorTask().getAwardTaskId(), now);
        if (lotteryNumRemain == 0) {
            return Response.fail(3, "手慢了，红包已被领完");
        }

        long awardTaskId = attr.getAnchorTask().getAwardTaskId();
        String seq = UUID.randomUUID().toString();
        String anchorLotteryKey = makeKey(attr, ANCHOR_LOTTERY_COUNT_KEY);
        long lotteryBusiId = BusiId.GAME_ECOLOGY.getValue();

        Response response = drawTaskLotteryNum(actId, uid, lotteryBusiId, now, awardTaskId, anchorLotteryKey, uid + "", seq);
        //达到总限了
        final int code3 = 3;
        if (response.getResult() == code3) {
            //恢复抽奖次数
            //actRedisDao.hIncrByKey(anchorLotteryKey, uid + "", 1);
            return Response.fail(3, "手慢了，红包已被领完");
        }

        log.info("anchorDrawLottery done@ uid:{} response:{} seq:{}", uid, seq, JSON.toJSONString(response));
        return response;
    }

    /**
     * 生成返回给前端的任务信息
     *
     * @param taskInfo
     * @param taskResult
     * @return
     */
    private Map<String, Object> toTaskItem(WarfareRegimentComponentAttr.TaskInfo taskInfo, Object taskResult) {
        int status = taskResult == null ? 0 : Integer.parseInt(taskResult.toString()) != 0 ? 1 : 2;
        return ImmutableMap.of("taskId", taskInfo.getTaskId(), "taskName", taskInfo.getTaskName(), "status", status);
    }

    /**
     * 生成用户的任务task-抽奖次数的member key
     *
     * @param taskInfo
     * @param uid
     * @param now
     * @return
     */
    private String getTaskKey(WarfareRegimentComponentAttr.TaskInfo taskInfo, long uid, Date now) {
        String taskKey = uid + "_" + taskInfo.getTaskId();
        if (StringUtils.isNoneBlank(taskInfo.getDateFormat())) {
            String dateString = DateUtil.format(now, taskInfo.getDateFormat());
            taskKey = taskKey + "_" + dateString;
        }
        return taskKey;
    }


    /**
     * 加入主播战力团 - 控制
     *
     * @param actId
     * @param uid
     * @param anchorUid
     * @param sid
     * @param ssid
     * @param ip
     * @param hdid
     * @return
     */
    public Response addAnchorRegiment(long actId, long uid, Long anchorUid, long sid, long ssid, String ip, String hdid) {
        WarfareRegimentComponentAttr attr = getUniqueComponentAttr(actId);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + 1);

        if (!actInfoService.inActTime(actId)) {
            return Response.fail(2, "活动已结束");
        }

        if (anchorUid == null || anchorUid < 0) {
            return Response.fail(2, "当前无主播在麦，无法加入站力团");
        }
        //主播自己允许在pc端加自己，不做风控
        if (!anchorUid.equals(uid)) {
            if (StringUtils.isBlank(hdid)) {
                log.warn("addAnchorRegiment ignore not hdid@ anchorUid:{} sid:{} ssid:{} uid:{}", anchorUid, sid, ssid, uid);
                return Response.fail(2, "操作异常，请重新操作");
            }

            boolean notRick = checkRisk(uid, anchorUid, hdid, ip, 0);
            if (!notRick) {
                return Response.fail(2, "账号异常，可前往10频道咨询");
            }
        }
        return addAnchorRegiment(actId, uid, anchorUid, sid, ssid, hdid, attr);
    }


    /**
     * 加入主播战力团
     *
     * @param actId
     * @param uid
     * @param anchorUid
     * @param sid
     * @param ssid
     * @param attr
     * @return
     */
    public Response addAnchorRegiment(long actId, long uid, Long anchorUid, long sid, long ssid, String hdid, WarfareRegimentComponentAttr attr) {

        String key = makeKey(attr, USER_WARFARE_REGIMENT_STATUS);
        String groupCode = redisConfigManager.getGroupCode(actId);
        boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), anchorUid + "-" + DateUtil.getNowYyyyMMddHHmmss());
        if (!first) {
            return Response.fail(2, "亲，只能加入一个战力团哦");
        }

        String seq = UUID.randomUUID().toString();
        int i = 0;
        Date now = commonService.getNow(actId);
        long time = now.getTime();
        do {
            i++;
            try {
                //上报数据
                Map<Long, String> actors = new HashMap<>(ImmutableMap.of(80003L, uid + "", 80001L, anchorUid + ""));
                Map<String, Long> actorScores = new HashMap<>(ImmutableMap.of("80001", attr.getAddAward(), "80003", System.currentTimeMillis()));

                UpdateRankingRequest request = new UpdateRankingRequest();
                request.setBusiId(BusiId.ZHUI_WAN.getValue());
                request.setActId(actId);
                request.setSeq(seq);
                request.setActors(actors);
                request.setRoleScores(actorScores);
                request.setItemId("ZW_ZLZ");
                request.setCount(1);
                request.setTimestamp(time);

                UpdateRankingResult result = hdztRankingThriftClient.getProxy().updateRanking(request);
                log.info("addAnchorRegiment updateRanking done@actors:{} score:{} seq:{} result:{} {}",
                        actors, JSON.toJSONString(actorScores), seq, result.getCode(), result.getReason());

                //关注主播-没用
                //yuleAttentionService.setFansFollow(anchorUid, uid, false);

                //子频道广播
                UserBaseInfo userBaseInfo = broadCastHelpService.getUserBaseInfo(uid, null);
                Long busiId = webdbThriftClient.getBusiId(sid, ssid);
                UserBaseInfo anchorBaseInfo = broadCastHelpService.getUserBaseInfo(anchorUid, BusiId.findByValue(busiId.intValue()));
                String addTip = attr.getUserAddAnchorTip();
                String userNick = broadCastHelpService.adjustName(userBaseInfo.getNick(), 10);
                String anchorNick = broadCastHelpService.adjustName(anchorBaseInfo.getNick(), 10);

                addTip = addTip.replace("##userName##", userNick)
                        .replace("##anchorName##", anchorNick);

                //标记用户完成任务
                String taskKey = getTaskKey(attr.getUserAddTask(), uid, now);
                actRedisDao.hset(groupCode, makeKey(attr, USER_TASK_INFO_KEY), taskKey, "1");

                //发送tip
                GameecologyActivity.GameEcologyMsg msg =
                        getNoticeMsg(actId, "user_add_anchor_regiment", addTip);
                svcSDKService.broadcastSub(sid, ssid, msg);

                log.info("addAnchorRegiment bro done@ anchorUid:{} sid:{} ssid:{} addTip:{}", anchorUid, sid, ssid, addTip);

                bigDataService.saveNoRankDataToFile(actId, BusiId.GAME_ECOLOGY, time, uid + "", RoleType.USER, anchorUid, 8, hdid);
                break;
            } catch (Exception e) {
                log.error("addAnchorRegiment updateRanking error,uid:{},seq:{},time:{},try:{}", uid, seq, time, i++, e);
            }

        } while (i <= 3);
        return Response.fail(0, "加入成功!");
    }

    /**
     * 加战力团的测试接口，没有风控
     *
     * @param actId
     * @param uid
     * @param anchorUid
     * @param sid
     * @param ssid
     * @return
     */
    public Response addAnchorRegimentTest(long actId, long uid, Long anchorUid, long sid, long ssid) {
        WarfareRegimentComponentAttr attr = getUniqueComponentAttr(actId);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + 1);

        if (!actInfoService.inActTime(actId)) {
            return Response.fail(2, "活动已结束");
        }
        return addAnchorRegiment(actId, uid, anchorUid, sid, ssid, "", attr);
    }

    private GameecologyActivity.GameEcologyMsg getNoticeMsg(long actId, String type, String noticeMsg) {
        GameecologyActivity.CommonNoticeResponse.Builder tip = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType(type)
                .setNoticeMsg(noticeMsg);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(tip).build();
        return msg;

    }

    /**
     * 抽奖
     *
     * @param actId
     * @param uid
     * @param busiId
     * @param awardTaskId
     * @param hashKey
     * @param memberKey
     * @param seq
     * @return
     */
    public Response drawTaskLotteryNum(long actId, long uid, Long busiId, Date date, long awardTaskId, String hashKey, String memberKey, String seq) {
        Clock clock = new Clock();
        String lockName = "lock-drawTaskLottery-" + hashKey + "-" + uid;
        int second = 10;
        Secret lock = null;
        String groupCode = redisConfigManager.getGroupCode(actId);
        try {
            lock = locker.lock(lockName, second);
            if (lock != null) {

                // 先获取奖品信息
                Map<Long, AwardModelInfo> packageInfoMap = hdztAwardServiceClient.queryAwardTasks(awardTaskId);
                if (CollectionUtils.isEmpty(packageInfoMap)) {
                    throw new SuperException("奖品信息维护中，请稍后重试！", 9999);
                }

                clock.tag();
                if (!deductOneLotteryChance(groupCode, hashKey, memberKey, seq)) {
                    return Response.fail(LotteryException.E_NO_AWARD_CHANCE, "抽奖机会不足");
                }
                clock.tag();
                String time = DateUtil.format(date, DateUtil.DEFAULT_PATTERN);
                BatchLotteryResult result = hdztAwardServiceClient.doLottery(time, busiId, uid, awardTaskId, 1, 0, seq);
                log.info("drawTaskLotteryNum done@, actId:{} busiId:{} seq:{} uid:{}, result:{} {}", actId, busiId, seq, uid, result, clock.tag());

                int code = Optional.ofNullable(result).map(BatchLotteryResult::getCode).orElse(-1);
                // 抽奖完整成功
                final int code5299 = 5299, code6000 = 6000;
                if (code == 0) {
                    // 是单抽，只有唯一结果
                    Map<Long, Long> recordIds = result.getRecordIds();
                    Map.Entry<Long, Long> next = recordIds.entrySet().iterator().next();
                    //packgeId 抽奖抽到的包Id
                    Long packageId = next.getKey();
                    String packageName = packageInfoMap.get(packageId).getPackageName();
                    String packageImage = packageInfoMap.get(packageId).getPackageImage();
                    long lotteryNum = Convert.toLong(actRedisDao.hget(groupCode, hashKey, memberKey));
                    return Response.success(ImmutableMap.of("packageId", packageId, "packageName", packageName, "packageImage", packageImage, "lotteryNum", lotteryNum));
                }
                //未抽中
                else if (code == code5299) {
                    return Response.fail(3, "未抽中奖品");
                } else if (code == code6000) {
                    return Response.fail(4, "本次抽发奖已经完成");
                } else {
                    return Response.fail(5, "未抽中奖品");
                }
            } else {
                return Response.fail(2, "请不要点击太快！");
            }
        } catch (Exception e) {
            log.info("drawTaskLotteryNum error@seq:{}, actId:{} busiId:{} uid:{}, {} {}", actId, busiId, seq, uid, clock.tag(), e.getMessage(), e);
            return Response.fail(2, "当前人数太多，请稍后再试！");
        } finally {
            if (lock != null) {
                locker.unlock(lockName, lock);
            }
        }
    }

    /**
     * 抽奖扣费
     *
     * @param hashKey
     * @param memberKey
     * @param seq
     * @return
     */
    private boolean deductOneLotteryChance(String groupCode, String hashKey, String memberKey, String seq) {
        Clock clock = new Clock();
        List<Long> chanceResult;
        //List<Long> chanceResult = actRedisDao.hIncrWithLimit(groupCode, hashKey, memberKey, -1, 0);
        if (true) {
            throw new RuntimeException("hash_incr_with_limit.lua,lua forbid");
        }
        log.info("drawTaskLotteryNum deduct chance done@seq:{}, hashKey:{}, memberKey:{}, lotteryChance:{} {}", seq, hashKey, memberKey, JSON.toJSONString(chanceResult), clock.tag());
        return chanceResult.get(0) == 1L;
    }


    /**
     * push 到追玩，sid或ssid为0不需要跳转 --老郭帮忙实现
     *
     * @param actId
     * @param uid
     * @param content
     * @param sid
     * @param ssid
     */
    public void pushToZw(long actId, long uid, String content, long sid, long ssid) {
        /*
        跳转说明：https://doc.yy.com/pages/viewpage.action?pageId=56655898 - 也可咨询 李勇

        陈振宇 提供：
            交友房 - MARK_FRIENDS = 3,
            约战 - PK_CHANNEL = 5,
            宝贝 - BABY_CHANNEL = 6;
         */
        // 先提取房间类型， 若 sid、ssid 不合法，则模板类型为 -1 （无效）
        long id = (sid > 0 && ssid > 0) ? webdbThriftClient.getBusiId(sid, ssid) : -1;
        BusiId busiId = BusiId.findByValue((int) id);
        int type = -1;
        if (busiId != null) {
            switch (busiId) {
                case MAKE_FRIEND:
                    type = 3;
                    break;
                case YUE_ZHAN:
                    type = 5;
                    break;
                case GAME_BABY:
                    type = 6;
                    break;
                default:
                    type = 3;
            }
        }

        // 咨询 追玩 android 的 熊峰 得到的
        String tpl = "zhuiwan://channel/live/%s/%s/%s";
        String push_link = (type == -1) ? "" : String.format(tpl, type, sid, ssid);
        String seq = UUID.randomUUID().toString();

        PopupMsg popupMsg = new PopupMsg();
        popupMsg.setPlatform(3);
        popupMsg.setContent(content);
        popupMsg.setLink(push_link);
        popupMsg.setTitle(commonService.getActAttr(actId, "push_title"));
        popupMsg.setIcon(commonService.getActAttr(actId, "push_icon"));
        popupMsg.setBackground(commonService.getActAttr(actId, "push_back"));

        try {
            BaseRsp baseRsp = zhuiWanPrizeIssueServiceClient.getProxy().sendPopupMessage(seq, uid, popupMsg);
            log.info("pushToZw done@actId:{}, popupMsg:{}, baseRsp:{}", actId, popupMsg, baseRsp);
        } catch (TException e) {
            log.error("pushToZw exception@actId:{}, popupMsg:{}, err:{}", actId, popupMsg, e.getMessage(), e);
        }
    }

    /**
     * 风控检查
     *
     * @param uid
     * @param anchorUid
     * @param hdid      设备id
     * @param ip
     * @param type      0=加入主播战力团，2=抽奖
     * @return
     */
    public boolean checkRisk(long uid, long anchorUid, String hdid, String ip, int type) {

        return type == 0 ?
                // 用户app端参团
                antiCheatGnService.check2021520ActJoinTeam(uid, anchorUid, ip, hdid) :
                // 用户完成所有任务抽奖
                antiCheatGnService.check2021520ActLuky(uid, ip, hdid);

    }

    /**
     * -1是不限制
     *
     * @param taskId
     * @param data
     * @return
     */
    private long queryTaskDailyLeft(Long taskId, Date data) {

        Map<Long, PackageLeftQuota> quotas = hdztAwardServiceClient.getTaskLeftQuota(taskId, data);
        Assert.notNull(quotas, "queryTaskDailyLeft error");

        long count = quotas.values().stream().mapToLong(PackageLeftQuota::getDailyLeft)
                .filter(dailyLeft -> dailyLeft >= 0).count();
        if (count == 0) {
            return -1L;
        }
        long left = quotas.values().stream().mapToLong(PackageLeftQuota::getDailyLeft)
                .filter(dailyLeft -> dailyLeft >= 0).sum();
        return left;
    }

    private long queryTaskTotalLeft(Long taskId, Date data) {

        Map<Long, PackageLeftQuota> quotas = hdztAwardServiceClient.getTaskLeftQuota(taskId, data);
        Assert.notNull(quotas, "queryTaskDailyLeft error");

        long count = quotas.values().stream().mapToLong(PackageLeftQuota::getTotalLeft)
                .filter(left -> left >= 0).count();
        if (count == 0) {
            return -1L;
        }
        long left = quotas.values().stream().mapToLong(PackageLeftQuota::getTotalLeft)
                .filter(totalLeft -> totalLeft >= 0).sum();
        return left;
    }

    public void test(long actId, long uid, String msg) {
        GameecologyActivity.CommonNoticeResponse.Builder tip = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType("zw_520_task_tip")
                .setNoticeMsg(msg);

        GameecologyActivity.GameEcologyMsg userTipsMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(tip).build();
        svcSDKService.unicastUid(uid, userTipsMsg);
    }

    private long queryUserInTemplate(long uid) {
        int i = 0;
        do {
            i++;
            ChannelInfoVo channelInfoVo = culClient.queryUserChannel(uid);
            if (channelInfoVo == null) {
                log.error("queryUserInTemplate error channer null@uid:{}", uid);
                continue;
            }
            return webdbThriftClient.getBusiIdNocahe(channelInfoVo.getSid(), channelInfoVo.getSsid());
        } while (i <= 3);
        return 0L;
    }


}
