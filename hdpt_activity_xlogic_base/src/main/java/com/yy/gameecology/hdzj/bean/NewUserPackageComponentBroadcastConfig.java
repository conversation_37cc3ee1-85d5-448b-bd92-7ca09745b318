package com.yy.gameecology.hdzj.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;

public class NewUserPackageComponentBroadcastConfig {

    @ComponentAttrField(labelText = "业务活动开始时间")
    private long startTime;

    @ComponentAttrField(labelText = "业务活动结束时间")
    private long endTime;

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }
}
