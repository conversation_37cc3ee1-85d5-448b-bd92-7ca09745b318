package com.yy.gameecology.hdzj.element.component.attr;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.bean.GiftToChance;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;

import java.util.Date;
import java.util.Map;

/**
 * 手动抽奖属性对象
 *
 * <AUTHOR>
 * @date 2022/03/30 17:42
 */
public class ManualLotteryComponentAttr extends ComponentAttr {

    // 抽奖错误尝试的次数，总共最多调用 1 + retry 次， 让抽奖尽量成功
    @ComponentAttrField(labelText = "抽奖尝试次数", remark = "抽奖错误尝试的次数，总共最多调用 1 + retry 次， 让抽奖尽量成功")
    private int retry = 2;

    // 是否进行连送累计，true：按连送累计， false：单次送礼的价值， 默认 false
    @ComponentAttrField(labelText = "是否进行连送累计", remark = "true：按连送累计， false：单次送礼的价值")
    private boolean combo = false;

    // 使用此组件的模版， 当外部事件中的 模板值 和 此处不同，会忽略其事件处理
    @ComponentAttrField(labelText = "使用此组件的模版", dropDownSourceBeanClass = TemplateSource.class)
    private int template = 0;

    // 挂靠的业务ID，抽奖时需要这个参数
    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    private long busiId = 200;

    /**
     * 允许抽奖的结束时间，不设置以活动时间为准，可以比活动开始时间晚
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ComponentAttrField(labelText = "抽奖的结束时间", remark = "不设置以活动时间为准，可以比活动开始时间晚")
    private Date endDate;

    // 礼物的抽奖次数换算Map，key：礼物id， GiftToChance.cost: 礼物数量， GiftToChance.chance: 抽奖机会数
    @ComponentAttrField(labelText = "礼物的抽奖次数配置"
            , subFields = {
            @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "礼物id"),
            @SubField(fieldName = Constant.VALUE, type = GiftToChance.class)
    })
    private Map<String, GiftToChance> giftToChanceMap = Maps.newHashMap();

    // 奖箱的抽奖次数换算Map，key: 奖箱ID， value：每次需要的抽奖机会次数
    @ComponentAttrField(labelText = "奖箱的抽奖次数配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "奖箱ID"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "每次需要的抽奖机会次数")
            })
    private Map<Integer, Integer> drawCostMap = Maps.newHashMap();

    // 抽奖机会的名字（具体活动命名的名字）
    @ComponentAttrField(labelText = "抽奖机会的名字", remark = "具体活动命名的名字")
    private String chanceName = "抽奖机会";

    // 外部抽奖机会奖项标识，务必特殊命名以便精确匹配！
    @ComponentAttrField(labelText = "外部抽奖机会奖项标识", remark = "务必特殊命名以便精确匹配")
    private String awardChanceCode = "";

    public int getRetry() {
        return retry;
    }

    public void setRetry(int retry) {
        this.retry = retry;
    }

    public boolean isCombo() {
        return combo;
    }

    public void setCombo(boolean combo) {
        this.combo = combo;
    }

    public int getTemplate() {
        return template;
    }

    public void setTemplate(int template) {
        this.template = template;
    }

    public long getBusiId() {
        return busiId;
    }

    public void setBusiId(long busiId) {
        this.busiId = busiId;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Map<String, GiftToChance> getGiftToChanceMap() {
        return giftToChanceMap;
    }

    public void setGiftToChanceMap(Map<String, GiftToChance> giftToChanceMap) {
        this.giftToChanceMap = giftToChanceMap;
    }

    public Map<Integer, Integer> getDrawCostMap() {
        return drawCostMap;
    }

    public void setDrawCostMap(Map<Integer, Integer> drawCostMap) {
        this.drawCostMap = drawCostMap;
    }

    public String getChanceName() {
        return chanceName;
    }

    public void setChanceName(String chanceName) {
        this.chanceName = chanceName;
    }

    public String getAwardChanceCode() {
        return awardChanceCode;
    }

    public void setAwardChanceCode(String awardChanceCode) {
        this.awardChanceCode = awardChanceCode;
    }
}
