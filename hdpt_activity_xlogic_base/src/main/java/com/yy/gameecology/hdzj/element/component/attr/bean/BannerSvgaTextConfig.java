package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-05-31 11:40
 **/
@Data
public class BannerSvgaTextConfig {

   @ComponentAttrField(labelText = "svga key")
   private String key;
   /**
    * 富文本消息，html格式
    * {uid:n} ：表示需要nick替换
    * {uid:an}：表示需要头像icon+nick
    * {img}：表示需要使用imgs list按顺序取出替换
    */
   @ComponentAttrField(labelText = "富文本消息", remark = "恭喜<font color=\\'#FF9022\\'> {uid:n} </font>获得<font color=\\'#FF9022\\'> 礼物名称X礼物数量</font>")
   private String text;

   @ComponentAttrField(labelText = "文本中内嵌图片,多张逗号隔开", remark = "配置示例： www.ddd.ccc.png,www.ccc.png")
   private String images;

   @ComponentAttrField(labelText = "字体大小", remark = "字体大小。\n不填，Android默认8，iOS默认24。\n安卓的单位是dp，ios的单位是px。\n安卓的要在ios的基础上除以3或者除以2，如果不行就找客户端同事。\n注意：小横幅时，该字段不起作用。配置示例： {\"android\":8,\"ios\":24}")
   private String fontSize;

   @ComponentAttrField(labelText = "文本内容位置", remark = "0：居中；1：靠左；2：靠右")
   private int gravity;

   @ComponentAttrField(labelText = "昵称最长长度", remark = "昵称最长长度 默认5个")
   private int nameCountLimit = 5;
}
