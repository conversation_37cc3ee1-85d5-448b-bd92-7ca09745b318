package com.yy.gameecology.hdzj.utils;

import com.yy.gameecology.common.utils.RequestUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.protocol.pb.zhuiwan.common.ZhuiyaPbCommon;
import com.yy.protocol.pb.zhuiwan.common.ZhuiyaPbCommon.*;
import com.yy.protocol.pb.zhuiwan.risk.ZhuiyaRisk;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2024/1/17 17:56
 * @Modified:
 */
public class ZhuiyaClientUtils {
    public static App toApp(String app) {
        if (app == null) {
            return App.APP_ZHUIWAN;
        }
        return switch (app) {
            case "yomi" -> App.APP_YOMI;
            case "pcyy" -> App.APP_PCYY;
            default -> App.APP_ZHUIWAN;
        };
    }

    public static Platform toPlatform(Integer platform) {
        if (platform == null) {
            return Platform.PLATFORM_UNKNOWN;
        }
        return switch (platform) {
            case 1 -> Platform.PLATFORM_ANDROID;
            case 2 -> Platform.PLATFORM_IOS;
            case 3 -> Platform.PLATFORM_PC;
            case 4 -> Platform.PLATFORM_H5;
            default -> Platform.PLATFORM_UNKNOWN;
        };
    }

    public static Client getClient(HttpServletRequest request) {

        String hostName = request.getHeader("x-fts-host-name");
        if (hostName == null) {
            String app = request.getParameter("app");
            if (StringUtils.isNotEmpty(app)) {
                hostName = app;
            }
        }
        // hostName=dreamer(代表追玩)
        String hdid = request.getHeader("YYHeader-y0");
        String pkg = request.getHeader("YYHeader-Market");
        int platform = Integer.parseInt(Optional.ofNullable(request.getHeader("YYHeader-Platform")).orElse("0"));
        String version = request.getHeader("YYHeader-Version");

        String realIp = RequestUtil.getRealIp(request);

        return Client.newBuilder()
                .setApp(toApp(hostName))
                .setHdid(StringUtil.trim(hdid))
                .setIp(StringUtil.trim(realIp))
                .setPlatform(toPlatform(platform))
                .setPkg(StringUtil.trim(pkg))
                .setVersion(StringUtil.trim(version))
                .build();
    }

    public static Client toClient(String hdid, String ip, String app, Integer clientType, String pkg, String version) {
        return Client.newBuilder()
                .setApp(toApp(app))
                .setHdid(StringUtil.trim(hdid))
                .setIp(StringUtil.trim(ip))
                .setPlatform(toPlatform(clientType))
                .setPkg(StringUtil.trim(pkg))
                .setVersion(StringUtil.trim(version))
                .build();
    }

    public static ZhuiyaRisk.Client toRiskClient(Client client) {
        return ZhuiyaRisk.Client.newBuilder()
                .setAppValue(client.getAppValue())
                .setPlatformValue(client.getPlatformValue())
                .setIp(StringUtil.trim(client.getIp()))
                .setHdid(StringUtil.trim(client.getHdid()))
                .setPkg(StringUtil.trim(client.getPkg()))
                .setVersion(StringUtil.trim(client.getVersion()))
                .build();
    }

    public static Integer getUsedChannel(ZhuiyaPbCommon.Client client) {
        return getUsedChannel(client.getApp(), client.getPlatform());

    }

    public static Integer getUsedChannel(String app, Integer clientType) {
        return getUsedChannel(toApp(app), toPlatform(clientType));
    }

    public static Integer getUsedChannel(App app, Platform platform) {
        Integer usedChannel = null;

        if (app == App.APP_PCYY) {
            //0 pc
            usedChannel = 0;
        } else if (app == App.APP_YOMI) {
            usedChannel = switch (platform) {
                //171 Yomi语音房 IOS
                case PLATFORM_IOS -> 171;
                //172 Yomi语音房 Android
                case PLATFORM_ANDROID -> 172;
                //10015 语音房 H5（新） - 移动端内页面
                case PLATFORM_H5 -> 10015;
                //  192	语音房 WEB -浏览器页面
                default -> null;
            };

        } else if (app == App.APP_ZHUIWAN) {
            usedChannel = switch (platform) {
                //158 Yomi交友 Android
                case PLATFORM_IOS -> 157;
                //158 Yomi交友 Android
                case PLATFORM_ANDROID -> 158;
                default -> null;
            };
        }

        return usedChannel;
    }


}
