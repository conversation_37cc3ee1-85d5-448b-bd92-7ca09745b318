package com.yy.gameecology.hdzj.element.redis;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableSortedMap;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.JiaoyouComboEndEvent;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.exception.BadRequestException;
import com.yy.gameecology.activity.exception.ParameterException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.JySendGiftLotteryComponentAttr;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztranking.Rank;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.IntStream;

/**
 * 必中逻辑增加假数据：
 * <p>
 * https://test-activity-ge.yy.com/testTool/sendGiftLottery/setJYSendGiftRank?actId=2022091001&index=500&mustCount=50&day=20220512
 *
 * @author: yulianzhu
 * @date: 2022.04.11
 * @description:
 */
@UseRedisStore
@Component
public class JySendGiftLotteryComponent extends BaseActComponent<JySendGiftLotteryComponentAttr> {

    private static final int MUST_1 = 1;

    private static final int MUST_2 = 2;

    private static final int MUST_3 = 3;

    /**
     * 保存用户送礼触发抽奖的顺序
     */
    private static final String USER_SEND_GIFT_RANK = "gift_combo_user_send_gift_rank";

    @Override
    public Long getComponentId() {
        return ComponentId.JY_SEND_GIFT_LOTTERY;
    }

    @HdzjEventHandler(value = JiaoyouComboEndEvent.class, canRetry = false)
    public void handleJiaoyouComboEndEvent(JiaoyouComboEndEvent event, JySendGiftLotteryComponentAttr attr) {
        Clock clock = new Clock();
        if (SysEvHelper.isLocal()) {
            return;
        }
        long actId = attr.getActId();
        if (!actInfoService.inActTime(actId)) {
            //不在时间内
            log.info("sendGiftLottery info not in activity time event = {}", JSON.toJSONString(event));
            return;
        }

        //总价值
        String giftId = MapUtils.getString(event.getExpand(), "propId");
        long giftNum = MapUtils.getLongValue(event.getExpand(), "count");
        //非活动礼物
        if (!attr.getGiftIds().contains(giftId)) {
            return;
        }
        log.info("handleJiaoyouComboEndEvent event:{}", JSON.toJSONString(event));

        Integer giftValue = attr.getGiftValueMap().get(giftId);
        Map.Entry<Long, Integer> boxType = ImmutableSortedMap.copyOf(attr.getValueBoxTypeMap()).floorEntry(giftValue * giftNum);
        if (boxType == null) {
            log.info("jy sendGiftLottery ignore@giftId:{} giftNum:{} seq:{},attr:{}", giftId, giftNum, event.getSeqId(), JSON.toJSONString(attr));
            return;
        }
        lottery(attr, event);
    }

    private void lottery(JySendGiftLotteryComponentAttr attr, JiaoyouComboEndEvent event) {
        Long userUid = event.getSendUid();
        //总价值
        String giftId = MapUtils.getString(event.getExpand(), "propId");
        int combo = MapUtils.getIntValue(event.getExpand(), "combo");
        long giftNum = MapUtils.getLongValue(event.getExpand(), "count");

        long time = System.currentTimeMillis();
        Integer giftValue = attr.getGiftValueMap().get(giftId);

        long giftTotalValue = giftValue * giftNum * combo;
        Map.Entry<Long, Integer> boxType = ImmutableSortedMap.copyOf(attr.getValueBoxTypeMap()).floorEntry(giftTotalValue);
        long taskId = attr.getBoxTypeTaskIdMap().get(boxType.getValue());
        Assert.isTrue(taskId > 0, "config error,cat find taskId,boxType=" + boxType);
        long lotteryCount = (giftTotalValue / boxType.getKey());
        //获取必中
        long bingo = 0;
        if (ArrayUtils.contains(attr.getMustWinBoxType(), boxType.getValue())) {
            log.info("try get bingo");
            bingo = getMustWinPackageId(attr, userUid, time);
        }

        //调抽奖接口
        String seq = event.getSeqId() + userUid;
        BatchLotteryResult result = hdztAwardServiceClient.doLottery(DateUtil.getNowYyyyMMddHHmmss(), 500, userUid, taskId, (int) lotteryCount, bingo, seq);
        log.info("lottery done@ uid:{} giftId:{} giftNum:{} lotteryCount:{} taskId:{} bingo:{} seq:{}, result:{}",
                userUid, giftId, giftNum * combo, lotteryCount, taskId, bingo, seq, result.toString());
        //广播由组件AwardUnicastComponent 完成
    }

    /**
     * 必中逻辑
     *
     * @param attr
     * @param uid
     * @param time
     * @return
     */
    private long getMustWinPackageId(JySendGiftLotteryComponentAttr attr, long uid, Long time) {

        long actId = attr.getActId();
        int mustWin = attr.getMustWin();
        //按送礼顺序必中
        if (mustWin == MUST_1 || mustWin == MUST_2) {
            //榜单排名限制
            if (attr.getLimitRank() > 0) {
                Assert.isTrue(attr.getMustWinRankId() > 0, "mustWinRankId error");
                Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(actId, attr.getMustWinRankId(), attr.getMustWinPhaseId(), "", uid + "", Maps.newHashMap());
                //排名之外
                if (rank.getRank() < 1 || rank.getRank() > attr.getLimitRank()) {
                    return 0L;
                }
            }
            //获取用户送礼排名
            String day = DateUtil.getPattenStrFromTime(time, DateUtil.PATTERN_TYPE2);
            //1是日排名
            String userSendGiftRankKeyName = mustWin == 1 ? USER_SEND_GIFT_RANK + "_" + day : USER_SEND_GIFT_RANK;
            String userSendGiftRankKey = makeKey(attr, userSendGiftRankKeyName);
            if(true){
                throw new RuntimeException("如果次组件要重新启用，则需要改造此处 去除lua");
            }
            DefaultRedisScript<Long> script = new DefaultRedisScript<>();
            script.setScriptText("if tonumber(redis.call('sadd', KEYS[1], ARGV[1])) == 1 then\n return redis.call('scard', KEYS[1])\n end");
            script.setResultType(Long.class);
            String groupCode = redisConfigManager.getGroupCode(actId);
            Long card = actRedisDao.getRedisTemplate(groupCode).execute(script, Arrays.asList(userSendGiftRankKey), String.valueOf(uid));
            int rank = Optional.ofNullable(card).map(Long::intValue).orElse(-1);


            return attr.getRankMustWinMap().getOrDefault(rank, 0L);

        } else if (mustWin == MUST_3) {
            Assert.isTrue(attr.getMustWinRankId() > 0, "mustWinRankId error");
            int rank = queryUserRank(attr, uid);
            return attr.getRankMustWinMap().getOrDefault(rank, 0L);
        }
        return 0L;
    }

    /**
     * 设置送礼顺序排名，预设前 mustCount-1个空位 ，测试接口，不允许生产访问
     *
     * @param actId
     * @param index
     * @param mustCount
     * @param day
     * @return
     */
    public Response setSendGiftRank(long actId, long index, int mustCount, String day) {
        if (!isMyDuty(actId)) {
            throw new BadRequestException("无效活动");
        }
        JySendGiftLotteryComponentAttr attr = getComponentAttr(actId, index);
        if (attr == null) {
            throw new ParameterException("index error");
        }
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            throw new BadRequestException("活动状态异常");
        }
        int mustWin = attr.getMustWin();
        if (mustWin != MUST_1 && mustWin != MUST_2) {
            throw new ParameterException("非送礼顺序必中");
        }
        String cmptTitle = getHdzjComponent(actId, index).getCmptTitle();
        if (StringUtils.isBlank(day) && mustWin == 1) {
            day = DateUtil.getPattenStrFromTime(System.currentTimeMillis(), DateUtil.PATTERN_TYPE2);
        }
        String userSendGiftRankKeyName = mustWin == 1 ? USER_SEND_GIFT_RANK + "_" + day : USER_SEND_GIFT_RANK;
        String userSendGiftRankKey = makeKey(attr, userSendGiftRankKeyName);
        String groupCode = redisConfigManager.getGroupCode(actId);
        actRedisDao.del(groupCode, userSendGiftRankKey);
        if (mustCount > 1) {
            String[] members = IntStream.range(1, mustCount).mapToObj(String::valueOf).toArray(String[]::new);
            actRedisDao.getRedisTemplate(groupCode).opsForSet().add(userSendGiftRankKey, members);
        }
        Set<String> memberSet = actRedisDao.sMembers(groupCode, userSendGiftRankKey);
        return Response.success(ImmutableSortedMap.of("title", cmptTitle, "members", memberSet));
    }

    /**
     * @param actId
     * @param index
     * @param day
     * @return
     */
    public Response getSendGiftRank(long actId, long index, String day) {
        if (!isMyDuty(actId)) {
            throw new BadRequestException("无效活动");
        }
        JySendGiftLotteryComponentAttr attr = getComponentAttr(actId, index);
        if (attr == null) {
            throw new ParameterException("index error");
        }
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            throw new BadRequestException("活动状态异常");
        }
        int mustWin = attr.getMustWin();
        if (mustWin != MUST_1 && mustWin != MUST_2) {
            throw new ParameterException("非送礼顺序必中");
        }
        String cmptTitle = getHdzjComponent(actId, index).getCmptTitle();
        if (StringUtils.isBlank(day) && mustWin == 1) {
            day = DateUtil.getPattenStrFromTime(System.currentTimeMillis(), DateUtil.PATTERN_TYPE2);
        }
        String userSendGiftRankKeyName = mustWin == 1 ? USER_SEND_GIFT_RANK + "_" + day : USER_SEND_GIFT_RANK;
        String userSendGiftRankKey = makeKey(attr, userSendGiftRankKeyName);
        String groupCode = redisConfigManager.getGroupCode(actId);

        Set<String> memberSet = actRedisDao.sMembers(groupCode, userSendGiftRankKey);
        return Response.success(ImmutableSortedMap.of("title", cmptTitle, "members", memberSet));
    }

    private int queryUserRank(JySendGiftLotteryComponentAttr attr, long uid) {
        long actId = attr.getActId();
        Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(actId, attr.getMustWinRankId(), attr.getMustWinPhaseId(), "", uid + "", Maps.newHashMap());
        return rank.getRank();
    }

}
