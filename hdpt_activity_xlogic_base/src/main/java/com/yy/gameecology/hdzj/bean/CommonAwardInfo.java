package com.yy.gameecology.hdzj.bean;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.utils.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
public class CommonAwardInfo {
    // yyyy-MM-dd HH:mm:ss 格式的时间戳
    private String time;
    private long uid;
    private long busiId;
    private String seq;
    private long taskId;
    private long packageId;
    private long amount;
    private String utime;

    public CommonAwardInfo(){
    }

    public CommonAwardInfo(String time, long uid, long busiId, String seq, long taskId, long packageId, long amount){
        this.time = time;
        this.uid = uid;
        this.busiId = busiId;
        this.seq = seq;
        this.taskId = taskId;
        this.packageId = packageId;
        this.amount = amount;
        this.utime = time;
    }

    public long getSeconds() {
        Date date = DateUtil.getDate(time);
        return DateUtil.getSeconds(date);
    }

    public String toString() {
        return JSON.toJSONString(this);
    }

    /**
     * 提取简要信息
     */
    public String outline() {
        return String.format("SuperWinnerAward[seq:%s, busiId:%s, uid:%s, time:%s, %s %s %s]",
                seq, busiId, uid, time, taskId, packageId, amount);
    }
}
