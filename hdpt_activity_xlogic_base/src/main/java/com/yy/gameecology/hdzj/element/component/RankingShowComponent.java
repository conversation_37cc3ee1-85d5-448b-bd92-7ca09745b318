package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.RankShowConfigItem;
import com.yy.gameecology.activity.bean.RankShowConfigVo;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.HdztPhaseConfigService;
import com.yy.gameecology.activity.service.HdztRankService;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.db.model.gameecology.ActShowRankConfig;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.RankingShowComponentAttr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * desc:榜单展示配置组件
 * <p>
 * 应用场景：
 * 1、交友右侧公屏榜单tab配置
 *
 * @createBy 曾文帜
 * @create 2021-06-29 20:39
 **/
@Component
public class RankingShowComponent extends BaseActComponent<RankingShowComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztRankService hdztRankService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private HdztPhaseConfigService hdztPhaseConfigService;

    @Override
    public Long getComponentId() {
        return ComponentId.RANKING_SHOW;
    }

    @Report
    @Cached(timeToLiveMillis = 5 * 1000)
    public List<RankShowConfigVo> getEffectShowRankConfig(Long actId, Long componentIndex) {
        return buildRankShowConfig(actId, componentIndex);
    }


    private List<RankShowConfigVo> buildRankShowConfig(Long actId, Long componentIndex) {
        List<RankShowConfigVo> vos = Lists.newArrayList();

        Date now = commonService.getNow(actId);
        List<ActShowRankConfig> configs = hdztRankService.getEffectShowRankConfig(actId, now);
        for (ActShowRankConfig config : configs) {
            RankShowConfigVo vo = new RankShowConfigVo();
            vo.setId(config.getId());
            vo.setName(config.getName());
            vo.setRemark(config.getRemark());
            vo.setExt(config.getExt());

            Map<String, String> paraMap = Maps.newHashMap();
            paraMap.put("actId", actId + "");
            List<RankShowConfigItem> items = JSON.parseArray(config.getConfig(), RankShowConfigItem.class);
            for (RankShowConfigItem item : items) {
                //---解析url参数
                //actId=${actId}&rankId=${rankId}&phaseId=${phaseId}&dateStr=${dateStr}&useNewWay=1&showType=1&&rankCount=10
                paraMap.put("rankId", item.getRankId() + "");

                //自动读取阶段
                Long phaseId = hdztPhaseConfigService.getRankCurPhaseId(actId, item.getRankId());
                paraMap.put("phaseId", phaseId + "");

                //日榜、小时榜的时候自动解析参数
                String dateStr = "";
                if (StringUtil.isNotBlank(item.getDateStrFormat())) {
                    dateStr = DateUtil.format(now, item.getDateStrFormat());
                }
                paraMap.put("dateStr", dateStr);

                String para = replacePara(item.getPara(), paraMap);
                item.setPara(para);
            }
            vo.setItems(items);

            vos.add(vo);
        }


        return vos;
    }


    private String replacePara(String para, Map<String, String> paraMap) {
        for (String key : paraMap.keySet()) {
            para = para.replace(String.format("${%s}", key), paraMap.get(key));
        }
        return para;
    }

}
