package com.yy.gameecology.hdzj.element.history.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021.09.18 18:31
 */
public class UserTaskBrocastComponentAttr extends ComponentAttr {
    /**
     * 任务等级需要广播的类型 广播类型 2=子频道，3=顶级频道，4=单业务广播；5=多业务广播
     */
    @ComponentAttrField(labelText = "广播类型",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "任务等级"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "广播等级", remark = "2=子频道，3=顶级频道，4=单业务广播；5=多业务广播")
            })
    private Map<Integer, Integer> levelBroTypeMap = Maps.newHashMap();

    @ComponentAttrField(labelText = "榜单id列表", remark = "多个时逗号分隔", subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private List<Long> rankIds;

    /**
     * 等级对应的分值
     */
    @ComponentAttrField(labelText = "等级对应的分值",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "榜单id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "任务等级"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "分数")
            })
    private Map<Long, Map<Long, Long>> level2score;
    /**
     * 获取过关的道具ID
     */
    @ComponentAttrField(labelText = "道具ID")
    private String itemId;
    @ComponentAttrField(labelText = "svga地址")
    private String svgaUrl;

    public Map<Long, Map<Long, Long>> getLevel2score() {
        return level2score;
    }

    public void setLevel2score(Map<Long, Map<Long, Long>> level2score) {
        this.level2score = level2score;
    }

    public String getSvgaUrl() {
        return svgaUrl;
    }

    public void setSvgaUrl(String svgaUrl) {
        this.svgaUrl = svgaUrl;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public Map<Integer, Integer> getLevelBroTypeMap() {
        return levelBroTypeMap;
    }

    public void setLevelBroTypeMap(Map<Integer, Integer> levelBroTypeMap) {
        this.levelBroTypeMap = levelBroTypeMap;
    }

    public List<Long> getRankIds() {
        return rankIds;
    }

    public void setRankIds(List<Long> rankIds) {
        this.rankIds = rankIds;
    }
}
