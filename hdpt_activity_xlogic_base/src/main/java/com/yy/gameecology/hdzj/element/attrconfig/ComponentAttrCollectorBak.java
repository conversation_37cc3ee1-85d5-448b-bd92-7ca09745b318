//package com.yy.gameecology.hdzj.element.attrconfig;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.collect.Maps;
//import com.google.common.collect.Sets;
//import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
//import com.yy.gameecology.common.db.model.gameecology.HdzjComponentAttrDefine;
//import com.yy.gameecology.common.exception.SuperException;
//import com.yy.gameecology.common.support.SysEvHelper;
//import com.yy.gameecology.common.utils.DateUtil;
//import com.yy.gameecology.hdzj.element.ActComponent;
//import com.yy.gameecology.hdzj.element.component.RankingRankAwardComponent;
//import lombok.Data;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import java.lang.reflect.Field;
//import java.lang.reflect.ParameterizedType;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * 组件属性收集器
// *
// * <AUTHOR>
// * @date 2022/2/25 15:44
// **/
//@Component
//public class ComponentAttrCollectorBak {
//    private static Logger logger = LoggerFactory.getLogger(ComponentAttrCollectorBak.class);
//    @Autowired
//    private ActComponent[] components;
//    @Autowired
//    private GameecologyDao gameecologyDao;
//
//    private static final Set<Class<?>> BASIC_TYPE_SET = Sets.newHashSet(Byte.class, Short.class, Integer.class, Long.class
//            , Float.class, Double.class, Character.class, Boolean.class, String.class, Date.class);
//
//    private static final Map<String, String> BASIC_TYPE_MAP = Maps.newHashMap();
//
//    private static final Map<String, String> PROP_TYPE_MAP = Maps.newHashMap();
//
//    private static final Map<Long, ActComponent> ACT_COMPONENT_MAP = Maps.newHashMap();
//
//    @PostConstruct
//    public void init() {
//        initBasicTypeMap();
//        initPropTypeMap();
//        initActComponentMap();
//    }
//
//    private void initBasicTypeMap() {
//        BASIC_TYPE_MAP.put(int.class.getName(), Number.class.getName());
//        BASIC_TYPE_MAP.put(byte.class.getName(), Number.class.getName());
//        BASIC_TYPE_MAP.put(short.class.getName(), Number.class.getName());
//        BASIC_TYPE_MAP.put(long.class.getName(), Number.class.getName());
//        BASIC_TYPE_MAP.put(float.class.getName(), Double.class.getName());
//        BASIC_TYPE_MAP.put(double.class.getName(), Double.class.getName());
//        BASIC_TYPE_MAP.put(char.class.getName(), Number.class.getName());
//        BASIC_TYPE_MAP.put(boolean.class.getName(), Boolean.class.getName());
//    }
//
//    private void initPropTypeMap() {
//        PROP_TYPE_MAP.put(Number.class.getName(), PropType.Number);
//        PROP_TYPE_MAP.put(Boolean.class.getName(), PropType.Switch);
//        PROP_TYPE_MAP.put(Date.class.getName(), PropType.DateTime);
//        PROP_TYPE_MAP.put(String.class.getName(), PropType.Text);
//        PROP_TYPE_MAP.put(Byte.class.getName(), PropType.Number);
//        PROP_TYPE_MAP.put(Short.class.getName(), PropType.Number);
//        PROP_TYPE_MAP.put(Integer.class.getName(), PropType.Number);
//        PROP_TYPE_MAP.put(Long.class.getName(), PropType.Number);
//        PROP_TYPE_MAP.put(Float.class.getName(), PropType.Number);
//        PROP_TYPE_MAP.put(Double.class.getName(), PropType.Number);
//        PROP_TYPE_MAP.put(Map.class.getName(), PropType.Table);
//    }
//
//    private void initActComponentMap() {
//        if (components == null) {
//            return;
//        }
//        for (ActComponent component : components) {
//            ACT_COMPONENT_MAP.put(component.getComponentId(), component);
//        }
//    }
//
//    /**
//     * 用于本地生成脚本,手动执行
//     **/
//    public void collectConfig() {
//        // key 为组件id
//        Map<Long, List<CommonConfigProp>> propMap = Maps.newHashMap();
//
//        for (ActComponent component : components) {
//            propMap.put(component.getComponentId(), collect(component));
//        }
//        for (Map.Entry<Long, List<CommonConfigProp>> entry : propMap.entrySet()) {
//            buildSql(entry.getValue());
//        }
//    }
//
//    /**
//     * 同步组件的属性定义到数据库
//     **/
//    public void synComponentAttrDefine(Long componentId) {
//        if (componentId == null || ACT_COMPONENT_MAP.get(componentId) == null) {
//            throw new SuperException("not found ActComponent for Id =" + componentId, SuperException.E_PARAM_ILLEGAL);
//        }
//        HdzjComponentAttrDefine query = new HdzjComponentAttrDefine();
//        query.setCmptId(componentId.intValue());
//
//        ActComponent actComponent = ACT_COMPONENT_MAP.get(componentId);
//
//        List<CommonConfigProp> props = collect(actComponent);
//        if (CollectionUtils.isEmpty(props)) {
//            gameecologyDao.delete(HdzjComponentAttrDefine.class, query);
//            return;
//        }
//
//        List<HdzjComponentAttrDefine> attrDefines = gameecologyDao.select(HdzjComponentAttrDefine.class, query, "");
//        Map<String, HdzjComponentAttrDefine> oldDefineMap = attrDefines.stream()
//                .collect(Collectors.toMap(define -> define.getCmptId() + define.getPropName() + define.getPid(), define -> define));
//
//        List<HdzjComponentAttrDefine> newDefines = new ArrayList<>();
//        int order = 0;
//        for (CommonConfigProp prop : props) {
//            HdzjComponentAttrDefine newDefine = toDefine(prop, order);
//            String key = prop.getComponentId() + prop.getPropName() + prop.getPid();
//
//            HdzjComponentAttrDefine oldDefine = oldDefineMap.get(key);
//            if (oldDefine != null) {
//                newDefine.setLabelText(oldDefine.getLabelText());
//                newDefine.setPlaceholder(oldDefine.getPlaceholder());
//                newDefine.setRemark(oldDefine.getRemark());
//
//                if ("DropDown".equals(oldDefine.getPropType())) {
//                    newDefine.setPropType(oldDefine.getPropType());
//                    newDefine.setExtJson(oldDefine.getExtJson());
//                }
//
//                if (!Objects.equals("null", oldDefine.getDefaultValue()) && StringUtils.isNotBlank(oldDefine.getDefaultValue())) {
//                    newDefine.setDefaultValue(oldDefine.getDefaultValue());
//                }
//            }
//            newDefines.add(newDefine);
//            order++;
//        }
//        gameecologyDao.delete(HdzjComponentAttrDefine.class, query);
//        gameecologyDao.batchInsert(HdzjComponentAttrDefine.class, newDefines);
//    }
//
//    private HdzjComponentAttrDefine toDefine(CommonConfigProp prop, int order) {
//        HdzjComponentAttrDefine newDefine = new HdzjComponentAttrDefine();
//        newDefine.setLevel(prop.getLevel());
//        newDefine.setShowOrder(order);
//        newDefine.setDefaultValue(prop.getDefaultValue());
//        newDefine.setCmptId(prop.getComponentId().intValue());
//        newDefine.setUpdateTime(new Date());
//        newDefine.setPropType(prop.getPropType());
//        newDefine.setCreateTime(new Date());
//        newDefine.setPid(prop.getPid());
//        newDefine.setExtJson(prop.getExtJson());
//        newDefine.setRemark(prop.getRemark());
//        newDefine.setPlaceholder(prop.getPlaceholder());
//        newDefine.setLabelText(prop.getLabelText());
//        newDefine.setValueType(prop.getValueType());
//        newDefine.setPropName(prop.getPropName());
//        newDefine.setRepeat(false);
//
//        return newDefine;
//    }
//
//    public List<CommonConfigProp> collect(ActComponent component) {
//        Long componentId = component.getComponentId();
//        List<CommonConfigProp> props = toProps(componentId, component.getMyAttrClass());
//
//        return props;
//    }
//
//    private List<CommonConfigProp> toProps(Long componentId, Class<?> attrClass) {
//        List<CommonConfigProp> props = new ArrayList<>();
//
//        // 获取组件Attribute所有字段
//        List<Field> fields = listAllField(attrClass);
//
//        fields.forEach(field -> {
//            // 没有@ComponentAttrField注解,不收集
//            ComponentAttrField attrField = field.getAnnotation(ComponentAttrField.class);
//            if (attrField == null) {
//                logger.info("componentId={},fieldName={} has no ComponentAttrField", componentId, field.getName());
//                return;
//            }
//
//            try {
//                CommonConfigProp prop = new CommonConfigProp();
//                prop.setComponentId(componentId);
//                prop.setCreateTime(new Date());
//                prop.setPropName(field.getName());
//                prop.setUpdateTime(new Date());
//                prop.setPid("#");
//
//                Class<?> fieldType = field.getType();
//                if (isBasicType(fieldType)) {
//                    buildBasicTypeAttrField(field, attrClass, fieldType, prop);
//                } else if (isCollectionType(fieldType)) {
//                    buildCollectionTypeAttrField(field, fieldType, prop, props);
//                } else if (Map.class.isAssignableFrom(fieldType)) {
////                    buildMapTypeComponentAttr(field, prop, props);
////
////                    MapMetaInfo mapMetaInfo = parseMapMetaInfo(field, componentId);
////                    prop.setExtJson(JSON.toJSONString(mapMetaInfo));
//
//                    List<CommonConfigProp> subProps = buildMapTypeAttrField(attrField, prop);
//                    if (CollectionUtils.isNotEmpty(subProps)) {
//                        props.addAll(subProps);
//                    }
//                }
//
//                // 设置注解展示的信息
//                setShowInfo(prop, attrField, fieldType);
//
//                props.add(prop);
//            } catch (Exception ex) {
//                if (SysEvHelper.isDeploy()) {
//                    return;
//                }
//                logger.error("toProp error,class={},field={}", attrClass.getName(), field.getName(), ex);
//                throw ex;
//            }
//        });
//
//        return props;
//    }
//
//    private void setShowInfo(CommonConfigProp prop, ComponentAttrField attrField, Class<?> fieldType) {
//        prop.setRemark(attrField.remark());
//        prop.setLabelText(attrField.labelText());
//        if (StringUtils.isNotEmpty(attrField.placeholder())) {
//            prop.setPlaceholder(attrField.placeholder());
//        } else {
//            prop.setPlaceholder(attrField.labelText());
//        }
//
//        if (Date.class.equals(fieldType)) {
//            JSONObject extJson = new JSONObject();
//            extJson.put("format", attrField.dateShowFormat());
//            extJson.put("value-format", attrField.dateValueFormat());
//            prop.setExtJson(extJson.toJSONString());
//        }
//
//        if (attrField.dropDown()) {
//            if (attrField.dropDownSourceBeanClass() == null || attrField.dropDownSourceBeanClass().length <= 0) {
//                throw new IllegalArgumentException("componentId=" + prop.getComponentId() + ",fieldName=" + prop.getPropName() + ",has no dropDownSource");
//            }
//            try {
//                prop.setPropType(PropType.DropDown);
//                Class<?>[] sourceClass = attrField.dropDownSourceBeanClass();
//                DropDownSource dropDownSource = (DropDownSource) sourceClass[0].newInstance();
//                prop.setExtJson(JSON.toJSONString(dropDownSource.listDropDown()));
//            } catch (Exception ex) {
//
//            }
//        }
//    }
//
//    private MapMetaInfo parseMapMetaInfo(Field mapField, long componentId) {
//        MapMetaInfo mapMetaInfo = new MapMetaInfo();
//        ParameterizedType parameterizedType = (ParameterizedType) mapField.getGenericType();
//        Class<?> key1 = parameterizedType.getActualTypeArguments()[0].getClass();
//        mapMetaInfo.setKey1(key1);
//
//        // Map<BT,BT> or Map<BT,OT>
//        if (!(parameterizedType.getActualTypeArguments()[1] instanceof ParameterizedType)) {
//            mapMetaInfo.setLevel(1);
//            mapMetaInfo.setValue(parameterizedType.getActualTypeArguments()[1].getClass());
//            return mapMetaInfo;
//        }
//
//        ParameterizedType innerParamType = (ParameterizedType) parameterizedType.getActualTypeArguments()[1];
//        if (innerParamType.getActualTypeArguments().length == 1) {
//            // Map<BT,List<BT>> or Map<BT,List<OT>>
//            Class<?> outKeyClass = (Class<?>) parameterizedType.getActualTypeArguments()[0];
//            if (innerParamType.getActualTypeArguments()[0] instanceof ParameterizedType) {
//                // Map<BT,List<List<>>> 非法
//                throw new IllegalArgumentException("ComponentId = " + componentId + ",field=" + mapField.getName() + " 非法");
//            }
//
//            Class<?> innerClass = (Class<?>) innerParamType.getActualTypeArguments()[0];
//            mapMetaInfo.setLevel(1);
//            mapMetaInfo.setValue(List.class);
//            mapMetaInfo.setListValue(innerClass);
//            return mapMetaInfo;
//        }
//
//        if (innerParamType.getActualTypeArguments().length == 2) {
//            Class<?> innerKeyClass = (Class<?>) innerParamType.getActualTypeArguments()[0];
//            mapMetaInfo.setKey2(innerKeyClass);
//
//            if (innerParamType.getActualTypeArguments()[1] instanceof ParameterizedType) {
//                // 第三层map
//                ParameterizedType third = (ParameterizedType) innerParamType.getActualTypeArguments()[1];
//                if (third.getActualTypeArguments().length == 1) {
//                    // Map<BT,Map<BT,List<BT>>> or Map<BT,Map<BT,List<OT>>>
//                    Class<?> innerClass = (Class<?>) third.getActualTypeArguments()[0];
//                    mapMetaInfo.setLevel(2);
//                    mapMetaInfo.setValue(List.class);
//                    mapMetaInfo.setListValue(innerClass);
//
//                    return mapMetaInfo;
//                }
//
//                // Map<BT,Map<BT,Map<BT,BT>>>
//                Class<?> thirdKeyClass = (Class<?>) third.getActualTypeArguments()[0];
//                Class<?> valueClass = (Class<?>) third.getActualTypeArguments()[1];
//                mapMetaInfo.setKey3(thirdKeyClass);
//                mapMetaInfo.setValue(valueClass);
//                mapMetaInfo.setLevel(3);
//
//                return mapMetaInfo;
//            }
//
//            // Map<BT,Map<BT,BT>>
//            mapMetaInfo.setLevel(2);
//            Class<?> valueClass = (Class<?>) innerParamType.getActualTypeArguments()[1];
//            mapMetaInfo.setValue(valueClass);
//            return mapMetaInfo;
//        }
//
//        return mapMetaInfo;
//    }
//
//    /**
//     * map类型的属性
//     **/
//    private List<CommonConfigProp> buildMapTypeAttrField(ComponentAttrField attrField, CommonConfigProp pProp) {
//        MapField[] mapFields = attrField.mapFields();
//        if (mapFields == null || mapFields.length <= 0) {
//            throw new IllegalArgumentException("not config mapField,componentId=" + pProp.getComponentId() + ",fieldName=" + pProp.getPropName());
//        }
//
//        JSONObject extJson = new JSONObject();
//        List<CommonConfigProp> subProps = new ArrayList<>();
//        for (MapField mapField : mapFields) {
//            extJson.put(mapField.fieldName(), mapField.type().getName());
//            // 基础类型
//            if (isBasicType(mapField.type())) {
//                subProps.add(buildCommonConfigProp(mapField.type().getName(), pProp.getComponentId(), buildPid(pProp), mapField.fieldName(), mapField.labelText(), mapField.remark()));
//            } else if (isCollectionType(mapField.type())) {
//
//            } else {
//                subProps.addAll(buildInnerClass(pProp, mapField.type()));
//            }
//        }
//        pProp.setExtJson(extJson.toJSONString());
//
//        return subProps;
//    }
//
//    /**
//     * 基本类型的属性字段
//     **/
//    private void buildBasicTypeAttrField(Field field, Class<?> attrClass, Class<?> fieldType, CommonConfigProp prop) {
//        try {
//            field.setAccessible(true);
//            Object defaultValue = field.get(attrClass.newInstance());
//            if (defaultValue != null && Date.class.equals(fieldType)) {
//                prop.setDefaultValue(DateUtil.format((Date) defaultValue));
//            } else if (defaultValue != null) {
//                prop.setDefaultValue(defaultValue.toString());
//            }
//        } catch (Exception ex) {
//            logger.error("buildBasicTypeAttrField error,class={},field={}", attrClass.getName(), field.getName(), ex);
//        }
//        prop.setValueType(BASIC_TYPE_MAP.getOrDefault(fieldType.getName(), fieldType.getName()));
//        prop.setPropType(PROP_TYPE_MAP.get(prop.getValueType()));
//    }
//
//    /**
//     * 列表类型的组件属性
//     **/
//    private void buildCollectionTypeAttrField(Field field, Class<?> fieldType, CommonConfigProp prop, List<CommonConfigProp> props) {
//        prop.setValueType(fieldType.getName());
//        ParameterizedType parameterizedType = (ParameterizedType) field.getGenericType();
//        if (parameterizedType.getActualTypeArguments()[0] instanceof ParameterizedType) {
//            ParameterizedType innerParamType = (ParameterizedType) parameterizedType.getActualTypeArguments()[0];
//            if (innerParamType.getActualTypeArguments().length == 1) {
//                // 泛型嵌套的是列表
//                if (innerParamType.getActualTypeArguments()[0] instanceof ParameterizedType) {
//                    // TODO 第三层泛型
//                    // 不支持
//                    throw new IllegalArgumentException("ComponentId = " + prop.getComponentId() + ",field=" + field.getName() + " 非法");
//                } else {
//                    Class<?> innerClass = (Class<?>) innerParamType.getActualTypeArguments()[0];
//                    if (isBasicType(innerClass)) {
//                        prop.setPropType(BASIC_TYPE_MAP.getOrDefault(innerClass.getName(), innerClass.getName()));
//                    } else {
//                        prop.setPropType(Object.class.getName());
//                        props.addAll(buildInnerClass(prop, innerClass));
//                    }
//                }
//            } else if (innerParamType.getActualTypeArguments().length == 2) {
//                // TODO 嵌套的是Map
//                // List<Map<BT,BT>> & List<Map<BT,OT>> & List<Map<BT,List<BT>>> & List<Map<BT,Set<BT>>>
//                if (innerParamType.getActualTypeArguments()[1] instanceof ParameterizedType) {
//                    // 第三层嵌套
//                    throw new IllegalArgumentException("ComponentId = " + prop.getComponentId() + ",field=" + field.getName() + " 非法");
//                } else {
//                    props.addAll(buildMap(prop, (Class<?>) innerParamType.getActualTypeArguments()[0]
//                            , (Class<?>) innerParamType.getActualTypeArguments()[1], 2));
//                }
//            }
//        } else {
//            // List<BT> & List<OT> check
//            Class<?> argType = (Class<?>) parameterizedType.getActualTypeArguments()[0];
//            prop.setLevel(1);
//            Map<String, String> extJson = Maps.newHashMap();
//            if (isBasicType(argType)) {
//                // List<BT>
//                extJson.put("valueType", argType.getName());
//                prop.setPropType("Text");
//            } else {
//                // List<OT>
//                prop.setPropType("Table");
//                extJson.put("valueType", Object.class.getName());
//                props.addAll(buildInnerClass(prop, argType));
//            }
//            prop.setExtJson(JSON.toJSONString(extJson));
//        }
//    }
//
//    /**
//     * Map类型的组件属性
//     **/
//    private void buildMapTypeComponentAttr(Field field, CommonConfigProp prop, List<CommonConfigProp> props) {
//        // Map
//        prop.setValueType(Map.class.getName());
//        prop.setPropType(PROP_TYPE_MAP.get(prop.getValueType()));
//        ParameterizedType parameterizedType = (ParameterizedType) field.getGenericType();
//        if (parameterizedType.getActualTypeArguments()[1] instanceof ParameterizedType) {
//            // Map<BT,List<BT>>
//            ParameterizedType innerParamType = (ParameterizedType) parameterizedType.getActualTypeArguments()[1];
//            if (innerParamType.getActualTypeArguments().length == 1) {
//                Class<?> outKeyClass = (Class<?>) parameterizedType.getActualTypeArguments()[0];
//                if (innerParamType.getActualTypeArguments()[0] instanceof ParameterizedType) {
//                    // TODO 第三层泛型
//                    // Map<BT,List<List<>>>
//                    throw new IllegalArgumentException("ComponentId = " + prop.getComponentId() + ",field=" + field.getName() + " 非法");
//                } else {
//                    Class<?> innerClass = (Class<?>) innerParamType.getActualTypeArguments()[0];
//                    if (isBasicType(innerClass)) {
//                        // Map<BT,List<BT>> & Map<BT,Set<BT>>
//                        prop.setLevel(1);
//                        List<CommonConfigProp> subProps = buildMap(prop, outKeyClass, List.class, 1);
//                        if (CollectionUtils.isNotEmpty(subProps)) {
//                            props.addAll(subProps);
//                        }
//                    } else {
//                        // Map<BT,List<OT>> & Map<BT,Set<OT>>
//                        List<CommonConfigProp> subProps = buildInnerClass(prop, innerClass);
//                        subProps.add(0, buildCommonConfigProp(outKeyClass.getName(), prop.getComponentId(), buildPid(prop), "key1"));
//                        if (CollectionUtils.isNotEmpty(subProps)) {
//                            props.addAll(subProps);
//                        }
//                    }
//                }
//            } else if (innerParamType.getActualTypeArguments().length == 2) {
//                // 两层Map
//                // TODO Map<BT,Map<BT,BT>> & Map<BT,Map<BT,OT>>
//                Class<?> outKeyClass = (Class<?>) parameterizedType.getActualTypeArguments()[0];
//                Class<?> innerKeyClass = (Class<?>) innerParamType.getActualTypeArguments()[0];
//                if (innerParamType.getActualTypeArguments()[1] instanceof ParameterizedType) {
//                    ParameterizedType third = (ParameterizedType) innerParamType.getActualTypeArguments()[1];
//
//                    List<CommonConfigProp> subProps = new ArrayList<>();
//                    if (third.getActualTypeArguments().length == 1) {
//                        // Map<BT,Map<BT,List>>
//                        Class<?> innerClass = (Class<?>) third.getActualTypeArguments()[0];
//                        subProps.add(0, buildCommonConfigProp(outKeyClass.getName(), prop.getComponentId(), buildPid(prop), "key1"));
//                        subProps.add(1, buildCommonConfigProp(innerKeyClass.getName(), prop.getComponentId(), buildPid(prop), "key2"));
//                        if (isBasicType(innerClass)) {
//                            subProps.add(buildCommonConfigProp(innerClass.getName(), prop.getComponentId(), buildPid(prop), "value"));
//                        } else {
//                            subProps.addAll(buildInnerClass(prop, innerClass));
//                        }
//                        props.addAll(subProps);
//                        prop.setLevel(2);
//
//                    } else {
//                        // Map<BT,Map<BT,Map<BT,BT>>>
//                        Class<?> thirdKeyClass = (Class<?>) third.getActualTypeArguments()[0];
//                        Class<?> valueClass = (Class<?>) third.getActualTypeArguments()[1];
//                        subProps = buildMap(prop, thirdKeyClass, valueClass, 3);
//                        subProps.add(0, buildCommonConfigProp(outKeyClass.getName(), prop.getComponentId(), buildPid(prop), "key1"));
//                        subProps.add(1, buildCommonConfigProp(innerKeyClass.getName(), prop.getComponentId(), buildPid(prop), "key2"));
//                        props.addAll(subProps);
//                        prop.setLevel(3);
//                    }
//                } else {
//                    // Map<BT,Map<BT,BT>>
//                    Class<?> valueClass = (Class<?>) innerParamType.getActualTypeArguments()[1];
//                    prop.setLevel(2);
//                    List<CommonConfigProp> subProps = buildMap(prop, innerKeyClass, valueClass, 2);
//                    subProps.add(0, buildCommonConfigProp(outKeyClass.getName(), prop.getComponentId(), buildPid(prop), "key1"));
//                    props.addAll(subProps);
//                }
//            }
//        } else {
//            // Map<BT.BT> & Map<BT,OT>
//            Class<?> keyClass = (Class<?>) parameterizedType.getActualTypeArguments()[0];
//            Class<?> valueClass = (Class<?>) parameterizedType.getActualTypeArguments()[1];
//            List<CommonConfigProp> subProps = buildMap(prop, keyClass, valueClass, 1);
//            if (CollectionUtils.isNotEmpty(subProps)) {
//                props.addAll(subProps);
//            }
//            prop.setLevel(1);
//        }
//    }
//
//    private List<CommonConfigProp> buildInnerClass(CommonConfigProp pProp, Class<?> innerClass) {
//        List<CommonConfigProp> innerProps = new ArrayList<>();
//        Field[] innerFields = innerClass.getDeclaredFields();
//        for (Field innerField : innerFields) {
//            CommonConfigProp prop = new CommonConfigProp();
//            prop.setComponentId(pProp.getComponentId());
//            prop.setPid(pProp.getComponentId() + "#" + pProp.getPropName());
//            prop.setCreateTime(new Date());
//            prop.setUpdateTime(new Date());
//
//            prop.setPropName(innerField.getName());
//            prop.setRemark(innerField.getName());
//            prop.setLabelText(innerField.getName());
//            prop.setValueType(BASIC_TYPE_MAP.getOrDefault(innerField.getType().getName(), innerField.getType().getName()));
//            prop.setPropType(BASIC_TYPE_MAP.getOrDefault(innerField.getType().getName(), innerField.getType().getName()));
//            innerProps.add(prop);
//        }
//
//        return innerProps;
//    }
//
//    private List<CommonConfigProp> buildMap(CommonConfigProp pProp, Class<?> keyClass, Class<?> valueClass, int level) {
//        List<CommonConfigProp> props = new ArrayList<>();
//        props.add(buildCommonConfigProp(keyClass.getTypeName(), pProp.getComponentId(), pProp.getComponentId() + "#" + pProp.getPropName(), "key" + level));
//        if (isBasicType(valueClass)) {
//            props.add(buildCommonConfigProp(valueClass.getTypeName(), pProp.getComponentId(), pProp.getComponentId() + "#" + pProp.getPropName(), "value"));
//        } else if (isCollectionType(valueClass)) {
//            props.add(buildCommonConfigProp(valueClass.getTypeName(), pProp.getComponentId(), pProp.getComponentId() + "#" + pProp.getPropName(), "value"));
//        } else {
//            props.addAll(buildInnerClass(pProp, valueClass));
//        }
//
//        return props;
//    }
//
//    private String buildPid(CommonConfigProp pProp) {
//        return pProp.getComponentId() + "#" + pProp.getPropName();
//    }
//
//    private CommonConfigProp buildCommonConfigProp(String typeName, Long componentId, String pid, String text) {
//        return buildCommonConfigProp(typeName, componentId, pid, text, text);
//    }
//
//    private CommonConfigProp buildCommonConfigProp(String typeName, Long componentId, String pid, String text, String labelText) {
//        return buildCommonConfigProp(typeName, componentId, pid, text, labelText, "");
//    }
//
//    private CommonConfigProp buildCommonConfigProp(String typeName, Long componentId, String pid, String propName, String labelText, String remark) {
//        CommonConfigProp prop = new CommonConfigProp();
//        prop.setPropType(BASIC_TYPE_MAP.getOrDefault(typeName, typeName));
//        prop.setValueType(typeName);
//        prop.setPropName(propName);
//        prop.setPid(pid);
//        prop.setLabelText(labelText);
//        prop.setComponentId(componentId);
//        prop.setCreateTime(new Date());
//        prop.setUpdateTime(new Date());
//        if (StringUtils.isEmpty(labelText)) {
//            labelText = propName;
//        }
//        if (StringUtils.isEmpty(remark)) {
//            remark = labelText;
//        }
//        prop.setRemark(remark);
//
//        return prop;
//    }
//
//    /**
//     * 获取所有属性
//     **/
//    private List<Field> listAllField(Class<?> attrClass) {
//        return new ArrayList<>(Arrays.asList(attrClass.getDeclaredFields()));
//    }
//
//    private boolean isBasicType(Class<?> clz) {
//        return clz.isPrimitive() || BASIC_TYPE_SET.contains(clz);
//    }
//
//    /**
//     * 使用父类判断 ???
//     **/
//    private boolean isCollectionType(Class<?> clz) {
//        return List.class.isAssignableFrom(clz) || Set.class.isAssignableFrom(clz);
//    }
//
//    @Data
//    private static class CommonConfigProp {
//        private Long componentId;
//        private String propName;
//        private String pid;
//        private String labelText;
//        private String propType;
//        private String remark;
//        private Date createTime;
//        private Date updateTime;
//        private String placeholder;
//        private String defaultValue;
//        private Integer level = 1;
//        private String valueType;
//        private String extJson;
//    }
//
//    @Data
//    private static class MapMetaInfo {
//        private Class<?> key1;
//        private Class<?> key2;
//        private Class<?> key3;
//        private Class<?> value;
//        private Class<?> listValue;
//        private Integer level;
//    }
//
//    /**
//     * 控件类型
//     */
//    public static class PropType {
//        /**
//         * <el-input-number></el-input-number>
//         */
//        public static final String Number = "Number";
//        /**
//         * <el-input></el-input>
//         **/
//        public static final String Text = "Text";
//        /**
//         * <el-date-picker></el-date-picker>
//         **/
//        public static final String DateTime = "DateTime";
//        /**
//         * <el-select></el-select>
//         * <p>
//         * 对应的<el-option></el-option> 配置到哪去
//         **/
//        public static final String DropDown = "DropDown";
//        /**
//         * <el-table></el-table>
//         * <p>
//         * 需要配套列
//         * <el-table-column></el-table-column>
//         **/
//        public static final String Table = "Table";
//        /**
//         * <el-switch></el-switch>
//         **/
//        public static final String Switch = "Switch";
//    }
//
//    public static void main(String[] args) {
//        ComponentAttrCollectorBak collector = new ComponentAttrCollectorBak();
//        collector.init();
//        List<CommonConfigProp> props = collector.collect(new RankingRankAwardComponent());
//        buildSql(props);
//    }
//
//    private static void buildSql(List<CommonConfigProp> props) {
//        int order = 0;
//        for (CommonConfigProp prop : props) {
//            String sql = "INSERT INTO `gameecology`.`hdzj_component_attr_define`(`cmpt_id`,`prop_name`,`pid`,`label_text`,`prop_type`,`value_type`,`placeholder`,`remark`,`create_time`,`update_time`,`default_value`,`level`,`extJson`,`show_order`) VALUES (%s,'%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s');";
//            sql = String.format(sql, prop.getComponentId(), prop.getPropName()
//                    , prop.getPid()
//                    , prop.getLabelText()
//                    , prop.getPropType()
//                    , prop.getValueType()
//                    , prop.getPlaceholder()
//                    , prop.getRemark()
//                    , DateUtil.format(new Date())
//                    , DateUtil.format(new Date())
//                    , prop.getDefaultValue() == null ? "" : prop.getDefaultValue()
//                    , prop.getLevel()
//                    , prop.getExtJson() == null ? "" : prop.getExtJson()
//                    , order++);
//
//            System.out.println(sql);
//        }
//    }
//}
