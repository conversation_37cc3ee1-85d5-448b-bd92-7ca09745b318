package com.yy.gameecology.hdzj.element.component;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.PhaseRankTag;
import com.yy.gameecology.activity.bean.rank.RankValueItemBase;
import com.yy.gameecology.activity.service.rankext.RankExtHandler;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.RankBuilderComponentAttr;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * desc:榜单展示配置
 * 有的参数不适合前端传递，可配置在这个组件控制榜单特定展示
 * 晋级榜pointmember处理
 * <AUTHOR>
 * @date 2023-07-13 16:28
 **/
@Component
public class RankBuilderComponent  extends BaseActComponent<RankBuilderComponentAttr> implements RankExtHandler {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

   @Override
   public Long getComponentId() {
      return ComponentId.RANK_BUILDER;
   }

    @Override
    public List<String> supportKeys() {
        return null;
    }

    @Override
    public List<Object> addViewExt(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        RankBuilderComponentAttr attr = tryGetUniqueComponentAttr(rankReq.getActId());
        if (attr == null || CollectionUtils.isEmpty(objectList)) {
            return objectList;
        }

        List<PhaseRankTag> loadPhaseRankTagConfig = attr.getLoadPhaseRankTagConfig();
        if (CollectionUtils.isEmpty(loadPhaseRankTagConfig)) {
            return objectList;
        }

        PhaseRankTag phaseRankTag = loadPhaseRankTagConfig.stream().filter(v -> v.getRankId() == rankReq.getRankId() &&
                v.getPhaseId() == rankReq.getPhaseId()).findFirst().orElse(null);
        if (phaseRankTag != null) {
            Long loginUid = rankReq.getLoginUid();
            if (StringUtil.isEmpty(rankReq.getPointedMember()) && loginUid == 0) {
                return objectList;
            }

            String tag = phaseRankTag.getTag();
            int size = objectList.size();
            try {
                RankValueItemBase o = (RankValueItemBase) objectList.get(size - 1);
                if (o.getRank() == -1) {
                    log.info("remove pointmember ");
                    objectList.remove(size - 1);
                    objectList.add(null);
                }
                if (o.getViewExt() == null) {
                    o.setViewExt(Maps.newHashMap());
                }
                o.getViewExt().put("tag", tag);
            } catch (Exception e) {
                log.error("build rankext tag error:{}", e.getMessage(), e);
            }
        }

        return objectList;
    }
}
