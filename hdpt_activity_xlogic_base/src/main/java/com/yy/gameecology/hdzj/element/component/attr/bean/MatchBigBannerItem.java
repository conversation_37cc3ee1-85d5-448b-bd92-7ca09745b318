package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-02-26 20:29
 **/
@Data
public class MatchBigBannerItem {
    @ComponentAttrField(labelText = "阶段Id")
    private Long phaseId;

    @ComponentAttrField(labelText = "取top N广播")
    private Integer top;

    @ComponentAttrField(labelText = "指定排名第几用户")
    private Integer indexRank;

    @ComponentAttrField(labelText = "广播的最小分值")
    private long minScore;
}
