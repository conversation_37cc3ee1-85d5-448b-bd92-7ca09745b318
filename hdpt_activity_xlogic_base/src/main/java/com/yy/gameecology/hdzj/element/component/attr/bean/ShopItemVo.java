package com.yy.gameecology.hdzj.element.component.attr.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ShopItemVo extends ShopItem {

    protected long amount;

    protected List<FireworkVo> fires;

    protected boolean canExchange;

    protected int exchangeNum;

    public ShopItemVo() {
    }

    public ShopItemVo(ShopItem shopItem) {
        this.sid = shopItem.sid;
        this.logo = shopItem.logo;
        this.title = shopItem.title;
        this.content = shopItem.content;
        this.totalAmount = shopItem.totalAmount;
        this.priceJson = shopItem.priceJson;
        this.cid = shopItem.cid;
        this.taskId = shopItem.taskId;
        this.packageId = shopItem.packageId;
        this.userLimit = shopItem.userLimit;
        this.successCode = shopItem.successCode;
        this.alert = shopItem.alert;
    }
}
