package com.yy.gameecology.hdzj.element.component.entity;

import com.yy.gameecology.common.db.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * 淘汰赛榜单事件记录表
 * 用于存储榜单变化事件，替代Redis的HDZT_RANK_KEY列表
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@TableColumn(underline = true)
public class KnockoutRankEventRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5159_rank_event_";

    public static String getTableName(long actId) {
        return TABLE_NAME + actId;
    }

    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<KnockoutRankEventRecord> ROW_MAPPER = (rs, rowNum) -> {
        KnockoutRankEventRecord result = new KnockoutRankEventRecord();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getLong("cmpt_use_inx"));
        result.setPhaseId(rs.getLong("phase_id"));
        result.setHdzkRankId(rs.getLong("hdzk_rank_id"));
        result.setMemberId(rs.getString("member_id"));
        result.setEventData(rs.getString("event_data"));
        result.setProcessed(rs.getInt("processed"));
        result.setSeq(rs.getString("seq"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        return result;
    };

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 组件使用索引
     */
    private Long cmptUseInx;

    /**
     * 阶段ID
     */
    private Long phaseId;

    /**
     * 中控榜单ID
     */
    private Long hdzkRankId;

    /**
     * 主播ID
     */
    private String memberId;

    /**
     * 事件数据JSON
     */
    private String eventData;

    /**
     * 是否已处理：0-未处理，1-已处理
     */
    private Integer processed;

    /**
     * 事件序列号
     */
    private String seq;

    /**
     * 创建时间
     */
    private Date createTime;

    // 状态常量
    public static final int PROCESSED_NO = 0;
    public static final int PROCESSED_YES = 1;
}
