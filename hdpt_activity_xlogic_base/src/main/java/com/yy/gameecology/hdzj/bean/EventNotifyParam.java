package com.yy.gameecology.hdzj.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/7 10:20
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventNotifyParam {

    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    @org.codehaus.jackson.annotate.JsonIgnore
    private List<HandlerBean> handlerBeans;

    private long actId;

    private Object event;

    private Class<?> eventClass;
}
