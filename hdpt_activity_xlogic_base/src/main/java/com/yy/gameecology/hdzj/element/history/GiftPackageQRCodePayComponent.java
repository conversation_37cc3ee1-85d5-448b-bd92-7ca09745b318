package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.WelfareInfo;
import com.yy.gameecology.activity.bean.mq.ChargeResponseEvent;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.TurnoverChargeOrderClient;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.exception.BadRequestException;
import com.yy.gameecology.activity.exception.ParameterException;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.TaskPackageInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.GiftPackageQRCodePayComponentAttr;
import com.yy.gameecology.hdzj.utils.ResponseUtils;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import com.yy.thrift.turnover_chargeorder.TAppId;
import com.yy.thrift.turnover_chargeorder.TChargeResponseInfo;
import com.yy.thrift.turnover_chargeorder.TPayKeyType;
import com.yy.thrift.turnover_chargeorder.TPayWay;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 新手礼包组件
 * 和V2相比升级点：
 * 1、接入扫码支付流程
 * 2、抽奖模型升级
 * <p>
 * A. 获取礼包购买状态 -- (与版本1一致)
 * 1. redis存有一批uid,用set存储,有个属性配置项指定这批uid是白名单还是黑名单
 * 2. 白名单时,只有当前用户在名单中才有资格购买;黑名单时,只有当前用户不在黑名单中才有资格购买
 * 3. 当用户有购买资格时,判断当前用户的购买次数是否超过限制,超过了则不允许购买
 * <p>
 * B.生成支付二维码
 * 1. 检查配置
 * 2. uid请求限制
 * 3. 调用营收生成支付二维码：返回订单id和二维码地址
 * <p>
 * C.支付回调
 * 1. 监听营收支付回调事件ChargeResponseEvent,每个业务一条topic
 * 2. 从扩展字段中获取业务透传数据
 * 3. 回调去重
 * 4. 判断订单状态是否成功,不成功,则直接记录订单状态到redis
 * 5. 判断购买数量限制:超过数量则对该订单进行退款
 * 6. 调用中台接口发放礼包内容
 * 7. 进行一轮抽奖,比记录抽奖状态到redis中
 * <p>
 * D. 查询支付状态
 * 1. 根据订单号从redis中查询支付状态和抽奖状态
 *
 * <AUTHOR>
 */
@UseRedisStore
@Component
@Slf4j
public class GiftPackageQRCodePayComponent extends BaseActComponent<GiftPackageQRCodePayComponentAttr> {

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private TurnoverChargeOrderClient turnoverChargeOrderClient;

    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private ActInfoService actInfoService;

    @Autowired
    private Locker locker;

    private static final String BUY_GIFT_PACKAGE_IP_LIMIT_KEY = "buyGiftPackageIpLimit:%s";

    private static final String BUY_RECORD = "buyRecord";

    private static final String BUY_LOCK = "GiftPackage_lock_%s";

    private static final String RATE_CONTROL_UID = "rate_uid_%s";

    private static final String ORDER_STATUS = "order_status";

    /**
     * 是否抽中信号弹等奖品
     */
    private static final String BINGO_STATUS = "bingo_status";

    //支付回调去重
    private static final String CALL_BACK_DUP = "call_back_dup_%s";


    /**
     * 1、判断购买资格，如果资格符合，生成支付二维码
     * 2、支付回调：扣减购买资格、抽奖、发货，如果资格扣减失败，需要退款？
     * <p>
     * 失败重试点，失败重试机制？
     */

    @Override
    public Long getComponentId() {
        return ComponentId.GIFT_PACKAGE_BUY_QRCODE_PAY;
    }

    public Response<JSONObject> giftPackageStatus(GiftPackageQRCodePayComponentAttr attr, long uid) {
        boolean hasQualification = true;
        String groupCode = getRedisGroupCode(attr.getActId());
        if (attr.isLimitQualification()) {
            boolean contains = actRedisDao.sIsMember(groupCode, attr.getLimitUidRedisKey(), String.valueOf(uid));
            hasQualification = (contains && attr.isAllow()) || (!contains && !attr.isAllow());
        }
        JSONObject data = new JSONObject();
        if (hasQualification) {
            String redisKey = getRedisKey(attr, BUY_RECORD);
            boolean canBuy =
                    Convert.toInt(actRedisDao.zscore(groupCode, redisKey, String.valueOf(uid)), 0)
                            < attr.getLimitBuyCount();
            data.put("canBuy", canBuy ? 1 : 0);
        } else {
            data.put("canBuy", -1);
        }

        data.put("payWay", attr.getPayWay());

        return Response.success(data);
    }

    /**
     * 查询支付状态
     * 0-待支付 1-支付成功 2-支付失败 3-有异常，不符合购买资格，退款申请中
     */
    public Response<Map<String, Object>> queryPayStatus(long actId, long index, long orderId) {
        Map<String, Object> result = Maps.newHashMap();

        GiftPackageQRCodePayComponentAttr attr = this.getComponentAttr(actId, index);
        int status = getOrderStatus(attr, orderId);
        result.put("status", status);

        String bingoStatusKey = makeKey(attr, BINGO_STATUS);
        String bingoStatus = actRedisDao.hget(getRedisGroupCode(actId), bingoStatusKey, orderId + "");
        result.put("bingoStatus", Convert.toInt(bingoStatus, 0));

        return Response.success(result);
    }

    /**
     * 发起支付请求，生成支付二维码
     *
     * @param payWay ZfbWap(7),  WeiXinQrCode(12)
     */
    public Response<Map<String, Object>> requestBuyGiftPackage(long actId, long index, long uid, int payWay, String ip) {
        //新用户资格是否符合
        //本次活动是否第一次购买
        //请求营收接口
        //设置用户订单状态key   0-发起订单  1-支付成功，待处理发货 2-支付成功已发货(终止)  3-支付失败(终止) 4-不符合资格发起退款 5-发起退款成功  6-发起退款失败人工接入

        GiftPackageQRCodePayComponentAttr attr = this.getComponentAttr(actId, index);
        if (!isMyDuty(attr.getActId())) {
            throw new BadRequestException("无效活动礼包");
        }
        if (!attr.getPayWay().containsKey(payWay + "")) {
            throw new BadRequestException("支付方式未配置");
        }
        Clock clock = new Clock();
        String groupCode = getRedisGroupCode(attr.getActId());

        //uid 限流防止恶意攻击
        String rateUidControlKey = makeKey(attr, String.format(RATE_CONTROL_UID, uid));
        final boolean rs = actRedisDao.setNX(groupCode, rateUidControlKey, System.currentTimeMillis() + "", 3);
        if (!rs) {
            return Response.fail(9001, "操作太频繁，请稍后重试");
        }

        Response<Map<String, Object>> response = check(attr, uid, ip);
        if (ResponseUtils.isFail(response)) {
            return response;
        }
        clock.tag();
        String lockName = getRedisKey(attr, String.format(BUY_LOCK, uid));
        Secret lock = null;
        try {
            lock = locker.lock(lockName, 30);
            if (lock != null) {
                String userGiftPackageKey = getRedisKey(attr, BUY_RECORD);
                // 这里不是真正给钱，不能增加购买次数，只能这样读取初步判断
                if (attr.getLimitBuyCount() > 0
                        && Convert.toLong(actRedisDao
                        .zScore(groupCode, userGiftPackageKey, String.valueOf(uid)), 0) >= attr.getLimitBuyCount()) {
                    return Response.fail(2, attr.getLimitBuyCountTip());
                }

                clock.tag();

                BigDecimal price = new BigDecimal(attr.getPrice()).divide(new BigDecimal(1000));

                String seq = UUID.randomUUID().toString();
                Map<String, Object> ext = Maps.newHashMap();
                ext.put("uid", uid);
                ext.put("seq", seq);
                ext.put("appId", attr.getTurnOverAppId());
                ext.put("cmptInx", attr.getCmptUseInx());
                ext.put("actId", attr.getActId());
                ext.put("cmptId", getComponentId());
                String extStr = JSON.toJSONString(ext);
                TChargeResponseInfo payRes = turnoverChargeOrderClient.payOrder(uid, price.doubleValue(), ip, TPayWay.findByValue(payWay), 1, TAppId.findByValue(attr.getTurnOverAppId()), extStr
                        , attr.getTurnOverActionId(), TPayKeyType.findByValue(attr.getTurnOverPayKeyType()));
                if (payRes == null || payRes.getCode() != 1 || StringUtil.isEmpty(payRes.getPayUrl())) {
                    log.error("turnoverChargeOrderClient.payOrder error,actId:{},uid:{},seq:{},response:{}", attr.getActId(), uid, seq, JSON.toJSONString(payRes));
                    return Response.fail(2, "网络错误,请稍后重试");
                }

                Map<String, Object> payResult = Maps.newHashMap();
                payResult.put("orderId", payRes.getOrderId());
                payResult.put("payUrl", payRes.getPayUrl());
                //返回支付二维码
                return Response.success(payResult);
            }
        } catch (Exception e) {
            log.warn("buyGiftPackage error {}", e.getMessage(), e);
        } finally {
            if (lock != null) {
                locker.unlock(lockName, lock);
            }
        }
        return Response.fail(2, "当前购买礼包人数较多，请稍后再试");

    }

    /**
     * 营收支付成功回调事件
     */
    @HdzjEventHandler(value = ChargeResponseEvent.class, canRetry = true)
    public void payCallBackHandler(ChargeResponseEvent event, GiftPackageQRCodePayComponentAttr attr) {
        log.info("payCallBackHandler,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        payCallBack(event, attr);
    }


    public void payCallBack(ChargeResponseEvent responseEvent, GiftPackageQRCodePayComponentAttr attr) {

        String groupCode = getRedisGroupCode(attr.getActId());
        JSONObject extend = JSONObject.parseObject(responseEvent.getExpand());
        long uid = extend.getLongValue("uid");
        String seq = extend.getString("seq");
        int appId = extend.getIntValue("appId");
        long orderId = responseEvent.getId();
        long cmptInx = extend.getLongValue("cmptInx");
        long actId = extend.getLongValue("actId");
        long cmptId = extend.getLongValue("cmptId");

        //这里要小心
        if (actId != attr.getActId() || !getComponentId().equals(cmptId) || cmptInx != attr.getCmptUseInx()) {
            log.warn("not my duty,event{},attr:{}", JSON.toJSONString(responseEvent), JSON.toJSONString(attr));
            return;
        }

        //回调去重，这里很重要，否则如果有重复回调可能会出现退款！！！
        String callBackDupKey = makeKey(attr, String.format(CALL_BACK_DUP, orderId));
        if (!actRedisDao.setNX(groupCode, callBackDupKey, System.currentTimeMillis() + "")) {
            log.warn("call backDup,event{},attr:{}", JSON.toJSONString(responseEvent), JSON.toJSONString(attr));
            return;
        }

        //判断支付状态是否为支付成功，支付不成功的记录日志，返回
        if (responseEvent.getStatus() != 1) {
            setOrderStatus(attr, orderId, 2);
            log.warn("payCallBack failed return,event:{},attr:{}", JSON.toJSONString(responseEvent), JSON.toJSONString(attr));
            return;
        }


        //判断有无购买过，是否满足购买条件，如果没购买过则退款
        String userGiftPackageKey = getRedisKey(attr, BUY_RECORD);
        if (attr.getLimitBuyCount() > 0) {

//                && actRedisDao
//                           .zIncrWithLimit(groupCode, userGiftPackageKey, uid + "", 1, attr.getLimitBuyCount())
//                           .get(0) != 1L
            //TODO 如果次组件要重新启用，则需要改造此处
            if (true) {
                throw new RuntimeException("zset_incr_with_limit.lua,lua forbid");
            }
            log.warn("payCallBack warn,超出购买资格限制,发起退款,response:{}", JSON.toJSONString(responseEvent));
            setOrderStatus(attr, orderId, 3);
            //退款
            TChargeResponseInfo response = turnoverChargeOrderClient
                    .reversePayCharge(uid, TAppId.findByValue(appId), orderId, "购买礼包失败,发起退款:" + orderId);
            if (response != null && response.getCode() == 1) {
                log.warn("reversePayCharge ok,orderId:{},uid:{}", orderId, uid);
            } else {
                log.error("reversePayCharge error,orderId:{},uid:{},rsp:{}", orderId, uid, JSON.toJSONString(response));
            }

            return;
        }

        // 发奖
        WelfareInfo welfareInfo = new WelfareInfo(seq, uid, attr.getAwards());
        batchWelfare(welfareInfo);

        // 抽奖，一定程度上可以忽略结果，接口失败了当没抽中
        BatchLotteryResult result = lottery(seq, uid, attr.getLotteryTaskId());
        if (result != null && result.getCode() == 0
                && MapUtils.isNotEmpty(result.getRecordIds())
                && result.getRecordIds().containsKey(attr.getBingoPackageId())) {
            log.info("lottery bingo,result:{}", JSON.toJSONString(result));
            String bingoStatusKey = makeKey(attr, BINGO_STATUS);
            actRedisDao.hset(groupCode, bingoStatusKey, orderId + "", attr.getBingoItemCount() + "");
        }

        setOrderStatus(attr, orderId, 1);

    }


    public boolean batchWelfare(WelfareInfo welfareInfo) {
        String seq = welfareInfo.getSeq();
        Long uid = welfareInfo.getUid();
        List<TaskPackageInfo> data = welfareInfo.getData();

        if (CollectionUtils.isEmpty(data)) {
            return true;
        }
        Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap();
        data.forEach(x -> {
            Map<Long, Integer> packageIdCount = taskPackageIds.getOrDefault(x.getTaskId(), Maps.newHashMap());
            Integer count = packageIdCount.getOrDefault(x.getPackageId(), 0);
            packageIdCount.put(x.getPackageId(), count + x.getCount());

            taskPackageIds.put(x.getTaskId(), packageIdCount);
        });
        BatchWelfareResult result = hdztAwardServiceClient.doBatchWelfare(seq, uid, taskPackageIds, DateUtil.getNowYyyyMMddHHmmss(), 3, Maps.newHashMap());
        return result != null && result.getCode() == 0;
    }

    private BatchLotteryResult lottery(String seq, Long uid, long taskId) {
        if (taskId > 0) {
            return hdztAwardServiceClient.doLottery(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(), uid, taskId, 1, 0, seq);
        }
        return null;
    }

    private int getOrderStatus(GiftPackageQRCodePayComponentAttr attr, long orderId) {
        String key = makeKey(attr, ORDER_STATUS);
        return Convert.toInt(actRedisDao.hget(getRedisGroupCode(attr.getActId()), key, orderId + ""), 0);
    }

    /**
     * @param status 0-待支付 1-支付成功 2-支付失败 3-有异常，不符合购买资格，退款申请中
     */
    private void setOrderStatus(GiftPackageQRCodePayComponentAttr attr, long orderId, int status) {
        String key = makeKey(attr, ORDER_STATUS);
        actRedisDao.hset(getRedisGroupCode(attr.getActId()), key, orderId + "", status + "");
    }


    private Response<Map<String, Object>> check(GiftPackageQRCodePayComponentAttr attr, long uid, String ip) {
        Response<Map<String, Object>> response = checkActivityIsEnabled(attr);
        if (ResponseUtils.isFail(response)) {
            return response;
        }
        response = checkLimitQualification(attr, uid);
        if (ResponseUtils.isFail(response)) {
            return response;
        }
        response = checkLimitIp(attr, ip);
        return response;
    }

    private Response<Map<String, Object>> checkLimitIp(GiftPackageQRCodePayComponentAttr attr, String ip) {
        String ipKey = getRedisKey(attr, String.format(BUY_GIFT_PACKAGE_IP_LIMIT_KEY, ip));
        if (StringUtils.isNotBlank(actRedisDao.get(getRedisGroupCode(attr.getActId()), ipKey))) {
            log.warn("buyGiftPackage ip limit,key：{}", ipKey);
            return Response.fail(2, "当前购买礼包人数较多，请稍后再试");
        }
        return Response.success(null);
    }

    @VisibleForTesting
    String getRedisKey(GiftPackageQRCodePayComponentAttr attr, String keySuffix) {
        if (attr.isGlobalLimitCount()) {
            return makeKey(attr.getActId(), attr.getCmptId(), 0L, keySuffix);
        }
        return makeKey(attr, keySuffix);
    }

    private Response<Map<String, Object>> checkLimitQualification(GiftPackageQRCodePayComponentAttr attr, long uid) {
        if (!commonService.checkWhiteList(attr.getActId(), RoleType.USER, String.valueOf(uid))) {
            return Response.fail(2, "活动暂未开始");
        }
        if (!attr.isLimitQualification()) {
            return Response.success(null);
        }
        try {
            boolean contains = actRedisDao.sIsMember(getRedisGroupCode(attr.getActId()), attr.getLimitUidRedisKey(), String.valueOf(uid));
            boolean hasQualification = (contains && attr.isAllow()) || (!contains && !attr.isAllow());
            if (!hasQualification) {
                return Response.fail(2, attr.getLimitQualificationTip());
            }
        } catch (Exception e) {
            // ignore redis exception
        }
        return Response.success(null);
    }

    private Response<Map<String, Object>> checkActivityIsEnabled(GiftPackageQRCodePayComponentAttr attr) {
        if (!actInfoService.inActTime(attr.getActId())) {
            return Response.fail(2, "不在活动时间内");
        }
        return Response.success(null);
    }

    public Response<JSONObject> giftPackageStatus(long actId, long index, long uid) {
        if (!isMyDuty(actId)) {
            throw new BadRequestException("无效活动礼包");
        }

        GiftPackageQRCodePayComponentAttr attr = getComponentAttr(actId, index);
        if (attr != null) {
            return giftPackageStatus(attr, uid);
        }
        throw new ParameterException("index error.");
    }
}
