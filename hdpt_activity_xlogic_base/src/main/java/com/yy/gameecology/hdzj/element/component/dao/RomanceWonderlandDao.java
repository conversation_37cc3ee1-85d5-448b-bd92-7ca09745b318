package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5148TopCoupleRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Slf4j
@Repository
public class RomanceWonderlandDao {

    @Autowired
    private GameecologyDao gameecologyDao;

    private static final String INSERT_IGNORE = "insert ignore into cmpt_5148_top_couple_record (act_id, date_code, rank_id, anchor_uid, user_uid, sid, ssid, score, package_id, create_time) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    public int addTopCpRecord(Cmpt5148TopCoupleRecord record) {
        return gameecologyDao.getJdbcTemplate().update(INSERT_IGNORE, record.getActId(), record.getDateCode(), record.getRankId(), record.getAnchorUid(), record.getUserUid(), record.getSid(), record.getSsid(), record.getScore(), record.getPackageId(), record.getCreateTime());
    }

    public List<Cmpt5148TopCoupleRecord> getTopCoupleRecords(long actId, String dateCode, Long rankId) {
        Cmpt5148TopCoupleRecord me = new Cmpt5148TopCoupleRecord();
        me.setActId(actId);
        me.setDateCode(dateCode);
        if (rankId != null) {
            me.setRankId(rankId);
        }

        return gameecologyDao.select(Cmpt5148TopCoupleRecord.class, me);
    }
}
