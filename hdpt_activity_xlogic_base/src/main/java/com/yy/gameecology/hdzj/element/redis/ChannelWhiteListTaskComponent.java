package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.common.bean.ChannelBaseInfo;
import com.yy.gameecology.common.consts.BigDataScoreType;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.BroadcastConfig;
import com.yy.gameecology.hdzj.bean.TaskPackageInfo;
import com.yy.gameecology.hdzj.bean.WhiteListTaskVo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.ChannelWhiteListTaskComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztranking.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2022-07-11 21:00
 **/
@UseRedisStore
@RestController
@RequestMapping("/cmpt/channelWhiteListTask")
@Component
public class ChannelWhiteListTaskComponent extends BaseActComponent<ChannelWhiteListTaskComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private LoginService loginService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private SignedService signedService;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private EnrollmentNewService enrollmentNewService;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    /**
     * 已完成任务标识
     */
    private static final String SKIP_TASK_TAG_KEY = "skip_task_tag";

    /**
     * 实际完成任务成员信息
     */
    private static final String COMPLETE_TASK_MEMBER_KEY = "complete_task_member";

    /**
     * 已发放奖励名额数量
     */
    private static final String AWARD_ALREADY_RELEASE = "award_already_release";

    @Override
    public Long getComponentId() {
        return ComponentId.CHANNEL_WHITE_LIST_TASK;
    }

    /**
     * 响应榜单变化事件，按所达分值区间发放奖励， 本函数做了防重检查，每个分值区间只能执行一次
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, ChannelWhiteListTaskComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        if (!isMyduty(rankId, phaseId, attr)) {
            return;
        }
        String redisGroup = getRedisGroupCode(attr.getActId());
        String memberId = event.getMember();
        long cmptTaskId = attr.getMemberTask().getOrDefault(memberId, attr.getDefaultTaskId());
        long taskScore = attr.getTaskIdScore().getOrDefault(cmptTaskId, 0L);
        if (taskScore <= 0) {
            log.info("task not config score:{}", taskScore);
            return;
        }
        long memberScore = getMemberScore(event, attr);
        if (memberScore >= taskScore) {
            String skipTaskKey = makeKey(attr, SKIP_TASK_TAG_KEY);
            String alreadyRelease = makeKey(attr, AWARD_ALREADY_RELEASE);
            //首次过任务
            boolean firstSkip = actRedisDao.hsetnx(redisGroup, skipTaskKey, memberId, memberScore + "|" + DateUtil.format(new Date()));
            if (firstSkip) {
                //扣额度
                List<Long> incResult;
                //List<Long> incResult = actRedisDao.incrValueWithLimit(redisGroup, alreadyRelease, 1, attr.getAwardLimit(), false);
                // TODO 如果次组件要重新启用，则需要改造此处
                if (true) {
                    throw new RuntimeException("string_incr_with_limit.lua,lua forbid");
                }
                if (incResult.get(0) != 1) {
                    log.info("额度已经用完，本次不发放");
                    return;
                }
                //保存数据
                saveCompleteTaskData(event, attr, memberId, cmptTaskId);
                //发如流通知
                sendMsg(attr, memberId, memberScore, cmptTaskId);
            }
        }

    }

    private void sendMsg(ChannelWhiteListTaskComponentAttr attr, String memberId, long score, long cmptTaskId) {
        threadPoolManager.get(Const.IMPORTANT_POOL).execute(new Runnable() {
            @Override
            public void run() {
                final int three = 3;
                for (int i = 1; i <= three; i++) {
                    String msg = String.format("<font color=\"green\">【公会完成高光任务通知】</font>\n" +
                            "sid:%s,asid:%s,荣耀值:%s\n" +
                            "任务id:%s【1-最高档 3-默认档位】\n" +
                            "本轮通知次数:%s（正常通知3次，怕漏发）", memberId, commonService.getAsid(Convert.toLong(memberId)), score, cmptTaskId, i);
                    baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());
                    SysEvHelper.waiting(1000);
                }
            }
        });
    }

    private void saveCompleteTaskData(RankingScoreChanged event, ChannelWhiteListTaskComponentAttr attr
            , String memberId, long cmptTaskId) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        log.info("set complete task,memberId:{},taskId:{}", memberId, cmptTaskId);
        //写完成任务记录
        String recordKey = makeKey(attr, COMPLETE_TASK_MEMBER_KEY);
        actRedisDao.hsetnx(redisGroup, recordKey, memberId, System.currentTimeMillis() + "");

        //符合扣额度标准，执行发奖
        if (attr.getTaskIdAward() != null && attr.getTaskIdAward().containsKey(cmptTaskId)) {
            List<TaskPackageInfo> packageInfo = attr.getTaskIdAward().get(cmptTaskId);
            for (TaskPackageInfo award : packageInfo) {
                String seq = makeKey(attr, "award_" + memberId + award.getTaskId() + "_" + award.getPackageId());
                log.info("begin doWelfareV2,seq:{},actId:{},memberId:{},taskId:{},packageId:{},count:{}"
                        , seq, attr.getActId(), memberId, award.getTaskId(), award.getPackageId(), award.getCount());
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), attr.getAwardBusiId(), Convert.toLong(memberId), award.getTaskId(), award.getCount(), award.getPackageId(), seq, Maps.newHashMap());
            }
        }
        //广播
        BroadcastConfig broadcastConfig = attr.getTaskIdBanner().get(cmptTaskId);
        threadPoolManager.get(Const.IMPORTANT_POOL).execute(new Runnable() {
            @Override
            public void run() {
                invokeBro(event, attr, memberId, broadcastConfig);
            }
        });
    }


    private void invokeBro(RankingScoreChanged event, ChannelWhiteListTaskComponentAttr attr, String memberId, BroadcastConfig config) {
        //延迟广播
        if (config.getDelayMillSeconds() > 0) {
            log.info("invokeBro delay,actId:{},uid:{},score:{},config:{}", attr.getActId(), memberId, getMemberScore(event, attr), JSON.toJSONString(config));
            SysEvHelper.waiting(config.getDelayMillSeconds());
        }

        int broType = config.getBroType();
        String bannerUrl = config.getBannerUrl();
        MemberInfo memberInfo = memberInfoService.getMemberInfo(0L, RoleType.findByValue(attr.getRoleType()), memberId);
        String nick = memberInfo.getName();
        long actId = attr.getActId();
        long score = getMemberScore(event, attr);

        String asId = "";
        EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(attr.getActId(), 0L, attr.getRoleType(), memberId);
        if (enrollmentInfo != null) {
            asId = enrollmentInfo.getSignAsid() + "";
        } else {
            log.error("bro can not get enrollmentInfo,actId:{},memberId:{}", actId, memberId);
        }

        Map<String, String> extMap = Maps.newHashMap();
        extMap.put("nickName", nick);
        extMap.put("logo", StringUtil.isNotBlank(memberInfo.getHdLogo()) ? memberInfo.getHdLogo() : memberInfo.getLogo());
        extMap.put("value", score + "");
        extMap.put("svgaUrl", bannerUrl);
        extMap.put("asid", asId);
        extMap.put("ext", config.getExt());

        int bannerId = config.getBannerId();
        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setBannerId(bannerId).setUserNick(nick)
                .setUserScore(score).setJsonData(JSON.toJSONString(extMap)).build();

        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();


        broadCastHelpService.broadcast(actId, BusiId.findByValue(attr.getBroBusiId()), broType, 0L, 0L, bannerBroMsg);
        log.info("broadcast,actId:{},broBusiId:{},broType:{},bannerBroMsg:{}", actId, attr.getBroBusiId(), broType, JsonFormat.printToString(bannerBroMsg));

        bigDataService.saveNoRankDataToFile(actId, BusiId.GAME_ECOLOGY, System.currentTimeMillis(), memberId
                , RoleType.ANCHOR, score, BigDataScoreType.RANK_SCORE_BANNER, config.getDesc() + "|" + config.getBannerId() + "|" + config.getBroType());
    }


    /**
     * 读取任务信息
     */
    @GetMapping("/queryTaskInfo")
    public Response<WhiteListTaskVo> queryTaskInfo(long actId, long index, HttpServletRequest req, HttpServletResponse resp) {
        long uid = loginService.getLoginYYUid(req, resp);
        if (uid <= 0) {
            return Response.fail(100, "未登录");
        }
        return queryTaskInfo(actId, index, uid);
    }

    public Response<WhiteListTaskVo> queryTaskInfo(long actId, long index, long loginUid) {
        long sid = signedService.getSignedSid(loginUid, Template.Jiaoyou);
        if (sid <= 0) {
            return Response.fail(101, "未签约");
        }

        String memberId = Convert.toString(sid);
        WhiteListTaskVo taskVo = new WhiteListTaskVo();

        //频道信息
        ChannelBaseInfo channelBaseInfo = commonService.getChannelInfo(sid, true);
        taskVo.setAsId(channelBaseInfo.getAsid());
        taskVo.setHeaderUrl(channelBaseInfo.getLogo());
        taskVo.setNick(channelBaseInfo.getName());

        //任务配置信息
        ChannelWhiteListTaskComponentAttr attr = getComponentAttr(actId, index);
        long taskId = attr.getMemberTask().getOrDefault(memberId, attr.getDefaultTaskId());
        long taskScore = attr.getTaskIdScore().getOrDefault(taskId, 0L);
        taskVo.setTaskId(taskId);
        taskVo.setTaskScore(taskScore);
        taskVo.setAwardLimit(attr.getAwardLimit());

        //剩余奖励名额
        String alreadyRelease = makeKey(attr, AWARD_ALREADY_RELEASE);
        long release = Convert.toLong(actRedisDao.get(getRedisGroupCode(actId), alreadyRelease), 0);
        taskVo.setLeftAward(attr.getAwardLimit() - release);

        //当前分值
        ActorQueryItem actorStatus = new ActorQueryItem();
        actorStatus.setActorId(memberId);
        actorStatus.setRoleType(attr.getRoleType());
        actorStatus.setWithStatus(false);
        actorStatus.setRankingId(attr.getRankId());
        actorStatus.setPhaseId(attr.getPhaseId());
        ActorInfoItem actorInfoItem = hdztRankingThriftClient.queryActorRankingInfo(actId, actorStatus);
        if (actorInfoItem != null) {
            taskVo.setCurScore(actorInfoItem.getScore());
        }

        //是否真实完成任务
        String recordKey = makeKey(attr, COMPLETE_TASK_MEMBER_KEY);
        String content = actRedisDao.hget(getRedisGroupCode(actId), recordKey, memberId);
        taskVo.setRealComplete(StringUtil.isNotBlank(content));

        return Response.success(taskVo);
    }

    private long getMemberScore(RankingScoreChanged event, ChannelWhiteListTaskComponentAttr attr) {
        return attr.getPhaseId() > 0 ? event.getPhaseScore() : event.getRankScore();
    }

    private boolean isMyduty(long rankId, long phaseId, ChannelWhiteListTaskComponentAttr attr) {
        return rankId == attr.getRankId() && phaseId == attr.getPhaseId();
    }
}
