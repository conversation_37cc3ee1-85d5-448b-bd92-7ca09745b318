package com.yy.gameecology.hdzj.element.redis;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.kite.KiteRoadMap;
import com.yy.gameecology.activity.bean.kite.KiteRoadMapCpInfo;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CpPeakRomanceComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.LotteryAwardConfig;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.DecimalFormat;
import java.util.*;

@UseRedisStore
@RequestMapping("/5112")
@RestController
@Component
public class CpPeakRomanceComponent extends BaseActComponent<CpPeakRomanceComponentAttr> {


    /**
     * 漂流瓶cp当前公里
     */
    private static final String PEAK_TOTAL_HEIGH = "peak_total_heigh";



    /**
     * 楼层路线图
     */
    private static final String PEAK_ROAD_MAP_CP_KEY = "peak_road_map_cp:%d";

    /**
     * 发放礼物总金额
     */
    private static final String PEAK_ROMANCE_TOTAL_AWARD_CONSUME = "peak_romance_total_award_consume";

    private static final String PEAK_AWARD_NOTICE_TYPE = "5112_peak_award";

    /**
     * 我的中奖记录
     */
    private final String MY_AWARD_LIST = "my_award_list:%s";

    /**
     * 全局中奖记录
     */
    private final String WHOLE_AWARD_LIST = "whole_award_list";


    /**
     * 主播最后收礼频道
     * <p>
     * TODO 监听榜单变化事件，记录每个主播最近一次收礼频道
     */
    private static final String LAST_RECEIVE_GIFT_CHANNEL = "last_receive_gift_channel";

    private static final long PEAK_CP_BRO_BANNER_ID = 5112001L;


    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;


    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Override
    public Long getComponentId() {
        return ComponentId.CP_PEAK_ROMANCE;
    }



    /**
     * 监听榜单变化
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void handleScoreChange(RankingScoreChanged event, CpPeakRomanceComponentAttr attr) {
        log.info("handleScoreChange event:{},attr:{}", JsonUtil.toJson(event), JsonUtil.toJson(attr));
        if (event.getRankId() != attr.getRankId()) {
            return;
        }
        if (event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        String eventSeq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        String[] members = event.getMember().split("\\|");
        long userUid = Long.parseLong(members[0]);
        long babyUid = Long.parseLong(members[1]);

        String groupCode = getRedisGroupCode(attr.getActId());

        String totalHeighKey = getTotalHeighKey(attr);
        String totalHeighSeq = makeKey(attr,"seq:totalHeigh:"+eventSeq);
        List<Long> ret;
        //TODO 如果次组件要重新启用，则需要改造此处
        if (true) {
            throw new RuntimeException("incrValueWithSeq.lua,lua forbid");
        }
//        List<Long> ret = actRedisDao.incrValueWithSeq(groupCode, totalHeighSeq, totalHeighKey, 1, DateUtil.ONE_MONTH_SECONDS);
        if (ret.get(1) <= 0) {
            log.warn("handleScoreChange incrValueWithSeq error  actId:{},addResult:{}", attr.getActId(), JsonUtil.toJson(ret));
            return;
        }

        long cnt = ret.get(0);
        long round = cnt / attr.getHeighLimit() + ((cnt % attr.getHeighLimit()) > 0 ? 1: 0);
        long roundHeigh = cnt % attr.getHeighLimit() == 0 ? attr.getHeighLimit() : (int)cnt% attr.getHeighLimit();

        //改为sorted存储路线图
        KiteRoadMapCpInfo roadMapCpInfoVo = new KiteRoadMapCpInfo();
        roadMapCpInfoVo.setUserUid(userUid);
        roadMapCpInfoVo.setBabyUid(babyUid);
        roadMapCpInfoVo.setCureentStep(roundHeigh);
        String roadMapKey = getRoundRoadMapKey(attr, round);
        actRedisDao.zAdd(groupCode,roadMapKey,JsonUtil.toJson(roadMapCpInfoVo),roundHeigh);

        //记录当前主持送礼时最新room，兼容没有传厅的情况
        String giftChlkey = makeKey(attr, LAST_RECEIVE_GIFT_CHANNEL);
        String subChannel = event.getActors().get(attr.getTingActor());
        if(StringUtils.isNotEmpty(subChannel)){
            actRedisDao.hset(groupCode, giftChlkey, String.valueOf(babyUid), subChannel);
        }

        if(roundHeigh==attr.getHeighLimit()){
            releasePeakCpAward(attr,eventSeq,userUid,babyUid,subChannel);
        }


    }

    private void releasePeakCpAward(CpPeakRomanceComponentAttr attr,String eventSeq,long userUid,long babyUid,String subChannel) {

        //抽奖，发奖
        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        String lotteryrHashSeq = MD5SHAUtil.getMD5(makeKey(attr,"seq:lottery:"+eventSeq + "_" + userUid + "_" + babyUid));
        BatchLotteryResult result = hdztAwardServiceClient.doBatchLottery(lotteryrHashSeq, attr.getBusiId(), userUid, attr.getLotteryTaskId(), 3);
        if (result.getCode() != 0) {
            log.error("ChampionCpComponent lottery error uid:{}, ret:{}",userUid, JsonUtil.toJson(result));
            throw new RuntimeException("抽奖失败 actId:"+attr.getActId());
        }
        Map<Long, Long> recordIds = result.getRecordPackages();
        final long lotteryPackageId = recordIds.values().stream().findFirst().orElse(0L);
        log.info("releasePeakCpAward uid:{},lotteryPackageId:{},actId:{}",userUid,lotteryPackageId,attr.getActId());
        LotteryAwardConfig awardConfig = attr.getAwardConfigs().stream().filter(v->v.getLotteryTaskId()==attr.getLotteryTaskId() && v.getLotteryPackageId()==lotteryPackageId).findFirst().orElse(null);
        if(awardConfig==null){
            log.error("releasePeakCpAward error show is null,actId:{},userUid:{},babyUid{}",attr.getActId(),userUid,babyUid);
            return;
        }

        //累计发放金额
        String groupCode = getRedisGroupCode(attr.getActId());
        String totalAwardKey = makeKey(attr,PEAK_ROMANCE_TOTAL_AWARD_CONSUME);
        String totalPoolSeq = makeKey(attr, "seq:totalAwardConsume:" + eventSeq);
        List<Long> addResult;
//        List<Long> addResult = actRedisDao.incrValueWithSeq(groupCode, totalPoolSeq, totalAwardKey, awardConfig.getGiftAmount() * 2, DateUtil.ONE_MONTH_SECONDS);
        //TODO 如果次组件要重新启用，则需要改造此处
        if (true) {
            throw new RuntimeException("incrValueWithSeq.lua,lua forbid");
        }
        if (addResult.get(1) <= 0) {
            log.warn("releasePeakCpAward awardpool incrValueWithSeq error  actId:{},uid:{},anchorUid:{},addResult:{},totalPoolSeq:{}", attr.getActId(), userUid, babyUid, JsonUtil.toJson(addResult), totalPoolSeq);
            return;
        }

        String userHashSeq = MD5SHAUtil.getMD5(makeKey(attr,"seq:welfare:"+eventSeq +  "_" + userUid + "_user" ));
        BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(time, attr.getBusiId(), userUid, awardConfig.getTaskId(), 1, awardConfig.getPackageId(), userHashSeq, 2);
        boolean suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
        if(!suc) {
            // 需要人工介入处理
            log.error("releasePeakCpAward user doWelfare error,  actId:{}, uid:{}, seq:{}  ret:{}", attr.getActId(), userUid, userHashSeq, batchWelfareResult);
        }

        String babyHashSeq =  MD5SHAUtil.getMD5(makeKey(attr,"seq:welfare:"+eventSeq +  "_" + babyUid + "_baby" ));
        batchWelfareResult =  hdztAwardServiceClient.doWelfare(time, attr.getBusiId(), babyUid, awardConfig.getTaskId(), 1, awardConfig.getPackageId(), babyHashSeq, 2);
        suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
        if(!suc) {
            // 需要人工介入处理
            log.error("releasePeakCpAward  baby doWelfare error,  actId:{}, uid:{}, seq:{} ret:{}", attr.getActId(), babyUid, babyHashSeq, batchWelfareResult);
        }

        //累计任务完成顶部广播
        String bannerSeq = "seq:banner:" + eventSeq;
        RetryTool.withRetryCheck(attr.getActId(), bannerSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
            String layerAwardIcon = StringUtils.isNotEmpty(awardConfig.getLayerAwardIcon()) ? awardConfig.getLayerAwardIcon() : awardConfig.getAwardIcon();
            broCpBanner(attr,userUid, babyUid, layerAwardIcon,awardConfig.getNum());
        });

        //获奖弹窗
        String noticeSeq = "seq:award:" + eventSeq;
        RetryTool.withRetryCheck(attr.getActId(), noticeSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
            broAwardNotice(attr,userUid, babyUid, awardConfig.getAwardName(),awardConfig.getAwardIcon(),awardConfig.getNum());
        });

        //添加获奖记录
        addAwardRecord(attr,userUid, babyUid, awardConfig.getAwardName(),awardConfig.getBigAward()>0,eventSeq);

        //发送如流通知
        sendPeakCpStatic( awardConfig, userUid, babyUid, subChannel,attr);
    }

    private void sendPeakCpStatic(LotteryAwardConfig awardConfig,long userUid,long babyUid,String subChannel,CpPeakRomanceComponentAttr attr) {
        String fly = null;
        if(StringUtils.isNotEmpty(subChannel)){
            String[] array = subChannel.split("_");
            long sid = Convert.toLong(array[0]);
            long ssid = Convert.toLong(array[1]);
            fly = String.format("yy://pd-[sid=%d&subid=%d]",sid,ssid);
        }

        Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfo(Lists.newArrayList(userUid,babyUid), Template.getTemplate((int)attr.getBusiId()));
        UserInfoVo userInfo = userInfoVoMap.get(userUid);
        UserInfoVo babyInfo = userInfoVoMap.get(babyUid);
        StringBuilder content = new StringBuilder();
        content.append("本轮巅峰CP：\n");
        content.append(babyInfo.getNick()).append("(").append(babyInfo.getUid()).append(")&");
        content.append(userInfo.getNick()).append("(").append(userInfo.getUid()).append(")\n");
        content.append("获得奖励：").append(awardConfig.getAwardName()).append("\n");
        content.append("所在房间飞机票：").append(fly).append("\n");

        String groupCode = getRedisGroupCode(attr.getActId());
        String totalAwardKey = makeKey(attr,PEAK_ROMANCE_TOTAL_AWARD_CONSUME);
        String totalAwardValue = actRedisDao.get(groupCode,totalAwardKey);

        if(StringUtils.isNotEmpty(totalAwardValue)) {
            content.append("全服当前累计发放礼物金额：").append(formatMoney(Convert.toLong(totalAwardValue,0))).append("元\n");
        }else{
            content.append("全服当前累计发放礼物金额：").append("0元\n");
        }

        String msg = buildActRuliuMsg(attr.getActId(), false, "浪漫巅峰玩法", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());
    }

    public static String formatMoney(long awardNum) {
        DecimalFormat df = new DecimalFormat("#.#");
        return df.format((float)awardNum/1000);
    }
    private void addAwardRecord(CpPeakRomanceComponentAttr attr,long userUid,long babyUid,String giftName,boolean bigAward,String eventSeq) {

        AwardRecord awardInfo = new AwardRecord();
        awardInfo.setUserUid(userUid);
        awardInfo.setBabyUid(babyUid);
        awardInfo.setGiftName(giftName);
        awardInfo.setTimestamp(commonService.getNow(attr.getActId()).getTime());
        awardInfo.setBigAward(bigAward);
        String content = JSON.toJSONString(awardInfo);
        //写我的中奖记录
        String userRecordKey = makeKey(attr, String.format(MY_AWARD_LIST, userUid));
        String anchorRecordKey = makeKey(attr, String.format(MY_AWARD_LIST, babyUid));

        String redisGroup = getRedisGroupCode(attr.getActId());
        String userSeq = makeKey(attr,"seq:userawardrecord:"+ eventSeq + "_" + userUid);
        //actRedisDao.lPushWithSeq(redisGroup, userSeq, userRecordKey, content, DateUtil.ONE_DAY_SECONDS);
        //TODO 如果次组件要重新启用，则需要改造此处
        if (true) {
            throw new RuntimeException("lPushWithSeq,lua forbid");
        }
        String anchorSeq = makeKey(attr,"seq:anchorawardrecord:"+ eventSeq + babyUid );
        //actRedisDao.lPushWithSeq(redisGroup, anchorSeq, anchorRecordKey, content, DateUtil.ONE_DAY_SECONDS);
        //TODO 如果次组件要重新启用，则需要改造此处
        if (true) {
            throw new RuntimeException("lPushWithSeq,lua forbid");
        }
        //写全服中奖记录
        String wholeKey = makeKey(attr, WHOLE_AWARD_LIST);
        String wholdSeq = makeKey(attr,"seq:wholdawardrecord:"+eventSeq + "_" + userUid+"_" + babyUid);
        //actRedisDao.lPushWithSeq(redisGroup, wholdSeq, wholeKey, content, DateUtil.ONE_DAY_SECONDS);
        //TODO 如果次组件要重新启用，则需要改造此处
        if (true) {
            throw new RuntimeException("lPushWithSeq,lua forbid");
        }
    }

    private void broAwardNotice(CpPeakRomanceComponentAttr attr,long userUid , long babyUid , String awardName,String awardIcon,int count) {

        Map<String, Object> noticeValue = Maps.newHashMap();
        noticeValue.put("giftName", awardName);
        noticeValue.put("giftIcon", awardIcon);
        noticeValue.put("giftCount",count);
        noticeValue.put("cpMember", userUid+"|"+babyUid);
        log.info("broAwardNotice commonNoticeUnicast actId:{},userUid:{},babyUid:{}",attr.getActId(),userUid,babyUid);
        commonBroadCastService.commonNoticeUnicast(attr.getActId(),PEAK_AWARD_NOTICE_TYPE , JsonUtil.toJson(noticeValue), StringUtils.EMPTY, userUid);
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), PEAK_AWARD_NOTICE_TYPE, JsonUtil.toJson(noticeValue), StringUtils.EMPTY, babyUid);
    }

    private void broCpBanner(CpPeakRomanceComponentAttr attr,long userUid , long babyUid, String giftIcon,int giftCount) {
        Set<Long> uids = new HashSet<>();
        uids.add(userUid);
        uids.add(babyUid);
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers,attr.getTemplateType());

        Map<String, Object> ext = Maps.newHashMap();
        UserInfoVo userInfoVo = userInfos.get(userUid);
        UserInfoVo babyInfoVo = userInfos.get(babyUid);
        ext.put("userUid",userUid);
        ext.put("babyUid",babyUid);
        ext.put("giftIcon",giftIcon);
        ext.put("giftCount",giftCount);
        if(userInfoVo!=null ){
            ext.put("userLogo",userInfoVo.getAvatarUrl());
            ext.put("userNick",userInfoVo.getNick());
        }

        if(babyInfoVo!=null) {
            ext.put("babyLogo",babyInfoVo.getAvatarUrl());
            ext.put("babyNick",babyInfoVo.getNick());
        }
        ext.put("nickExtUsers",JsonUtil.toJson(multiNickUsers));

        UserCurrentChannel channel = getAnchorAwardChannel(attr, babyUid);
        if(channel!=null){
            ext.put("sid",channel.getTopsid());
            ext.put("ssid",channel.getSubsid());
        }

        commonBroadCastService.commonBannerBroadcast(0, 0, 0, com.yy.thrift.broadcast.Template.findByValue(attr.getBroTemplate()),4
                , attr.getActId(), 0L,0L, PEAK_CP_BRO_BANNER_ID, 0L, ext);
        log.info("broCpBanner commonBannerBroadcast ext:{}",JsonUtil.toJson(ext));
    }


    private UserCurrentChannel getAnchorAwardChannel(CpPeakRomanceComponentAttr attr, long anchorUid) {
        UserCurrentChannel channel = commonService.getUserCurrentChannel(anchorUid);
        if (channel != null) {
            return channel;
        }

        //最近一次收礼的频道
        String key = makeKey(attr, LAST_RECEIVE_GIFT_CHANNEL);
        String lastReceive = actRedisDao.hget(getRedisGroupCode(attr.getActId()), key, Convert.toString(anchorUid));
        if (StringUtils.isNotEmpty(lastReceive)) {
            channel = new UserCurrentChannel();
            String[] array = lastReceive.split("_");
            channel.setTopsid(Convert.toLong(array[0]));
            channel.setSubsid(Convert.toLong(array[1]));
            return channel;
        }

        log.error("getAnchorAwardChannel null,anchorUid:{}", anchorUid);
        return null;
    }
    /**
     * 楼层信息 type 1 本轮 2 上轮
     */
    @RequestMapping(value = "/getRoadMapCpList")
    public Response<KiteRoadMap> getRoadMapCpList(@RequestParam("actId") long actId,
                                                  @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx,
                                                  int type) {

        CpPeakRomanceComponentAttr attr = getComponentAttr(actId, cmptInx);
        String groupCode = getRedisGroupCode(attr.getActId());
        String totalHeighKey = getTotalHeighKey(attr);
        int cnt = Convert.toInt(actRedisDao.get(groupCode, totalHeighKey));
        long nowRound = cnt / attr.getHeighLimit() + ((cnt % attr.getHeighLimit()) > 0 ? 1: 0);
        //没有人送礼的时候，显示为第一轮
        if(cnt==0){
            nowRound = 1;
        }
        long round = (type==1) ? nowRound : nowRound-1;

        KiteRoadMap rsp = new KiteRoadMap();
        rsp.setRound(nowRound);
        List<KiteRoadMapCpInfo> list = Lists.newArrayList();
        if(round>0){
            String roadMapKey = getRoundRoadMapKey(attr, round);
            Set<ZSetOperations.TypedTuple<String>> tuples  = actRedisDao.zrevRange(groupCode,roadMapKey,attr.getHeighLimit());
            if (!CollectionUtils.isEmpty(tuples)) {
                Set<Long> uids = Sets.newHashSetWithExpectedSize(tuples.size()*2);
                for (ZSetOperations.TypedTuple<String> tuple : tuples) {
                    KiteRoadMapCpInfo record = JSON.parseObject( tuple.getValue(), KiteRoadMapCpInfo.class);
                    uids.add(record.getUserUid());
                    uids.add(record.getBabyUid());
                    list.add(record);
                }
                Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
                Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers,810);
                list.stream().forEach(v -> {
                    long babyUid = v.getBabyUid();
                    long userUid = v.getUserUid();
                    //获取正在开播频道
                    ChannelInfoVo onMicChannel = onMicService.getOnMicChannel(babyUid);
                    if (onMicChannel != null) {
                        v.setSid(onMicChannel.getSid());
                        v.setSsid(onMicChannel.getSsid());
                    }

                    if(userInfos!=null && userInfos.containsKey(userUid)) {
                        v.setUserLogo(userInfos.get(userUid).getAvatarUrl());
                        v.setUserNick(userInfos.get(userUid).getNick());
                    }
                    if(userInfos!=null && userInfos.containsKey(babyUid)) {
                        v.setBabyLogo(userInfos.get(babyUid).getAvatarUrl());
                        v.setBabyNick(userInfos.get(babyUid).getNick());
                    }

                });

                rsp.setList(list);
                rsp.setNickExtUsers(multiNickUsers);
            }

        }

        return  Response.success(rsp);

    }



    private String getRoundRoadMapKey(CpPeakRomanceComponentAttr attr,long round) {

        return makeKey(attr, String.format(PEAK_ROAD_MAP_CP_KEY, round));
    }

    private String getTotalHeighKey(CpPeakRomanceComponentAttr attr) {
        return makeKey(attr, PEAK_TOTAL_HEIGH);
    }

    /**
     * 我的获奖记录
     */
    @RequestMapping("/queryMyAwardRecord")
    public Response<AwardRecordRsp> queryMyAwardRecord(HttpServletRequest request, HttpServletResponse response,
                                                                           Long actId, long cmptInx) {
        Long uid = getLoginYYUid(request, response);
        if(uid <= 0L) {
            return Response.fail(400, "未登录");
        }
        CpPeakRomanceComponentAttr attr = getComponentAttr(actId,cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        String userRecordKey = makeKey(attr, String.format(MY_AWARD_LIST, uid));
        AwardRecordRsp  rsp = getAwardListByKey(actId,userRecordKey,null,false);
        return Response.success(rsp);

    }


    /**
     * 全服获奖记录
     */
    @RequestMapping("/queryAllAwardRecord")
    public Response<AwardRecordRsp> queryAllAwardRecord(HttpServletRequest request, HttpServletResponse response,
                                                                            Long actId, long cmptInx) {
        CpPeakRomanceComponentAttr attr = getComponentAttr(actId,cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        String wholeKey = makeKey(attr, WHOLE_AWARD_LIST);
        AwardRecordRsp  rsp = getAwardListByKey(actId,wholeKey,attr.getAllAwardTopN(),true);
        return Response.success(rsp);
    }


    private AwardRecordRsp getAwardListByKey(long actId, String awardKey,Integer awardTopN,boolean getMicChannel) {

        awardTopN = awardTopN==null ? 500 : awardTopN;
        AwardRecordRsp rsp = new AwardRecordRsp();
        String redisGroup = getRedisGroupCode(actId);
        List<String> awardRecord = actRedisDao.lrange(redisGroup, awardKey, 0,awardTopN-1);
        if (CollectionUtils.isNotEmpty(awardRecord)) {
            List<AwardRecord> list = Lists.newArrayListWithCapacity(awardRecord.size());
            Set<Long> uids = Sets.newHashSetWithExpectedSize(100);
            for (String item : awardRecord) {
                AwardRecord record = JSON.parseObject(item, AwardRecord.class);
                uids.add(record.getUserUid());
                uids.add(record.getBabyUid());
                list.add(record);
            }
            Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
            Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers,810);
            for(AwardRecord item : list) {
                UserInfoVo userInfoVo = userInfos.get(item.getUserUid());
                UserInfoVo babyInfoVo =  userInfos.get(item.getBabyUid());
                if (userInfoVo != null) {
                    item.setUserNick(userInfoVo.getNick());
                    item.setUserLogo(userInfoVo.getAvatarUrl());
                }
                if(babyInfoVo!=null) {
                    item.setBabyNick(babyInfoVo.getNick());
                    item.setBabyLogo(babyInfoVo.getAvatarUrl());
                }
                if(getMicChannel){
                    //获取正在开播频道
                    ChannelInfoVo onMicChannel = onMicService.getOnMicChannel(item.getBabyUid());
                    if (onMicChannel != null) {
                        item.setSid(onMicChannel.getSid());
                        item.setSsid(onMicChannel.getSsid());
                    }
                }
            }

            rsp.setList(list);
            rsp.setNickExtUsers(multiNickUsers);
        }

        return rsp;
    }


    @Data
    private static class AwardRecordRsp {
        private List<AwardRecord> list;

        private Map<String, Map<String, MultiNickItem>> nickExtUsers;
    }

    @Data
    private static class AwardRecord {
        private long userUid;
        private String userLogo;
        private String userNick;
        private long babyUid;
        private String babyLogo;
        private String babyNick;
        private String giftName;

        private long timestamp;

        private boolean bigAward;

        /**
         * 开播频道
         */
        private Long sid;
        private Long ssid;
    }


}
