package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.CpFireWork;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Data
public class CpDiaryComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "cp榜单id")
    private long cpRankId;

    @ComponentAttrField(labelText = "cp日榜单id")
    private long cpDailyRankId;

    @ComponentAttrField(labelText = "cp 用户对主持贡献榜单id")
    private long cpContributeRankId;

    @ComponentAttrField(labelText = "cp 主持对用户贡献榜单id")
    private long cpAntiContributeRankId;

    @ComponentAttrField(labelText = "cp 用户对主持日贡献榜单id")
    private long cpContributeDailyRankId;

    @ComponentAttrField(labelText = "cp 主持对用户日贡献榜单id")
    private long cpAntiContributeDailyRankId;

    @ComponentAttrField(labelText = "厅角色id")
    private String tingRoleId;

    @ComponentAttrField(labelText = "全程阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "cp榜单拉取数量")
    private long rankLimit;

    @ComponentAttrField(labelText = "cp首送提示分数阈值")
    private long scoreThreshold;

    @ComponentAttrField(labelText = "累计任务阈值", remark = ",多个逗号分隔", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
    })
    private List<Long> missions;

    @ComponentAttrField(labelText = "每日任务阈值", remark = ",多个逗号分隔", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
    })
    private List<Long> dailyMissions;


    @ComponentAttrField(labelText = "每日任务奖池",remark = "每日任务奖池",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "每日任务等级"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = Long.class, labelText = "对应奖包")
            })
    private Map<Integer, List<Long>> dailyMissionAward = Maps.newHashMap();

    @ComponentAttrField(labelText = "累计任务奖池",remark = "累计任务奖池",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "累计任务等级"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = Long.class, labelText = "对应奖包")
            })
    private Map<Integer, List<Long>> missionAward = Maps.newHashMap();

    @ComponentAttrField(labelText = "每日任务名配置",remark = "每日任务名配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "每日任务等级"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "对应任务名")
            })
    private Map<Integer, String> dailyMissionName =  Maps.newHashMap();

    @ComponentAttrField(labelText = "累计任务名配置",remark = "累计任务名配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "累计任务等级"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "对应任务名")
            })
    private Map<Integer, String> missionName =  Maps.newHashMap();

    @ComponentAttrField(labelText = "奖包奖励别名",remark = "奖包奖励别名配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "奖包id"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "对应别名")
            })
    private Map<Long, String> awardPackage =  Maps.newHashMap();

    @ComponentAttrField(labelText = "烟花配置",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = CpFireWork.class)})
    private List<CpFireWork> fireWorkList = Collections.emptyList();

    @ComponentAttrField(labelText = "cp任务奖池id")
    private long cpMissionTaskId;

    @ComponentAttrField(labelText = "广播模板", remark = "2==宝贝 3==交友 5==语音房(技能卡)")
    private int broTemplate;



}
