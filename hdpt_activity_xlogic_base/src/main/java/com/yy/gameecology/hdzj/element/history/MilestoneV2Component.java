package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.service.EnrollmentService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.MyListUtils;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.MilestoneV2ComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于陪玩团,目前陪玩业务已经不做活动,先暂时移动到历史文件夹
 *
 * @Author: CXZ
 * @Desciption: 里程碑V2组件
 * @Date: 2021/09/16 15:09
 * @Modified:
 */
@Component
@Deprecated
public class MilestoneV2Component extends BaseActComponent<MilestoneV2ComponentAttr> {

    private Logger logger = LoggerFactory.getLogger(MilestoneV2Component.class);

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private EnrollmentService enrollmentService;


    @Override
    public Long getComponentId() {
        return ComponentId.MILE_STONE_V2;
    }

    /***
     * 结算里程碑
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = false)
    public void settleMilestone(PhaseTimeEnd event, MilestoneV2ComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        MilestoneV2ComponentAttr.LevelConfig config = attr.getLevelConfig(rankId, phaseId);

        if (config == null) {
            return;
        }
        long actId = attr.getActId();
        long cmptUseInx = attr.getCmptUseInx();
        long thresholdScore = config.getThresholdScore();
        int level = config.getLevel();

        log.info("settleMilestone begin,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));

        String endTime = event.getEndTime();
        Date eventDate = DateUtil.getDate(endTime);
        String dateStr = TimeKeyHelper.getTimeCode(event.getTimeKey(), eventDate);

        //获取符合要求的陪玩团榜单数据
        List<Rank> ranks = queryRanksByThreshold(actId, rankId, phaseId, dateStr, 100, thresholdScore);

        Map<String, QueryRankingRequest> playerRequests = Maps.newHashMap();
        Map<String, QueryRankingRequest> anchorRequests = Maps.newHashMap();

        for (Rank rank : ranks) {
            String teamId = rank.getMember();
            long score = Convert.toLong(rank.getScore());

            if (score < thresholdScore) {
                continue;
            }

            playerRequests.put(teamId, setupRequest(actId, phaseId, config.getPlayerRankId(), dateStr, config.getPlayerTopN(), teamId));
            anchorRequests.put(teamId, setupRequest(actId, phaseId, config.getAnchorRankId(), dateStr, config.getAnchorTopN(), teamId));

            log.info("settleMilestone info@actId:{} index:{} teamId:{} score:{} level:{}", actId, cmptUseInx, teamId, score, level);

            bigDataService.saveNoRankDataToFile(actId, BusiId.PEI_WAN, eventDate.getTime(), teamId, RoleType.PWTUAN, level
                    , 15, "", 0, score);
        }

        award(playerRequests, anchorRequests, eventDate, config, attr);

    }

    /**
     * 全业务广播横幅
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void doMilestoneBroadcast(TaskProgressChanged event, MilestoneV2ComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        MilestoneV2ComponentAttr.LevelConfig config = attr.getLevelConfig(rankId, phaseId);
        if (config == null || !config.isBroadcast()) {
            return;
        }
        String teamId = event.getMember();
        doMilestoneBroadcastTop(attr.getActId(), teamId);
        log.info("doMilestoneBroadcast done@actId:{},index:{},level:{},teamId:{}"
                , attr.getActId(), attr.getCmptUseInx(), config.getLevel(), teamId);
    }

    private void award(Map<String, QueryRankingRequest> playerRequests, Map<String, QueryRankingRequest> anchorRequests,
                       Date eventDate, MilestoneV2ComponentAttr.LevelConfig config, MilestoneV2ComponentAttr attr) {


        Clock clock = new Clock();
        long actId = attr.getActId();
        long cmptUseInx = attr.getCmptUseInx();
        Map<String, BatchRankingItem> playerMap = hdztRankingThriftClient.queryBatchRanking(playerRequests, null);
        Map<String, BatchRankingItem> anchorMap = hdztRankingThriftClient.queryBatchRanking(anchorRequests, null);

        String time = DateUtil.getNowYyyyMMddHHmmss();
        Map<Long, Map<Long, Integer>> playerAwardPackageIds = Collections.singletonMap(attr.getTaskId(), Collections.singletonMap(config.getPlayerAwardPackageId(), 1));
        for (Map.Entry<String, BatchRankingItem> entry : playerMap.entrySet()) {
            List<Rank> ranks = entry.getValue().getData();
            String hoverMemberId = entry.getKey();
            if (CollectionUtils.isEmpty(ranks)) {
                log.error("milestoneComponent award ignore not find player rank@actId:{} index:{} hoverMemberId:{}",
                        actId, cmptUseInx, hoverMemberId);
                continue;
            }
            for (Rank rank : ranks) {
                hdztAwardServiceClient.doBatchWelfare(Convert.toLong(rank.getMember()), playerAwardPackageIds, time, attr.getRetry(), Maps.newHashMap());
                bigDataService.saveNoRankDataToFile(actId, BusiId.PEI_WAN, eventDate.getTime(), rank.getMember(), RoleType.USER, config.getLevel()
                        , 15, hoverMemberId);
            }
        }
        Map<Long, Map<Long, Integer>> anchorAwardPackageIds = Collections.singletonMap(attr.getTaskId(), Collections.singletonMap(config.getAnchorAwardPackageId(), 1));
        for (Map.Entry<String, BatchRankingItem> entry : anchorMap.entrySet()) {
            List<Rank> ranks = entry.getValue().getData();
            String hoverMemberId = entry.getKey();
            if (CollectionUtils.isEmpty(ranks)) {
                log.error("milestoneComponent award ignore not find anchor rank@actId:{} index:{} hoverMemberId:{}",
                        actId, cmptUseInx, hoverMemberId);
                continue;
            }
            for (Rank rank : ranks) {
                hdztAwardServiceClient.doBatchWelfare(Convert.toLong(rank.getMember()), anchorAwardPackageIds, time, attr.getRetry(), Maps.newHashMap());
                bigDataService.saveNoRankDataToFile(actId, BusiId.PEI_WAN, eventDate.getTime(), rank.getMember(), RoleType.ANCHOR, config.getLevel()
                        , 15, hoverMemberId);
            }
        }

        logger.info("milestone award finish :{} ", clock.tag());

    }

    private QueryRankingRequest setupRequest(long actId, long phaseId, long rankId, String dateStr, long topN, String teamId) {
        QueryRankingRequest request = new QueryRankingRequest();
        request.setActId(actId);
        request.setRankingId(rankId);
        request.setPhaseId(phaseId);
        request.setDateStr(dateStr);
        request.setRankingCount(topN);
        Map<String, String> ext = Maps.newHashMap();
        ext.put(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, teamId);
        request.setExtData(ext);
        return request;
    }

    /**
     * 做全频道广播
     *
     * <AUTHOR>
     */
    private void doMilestoneBroadcastTop(long actId, String teamId) {
        MemberInfo memberInfo = memberInfoService.getPwTeam(teamId);
        long asid = Convert.toLong(memberInfo.getAsid());
        GameecologyActivity.Act202011_PwMilestoneBanner.Builder pwMilestoneBanner =
                GameecologyActivity.Act202011_PwMilestoneBanner.newBuilder()
                        .setTeamName(memberInfo.getName())
                        .setAsid(asid).setActId(actId);

        GameecologyActivity.GameEcologyMsg message = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setAct202011PwMilestoneBanner(pwMilestoneBanner)
                .setUri(GameecologyActivity.PacketType.kAct202011_PwMilestoneBanner_VALUE)
                .build();
        svcSDKService.broadcastAllChanelsInPW(actId, message);
        logger.info("doMilestoneBroadcastTop done teamId:{} asid:{}", teamId, asid);
    }

    /**
     * 查询 符合门槛的 榜单List
     *
     * @param actId
     * @param rankId
     * @param phaseId
     * @param dateStr
     * @param querySize
     * @param threshold
     * @return
     */
    private List<Rank> queryRanksByThreshold(long actId, long rankId, long phaseId, String dateStr, int querySize, long threshold) {
        List<Rank> ranks;
        Clock clock = new Clock();
        do {
            ranks = hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, dateStr, querySize, Maps.newHashMap());
            long lastValue = Optional.ofNullable(MyListUtils.last(ranks)).map(Rank::getScore).orElse(0L);
            //最后一名的流水币阈值或者榜单已经没有数据
            if (lastValue < threshold || ranks.size() < querySize) {
                break;
            }
            //最后一名比阈值高并且后面还有数据的，重新查一次
            querySize *= 2;
            clock.tag();
        } while (true);

        List<Rank> newRanks = ranks.stream()
                .filter(rank -> rank.getScore() >= threshold)
                .collect(Collectors.toList());

        log.info("queryRanksByThreshold done@actId:{} rankId:{},phaseId:{},weekDay:{},querySize:{},ranks size:{} new ranks size:{} clock:{}",
                actId, rankId, phaseId, dateStr, querySize, ranks.size(), newRanks.size(), clock.tag());

        return newRanks;
    }


    /**
     * 查询团当天的里程碑任务状态
     *
     * @param actId
     * @param teamId
     * @return
     */
    public JSONObject getPwMilestoneStatus(long actId, Long teamId) {

        MilestoneV2ComponentAttr attr = getUniqueComponentAttr(actId);
        Assert.notNull(attr, "not find attr,actId=" + actId);

        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityCacheNoTime(actId);
        Assert.notNull(activityInfoVo, "activityInfoVo not find,actId=" + actId);


        JSONObject json = new JSONObject();

        Date now = commonService.getNow(actId);
        String day = DateUtil.format(now, DateUtil.PATTERN_TYPE2);

        Map<String, QueryRankingRequest> rankingRequests = Maps.newHashMap();
        Map<String, BatchRankingItem> result = Maps.newHashMap();
        List<MilestoneV2ComponentAttr.LevelConfig> configs = attr.getLevelConfigs().stream()
                .sorted(Comparator.comparingInt(MilestoneV2ComponentAttr.LevelConfig::getLevel))
                .collect(Collectors.toList());

        for (MilestoneV2ComponentAttr.LevelConfig levelConfig : configs) {
            QueryRankingRequest rankingRequest = new QueryRankingRequest();
            long rankId = levelConfig.getMainRankId();
            rankingRequest.setActId(actId);
            rankingRequest.setRankingId(rankId);
            rankingRequest.setPhaseId(levelConfig.getPhaseId());
            rankingRequest.setDateStr(day);
            rankingRequest.setPointedMember(teamId + "");
            rankingRequests.put(String.valueOf(rankId), rankingRequest);
        }

        result = hdztRankingThriftClient.queryBatchRanking(rankingRequests, null);

        List<JSONObject> tasks = Lists.newArrayList();

        for (MilestoneV2ComponentAttr.LevelConfig levelConfig : configs) {
            long rankId = levelConfig.getMainRankId();

            BatchRankingItem item = result.get(String.valueOf(rankId));
            long score = Optional.ofNullable(item).map(BatchRankingItem::getPointedRank)
                    .map(Rank::getScore).orElse(0L);

            JSONObject task = new JSONObject();
            task.put("score", score);
            task.put("target", levelConfig.getThresholdScore());
            task.put("rankId", rankId);
            tasks.add(task);
        }

        json.put("currTime", now.getTime() / 1000);
        json.put("tasks", tasks);

        return json;

    }

    /**
     * 查询公会下的所有团
     *
     * @param actId
     * @param sid
     * @return
     */
    public List<MemberInfo> getPwChannelAllTuan(long actId, Long sid) {

        MilestoneV2ComponentAttr attr = getUniqueComponentAttr(actId);
        Assert.notNull(attr, "not find attr,actId=" + actId);

        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityCacheNoTime(actId);
        Assert.notNull(activityInfoVo, "activityInfoVo not find,actId=" + actId);

        if (sid == null || sid <= 0) {
            return Lists.newArrayList();
        }

        List<String> memberIds;
        if (attr.getTuanContributionRankId() > 0) {
            HashMap<String, String> ext = Maps.newHashMap();
            ext.put(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, sid.toString());
            List<Rank> ranks = hdztRankingThriftClient.queryRankingCache(actId,
                    attr.getTuanContributionRankId(), 0, "", 1000, "", ext);
            memberIds = ranks.stream().map(Rank::getMember).collect(Collectors.toList());
        } else {
            List<EnrollmentInfo> enrollmentInfos = enrollmentService.getEntryConfigInfosBySign(actId, 90060L, sid);
            memberIds = enrollmentInfos.stream().map(EnrollmentInfo::getMemberId).collect(Collectors.toList());
        }


        Map<String, MemberInfo> memberInfoMap = memberInfoService.getPwTeamMap(memberIds);

        List<MemberInfo> memberInfoList = memberIds.stream()
                .map(memberInfoMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return memberInfoList;
    }
}
