package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.common.bean.UserInfo;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.PKRecord;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.HonorHallPkRecordComponentAttr;
import com.yy.java.webdb.WebdbChannelInfo;
import com.yy.thrift.hdztranking.GroupMemberItem;
import com.yy.thrift.hdztranking.PkGroupItem;
import com.yy.thrift.hdztranking.PkInfo;
import com.yy.thrift.hdztranking.Rank;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021.09.23 15:33
 */
@Deprecated
@Component
public class HonorHallPkRecordComponent extends BaseActComponent<HonorHallPkRecordComponentAttr> {
    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Override
    public Long getComponentId() {
        return ComponentId.HONOR_HALL_PK_RECORD_V2;
    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void pkRecord(PhaseTimeEnd event, HonorHallPkRecordComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        String endTime = event.getEndTime();
        long actId = event.getActId();

        if (actId != attr.getActId() || !attr.getRankIds().contains(rankId)) {
            return;
        }
        String rankPhase = rankId + ":" + phaseId;
        if (!attr.getRankPhaseList().contains(rankPhase)) {
            return;
        }
        String redisGroupCode = getRedisGroupCode(actId);

        String key = makeKey(actId, attr.getCmptId(), attr.getCmptUseInx(), "");
        if (!actRedisDao.hsetnx(redisGroupCode, key, event.getEkey(), DateUtil.getNowYyyyMMddHHmmss())) {
            return;
        }
        String dateStr = "";
        //日榜
        if (event.getTimeKey() == 1) {
            dateStr = DateUtil.format(DateUtil.getDate(endTime), DateUtil.PATTERN_TYPE2);
        }
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, dateStr, 1000, Maps.newHashMap());
        if (CollectionUtils.isEmpty(ranks)) {
            log.warn("pkRecord no data, rankId:{} phaseId:{}", rankId, phaseId);
            return;
        }

        List<PKRecord> pkRecords = setUpPKRecord(actId, rankId, phaseId, ranks, endTime, dateStr);
        String redisKey = Const.addActivityPrefix(actId, attr.getRedisKey());
        Secret lock = null;
        String pkRecord = "pk_record";
        try {
            Clock clock = new Clock();
            boolean flag = true;
            while (flag) {
                lock = locker.lock(pkRecord, 5);
                if (lock != null) {
                    handlePKRecordData(rankId, phaseId, redisGroupCode, pkRecords, redisKey);
                    flag = false;
                    log.info("handlePKRecordData done clock:{}, rankId:{} phaseId:{}", clock.tag(), rankId, rankPhase);
                } else {
                    SysEvHelper.waiting(1000);
                }
            }
        } finally {
            if (lock != null) {
                locker.unlock(pkRecord, lock);
            }
        }
    }

    private void handlePKRecordData(long rankId, long phaseId, String redisGroupCode, List<PKRecord> pkRecords, String redisKey) {
        String records = actRedisDao.get(redisGroupCode, redisKey);
        List<PKRecord> recordList = JSON.parseArray(records, PKRecord.class);
        if (recordList != null) {
            pkRecords.addAll(recordList);
        }
        pkRecords.sort(Comparator.comparingLong(PKRecord::getTotalScore).reversed());

        actRedisDao.set(redisGroupCode, redisKey, JSON.toJSONString(pkRecords));
        log.info("pkRecord done, rankId:{} phaseId:{}", rankId, phaseId);
    }

    private List<PKRecord> setUpPKRecord(long actId, long rankId, long phaseId, List<Rank> ranks, String endTime, String dateStr) {
        PkInfo pkInfo = hdztRankingThriftClient.queryPhasePkgroup(actId, rankId, phaseId, dateStr);
        Map<String, Rank> members = Maps.newHashMap();
        for (Rank rank : ranks) {
            members.put(rank.getMember(), rank);
        }
        List<PKRecord> pkRankItems = new ArrayList<>();
        List<PkGroupItem> pkGroupItems = pkInfo.getPkGroupItems();
        for (PkGroupItem pkGroupItem : pkGroupItems) {
            for (List<GroupMemberItem> memberPkItem : pkGroupItem.getMemberPkItems()) {
                String memberId1 = memberPkItem.get(0).getMemberId();
                String memberId2 = memberPkItem.get(1).getMemberId();
                PKRecord.PkRankItem member1 = rank2item(members.get(memberId1));
                PKRecord.PkRankItem member2 = rank2item(members.get(memberId2));

                PKRecord rankPKRecord = new PKRecord(member1, member2, endTime);
                pkRankItems.add(rankPKRecord);
            }
        }
        return pkRankItems;
    }

    private void setupInfo(MemberInfo memberInfo, PKRecord.PkRankItem rankItem) {
        rankItem.setLogo(memberInfo.getLogo());
        rankItem.setNick(memberInfo.getName());
        rankItem.setAsid(memberInfo.getAsid());
    }

    public List<PKRecord> queryPKRecordByKey(long actId, String recordKey, String type) {
        String redisKey = Const.addActivityPrefix(actId, recordKey);
        String records = actRedisDao.get(getRedisGroupCode(actId), redisKey);
        if (StringUtil.isBlank(records)) {
            return Lists.newArrayList();
        }
        List<PKRecord> pkRecords = JSON.parseArray(records, PKRecord.class);
        pkRecords = pkRecords.subList(0, Math.min(100, pkRecords.size()));

        List<String> memberIds = new ArrayList<>();
        for (PKRecord pkRecord : pkRecords) {
            memberIds.add(pkRecord.getWinner().getMemberId());
            memberIds.add(pkRecord.getLoser().getMemberId());
        }

        final String channelStr = "channel";
        if (channelStr.equals(type)) {
            List<String> collect = memberIds.stream().distinct().collect(Collectors.toList());
            Map<String, MemberInfo> memberInfoMap = queryChannelInfo(collect);
            for (PKRecord pkRecord : pkRecords) {
                setUpChannelInfo(pkRecord.getWinner(), memberInfoMap);
                setUpChannelInfo(pkRecord.getLoser(), memberInfoMap);
            }
            return pkRecords;
        }

        List<Long> collect = memberIds.stream().distinct().map(Convert::toLong).collect(Collectors.toList());
        Map<Long, UserInfo> userInfoMap = webdbThriftClient.batchGetUserInfo2(collect);
        for (PKRecord pkRecord : pkRecords) {
            setUpInfo(pkRecord.getWinner(), userInfoMap);
            setUpInfo(pkRecord.getLoser(), userInfoMap);
        }
        return pkRecords;
    }

    private void setUpInfo(PKRecord.PkRankItem item, Map<Long, UserInfo> map) {
        UserInfo userInfo = map.get(Convert.toLong(item.getMemberId()));
        item.setNick(userInfo.getNickName());
        item.setLogo(userInfo.getAvatar());
    }

    private void setUpChannelInfo(PKRecord.PkRankItem item, Map<String, MemberInfo> map) {
        MemberInfo memberInfo = map.get(item.getMemberId());
        item.setNick(memberInfo.getName());
        item.setLogo(memberInfo.getLogo());
        item.setAsid(memberInfo.getAsid());

    }

    private PKRecord.PkRankItem rank2item(Rank rank) {
        PKRecord.PkRankItem item = new PKRecord.PkRankItem();
        item.setMemberId(rank.getMember());
        item.setRank(rank.getRank());
        item.setScore(rank.getScore());
        return item;
    }

    private Map<String, MemberInfo> queryChannelInfo(List<String> memberIds) {
        Map<String, MemberInfo> memberInfoMap = Maps.newHashMap();
        List<Long> sids = Lists.newArrayList();
        memberIds.forEach(x -> {
            sids.add(Convert.toLong(x, 0));
        });
        Map<Long, WebdbChannelInfo> channelInfoMap = webdbThriftClient.batchGetChannelInfo(sids);
        for (long sid : channelInfoMap.keySet()) {
            WebdbChannelInfo channelInfo = channelInfoMap.get(sid);
            MemberInfo memberInfo = new MemberInfo();
            if (channelInfo != null) {
                memberInfo.setName(channelInfo.getName());
                memberInfo.setLogo(WebdbUtils.getLogo(channelInfo));
                memberInfo.setAsid(channelInfo.getAsid());
            } else {
                memberInfo.setName(StringUtils.EMPTY);
                memberInfo.setLogo(StringUtils.EMPTY);
                memberInfo.setAsid(StringUtils.EMPTY);
            }
            memberInfoMap.put(sid + "", memberInfo);
        }
        return memberInfoMap;
    }
}
