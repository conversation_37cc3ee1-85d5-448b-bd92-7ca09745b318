package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.hdzt.RankPhasePair;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BroadcastTypeSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.Map;
import java.util.Set;

/**
 * @Author: CXZ
 * @Desciption: 开播横幅
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
public class AnchorStartBannerComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "榜单id", remark = "用于结算排名,发放奖励")
    private long rankId;
    @ComponentAttrField(labelText = "阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "累积开播奖励榜单阶段", subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = RankPhasePair.class)}, remark = "多个使用逗号隔开")
    private Set<RankPhasePair> rankPhasePairs;
    @ComponentAttrField(labelText = "每日限制广播次数")
    private Integer dayLimitCount;
    @ComponentAttrField(labelText = "广播开始时间", remark = "一般是活动结算后发放,这里可以填活动结束时间")
    private String startDate;
    @ComponentAttrField(labelText = "广播结束时间", remark = "开始时间加上奖励的最长天数作为结束时间")
    private String finallyDate;
    @ComponentAttrField(labelText = "默认文案模板", remark = "示例：<p>520活动优质主播<span style='color: #ffdc3a'>##name##</span>正在直播，会唱会跳还会玩，<span style='color: #ffdc3a; text-decoration: underline''>极速围观>></span></p>")
    private String defaultAwardTip = "";
    @ComponentAttrField(labelText = "奖励天数配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "榜单名次"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "奖励天数")
            })
    private Map<Integer, Integer> rankAwardDayMap;
    @ComponentAttrField(labelText = "文案模板", remark = "模板示例：<p>520活动优质主播<span style='color: #ffdc3a'>##name##</span>正在直播，会唱会跳还会玩，<span style='color: #ffdc3a; text-decoration: underline''>极速围观>></span></p>",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "榜单名次"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "文案模板")
            })
    private Map<Integer, String> rankAwardContextMap = Maps.newHashMap();

    @ComponentAttrField(labelText = "角色对应的文案",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "角色id"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "角色文案")
            })
    private Map<Long, String> roleShowNameMap = Maps.newHashMap();
    /**
     * 定时器广播时间段的开始时间（做时间段划分）
     */
    @ComponentAttrField(labelText = "广播时间", remark = "没有配置则不需要限制,多个时间点用逗号分隔,如：0,18,21 表示在0/18/21点这3个小时会进行横幅广播",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Integer.class)})
    private Set<Integer> broStartHours = Sets.newHashSet();

    /**
     * 广播类型 2=子频道（这个没有），3=顶级频道（这个没有），4=单业务广播；5=多业务广播
     */
    @ComponentAttrField(labelText = "广播类型", dropDownSourceBeanClass = BroadcastTypeSource.class)
    private Integer rankBorType = 5;

    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(long phaseId) {
        this.phaseId = phaseId;
    }

    public Set<RankPhasePair> getRankPhasePairs() {
        return rankPhasePairs;
    }

    public void setRankPhasePairs(Set<RankPhasePair> rankPhasePairs) {
        this.rankPhasePairs = rankPhasePairs;
    }

    public int getDayLimitCount() {
        return dayLimitCount;
    }

    public void setDayLimitCount(int dayLimitCount) {
        this.dayLimitCount = dayLimitCount;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getFinallyDate() {
        return finallyDate;
    }

    public void setFinallyDate(String finallyDate) {
        this.finallyDate = finallyDate;
    }

    public String getDefaultAwardTip() {
        return defaultAwardTip;
    }

    public void setDefaultAwardTip(String defaultAwardTip) {
        this.defaultAwardTip = defaultAwardTip;
    }

    public Map<Integer, Integer> getRankAwardDayMap() {
        return rankAwardDayMap;
    }

    public void setRankAwardDayMap(Map<Integer, Integer> rankAwardDayMap) {
        this.rankAwardDayMap = rankAwardDayMap;
    }

    public Map<Integer, String> getRankAwardContextMap() {
        return rankAwardContextMap;
    }

    public void setRankAwardContextMap(Map<Integer, String> rankAwardContextMap) {
        this.rankAwardContextMap = rankAwardContextMap;
    }

    public Map<Long, String> getRoleShowNameMap() {
        return roleShowNameMap;
    }

    public void setRoleShowNameMap(Map<Long, String> roleShowNameMap) {
        this.roleShowNameMap = roleShowNameMap;
    }

    public Set<Integer> getBroStartHours() {
        return broStartHours;
    }

    public void setBroStartHours(Set<Integer> broStartHours) {
        this.broStartHours = broStartHours;
    }

    public Integer getRankBorType() {
        return rankBorType;
    }

    public void setRankBorType(Integer rankBorType) {
        this.rankBorType = rankBorType;
    }

}
