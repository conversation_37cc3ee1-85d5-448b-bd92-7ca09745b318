package com.yy.gameecology.hdzj.bean;

import lombok.Data;

/**
 * desc:宝箱
 *
 * <AUTHOR>
 * @date 2023-11-01 19:42
 **/
@Data
public class TreasureInfo {

   /**
    * 宝箱id
    */
   private String id;

   /**
    * 任务id
    */
   private long taskId;

   /**
    * 触发的任务名称
    */
   private String taskName;
   /**
    * 创建时间，ms
    */
   private long createTime;

   /**
    * 当前时间，ms
    */
   private long currentTime;
   /**
    * 过期时间，ms
    */
   private long expireTime;

   /**
    * app显示过期时间，ms
    */
   private long appExpireTime;

   /**
    * 宝箱广播范围
    */
   private long broType;

   /**
    * 触发的顶级频道
    */
   private long sid;

   /**
    * 触发的子频道
    */
   private long ssid;

   /**
    * 触发的家族id
    */
   private long familyId;
   /**
    * 触发者uid
    */
   private long uid;

   /**
    * 触发者昵称base64
    */
   private String userName;
}
