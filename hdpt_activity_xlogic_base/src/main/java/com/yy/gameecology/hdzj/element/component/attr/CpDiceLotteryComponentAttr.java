package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
public class CpDiceLotteryComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "神豪角色")
    private long userActor;

    @ComponentAttrField(labelText = "主持角色")
    private long anchorActor;

    @ComponentAttrField(labelText = "过任务的榜单")
    private long rankId;

    @ComponentAttrField(labelText = "过任务的阶段")
    private long phaseId;

    @ComponentAttrField(labelText = "广播模板", remark = "2==宝贝 3==交友 5==语音房(技能卡)")
    private int broTemplate;

    @ComponentAttrField(labelText = "获取webdb信息的templateType", remark = "交友 1 聊天室 810")
    private int templateType = 810;

    @ComponentAttrField(labelText = "每次最多使用抽奖券/骰子")
    private int maxConsume;

    @ComponentAttrField(labelText = "每次随机最大数")
    private int lotteryMaxValue;

    @ComponentAttrField(labelText = "发奖任务Id")
    private long welfareTaskId;

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    protected int busiId;

    @ComponentAttrField(labelText = "异步发奖组件index")
    protected int asyncRewardInx;

    @ComponentAttrField(labelText = "每对cp限额",remark = "单位紫水晶/金钻")
    private long awardSum;

    @ComponentAttrField(labelText = "每对cp最大抽奖券/骰子数")
    private long maxTicketCount = 500;

    @ComponentAttrField(labelText = "大奖id",remark = "逗号分割")
    private String bigRewardStr;

    @ComponentAttrField(labelText = "未中奖包id")
    private long invalidPackageId;

    @ComponentAttrField(labelText = "开始时间", remark = "玩法开始时间")
    private Date beginTime;

    @ComponentAttrField(labelText = "结束时间", remark = "玩法结束时间")
    private Date endTime;

    @ComponentAttrField(labelText = "奖励列表", remark = "",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "位置", remark = "从1开始"),
                    @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class, labelText = "奖励配置")
            })
    private Map<Long, AwardAttrConfig> cpTaskPackageReward = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "带库存奖励",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "奖励奖包Id"),
                    @SubField(fieldName = Constant.VALUE, type = RestrictGiftInfo.class, labelText = "发奖相关配置")
            })
    private Map<Long, RestrictGiftInfo> stockReward = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "统计配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "奖励奖包Id"),
                    @SubField(fieldName = Constant.VALUE, type = StaticsInfo.class, labelText = "统计配置")
            })
    private Map<Long, StaticsInfo> staticsConfig = Maps.newLinkedHashMap();

//    @ComponentAttrField(labelText = "抽奖packageId--->发奖映射",
//            subFields = {
//                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "抽奖packageId"),
//                    @SubField(fieldName = Constant.VALUE, type = WelfareValuePair.class, labelText = "发奖相关配置", remark = "发奖Id大于0时有效")
//            })
//    private Map<Long, WelfareValuePair> lotteryPackageIdMap = Collections.emptyMap();

//    @ComponentAttrField(labelText = "奖励信息",
//            subFields = {
//                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "packageId"),
//                    @SubField(fieldName = Constant.VALUE, type = Award.class, labelText = "奖品信息", remark = "奖品信息")
//            })
//    private Map<Long, Award> awardMap = Collections.emptyMap();

//    @ComponentAttrField(labelText = "额外奖励信息",
//            subFields = {
//                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "packageId"),
//                    @SubField(fieldName = Constant.VALUE, type = Award.class, labelText = "奖品信息", remark = "奖品信息")
//            })
//    private Map<Long, Award> specialAwardMap = Collections.emptyMap();

    @Data
    public static class RestrictGiftInfo {
        @ComponentAttrField(labelText = "奖池Id")
        protected long rewardTaskId;

        @ComponentAttrField(labelText = "奖励名称")
        protected String awardName;

        @ComponentAttrField(labelText = "全服上限")
        protected long upLimit;

        @ComponentAttrField(labelText = "全服需要抽奖券生成库存1",remark = "")
        protected long needTicket;
    }

    @Data
    public static class StaticsInfo {
        @ComponentAttrField(labelText = "奖励名称")
        protected String awardName;

        @ComponentAttrField(labelText = "模拟概率")
        protected String ratio;

        @ComponentAttrField(labelText = "总上限",remark = "-1 没有上限")
        protected long upLimit;

        @ComponentAttrField(labelText = "金额",remark = "单位紫水晶/金钻")
        protected long amount;
    }
}
