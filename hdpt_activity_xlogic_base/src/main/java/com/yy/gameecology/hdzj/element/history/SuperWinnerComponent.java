package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.AccumulatedRecord;
import com.yy.gameecology.activity.bean.JiaoyouComboEndEvent;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.client.thrift.UserinfoThriftClient;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.PBCommonBannerId;
import com.yy.gameecology.common.consts.PBCommonNoticeType;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.*;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.SuperWinnerComponentAttr;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import com.yy.gameecology.hdzj.utils.RandomUtil;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 *  - 交宝连送、约战单笔金额满足流水后出宝箱，先报名后开奖，实时发奖励
 * 注意：这里的实现假定奖励发放没有限额（若有限额会很复杂，需要和产品明确限额发放处理逻辑）
 *
 * <AUTHOR>
 * @date 2021/11/02 14:25
 */
@Deprecated
@Component
public class SuperWinnerComponent extends BaseActComponent<SuperWinnerComponentAttr> {

    // 礼物连送 redis hash key
    public static final String RK_GIFT_COMBO = "sw_gift_combo";

    // 存放宝箱的 redis list key
    public static final String RK_BOX_LIST = "sw_box_list";

    // 存放宝箱详情的 redis hash key
    public static final String RK_BOX_DETAIL = "sw_box_detail";

    // 宝箱开启状态标记 redis hash key， 防止一个宝箱重复开启
    public static final String RK_BOX_OPEN = "sw_box_open";

    // 奖励发放 redis list key， 用于削峰、出错重试 等
    public static final String RK_AWARD_ISSUE = "sw_award_issue";

    // 记录宝箱参与抽奖的用户 redis zset key，时间为score，用来标记参与顺序， 变化部分是 宝箱ID
    public static final String RK_ATTEND = "sw_attend:%s";

    // 宝箱报名倒计时 redis string key， 变化部分是 宝箱ID
    public static final String RK_COUNTDOWN = "sw_countdown:%s";

    // 宝箱抽奖历史 redis zset key， 变化部分是 宝箱ID
    public static final String RK_HIT_HISTORY = "sw_hit_history:%s";

    @Autowired
    private ThreadPoolManager threadPoolManager;


    @Autowired
    protected UserinfoThriftClient userinfoThriftClient;

    @Override
    public Long getComponentId() {
        return ComponentId.SUPER_WINNER;
    }

    /**
     * 响应礼物流水事件，跳过交友的，其余做连击处理
     */
    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = false)
    public void onSendGiftEvent(SendGiftEvent event, SuperWinnerComponentAttr attr) {
        Clock clock = new Clock();
        try {
            long cmptUseInx = attr.getCmptUseInx();
            if (attr.getTemplate() == Template.Jiaoyou.getValue()) {
                log.info("onSendGiftEvent2 ignore@cmptUseInx:{}, ignore jiaoyou event:{}", cmptUseInx, event.outline());
                return;
            }

            SendGiftBO bo = prepare(event);
            if (!check(bo, attr)) {
                String giftIds = JSON.toJSONString(attr.getGiftPriceMap().keySet());
                log.info("onSendGiftEvent2 skip@check fail, cmptUseInx:{}, event:{}, template:{}, giftIds:{}", cmptUseInx, event.outline(), attr.getTemplate(), giftIds);
                return;
            }

            String boxId = null;
            if (attr.isCombo()) {
                giftCombo(bo, attr);
            } else {
                GiftPlaneBO giftPlaneBO = buildBoxBO(1, bo);
                boxId = processGiftAwardBox(event.getSeq(), giftPlaneBO, attr);
            }
            log.info("onSendGiftEvent2 done@cmptUseInx:{}, event:{}, boxId:{} {}", cmptUseInx, event, boxId, clock.tag());
        } catch (Throwable t) {
            log.error("onSendGiftEvent2 exception@event:{}, attr:{}, err:{} {}", event, attr, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 交友的用连送结束事件，和业务统一
     */
    @HdzjEventHandler(value = JiaoyouComboEndEvent.class, canRetry = false)
    public void jiaoyouComboEndEvent(JiaoyouComboEndEvent event, SuperWinnerComponentAttr attr) {
        Clock clock = new Clock();
        try {
            if (attr.getTemplate() != Template.Jiaoyou.getValue()) {
                log.info("jiaoyouComboEndEvent2 ignore@not jiaoyou template:{}, event:{}", attr.getTemplate(), event.outline());
                return;
            }

            //总价值
            long priceSum = MapUtils.getLongValue(event.getExpand(), "priceSum");
            String giftId = MapUtils.getString(event.getExpand(), "propId");
            int combo = MapUtils.getIntValue(event.getExpand(), "combo");
            long giftNum = MapUtils.getLongValue(event.getExpand(), "count");
            long player = event.getSendUid();
            long anchor = event.getRecvUid();

            //少于xx yb不起飞机
            int threshold = attr.getThresholdFirePlane() * 100;
            if (priceSum < threshold) {
                log.info("jiaoyouComboEndEvent2 skip1@priceSum < {}, event:{}", threshold, event.outline());
                return;
            }

            //礼物控制
            if (!attr.getGiftIds().contains(giftId)) {
                log.info("jiaoyouComboEndEvent2 skip2@invalid giftId, event:{}", event.outline());
                return;
            }

            //seq控制
            String key = makeKey(attr, "seq:" + event.getSeqId());
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            if (!actRedisDao.setNX(groupCode, key, DateUtil.today())) {
                log.warn("jiaoyouComboEndEvent2 fail@exist key:{}, event:{}", key, event.outline());
                return;
            }

            //准备宝箱
            long sid = event.getSid();
            long ssid = event.getSsid();
            String boxId = readyAwardBox(event.getSeqId(), sid, ssid, player, attr, giftId, giftNum, combo);

            //上报数据到海度
            GiftPlaneBO gpbo = new GiftPlaneBO(combo, player, anchor, giftId, giftNum, sid, ssid);
            updateToHive(boxId, gpbo, attr);

            log.info("jiaoyouComboEndEvent2 done@boxId:{}, event:{} {}", boxId, event, clock.tag());
        } catch (Throwable t) {
            log.error("jiaoyouComboEndEvent2 exception@event:{}, attr:{}, err:{} {}", event, attr, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 用户参与抽奖
     */
    public int attend(long actId, long sid, long ssid, long uid, String boxId) {
        long cmptUseInx = getCmptUseInx(boxId);
        SuperWinnerComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);

        // 先做手机检查
        if (Const.isOk1(attr.getCheckMobileFlag())) {
            boolean strict = Const.isOk1(attr.getStrickCheckMobile());
            if (!userinfoThriftClient.checkUserMobile(uid, strict)) {
                return 3;
            }
        }

        // 宝箱key不存在 或者 已经过期了
        String groupCode = redisConfigManager.getGroupCode(actId);
        String boxKey = makeKey(attr, String.format(RK_COUNTDOWN, boxId));
        if (!actRedisDao.hasKey(groupCode, boxKey)) {
            return 2;
        }

        // 若已经报名了
        String zsetKey = makeKey(attr, String.format(RK_ATTEND, boxId));
        long time = Long.parseLong(DateUtil.today("yyyyMMddHHmmss"));
        Long rank = actRedisDao.zRevRank(groupCode, zsetKey, String.valueOf(uid));
        if (rank != null) {
            return 1;
        }

        // 若没有报名，则报名
        boolean flag = actRedisDao.zAdd(groupCode, zsetKey, String.valueOf(uid), time);
        return flag ? 0 : 1;
    }

    /**
     * 结束连击处理的定时器
     */
    @Scheduled(cron = "*/1 * * * * ? ")
    @NeedRecycle(author = "guoliping", notRecycle = true)
    public void finishGiftCombo() {
        Clock clock = new Clock();
        Set<Long> activityIds = getComponentEffectActIds();
        activityIds.stream()
                .filter(actId -> actInfoService.inActTime(actId))
                .map(this::getAllComponentAttrs)
                .flatMap(Collection::stream)
                .forEach(this::doFinishGiftCombo);
        log.info("finishGiftCombo Scheduled done@act size:{} {}", activityIds.size(), clock.tag());
    }

    /**
     * 广播超级宝箱，scanBox()的实现解决了并发问题，锁超时也没有问题的
     */
    @Scheduled(cron = "*/3 * * * * ? ")
    @NeedRecycle(author = "guoliping", notRecycle = true)
    public void broadcastBox() {
        Clock clock = new Clock();
        String timerName = "locker_broadcast_super_winner_box_" + this.getComponentId();
        timerSupport.work(timerName, 3, () -> scanBox());
        log.info("broadcastBox Scheduled done@{}", clock.tag());
    }

    /**
     * 发放奖励， 使用 list 的 pop、push， 避免了定时器锁的需求
     */
    @Scheduled(cron = "*/5 * * * * ? ")
    @NeedRecycle(author = "guoliping", notRecycle = true)
    public void giveAwards() {
        Clock clock = new Clock();
        Set<Long> activityIds = this.getComponentEffectActIds();
        for (Long actId : activityIds) {
            String groupCode = redisConfigManager.getGroupCode(actId);
            List<SuperWinnerComponentAttr> attrs = this.getAllComponentAttrs(actId);
            for (SuperWinnerComponentAttr attr : attrs) {
                String listKey = makeKey(attr, RK_AWARD_ISSUE);

                // 记录队列当前长度, 若为0直接结束循环
                long len = actRedisDao.llen(groupCode, listKey);
                if (len == 0) {
                    continue;
                }

                long total = len;
                String content = null;
                while ((content = actRedisDao.lpop(groupCode, listKey)) != null) {
                    giveOneAward(groupCode, listKey, content, attr);
                    // 因为giveOneAward中会将失败的重新入队，这里用来防止无限循环，多机情况下会有一些冗余尝试，影响不大
                    if (--len < 0) {
                        log.warn("giveAwards break@key:{}, total:{}, len:{}", listKey, total, len);
                        break;
                    }
                }
            }
        }
        log.info("giveAwards Scheduled done@act size:{} {}", activityIds.size(), clock.tag());
    }

    /**
     * 需要任务调度
     */
    private void doFinishGiftCombo(SuperWinnerComponentAttr attr) {
        if (!attr.isCombo()) {
            return;
        }
        Clock clock = new Clock();
        String key = null;
        List<AccumulatedRecord> records = null;
        try {
            key = makeKey(attr, RK_GIFT_COMBO);
            //records = accumulatedService.getUseAccumulatedRecord(attr.getActId(), key);
            //TODO 如果次组件要重新启用，则需要改造此处
            if (true) {
                throw new RuntimeException("getUseAccumulatedRecord.lua,lua forbid");
            }
            records.forEach(record -> makeGiftAwardBox(attr, record));
            log.info("doFinishGiftCombo doing@actId:{}, cmptUseInx:{}, recordSize:{} {}", attr.getActId(), attr.getCmptUseInx(), records.size(), clock.tag());
        } catch (Throwable t) {
            int size = records == null ? 0 : records.size();
            log.error("doFinishGiftCombo exception@attr:{}, recordSize:{}, err:{} {}", attr, size, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 制作宝箱
     */
    private void makeGiftAwardBox(SuperWinnerComponentAttr attr, AccumulatedRecord record) {
        if (record != null) {
            SendGiftBO giftBO = JSONUtils.parseObject(record.getData(), SendGiftBO.class);
            GiftPlaneBO giftPlaneBO = buildBoxBO(record.getCount(), giftBO);
            String[] seqs = record.getSeq().split(",");
            String lastSeq = StringUtil.trim(seqs[seqs.length - 1]);
            processGiftAwardBox(lastSeq, giftPlaneBO, attr);
        }
    }

    private GiftPlaneBO buildBoxBO(int count, SendGiftBO bo) {
        if (bo == null) {
            return null;
        }
        GiftPlaneBO planeBO = new GiftPlaneBO();
        planeBO.setCount(count);
        planeBO.setSendUid(bo.getSendUid());
        planeBO.setRecvUid(bo.getRecvUid());
        planeBO.setGiftId(bo.getGiftId());
        planeBO.setGiftNum(bo.getGiftNum());
        planeBO.setSid(bo.getSid());
        planeBO.setSsid(bo.getSsid());
        return planeBO;
    }

    /**
     * 连击
     */
    private void giftCombo(SendGiftBO bo, SuperWinnerComponentAttr attr) {
        String key = makeKey(attr, RK_GIFT_COMBO);
        String boRecord = JSONUtils.toJsonString(bo);
        String judge = bo.getRecvUid() + "_" + bo.getGiftId() + "_" + bo.getGiftNum();
        AccumulatedRecord record;
        //AccumulatedRecord record = accumulatedService.accumulatedUserData(attr.getActId(), key, bo.getSendUid(), boRecord, judge, attr.getComboTime(), bo.getSeq());
        //TODO 如果次组件要重新启用，则需要改造此处
        if (true) {
            throw new RuntimeException("userAccumulated.lua,lua forbid");
        }
        log.info("giftCombo calc@bo:{} -> record:{}", boRecord, JSON.toJSONString(record));
        makeGiftAwardBox(attr, record);
    }

    /**
     * 处理送礼宝箱
     */
    private String processGiftAwardBox(String lastSeq, GiftPlaneBO gpbo, SuperWinnerComponentAttr attr) {
        // 不够出宝箱，直接返回
        int awardBoxCount = getAwardBoxCount(gpbo, attr);
        if (awardBoxCount <= 0) {
            log.info("processGiftAwardBox skip@template:{}, seq:{}, not enough {}", attr.getTemplate(), lastSeq, gpbo.outline());
            return "";
        }

        //准备宝箱
        String boxId = readyAwardBox(lastSeq, gpbo.getSid(), gpbo.getSsid(), gpbo.getSendUid(), attr,
                gpbo.getGiftId(), gpbo.getGiftNum(), gpbo.getCount());

        //上报数据到海度
        updateToHive(boxId, gpbo, attr);

        return boxId;
    }

    /**
     * 准备宝箱：先入redis保存，然后发业务横幅
     */
    private String readyAwardBox(String seq, long sid, long ssid, long sendUid, SuperWinnerComponentAttr attr, String giftId, long giftNum, long combo) {
        Clock clock = new Clock();
        long busiId = attr.getBusiId(attr.getTemplate());
        SuperWinnerBox box = new SuperWinnerBox(seq, attr.getTemplate(), busiId, attr.getCmptUseInx(), sendUid, sid, ssid, giftId, giftNum, combo);

        List<Object> list = null;
        GameecologyActivity.GameEcologyMsg msg = null;

        // 先宝箱入 redis
        String boxId = box.getBoxId();
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        list = actRedisDao.getRedisTemplate(groupCode).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                String listKey = makeKey(attr, RK_BOX_LIST);
                String hashKey = makeKey(attr, RK_BOX_DETAIL);
                connection.openPipeline();
                connection.hSet(hashKey.getBytes(), boxId.getBytes(), JSON.toJSONString(box).getBytes());
                connection.rPush(listKey.getBytes(), ("0|" + boxId).getBytes());
                return connection.closePipeline();
            }
        });

        //出 业务 宝箱横幅
        // 已和刘俊杰协商填 3
        GameecologyActivity.BannerBroadcast.Builder banner = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(attr.getActId())
                .setBannerId(PBCommonBannerId.SUPPER_WINNER)
                .setBannerType(0)
                .setUserNick(commonService.getNickName(sendUid, false));
        msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(banner).build();
        svcSDKService.broadcastTemplate(Template.findByValue(attr.getTemplate()), msg);

        log.info("readyAwardBox ok@box:{}, list:{}, msg:{} {}", box, JSON.toJSONString(list), msg, clock.tag());

        return boxId;
    }


    /**
     * 上报数据到海度
     */
    private void updateToHive(String boxId, GiftPlaneBO bo, SuperWinnerComponentAttr attr) {
        if (StringUtil.isNotBlank(boxId)) {
            long actId = attr.getActId();
            Date now = commonService.getNow(actId);
            long total = bo.getCount() * bo.getGiftNum();
            BusiId busiId = broadCastHelpService.changeBroTemplate2BusiId(Template.findByValue(attr.getTemplate()));
            bigDataService.saveNoRankDataToFile(boxId, actId, busiId, now.getTime(), bo.getSendUid() + "", RoleType.USER, total
                    , 14, bo.getGiftId(), bo.getCount(), bo.getRecvUid());
        }
    }

    private int getAwardBoxCount(GiftPlaneBO bo, SuperWinnerComponentAttr attr) {
        GiftBO giftBO = bo == null ? null : attr.getGiftPriceMap().get(bo.getGiftId());
        return giftBO == null ? 0 : BigInteger.valueOf(giftBO.getPrice())
                .multiply(BigInteger.valueOf(bo.getGiftNum()))
                .multiply(BigInteger.valueOf(bo.getCount()))
                .divide(BigInteger.valueOf(attr.getThresholdFirePlane())).intValue();
    }

    /**
     * 准备数据
     */
    private SendGiftBO prepare(SendGiftEvent event) {
        return SendGiftBO.builder()
                .template(event.getTemplate())
                .seq(event.getSeq())
                .sendUid(event.getSendUid())
                .recvUid(event.getRecvUid())
                .giftId(event.getGiftId())
                .giftNum(event.getGiftNum())
                .sid(event.getSid())
                .ssid(event.getSsid())
                .eventTime(event.getEventTime())
                .sourceChannel(event.getSourceChannel())
                .signSid(event.getSignSid())
                .jsonMap(event.getJsonMap())
                .build();
    }

    private boolean check(SendGiftBO bo, SuperWinnerComponentAttr attr) {
        Template template = bo.getTemplate();
        if (template == null || template.getValue() != attr.getTemplate()) {
            return false;
        }

        String giftId = bo.getGiftId();
        Map<String, GiftBO> giftMap = attr.getGiftPriceMap();
        if (MapUtils.isNotEmpty(giftMap)) {
            return giftMap.containsKey(giftId);
        }
        return attr.isAllGifts() || attr.getGiftIds().contains(giftId);
    }

    /**
     * 是否处理该gift
     *
     * @return true:符合 false:不符合
     */
    private long getCmptUseInx(String boxId) {
        return Long.parseLong(boxId.split("_")[0]);
    }

    public void scanBox() {
        Clock clock = new Clock();
        Set<Long> activityIds = this.getComponentEffectActIds();
        for (Long actId : activityIds) {
            List<SuperWinnerComponentAttr> attrs = this.getAllComponentAttrs(actId);
            for (SuperWinnerComponentAttr attr : attrs) {
                processBox(attr);
            }
        }
        log.info("scanBox Scheduled done@act size:{} {}", activityIds.size(), clock.tag());
    }

    /**
     * 判断宝箱条目是否有效
     */
    private boolean isValidBoxItem(String boxItem) {
        if (StringUtil.isBlank(boxItem)) {
            return false;
        }
        String[] values = boxItem.split("\\|");
        final int two = 2;
        if (values.length != two) {
            return false;
        }
        // 首元素为 0 或者是 yyyyMMddHHmmss 格式的字符串
        String v = values[0];
        return StringUtil.ZERO.equals(v) ? true : DateUtil.getDate(v, DateUtil.PATTERN_TYPE1) != null;
    }

    private void processBox(SuperWinnerComponentAttr attr) {
        Clock clock = new Clock();
        String boxItem = null;
        try {
            // 1. 先查看队队首的宝箱，若为无效元素，直接返回
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            StringRedisTemplate rt = actRedisDao.getRedisTemplate(groupCode);
            String listKey = makeKey(attr, RK_BOX_LIST);

            boxItem = actRedisDao.lindex(groupCode, listKey, 0);
            if (boxItem == null) {
                // log.info("processBox ignore@listKey:{} is empty! {}", listKey, clock.tag());
                return;
            }

            // 2. 去掉无效的宝箱条目
            if (!isValidBoxItem(boxItem)) {
                long size = actRedisDao.lrem(groupCode, listKey, boxItem);
                if (size < 1) {
                    log.error("processBox invalid1@boxItem:{}, listKey:{} modified by other!!! {}", boxItem, listKey, clock.tag());
                } else {
                    log.error("processBox invalid2@boxItem:{}, listKey:{} removed size:{} {}", boxItem, listKey, size, clock.tag());
                }
                return;
            }

            // 3. 准备数据，在 redis lua 中做倒计时
            Date now = new Date();
            String[] values = boxItem.split("\\|");
            long startTime = Long.parseLong(values[0]);
            String boxId = values[1];
            long boxShowSeconds = attr.getBoxShowSeconds();
            long left = getLeft(now, startTime, boxShowSeconds);
            String boxKey = makeKey(attr, String.format(RK_COUNTDOWN, boxId));
            long nowTime = Long.parseLong(DateUtil.format(now, DateUtil.PATTERN_TYPE1));
            //long ret = actRedisDao.lineUpCountdown(groupCode, listKey, boxKey, left, boxId, startTime, nowTime, boxShowSeconds);
            long ret;
            //TODO 如果次组件要重新启用，则需要改造此处
            if (true) {
                throw new RuntimeException("line_up_countdown.lua,lua forbid");
            }
            if (ret != 1) {
                log.error("processBox wrong@boxItem:{}, listKey:{}, boxKey:{}, left:{}, ret:{} {}", boxItem, listKey, boxKey, left, ret, clock.tag());
                return;
            }

            // 4. 异步处理宝箱广播
            threadPoolManager.get(Const.GENERAL_POOL).execute(() -> doBoxBroadcast(attr, boxId, left));
        } catch (Throwable t) {
            log.error("processBox exception@boxItem:{}, attr:{}, err:{} {}", boxItem, attr, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 计算倒计时剩余秒数
     */
    private long getLeft(Date now, long startTime, long boxShowSeconds) {
        if (startTime == 0) {
            return boxShowSeconds;
        }
        Date start = DateUtil.getDate(String.valueOf(startTime), DateUtil.PATTERN_TYPE1);
        long sNow = DateUtil.getSeconds(now);
        long sOld = DateUtil.getSeconds(start);
        return boxShowSeconds - (sNow - sOld);
    }

    /**
     * 做宝箱通知：通知横幅 或者 通知开奖结果
     */
    public void doBoxBroadcast(SuperWinnerComponentAttr attr, String boxId, long left) {
        Clock clock = new Clock();
        SuperWinnerBox box = null;
        GameecologyActivity.CommonNoticeResponse.Builder panel = null;

        try {
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            String hashKey = makeKey(attr, RK_BOX_DETAIL);
            String boxInfo = actRedisDao.hget(groupCode, hashKey, boxId);
            box = JSON.parseObject(boxInfo, SuperWinnerBox.class);
            long busiId = attr.getBusiId(attr.getTemplate());
            if (left > 0) {
                // 倒计时未结束，发出一次宝箱广播
                panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                        .setActId(attr.getActId())
                        .setNoticeType(PBCommonNoticeType.SUPER_WINNER_BOX)
                        .setNoticeMsg(commonService.getNickName(box.getUid(), false))
                        .setNoticeValue(String.valueOf(box.getUid()))
                        .setExtJson(JSON.toJSONString(ImmutableMap.of("busiId", busiId, "boxId", boxId, "seconds", left
                        )));
                GameecologyActivity.GameEcologyMsg msgPanel = GameecologyActivity.GameEcologyMsg.newBuilder()
                        .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                        .setCommonNoticeResponse(panel).build();
                svcSDKService.broadcastSub(box.getSid(), box.getSsid(), msgPanel);
                log.info("doBoxBroadcast broadcastSub ok@box:{}, panel:{} {}", box, panel, clock.tag());
            } else {
                // 倒计时已结束， 开宝箱
                openBox(box, attr);
            }
        } catch (Throwable t) {
            log.info("doBoxBroadcast exception@box:{}, panel:{}, err:{} {}", box, panel, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 处理开奖，并广播开奖结果
     */
    public void openBox(SuperWinnerBox box, SuperWinnerComponentAttr attr) {
        Clock clock = new Clock();
        String boxId = box.getBoxId();
        String seq = box.getSeq();
        long uid = box.getUid();

        // 检查奖池等级概率设置
        Map<Long, Long> awardProbabilitys = attr.getAwardProbabilitys();
        if (MapUtils.isEmpty(awardProbabilitys)) {
            log.error("openBox fail@awardProbabilitys is empty! boxId:{}, seq:{}, uid:{}", boxId, seq, uid);
            return;
        }

        // 检查基础占比设置
        int basePercentage = attr.getBasePercentage();
        final int oneHundred = 100;
        if (basePercentage < 0 || basePercentage > oneHundred) {
            log.error("openBox fail@basePercentage {} not in [0 ~ 100]! boxId:{}, seq:{}, uid:{}", basePercentage, boxId, seq, uid);
            return;
        }

        // 先判断以前是否开过宝箱
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String openKey = makeKey(attr, RK_BOX_OPEN);
        if (!actRedisDao.hsetnx(groupCode, openKey, boxId, DateUtil.today())) {
            String at = actRedisDao.hget(groupCode, openKey, boxId);
            log.error("openBox boxId:{} has done!!!! at:{}, seq:{}, uid:{}", boxId, at, seq, uid);
            return;
        }

        // 从list中取出前 x 名用户，然后将报名key设置 30分钟后过期，防止数据堆积
        String zsetKey = makeKey(attr, String.format(RK_ATTEND, boxId));
        Set<ZSetOperations.TypedTuple<String>> set = actRedisDao.zrange(groupCode, zsetKey, attr.getMaxHitUserNum());
        actRedisDao.setExpire(groupCode, zsetKey, 1800);

        List<Map<String, Object>> topHitUsers = Lists.newArrayList();
        Map<String, Long> allHitUsers = Maps.newHashMap();
        BusiId busiId = BusiId.findByValue(attr.getBusiId(attr.getTemplate()));

        // 随机分配
        randomDistribute(box, attr, awardProbabilitys, basePercentage, set, topHitUsers, allHitUsers, busiId);

        // 开箱通知
        openBoxNotify(box, attr, clock, boxId, topHitUsers, allHitUsers, busiId);
    }

    /**
     * 开箱结果通知
     */
    private void openBoxNotify(SuperWinnerBox box, SuperWinnerComponentAttr attr, Clock clock, String boxId,
                               List<Map<String, Object>> topHitUsers, Map<String, Long> allHitUsers, BusiId busiId) {
        // 将结果广播出去
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType(PBCommonNoticeType.SUPER_WINNER_RESULT)
                .setExtJson(JSON.toJSONString(ImmutableMap.of(
                        "busiId", busiId.getValue(), "boxId", boxId, "topHitUsers", topHitUsers, "allHitUsers", allHitUsers
                )));
        GameecologyActivity.GameEcologyMsg msgPanel = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
        svcSDKService.broadcastSub(box.getSid(), box.getSsid(), msgPanel);

        log.info("openBoxNotify ok@box:{}, panel:{} {}", box, panel, clock.tag());
    }

    /**
     * 随机分配
     */
    private void randomDistribute(SuperWinnerBox box, SuperWinnerComponentAttr attr, Map<Long, Long> awardProbabilitys, int basePercentage,
                                  Set<ZSetOperations.TypedTuple<String>> set, List<Map<String, Object>> topHitUsers, Map<String, Long> allHitUsers, BusiId busiId) {
        if (CollectionUtils.isEmpty(set)) {
            return;
        }

        // 按概率先确定选择哪个级别的奖励，随机打乱用户列表
        List<Long> users = set.stream()
                .filter(Objects::nonNull)
                .filter(tuple -> StringUtils.isNumeric(tuple.getValue()))
                .map(tuple -> Long.parseLong(tuple.getValue())).collect(Collectors.toList());
        Collections.shuffle(users);

        // 构造奖励 和 奖励概率 对应的 list，确保下标对应
        List<Long> awardList = Lists.newArrayList();
        List<Long> probabilityList = Lists.newArrayList();
        for (Long key : awardProbabilitys.keySet()) {
            awardList.add(key);
            probabilityList.add(awardProbabilitys.get(key));
        }

        // 计算每个人的基础奖励
        int size = users.size();
        int level = RandomUtil.randomIndex(probabilityList);
        long totalAward = awardList.get(level);
        long baseAward = totalAward * basePercentage / 100 / size;

        // 扣除基础奖励后的剩余奖励总数
        long leftAward = totalAward - (baseAward * size);

        // 剩下的按所有抽奖人数出随机加权数
        int[] quotas = RandomUtil.makeRandomQuotas(size, attr.getRandomBound());

        // 得到随机奖励数组
        long[] randomAwards = RandomUtil.divide(quotas, leftAward);

        // 组装 用户:奖励 Map (因pc前端解析json要求 key必须有引号， 所以这里将 uid 用 String 类型）
        for (int i = 0; i < size; i++) {
            allHitUsers.put(users.get(i).toString(), baseAward + randomAwards[i]);
        }

        // 奖励发放入redis队列
        saveAward(box, attr, allHitUsers);

        // 按分值排序
        List<Map.Entry<String, Long>> list = Lists.newArrayList(allHitUsers.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<String, Long>>() {
            //降序排序
            public int compare(Map.Entry<String, Long> o1, Map.Entry<String, Long> o2) {
                return o2.getValue().compareTo(o1.getValue());
            }
        });

        // 取出前面4个(不足按size）分高的，构造 top N 获奖信息
        for (int i = 0; i < Math.min(attr.getTopHitUserNum(), size); i++) {
            long userUid = Long.parseLong(list.get(i).getKey());
            long userAward = list.get(i).getValue();
            UserBaseInfo ubi = broadCastHelpService.getUserBaseInfo(userUid, busiId);
            topHitUsers.add(ImmutableMap.of("nick", ubi.getNick(), "amount", userAward));
        }
    }

    /**
     * 先保存奖励到 redis list 中
     */
    private void saveAward(SuperWinnerBox box, SuperWinnerComponentAttr attr, Map<String, Long> allHitUsers) {
        // 先构造记录
        int inx = 1;
        long busiId = box.getBusiId();
        String time = DateUtil.today();
        final String boxId = box.getBoxId();
        List<SuperWinnerAward> awardList = Lists.newArrayList();
        for (String uid : allHitUsers.keySet()) {
            long amount = allHitUsers.get(uid);
            String seq = boxId + "#" + (inx++);
            awardList.add(new SuperWinnerAward(time, Long.parseLong(uid), busiId, seq, attr.getTaskId(), attr.getPackageId(), amount));
            log.info("OPEN_BOX_HIT {} -> {} {}", seq, uid, amount);
        }

        // 批量入redis
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        actRedisDao.getRedisTemplate(groupCode).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                byte[] listKey = makeKey(attr, RK_AWARD_ISSUE).getBytes();
                byte[] zsetkey = makeKey(attr, String.format(RK_HIT_HISTORY, boxId)).getBytes();
                connection.openPipeline();
                for (int i = 0; i < awardList.size(); i++) {
                    SuperWinnerAward award = awardList.get(i);
                    connection.rPush(listKey, JSON.toJSONString(award).getBytes());
                    connection.zAdd(zsetkey, award.getAmount(), String.valueOf(award.getUid()).getBytes());
                }
                return connection.closePipeline();
            }
        });
    }

    /**
     * 发放一个奖励
     */
    private void giveOneAward(String groupCode, String listKey, String content, SuperWinnerComponentAttr attr) {
        Clock clock = new Clock();
        try {
            SuperWinnerAward swa = JSON.parseObject(content, SuperWinnerAward.class);
            if (swa == null) {
                log.error("giveOneAward ignore@invalid content:{}", content);
                return;
            }

            // 超过一天的不再处理
            Date now = new Date();
            long diff = DateUtil.getSeconds(now) - swa.getSeconds();
            if (diff > attr.getIssueRetrySeconds()) {
                log.error("giveOneAward expired@time too long:{}s, {} {}", diff, attr.getIssueRetrySeconds(), swa.outline());
                return;
            }

            // 进行发放（注意：若有限额控制，则发抽奖发放会变得很复杂）
            String time = DateUtil.format(now);
            long packageId = swa.getPackageId();
            BatchWelfareResult result = hdztAwardServiceClient.doWelfare(time, swa.getBusiId(), swa.getUid(), swa.getTaskId(), (int) swa.getAmount(), packageId, swa.getSeq());
            if (result != null && result.getCode() == 0) {
                Map<Long, Long> recordIds = result.getRecordIds();
                if (MapUtils.isEmpty(recordIds) || !recordIds.containsKey(packageId)) {
                    // 可能超额等原因导致实际没有发成功，这里打日志告警结束，以免堆积
                    boolean exp = StringUtil.trim(result.getReason()).contains("异步发放中");
                    if (exp) {
                        log.info("giveOneAward async ok@{} doWelfare:{}", swa.outline(), result);
                    } else {
                        log.error("giveOneAward wrong@recordIds no packageId:{}, {} doWelfare:{}", packageId, swa.outline(), result);
                    }
                } else {
                    log.info("giveOneAward ok@{} doWelfare:{}", swa.outline(), result);
                }
                return;
            }
            clock.tag();

            /*
             发放失败的重新入redis，若还失败，只能打日志告警
             注意 swa 前面不应有更新，防止入队的内容被错误变化了
             */
            swa.setUtime(time);
            Long len = actRedisDao.rpush(groupCode, listKey, JSON.toJSONString(swa));
            log.error("giveOneAward fail@key:{}, content:{}, doWelfare:{}, rightPush ok len:{} {}", listKey, content, result, len, clock.tag());
        } catch (Throwable t) {
            log.error("giveOneAward rightPush exception@key:{}, content:{}, err:{} {}", listKey, content, t.getMessage(), clock.tag(), t);
        }
    }
}
