package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-02-21 20:34
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class TaskChangeUpdateRankComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "一天上报一次", remark = "勾选这个，上报分数的时候控制,1个member1天只产生1个seq")
    private boolean onceADay = false;

    @ComponentAttrField(labelText = "更新榜单业务id")
    private int busiId;

    @ComponentAttrField(labelText = "任务事件来源榜单Id", remark = "多个时用逗号分隔",
            subFields = {
                    @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
            })
    private List<Long> rankId;

    @ComponentAttrField(labelText = "任务事件来源阶段Id")
    private long phaseId;

    @ComponentAttrField(labelText = "最低过任务上报等级")
    private int minCurrTaskIndex;

    @ComponentAttrField(labelText = "更新榜单礼物id")
    private String itemId;

    @ComponentAttrField(labelText = "上报榜单角色id", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
    })
    private Set<Long> roleId;

    @ComponentAttrField(labelText = "完成一轮任务增加的分数")
    private long roundCompleteScore;

    @ComponentAttrField(labelText = "每级任务增加的分数", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务等级", remark = "从1开始"),
            @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "增加的分数")
    }, remark = "每级任务增加的分数，不考虑轮次")
    private Map<Long, Long> taskLevelScores;

    @ComponentAttrField(labelText = "更新榜单重试次数")
    private int updateRankRetryTimes;


}
