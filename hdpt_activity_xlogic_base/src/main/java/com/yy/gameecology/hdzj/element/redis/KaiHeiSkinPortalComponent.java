package com.yy.gameecology.hdzj.element.redis;

import com.google.common.base.Splitter;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.http.XPushServiceHttpClient;
import com.yy.gameecology.activity.client.yrpc.BaymaxServiceClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.NewUserStatus;
import com.yy.gameecology.common.db.mapper.gameecology.ComponentWhitelistMapper;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.bean.daytask.CurDayTask;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.WhitelistComponent;
import com.yy.gameecology.hdzj.element.component.attr.DayTaskComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.KaiHeiSkinComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.KaiHeiSkinPortalComponentAttr;
import com.yy.gameecology.hdzj.element.history.DayTaskComponent;
import com.yy.protocol.pb.zhuiwan.risk.ZhuiyaRisk;
import com.yy.thrift.hdztranking.RoleType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;

@UseRedisStore
@Slf4j
@RestController
@RequestMapping("5109")
public class KaiHeiSkinPortalComponent extends BaseActComponent<KaiHeiSkinPortalComponentAttr> {

    private static final String GAME_USER_SERVICE = "https://services.bigda.com";

    private static final String GAME_USER_PATH = "/data-api/bigda/trino/query/pcyy_game_user";

    private static final String APP_KEY = "8AEB760C4FDE4495A15A9DD23513E579";

    private static final String APP_SECRET = "E5407A7576974F76BCC8BEAD0950AB33";

    private static final String SKIN_PICK_PREFIX = "skin_pick:%d";

    private static final String SEND_PUSH_PREFIX = "send_push:%d";

    private static final String WHITELIST_WEEK_STATISTIC = "whitelist_week_statistics";

    private static final int PAGE_SIZE = 500;

    private static final int ACT_USER_MOD = 20;

    private static final int[] PUSH_PLATFORM = {3, 4};

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private WhitelistComponent whitelistComponent;

    @Autowired
    private ComponentWhitelistMapper componentWhitelistMapper;

    @Autowired
    private DayTaskComponent dayTaskComponent;

    @Autowired
    private KaiHeiSkinComponent kaiHeiSkinComponent;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private XPushServiceHttpClient xPushServiceHttpClient;

    @Autowired
    private BaymaxServiceClient baymaxServiceClient;

    @Override
    public Long getComponentId() {
        return ComponentId.KAI_HEI_SKIN_PORTAL;
    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(cron = "22 0,30,50 10 ? * 1 ")
    public void syncGameUser() {
        Set<Long> actIds = getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        for (long actId : actIds) {
            ActivityInfoVo activityInfo = actInfoService.queryActivityInfo(actId);
            if (activityInfo == null) {
                continue;
            }

            Date now = commonService.getNow(actId);

            if (!actInfoService.inActShowTime(now, activityInfo)) {
                continue;
            }

            KaiHeiSkinPortalComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }

            Date shutDate =  DateUtils.addDays(new Date(activityInfo.getEndTime()), -attr.getShutPortalAheadDays());
            if (now.after(shutDate)) {
                log.info("syncGameUser skip for ahead shut portal days shutDate:{}", shutDate);
                continue;
            }

            Date lastMonday = DateUtil.getPreWeekDayDate(now, Calendar.MONDAY);
            String monday = DateFormatUtils.format(lastMonday, DateUtil.PATTERN_TYPE5);

            doSyncGameUser(attr, monday);
        }
    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(initialDelay = 1300, fixedDelay = 10000)
    public void sendPush() {
        Set<Long> actIds = getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        for (long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            KaiHeiSkinPortalComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }

            Date now = commonService.getNow(actId);
            LocalTime curTime = DateUtil.toLocalDateTime(now).toLocalTime();
            if (curTime.isBefore(attr.getPushStartTime()) || curTime.isAfter(attr.getPushEndTime())) {
                continue;
            }

            int offset = 0;
            List<String> members = componentWhitelistMapper.selectMembers(actId, attr.getLoginCmptIndex(), offset, PAGE_SIZE);

            LocalDate nowDate = now.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            String groupCode = getRedisGroupCode(actId);
            while (CollectionUtils.isNotEmpty(members)) {
                for (String member : members) {
                    if (!StringUtils.isNumeric(member)) {
                        continue;
                    }

                    long uid = Long.parseLong(member);
                    trySendPush(attr, nowDate, groupCode, uid);
                }

                offset += PAGE_SIZE;
                members = componentWhitelistMapper.selectMembers(actId, attr.getLoginCmptIndex(), offset, PAGE_SIZE);
            }
        }
    }

    private void trySendPush(KaiHeiSkinPortalComponentAttr attr, LocalDate nowDate, String groupCode, long uid) {
        DayTaskComponentAttr taskAttr = dayTaskComponent.tryGetUniqueComponentAttr(attr.getActId());
        if (taskAttr == null) {
            return;
        }

        String member = String.valueOf(uid);
        CurDayTask dayTask = dayTaskComponent.queryCurDayTask(taskAttr, member);
        if (dayTask == null) {
            return;
        }

        if (StringUtils.isEmpty(dayTask.getCompleteDayCode())) {
            return;
        }

        LocalDate lastCompleteDate = LocalDate.parse(dayTask.getCompleteDayCode(), DateUtil.YYYY_MM_DD);

        Period period = Period.between(lastCompleteDate, nowDate);
        if (period.getYears() == 0 && period.getMonths() == 0 && period.getDays() < attr.getPushDays()) {
            log.info("trying to send push with uid:{} day period not match:{}", uid, period.getDays());
            return;
        }

        String key = makeKey(attr, String.format(SEND_PUSH_PREFIX, uid % ACT_USER_MOD));
        String value = actRedisDao.hget(groupCode, key, member);
        String dateStr = nowDate.format(DateUtil.YYYY_MM_DD);
        if (!StringUtils.contains(value, StringUtil.VERTICAL_BAR)) {
            String newValue = dateStr + StringUtil.VERTICAL_BAR + "1";
            boolean set = actRedisDao.hsetnx(groupCode, key, member, newValue);
            if (set) {
                doSendPush(attr, uid);
            }

            return;
        }

        String[] arr = StringUtils.split(value, StringUtil.VERTICAL_BAR);
        LocalDate lastSendDate = LocalDate.parse(arr[0], DateUtil.YYYY_MM_DD);
        period = Period.between(lastSendDate, nowDate);
        if (period.getYears() == 0 && period.getMonths() == 0 && period.getDays() < attr.getSilenceDays()) {
            log.info("trying to send push with uid:{} silence period not match:{}", uid, period.getDays());
            return;
        }

        int times = Integer.parseInt(arr[1]);
        if (times >= attr.getPushTimes()) {
            log.info("trying to send push with uid:{} push times exceed:{}", uid, times);
            return;
        }

        String newValue = dateStr + StringUtil.VERTICAL_BAR + (++times);
        boolean set;
        //boolean set = actRedisDao.hCompareAndSet(groupCode, key, member, value, newValue);
        if (true) {
            throw new RuntimeException("hash_cas.lua,lua forbid");
        }
        if (set) {
            doSendPush(attr, uid);
        }
    }

    private void doSendPush(KaiHeiSkinPortalComponentAttr attr, long uid) {
        KaiHeiSkinComponentAttr skinAttr = kaiHeiSkinComponent.tryGetUniqueComponentAttr(attr.getActId());
        String tips = kaiHeiSkinComponent.getNoticeTips(skinAttr, uid);
        if (StringUtils.isNotEmpty(tips)) {
            xPushServiceHttpClient.push(attr.getPushAppid(), null, attr.getPushTitle(), tips, attr.getPushLink(), StringUtils.EMPTY, PUSH_PLATFORM, String.valueOf(uid));
        }

        log.info("doSendPush to uid:{} with:{}", uid, tips);
    }

    public void doSyncGameUser(KaiHeiSkinPortalComponentAttr attr, String monday) {
        if (commonService.isGrey(attr.getActId())) {
            log.warn("act is grey:{}", attr.getActId());
            return;
        }
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId()));
        for (int pageIndex = 1; pageIndex < 100000; pageIndex++) {
            String lockKey = makeKey(attr, String.format("sync_game_user:%s:%d", monday, pageIndex));
            boolean set = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 30, TimeUnit.MINUTES));
            if (!set) {
                continue;
            }

            try {
                List<Long> uids = queryGameUids(monday, pageIndex, PAGE_SIZE);
                if (CollectionUtils.isEmpty(uids)) {
                    log.info("doSyncGameUser with uids is empty monday:{} pageIndex:{}", monday, pageIndex);
                    break;
                }

                List<String> val = new ArrayList<>(uids.size());
                for (long uid : uids) {
                    boolean added = doAddUid(attr, uid);
                    if (added) {
                        val.add(String.valueOf(uid));
                    }
                }

                if (!val.isEmpty()) {
                    int rs = baymaxServiceClient.batchAdd(attr.getBiz(), val);
                    log.info("doSyncGameUser add whitelist with val:{} rs:{}", val, rs);
                }
            } catch (Exception e) {
                log.error("doSyncGameUser exception:", e);
                redisTemplate.delete(lockKey);
            }
        }

    }

    private boolean doAddUid(KaiHeiSkinPortalComponentAttr attr, long uid) {
        int effected = whitelistComponent.ignoreAdd(attr.getActId(), attr.getWhitelistCmptIndex(), String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
        if (effected > 0) {
            updateWhitelistStatic(attr);
        }

        return effected > 0;
    }

    private void updateWhitelistStatic(KaiHeiSkinPortalComponentAttr attr) {
        String groupCode = getRedisGroupCode(attr.getActId());
        Date now = commonService.getNow(attr.getActId());
        String mondayDate = DateUtil.getMondayDate(now, DateUtil.PATTERN_TYPE2);

        String key = makeKey(attr,WHITELIST_WEEK_STATISTIC);
        actRedisDao.hIncrByKey(groupCode,key,mondayDate,1,DateUtil.ONE_WEEK_SECONDS);
    }


    /**
     * 获取本周增加的白名单个数
     * @param now
     * @return
     */
    public long getWeekAddWhitelist(long actId, Date now) {
        KaiHeiSkinPortalComponentAttr attr = tryGetUniqueComponentAttr(actId);
        String groupCode = getRedisGroupCode(attr.getActId());
        String mondayDate = DateUtil.getMondayDate(now, DateUtil.PATTERN_TYPE2);
        String key = makeKey(attr,WHITELIST_WEEK_STATISTIC);
        String value = actRedisDao.hget(groupCode,key,mondayDate);
        return Convert.toLong(value,0);
    }


    @GetMapping("testSendPush")
    public Response<String> testSendPush(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex, @RequestParam("userId") Long userId) {
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return Response.fail(-1, "not grey" + System.currentTimeMillis());
        }
        doSendPush(getComponentAttr(actId, cmptIndex), userId);
        return Response.success("success:" + System.currentTimeMillis());
    }


    @GetMapping("sync")
    public Response<?> sync(@RequestParam("actId") int actId, @RequestParam("cmptIndex") long cmptIndex, @RequestParam("monday") String monday) {
        KaiHeiSkinPortalComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "param error");
        }

        long uid = getLoginYYUid();
        if (uid != 50018033) {
            return Response.fail(403,"unsupported operation");
        }

        doSyncGameUser(attr, monday);

        return Response.ok();
    }

    @GetMapping("addWhitelist")
    public Response<?> addWhitelist(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex, @RequestParam("uid") long uid) {
        KaiHeiSkinPortalComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "param error");
        }

        long loginYYUid = getLoginYYUid();
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return Response.fail(403, "unsupported operation");
        }

        if (SysEvHelper.isDeploy()) {
            if (!hdztRankingThriftClient.checkWhiteList(actId, RoleType.USER, Convert.toString(uid))) {
                return Response.fail(403, "unsupported operation");
            }
        }

        ActivityInfoVo activityInfo = actInfoService.queryActivityInfo(actId);
        if (activityInfo == null) {
            return Response.fail(400, "activity not exist");
        }

        Date now = commonService.getNow(actId);

        Date shutDate =  DateUtils.addDays(new Date(activityInfo.getEndTime()), -attr.getShutPortalAheadDays());
        if (now.after(shutDate)) {
            log.info("syncGameUser skip for ahead shut portal days shutDate:{}", shutDate);
            return Response.fail(400, "over shut portal time");
        }

        boolean added = doAddUid(attr, uid);
        if (added) {
            int result = baymaxServiceClient.add(attr.getBiz(), String.valueOf(uid));
            log.info("addWhitelist with loginYYUid:{}, uid:{} result:{}",loginYYUid, uid, result);
        }

        return Response.ok();
    }

    @GetMapping("sdler")
    public Response<Set<String>> getWhitelist(@RequestParam("actId") int actId, @RequestParam("cmptIndex") long cmptIndex, @RequestParam("bucket") int bucket) {
        return Response.success(Collections.emptySet());
    }

    @GetMapping("appPortalInfo")
    public TopFuncAttachResp queryAppPortalInfo(@RequestParam("actId") int actId,
                                                @RequestParam("cmptIndex") long cmptIndex,
                                                @RequestParam(name = "uid") long uid,
                                                @RequestParam(name = "hdid") String hdid) {
        log.info("appPortalInfo actId:{},uid:{},hdid:{}", actId, uid, hdid);
        TopFuncAttachResp resp = new TopFuncAttachResp();
        KaiHeiSkinPortalComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            resp.setResult(400);
            resp.setMsg("activity not exist");
            return resp;
        }

        if (uid <= 0) {
            resp.setResult(401);
            resp.setMsg("需要登录态");
            log.info("appPortalInfo login empty actId:{},uid:{},hdid:{}", actId, uid, hdid);
            return resp;
        }

        TopFuncAttachVo data = new TopFuncAttachVo();
        resp.setData(data);
        boolean inWhitelist = inWhitelist(attr, uid);
        if (!inWhitelist) {
            data.setShow(false);
            log.info("appPortalInfo white list empty actId:{},uid:{},hdid:{}", actId, uid, hdid);
            return resp;
        }

        KaiHeiSkinComponentAttr skinAttr = kaiHeiSkinComponent.tryGetUniqueComponentAttr(actId);
        if (skinAttr == null) {
            data.setShow(false);
            return resp;
        }

        Date now = commonService.getNow(actId);
        ActivityInfoVo activityInfoVo = actInfoService.queryActivityInfo(actId);
        if (now.getTime() > activityInfoVo.getEndTime() || now.getTime() < activityInfoVo.getBeginTime()) {
            data.setShow(false);
            log.info("appPortalInfo act time actId:{},uid:{},hdid:{}", actId, uid, hdid);
            return resp;
        }

        int userStatus = kaiHeiSkinComponent.queryUserStatus(skinAttr, uid, hdid);
        if (userStatus == NewUserStatus.NOT_IN_LIST) {
            data.setShow(false);
            log.info("appPortalInfo white list empty actId:{},uid:{},hdid:{}", actId, uid, hdid);
            return resp;
        }

        long closeTime = kaiHeiSkinComponent.getUserCloseEntryTime(skinAttr, uid);
        if (now.getTime() >= closeTime) {
            data.setShow(false);
            log.info("appPortalInfo closeTime end actId:{},uid:{},hdid:{},closeTime:{}", actId, uid, hdid, closeTime);
            return resp;
        }

        // 获取tips
        data.setShow(true);
        String tips = kaiHeiSkinComponent.getNoticeTips(skinAttr, uid);
        data.setTips(tips);
        if (StringUtils.isNotEmpty(tips)) {
            data.setTipsSeq(DateFormatUtils.format(now, DateUtil.PATTERN_TYPE2));
        }

        log.info("appPortalInfo done actId:{},uid:{},hdid:{}", actId, uid, hdid);
        return resp;
    }

    @GetMapping("portalInfo")
    public Response<TopFuncAttachVo> queryPortalInfo(@RequestParam("actId") int actId,
                                                     @RequestParam("cmptIndex") long cmptIndex,
                                                     @RequestParam(name = "56637EAD0B6CA194498BFFD27ADBB94A", defaultValue = "0") long userId) {
        KaiHeiSkinPortalComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        long loginUid = getLoginYYUid();
        if (loginUid <= 0 && userId <= 0) {
            log.info("portalInfo login empty uid:{}", loginUid);
            return Response.fail(401, "请先登录");
        }
        if (userId > 0) {
            loginUid = userId;
        }

        Clock clock = new Clock();
        log.info("portalInfo begin uid:{}", loginUid);

        TopFuncAttachVo data = new TopFuncAttachVo();
        boolean inWhitelist = inWhitelist(attr, loginUid);
        if (!inWhitelist) {
            data.setShow(false);
            log.info("portalInfo whitelist empty uid:{}", loginUid);
            return Response.success(data);
        }

        KaiHeiSkinComponentAttr skinAttr = kaiHeiSkinComponent.tryGetUniqueComponentAttr(actId);
        if (skinAttr == null) {
            data.setShow(false);
            return Response.success(data);
        }

        // 活动结束后不展示
        Date now = commonService.getNow(actId);
        ActivityInfoVo activityInfoVo = actInfoService.queryActivityInfo(actId);
        if (now.getTime() > activityInfoVo.getEndTime() || now.getTime() < activityInfoVo.getBeginTime()) {
            data.setShow(false);
            log.info("portalInfo act time uid:{}", loginUid);
            return Response.success(data);
        }

        Date aheadDate = DateUtils.addDays(new Date(activityInfoVo.getEndTime()), -attr.getShutPortalAheadDays());
        // 活动结束前x天，不在app登录名单里不展示
        if (now.after(aheadDate)) {
            int userStatus = kaiHeiSkinComponent.queryUserStatus(skinAttr, loginUid);
            if (userStatus == NewUserStatus.NOT_IN_LIST) {
                data.setShow(false);
                log.info("portalInfo status list empty uid:{}", loginUid);
                return Response.success(data);
            }
        }

        long closeTime = kaiHeiSkinComponent.getUserCloseEntryTime(skinAttr, loginUid);
        if (now.getTime() >= closeTime) {
            data.setShow(false);
            log.info("portalInfo close time uid:{},now:{},closeTime:{}", now.getTime(), closeTime, loginUid);
            return Response.success(data);
        }

        // 获取tips
        data.setShow(true);
        String tips = kaiHeiSkinComponent.getNoticeTips(skinAttr, loginUid);
        data.setTips(tips);
        if (StringUtils.isNotEmpty(tips)) {
            data.setTipsSeq(DateFormatUtils.format(now, DateUtil.PATTERN_TYPE2));
        }

        log.info("portalInfo done uid:{},cost:{}", loginUid, clock.tag());
        return Response.success(data);
    }

    /**
     * 在App弹窗中判断新老用户
     * @param actId
     * @param cmptIndex
     * @param app
     * @param hdid
     * @param clientType
     * @return
     */
    @GetMapping("userStatus")
    public Response<Integer> queryUserStatus(@RequestParam("actId") int actId,
                                             @RequestParam("cmptIndex") long cmptIndex,
                                             @RequestHeader(name = "x-fts-host-name", required = false) String app,
                                             @RequestHeader(name = "YYHeader-y0", required = false) String hdid,
                                             @RequestHeader(value = "YYHeader-Platform", required = false, defaultValue = "3") int clientType) {
        KaiHeiSkinPortalComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "param error");
        }

        long uid = getLoginYYUid();

        if (uid <= 0) {
            return Response.fail(401, "您还没有登录，请完成登录后刷新本页面");
        }

        // 获取是否新用户
        KaiHeiSkinComponentAttr attr1 = kaiHeiSkinComponent.tryGetUniqueComponentAttr(actId);
        var actInfo = actInfoService.queryActivityInfo(attr.getActId());
        int newUserStatus = kaiHeiSkinComponent.getOrSaveUserNewState(actInfo,attr1,uid,hdid);

        return Response.success(newUserStatus);
    }

    /**
     * 活动页面查询用户状态：皮肤领取状态、是否登录app、是否新用户
     * @param actId
     * @param cmptIndex
     * @return
     */
    @GetMapping("skinStatus")
    public Response<SkinInfo> queryUserSkinInfo(@RequestParam("actId") int actId,
                                                @RequestParam("cmptIndex") long cmptIndex) {

        KaiHeiSkinPortalComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "param error");
        }

        long uid = getLoginYYUid();

        if (uid <= 0) {
            return Response.fail(401, "您还没有登录，请完成登录后刷新本页面");
        }

        boolean whitelist = inWhitelist(attr, uid);
        if (!whitelist) {
            return Response.fail(403, "您不符合参赛资格");
        }

        // 获取是否新用户
        KaiHeiSkinComponentAttr attr1 = kaiHeiSkinComponent.tryGetUniqueComponentAttr(actId);
        int newUserStatus = kaiHeiSkinComponent.queryUserStatus(attr1, uid);
        SkinInfo data = new SkinInfo();
        if (newUserStatus == NewUserStatus.OLD_USER) {
            data.setStatus(3);
            return Response.success(data);
        }

        data.setSkinList(attr.getSkinConfigs());
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
        String key = makeKey(attr, String.format(SKIN_PICK_PREFIX, uid % ACT_USER_MOD));
        String value = redisTemplate.<String, String>opsForHash().get(key, String.valueOf(uid));
        if (!StringUtils.isNumeric(value)) {
            data.setStatus(0);
            return Response.success(data);
        }

        // 判断是否登录过App
        int status = newUserStatus == NewUserStatus.NOT_IN_LIST ? 1 : 2;
        data.setStatus(status);

        int skinId = Integer.parseInt(value);
        KaiHeiSkinPortalComponentAttr.SkinConfig skin = attr.getSkinConfigs().stream()
                .filter(skinConfig -> skinConfig.getSkinId() == skinId)
                .findFirst()
                .orElse(null);

        data.setSkin(skin);
        return Response.success(data);
    }

    public KaiHeiSkinPortalComponentAttr.SkinConfig queryUserSkin(long actId, long uid) {
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
        var attr = tryGetUniqueComponentAttr(actId);
        String key = makeKey(attr, String.format(SKIN_PICK_PREFIX, uid % ACT_USER_MOD));
        String value = redisTemplate.<String, String>opsForHash().get(key, String.valueOf(uid));
        if (StringUtil.isBlank(value)) {
            return null;
        }
        int skinId = Convert.toInt(value, 0);
        return attr.getSkinConfigs().stream()
                .filter(skinConfig -> skinConfig.getSkinId() == skinId)
                .findFirst()
                .orElse(null);
    }


    @GetMapping("skinPick")
    public Response<?> pickSkin(@RequestParam("actId") int actId,
                                @RequestParam("cmptIndex") long cmptIndex,
                                @RequestParam("skinId") int skinId,
                                @RequestHeader(name = "x-fts-host-name", required = false) String app,
                                @RequestHeader(name = "YYHeader-y0", required = false) String hdid,
                                @RequestHeader(value = "YYHeader-Platform", required = false, defaultValue = "3") int clientType) {
        KaiHeiSkinPortalComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "param error");
        }

        long uid = getLoginYYUid();

        if (uid < 0) {
            return Response.fail(401, "login is need");
        }

        KaiHeiSkinPortalComponentAttr.SkinConfig skin = attr.getSkinConfigs().stream()
                .filter(skinConfig -> skinConfig.getSkinId() == skinId)
                .findFirst()
                .orElse(null);

        if (skin == null) {
            return Response.fail(400, "皮肤不存在");
        }

        boolean whitelist = inWhitelist(attr, uid);
        if (!whitelist) {
            return Response.fail(403, "您还没有资格参与活动哦");
        }

        DayTaskComponentAttr dayTaskAttr = dayTaskComponent.tryGetUniqueComponentAttr(attr.getActId());
        KaiHeiSkinComponentAttr kaiHeiAttr = kaiHeiSkinComponent.tryGetUniqueComponentAttr(attr.getActId());
        if (dayTaskAttr != null && kaiHeiAttr != null) {
            long balance = dayTaskComponent.queryPoolBalance(dayTaskAttr, kaiHeiAttr.getQCoinAwardPoolPackageId());
            if (balance <= 0) {
                return Response.fail(405, "奖池已抢完，活动已结束");
            }
        }

        String key = makeKey(attr, String.format(SKIN_PICK_PREFIX, uid % ACT_USER_MOD));
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
        Boolean rs = redisTemplate.opsForHash().putIfAbsent(key, String.valueOf(uid), String.valueOf(skinId));
        log.info("pickSkin put with uid:{} skinId:{} rs:{}", uid, skinId, rs);

        if (!rs) {
            return Response.fail(404, "已选择皮肤");
        }

        ZhuiyaRisk.Platform platform = ZhuiyaRisk.Platform.forNumber(clientType);
        boolean fromPC = platform == ZhuiyaRisk.Platform.PLATFORM_UNKNOWN || platform == ZhuiyaRisk.Platform.PLATFORM_PC;
        if (fromPC) {
            //从PC 领取皮肤成功的后置处理
            threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
                ActivityInfoVo activityInfoVo = actInfoService.queryActivityInfo(actId);
                if (activityInfoVo != null) {
                    reportUserOrient(actId, uid, attr.getApps(), new Date(activityInfoVo.getEndTime()));
                    log.info("reportUserOrient success with uid:{}", uid);
                }
            });
        }

        return Response.ok();
    }

    public boolean inWhitelist(KaiHeiSkinPortalComponentAttr attr, long uid) {
        return whitelistComponent.inWhitelist(attr.getActId(), attr.getWhitelistCmptIndex(), String.valueOf(uid));
    }

    public List<Long> queryGameUids(String monday, int pageIndex, int pageSize) {
        GameUserResp resp = queryGameUsers(monday, pageIndex, pageSize);
        if (resp == null || resp.code != 0) {
            log.error("doSyncGameUser queryGameUsers fail: resp:{}", resp);
            throw new RuntimeException("query game users fail");
        }

        if (CollectionUtils.isEmpty(resp.data)) {
            return Collections.emptyList();
        }

        return resp.data.stream().map(UidItem::getUid).filter(StringUtils::isNumeric).map(Long::parseLong).toList();
    }

    public GameUserResp queryGameUsers(String monday, int pageIndex, int pageSize) {
        String path = GAME_USER_PATH + "?week_bgn_dt=" + monday + "&pageIndex=" + pageIndex + "&pageSize=" + pageSize;
        String token = TokenGrant.token(APP_KEY, APP_SECRET, path, 300L);

        HttpHeaders headers = new HttpHeaders();
        headers.add("signToken", token);

        RequestEntity<?> entity = new RequestEntity<>(headers, HttpMethod.GET, URI.create(GAME_USER_SERVICE + path));

        ResponseEntity<GameUserResp> response = restTemplate.exchange(entity, GameUserResp.class);
        if (response.getStatusCode() == HttpStatus.OK) {
            return response.getBody();
        }

        log.error("queryGameUsers fail status code:{}", response.getStatusCode());
        return null;
    }

    private void reportUserOrient(long actId, long uid, String apps, final Date endTime) {
        Splitter.on(',').trimResults().omitEmptyStrings().split(apps).forEach(app -> {
            String key = "act:" + actId + ":" + app;
            zhuiWanPrizeIssueServiceClient.reportUserOrient(uid, key, endTime);
        });
    }

    @Getter
    @Setter
    public static class UidItem {
        protected String uid;
    }

    @Getter
    @Setter
    @ToString
    public static class GameUserResp {

        protected int code;

        protected String message;

        protected List<UidItem> data;

        protected int pageIndex;

        protected int totalCount;
    }

    @Getter
    @Setter
    public static class TopFuncAttachVo {
        /** 是否需要显示该功能 */
        private boolean show;
        /** 下方tips展示文案 （值为空则不需要展示） */
        private String tips;
        /** 下方 tips 展示控制seq，如果客户端本地该 seq 已经展示过了，那么不需要展示； 如果该值为空，表示无需展示 */
        private String tipsSeq;
    }

    @Getter
    @Setter
    public static class TopFuncAttachResp {

        protected int result = 0;

        protected String msg;

        protected TopFuncAttachVo data;
    }

    @Getter
    @Setter
    public static class SkinInfo {

        /** 0-未选皮肤，1-已选皮肤未登录App，2-已选皮肤已登录App的新用户，3-已选皮肤已登录App的旧用户 **/
        protected int status;

        protected KaiHeiSkinPortalComponentAttr.SkinConfig skin;

        protected List<KaiHeiSkinPortalComponentAttr.SkinConfig> skinList;
    }
}
