package com.yy.gameecology.hdzj.bean.pepc;

import com.yy.gameecology.common.db.model.gameecology.pepc.PepcMatchAwardRecord;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class PepcAwardRecord {

    protected long awardId;

//    protected long phaseId;

    protected int awardType;

    protected long uid;

    protected long amount;

    protected String awardDesc;

    protected int awardState;

    protected Date awardTime;

    public PepcAwardRecord() {
    }

    public PepcAwardRecord(PepcMatchAwardRecord record) {
        this.awardId = record.getId();
//        this.phaseId = record.getPhaseId();
        this.awardType = record.getAwardType();
        this.uid = record.getUid();
        this.amount = record.getAmount();
        this.awardDesc = record.getAwardDesc();
        this.awardState = record.getAwardState();
        this.awardTime = record.getAwardTime();
    }

    public long getId() {
        return this.awardId;
    }
}
