package com.yy.gameecology.hdzj.bean;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class PersonalTaskDataVo {
    /**
     * 完成人数
     */
    @JSONField(ordinal = 1)
    private long finishCount = 0;
    /**
     * 获得的总奖金
     */
    @JSONField(ordinal = 2)
    private int totalAward = 0;
    @JSONField(ordinal = 3)
    private List<PersonalTaskAwardVo> list;

    public PersonalTaskDataVo(long finishCount, int totalAward, List<PersonalTaskAwardVo> list) {
        this.finishCount = finishCount;
        this.totalAward = totalAward;
        this.list = list;
    }
}
