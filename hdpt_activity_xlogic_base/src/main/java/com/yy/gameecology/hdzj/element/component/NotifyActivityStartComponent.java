package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.bean.hdzt.ActivityTimeStart;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.NotifyActivityStartComponentAttr;
import org.springframework.stereotype.Component;

/**
 * 活动开始后调用一系列 内部/外部接口 配合活动开启处理
 * 注意：只负责可靠完成通知（对方收到通知就算完成任务），不应有复杂逻辑。
 *
 * <AUTHOR>
 * @date 2021/11/02 14:25
 */
@Component
public class NotifyActivityStartComponent extends BaseActComponent<NotifyActivityStartComponentAttr> {

    @Override
    public Long getComponentId() {
        return ComponentId.NOTIFY_ACTIVITY_START;
    }

    @Override
    public boolean isUniq1UseIndex() {
        return true;
    }


    @HdzjEventHandler(value = ActivityTimeStart.class, canRetry = true)
    public void onActivityTimeStart(ActivityTimeStart event, NotifyActivityStartComponentAttr attr) {
        log.info("onActivityTimeStart done -> event:{}, attr:{}", event, attr);
    }
}
