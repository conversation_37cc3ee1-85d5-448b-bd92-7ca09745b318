package com.yy.gameecology.hdzj.element.history;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.service.EnrollmentService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.MilestoneV3ComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.UpdateRankingRequest;
import com.yy.thrift.hdztranking.UpdateRankingResult;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于陪玩团,目前陪玩业务已经不做活动,先暂时移动到历史文件夹
 * <p>
 * 按完成任务时刻,记录top5(上报数据方式)
 *
 * <AUTHOR>
 */
@Component
@Deprecated
public class MilestoneV3Component extends BaseActComponent<MilestoneV3ComponentAttr> {

    private Logger logger = LoggerFactory.getLogger(MilestoneV3Component.class);

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private EnrollmentService enrollmentService;

    @Override
    public Long getComponentId() {
        return ComponentId.MILE_STONE_V3;
    }

    /***
     * 结算里程碑
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void onRankingScoreChanged(TaskProgressChanged event, MilestoneV3ComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        if (rankId != attr.getRankId() || phaseId != attr.getPhaseId()) {
            return;
        }

        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        if (!actRedisDao.setNX(groupCode, makeKey(attr, event.getSeq()), DateUtil.getNowYyyyMMddHHmmss())) {
            return;
        }
        //1.获取过关的等级
        long currTaskIndex = event.getCurrTaskIndex();
        long startTaskIndex = event.getStartTaskIndex() + 1;
        String member = event.getMember();
        long timeKey = event.getTimeKey();
        String occurTime = event.getOccurTime();
        for (long i = startTaskIndex; i <= currTaskIndex; i++) {
            recordAndAward(i, timeKey, occurTime, attr, member);
        }
    }

    private void recordAndAward(long level, long timeKey, String occurTime, MilestoneV3ComponentAttr attr, String member) {
        //1.获取贡献榜top n
        MilestoneV3ComponentAttr.LevelConfigV3 config = attr.getLevelConfigMap().get(level);
        Map<String, String> srcMember = Maps.newHashMap();
        srcMember.put(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, member);

        Date date = DateUtil.getDate(occurTime);
        String timeCode = TimeKeyHelper.getTimeCode(timeKey, date);
        List<Rank> awardPlayerRanks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getPlayerRankId(), attr.getPhaseId(), timeCode,
                config.getPlayerTopN(), srcMember);
        List<Rank> awardAnchorRanks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getAnchorRankId(), attr.getPhaseId(), timeCode,
                config.getAnchorTopN(), srcMember);

        //3.下发奖励
        //神豪
        String time = DateUtil.getNowYyyyMMddHHmmss();
        for (Rank rank : awardPlayerRanks) {
            Map<Long, Map<Long, Integer>> playerAwardPackageIds = Collections.singletonMap(config.getAwardTaskId(),
                    Collections.singletonMap(config.getPlayerAwardPackageId(), config.getAwardCount()));
            hdztAwardServiceClient.doBatchWelfare(Convert.toLong(rank.getMember()), playerAwardPackageIds, time, attr.getRetry(), Maps.newHashMap());
            log.info("MilestoneV3Component.recordAndAward doBatchWelfare ");
        }
        //主播
        for (Rank rank : awardAnchorRanks) {
            Map<Long, Map<Long, Integer>> anchorAwardPackage = Collections.singletonMap(config.getAwardTaskId(),
                    Collections.singletonMap(config.getAnchorAwardPackageId(), config.getAwardCount()));
            hdztAwardServiceClient.doBatchWelfare(Convert.toLong(rank.getMember()), anchorAwardPackage, time, attr.getRetry(), Maps.newHashMap());
        }
        //4.发全频道广播
        if (config.isBroadcast()) {
            doMilestoneBroadcastTop(attr.getActId(), member);
        }

        List<Rank> playerRanks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getPlayerRankId(), attr.getPhaseId(), timeCode,
                config.getRecordTopN(), srcMember);
        List<Rank> anchorRanks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getAnchorRankId(), attr.getPhaseId(), timeCode,
                config.getRecordTopN(), srcMember);

        //5.上报记录数据
        // 5.1 获取actorId
        Long playerActor = attr.getActors().get("player");
        Long anchorActor = attr.getActors().get("anchor");
        try {
            for (Rank rank : playerRanks) {
                Map<Long, String> actorMap = Maps.newHashMap();
                actorMap.put(playerActor, rank.getMember());
                actorMap.put(attr.getSubjectActor(), member);
                Map<Long, Long> rankScore = Maps.newHashMap();
                rankScore.put(config.getPlayerRecordRankId(), rank.getScore());
                updateRecord(attr.getActId(), attr.getBusiId(), attr.getGiftId(), date.getTime(), actorMap, rankScore);

            }
            for (Rank rank : anchorRanks) {
                Map<Long, String> actorMap = Maps.newHashMap();
                actorMap.put(anchorActor, rank.getMember());
                actorMap.put(attr.getSubjectActor(), member);
                Map<Long, Long> rankScore = Maps.newHashMap();
                rankScore.put(config.getAnchorRecordRankId(), rank.getScore());
                updateRecord(attr.getActId(), attr.getBusiId(), attr.getGiftId(), date.getTime(), actorMap, rankScore);
            }
        } catch (Exception e) {
            logger.error("updateRecord error e:{}", e.getMessage(), e);
        }
    }


    private void updateRecord(long actId, long busiId, String giftId, long time, Map<Long, String> actorMap, Map<Long, Long> rankScore) throws TException {
        String seq = UUID.randomUUID().toString();
        UpdateRankingRequest request = new UpdateRankingRequest();
        request.setBusiId(busiId);
        request.setActId(actId);
        request.setSeq(seq);
        request.setActors(actorMap);

        request.setRankScores(rankScore);
        request.setItemId(giftId);
        request.setCount(1);
//        request.setScore();
        request.setTimestamp(time);
        UpdateRankingResult result = hdztRankingThriftClient.getProxy().updateRanking(request);
        log.info("MilestoneV3Component.updateRecord done: result:{}, request:{}", result.toString(), request.toString());
    }

    /**
     * 做全频道广播
     *
     * <AUTHOR>
     */
    private void doMilestoneBroadcastTop(long actId, String teamId) {
        MemberInfo memberInfo = memberInfoService.getPwTeam(teamId);
        long asid = Convert.toLong(memberInfo.getAsid());
        GameecologyActivity.Act202011_PwMilestoneBanner.Builder pwMilestoneBanner =
                GameecologyActivity.Act202011_PwMilestoneBanner.newBuilder()
                        .setTeamName(memberInfo.getName())
                        .setAsid(asid).setActId(actId);

        GameecologyActivity.GameEcologyMsg message = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setAct202011PwMilestoneBanner(pwMilestoneBanner)
                .setUri(GameecologyActivity.PacketType.kAct202011_PwMilestoneBanner_VALUE)
                .build();
        svcSDKService.broadcastAllChanelsInPW(actId, message);
        logger.info("doMilestoneBroadcastTop done teamId:{} asid:{}", teamId, asid);
    }

    /**
     * 查询公会下的所有团
     *
     * @param actId
     * @param sid
     * @return
     */
    public List<MemberInfo> getPwChannelAllTuan(long actId, Long sid) {

        MilestoneV3ComponentAttr attr = getUniqueComponentAttr(actId);
        Assert.notNull(attr, "not find attr,actId=" + actId);

        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityCacheNoTime(actId);
        Assert.notNull(activityInfoVo, "activityInfoVo not find,actId=" + actId);

        if (sid == null || sid <= 0) {
            return Lists.newArrayList();
        }

        List<String> memberIds;
        if (attr.getTuanContributionRankId() > 0) {
            HashMap<String, String> ext = Maps.newHashMap();
            ext.put(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, sid.toString());
            List<Rank> ranks = hdztRankingThriftClient.queryRankingCache(actId,
                    attr.getTuanContributionRankId(), 0, "", 1000, "", ext);
            memberIds = ranks.stream().map(Rank::getMember).collect(Collectors.toList());
        } else {
            List<EnrollmentInfo> enrollmentInfos = enrollmentService.getEntryConfigInfosBySign(actId, 90060L, sid);
            memberIds = enrollmentInfos.stream().map(EnrollmentInfo::getMemberId).collect(Collectors.toList());
        }


        Map<String, MemberInfo> memberInfoMap = memberInfoService.getPwTeamMap(memberIds);

        List<MemberInfo> memberInfoList = memberIds.stream()
                .map(memberInfoMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return memberInfoList;
    }
}
