package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ChannelWatchwordLotteryComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    protected long busiId;

    @ComponentAttrField(labelText = "口令文案")
    protected String chatText;

    @ComponentAttrField(labelText = "口令正则匹配，不含标点符号")
    protected String targetWord;

    @ComponentAttrField(labelText = "抽奖奖池ID")
    protected long tAwardTskId;

    @ComponentAttrField(labelText = "每个口令只抽一次", remark = "及时口令匹配上了，每次口令只能触发一个抽奖（要完成一次抽奖）")
    protected boolean singleLottery;
}
