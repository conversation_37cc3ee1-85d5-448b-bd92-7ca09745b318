package com.yy.gameecology.hdzj.element.component;

import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.bean.hdzt.ActivityTimeEnd;
import com.yy.gameecology.activity.client.thrift.TurnoverServiceClient;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.NotifyActivityEndComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.NotifyActivityEndComponentAttr.TurnoverCancelGiftActTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 活动结束后调用一系列 内部/外部接口 配合活动善后处理
 * 注意：只负责可靠完成通知（对方收到通知就算完成任务），不应有复杂逻辑。
 *
 * <AUTHOR>
 * @date 2021/11/02 14:25
 */
@Component
public class NotifyActivityEndComponent extends BaseActComponent<NotifyActivityEndComponentAttr> {

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private TurnoverServiceClient turnoverServiceClient;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Override
    public Long getComponentId() {
        return ComponentId.NOTIFY_ACTIVITY_END;
    }

    @Override
    public boolean isUniq1UseIndex() {
        return true;
    }

    @HdzjEventHandler(value = ActivityTimeEnd.class, canRetry = true)
    public void onActivityTimeEnd(ActivityTimeEnd event, NotifyActivityEndComponentAttr attr) {
        log.info("onActivityTimeEnd done -> event:{}, attr:{}", event, attr);

        // 异步通知营收系统下掉指定礼物的活动属性
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> notifyTurnoverCancelGiftActTag(event, attr));
    }

    /**
     * 通知营收系统下掉指定礼物的活动属性
     */
    private void notifyTurnoverCancelGiftActTag(ActivityTimeEnd event, NotifyActivityEndComponentAttr attr) {
        List<TurnoverCancelGiftActTag> list = attr.getNotifyTurnoverCancelGiftActTag();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        String seq = event.getSeq();
        long actId = attr.getActId();

        // 逐个通知
        for (TurnoverCancelGiftActTag item : list) {
            Clock clock = new Clock();
            String content = "notifyTurnoverCancelGiftActTag -> ";
            try {
                int ret = turnoverServiceClient.offlineActivityProps(item.getAppId(), item.getPropId(), item.getReuseMoneyGun(), item.getTag());
                log.info("notifyTurnoverCancelGiftActTag ok@actId:{}, seq:{}, item:{} ret:{} {}", actId, seq, item, ret, clock.tag());
                content += (ret == 1 ? "OK, 通知营收下礼物活动标签【成功】" : "FAIL!!! 通知营收下礼物活动标签【失败】") + item + ", ret=" + ret;
            } catch (Throwable t) {
                log.error("notifyTurnoverCancelGiftActTag exception@actId:{}, seq:{}, item:{}, err:{} {}", actId, seq, item, t.getMessage(), clock.tag());
                content += "EXCEPTION!!! 通知营收下掉礼物活动标签【异常】" + item + ", err:" + t.getMessage();
            }
            doHint(actId, seq, content, attr);
        }
    }

    /**
     * 如流通知相关群、人员等
     *
     * @param content
     * @param attr
     */
    private void doHint(long actId, String seq, String content, NotifyActivityEndComponentAttr attr) {
        // 附上活动id和seq， 方便定位排障
        String message = String.format("%s, %s, seq:%s", actId, content, seq);

        // 1. 特意使用 error 消息， 借助公司自建的告警通道做通知！
        log.error(content);

        // 2. 再走百度如流做一次通知，双保险
        //baiduInfoFlowRobotService.sendNotify(attr.getRlGroupId(), attr.getRlBaiduWebhook(), content, attr.getRlUserIds());
        baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, content, attr.getRlUserIds());
    }
}
