package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.RoundingDownSource;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.Map;
import java.util.Set;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-11-02 21:01
 **/
public class CarvePoolComponentAttr extends ComponentAttr {
    /**
     * 要结算的榜单id
     */
    @ComponentAttrField(labelText = "榜单id")
    private long rankId;

    /**
     * 阶段id
     */
    @ComponentAttrField(labelText = "阶段id列表", remark = "多个用逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private Set<Long> phaseId;

    /**
     * top n的
     */
    @ComponentAttrField(labelText = "榜单前几名")
    private long topN;

    /**
     * 发奖保留多少位。 10 代表舍弃个位，100代表舍弃百位和个位
     */
    @ComponentAttrField(labelText = "发奖保留多少位", dropDownSourceBeanClass = RoundingDownSource.class)
    private int roundingDown = 10;

    @ComponentAttrField(labelText = "奖池id")
    private Long taskId;

    /**
     * 奖励的奖品
     * 第一层key busiId / 第二层key taskId value packageId
     */
    @ComponentAttrField(labelText = "奖品配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "业务id", remark = "200:游戏生态,400:游戏宝贝,500:交友,600:约战,900:陪玩"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "奖池id"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "奖包id")
            })
    private Map<Long, Map<Long, Long>> busiTaskIdPackageId = Maps.newHashMap();

    /**
     * 总奖池数量
     */
    @ComponentAttrField(labelText = "总奖池数量")
    private long poolTotal;


    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public Set<Long> getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(Set<Long> phaseId) {
        this.phaseId = phaseId;
    }

    public long getPoolTotal() {
        return poolTotal;
    }

    public void setPoolTotal(long poolTotal) {
        this.poolTotal = poolTotal;
    }

    public long getTopN() {
        return topN;
    }

    public void setTopN(long topN) {
        this.topN = topN;
    }

    public Map<Long, Map<Long, Long>> getBusiTaskIdPackageId() {
        return busiTaskIdPackageId;
    }

    public void setBusiTaskIdPackageId(Map<Long, Map<Long, Long>> busiTaskIdPackageId) {
        this.busiTaskIdPackageId = busiTaskIdPackageId;
    }

    public long getTaskId() {
        return taskId;
    }

    public void setTaskId(long taskId) {
        this.taskId = taskId;
    }

    public int getRoundingDown() {
        return roundingDown;
    }

    public void setRoundingDown(int roundingDown) {
        this.roundingDown = roundingDown;
    }
}
