package com.yy.gameecology.hdzj.bean;

public class TiGuanMyDetail {

    private long uid;

    private String nickname;

    // 分值
    private long score;

    // 小组中的排名 1 ~ n， rank = -1 表示还未加入挑战小组
    private long rank;

    // 负分表示落后第1名的分，正分表示领先第2名的分
    private long gap;

    // ture：擂主， false：挑战者
    private boolean host;

    public TiGuanMyDetail() {
    }

    public TiGuanMyDetail(long uid, String nickname, long score, long rank, long gap, boolean host) {
        this.uid = uid;
        this.nickname = nickname;
        this.score = score;
        this.rank = rank;
        this.gap = gap;
        this.host = host;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public long getScore() {
        return score;
    }

    public void setScore(long score) {
        this.score = score;
    }

    public long getRank() {
        return rank;
    }

    public void setRank(long rank) {
        this.rank = rank;
    }

    public long getGap() {
        return gap;
    }

    public void setGap(long gap) {
        this.gap = gap;
    }

    public boolean isHost() {
        return host;
    }

    public void setHost(boolean host) {
        this.host = host;
    }
}
