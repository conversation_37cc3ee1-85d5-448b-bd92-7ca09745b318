package com.yy.gameecology.hdzj.element.component.service;

import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt1016UserMedal;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.NanoIdUtils;
import com.yy.gameecology.hdzj.element.component.attr.YOMedalComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.YOMedalDao;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Slf4j
@Service
public class YOMedalService {

    @Autowired
    private YOMedalDao yoMedalDao;

    @Autowired
    protected HdztAwardServiceClient hdztAwardServiceClient;

    @Transactional(rollbackFor = Throwable.class)
    public boolean tryGrantMedal(YOMedalComponentAttr attr, long uid, long packageId, int level, Date time) {
        Cmpt1016UserMedal userMedal = yoMedalDao.selectUserMedal(attr.getActId(), uid);
        if (userMedal == null) {
            int rs = yoMedalDao.addUserMedal(attr.getActId(), uid, level, time);
            if (rs <= 0) {
                return false;
            }

            doWelfareMedal(attr, uid, packageId);

            return true;
        }

        if (userMedal.getLevel() >= level) {
            return false;
        }

        int rs = yoMedalDao.updateUserMedal(userMedal.getId(), level, time);
        if (rs <= 0) {
            return false;
        }

        doWelfareMedal(attr, uid, packageId);

        return true;
    }

    private void doWelfareMedal(YOMedalComponentAttr attr, long uid, long packageId) {
        String seq = "grant_medal:" + attr.getActId() + ":" + packageId + ":" + uid;
        if (SysEvHelper.isDev()) {
            seq = NanoIdUtils.id();
        }
        BatchWelfareResult welfareResult = hdztAwardServiceClient.doWelfare(attr.getBusiId(), uid, attr.getTAwardTskMedalId(), 1, packageId, seq);
        log.info("onZhuiwanLogin welfare medal with seq:{}, result:{}", seq, welfareResult);

        if (welfareResult == null || welfareResult.getCode() != 0) {
            throw new RuntimeException("welfare fail");
        }
    }
}
