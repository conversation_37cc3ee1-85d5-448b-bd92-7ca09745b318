package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.actlayer.*;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.TurnoverFamilyThriftClient;
import com.yy.gameecology.activity.client.thrift.ZhuiwanRoomInfoClient;
import com.yy.gameecology.activity.exception.LotteryException;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.OnlineChannelService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.activity.service.layer.BroActLayerService;
import com.yy.gameecology.activity.service.layer.LayerSupport;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.db.model.gameecology.ComponentWhitelist;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.HdzjHelper;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.AwardResult;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.ChannelChatLotteryComponent;
import com.yy.gameecology.hdzj.element.component.WhitelistComponent;
import com.yy.gameecology.hdzj.element.component.attr.AnchorHourPkComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.ChannelChatLotteryComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardPackageAttrConfig;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztranking.*;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-11-14 20:48
 **/
@UseRedisStore
@Component
@RestController
@RequestMapping("/5116")
public class AnchorHourPkComponent extends BaseActComponent<AnchorHourPkComponentAttr> implements LayerSupport {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BroActLayerService broActLayerService;

    @Autowired
    private OnlineChannelService onlineChannelService;

    @Autowired
    private WhitelistComponent whitelistComponent;


    @Autowired
    private ChannelChatLotteryComponent channelChatLotteryComponent;

    @Autowired
    private TurnoverFamilyThriftClient turnoverFamilyThriftClient;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private ZhuiwanRoomInfoClient zhuiwanRoomInfoClient;

    private static final int QUERY_FAMILY_BATCH_SIZE = 200;

    private static final int QUERY_PK_RANK_COUNT = 30000;

    /**
     * pk数据未初始化完成前，更新榜单临时存放队列
     */
    private static final String UPDATE_RANKING_QUEUE = "update_ranking_queue";

    /**
     * hash
     * 用户pk对阵信息
     * %s == yyyyMMddHH
     */
    private static final String PK_INFO_KEY = "pk_info:%s";

    private static final String PK_DAY_INFO_KEY = "pk_day_info:%s";

    /**
     * hash
     * pk初始化记录标记
     */
    private static final String PK_INIT_TAG = "pk_init_tag";

    /**
     * hash
     * pk结算记录标记
     */
    private static final String PK_SETTLE_TAG = "pk_settle_tag";

    private final static String TOTAL_AWARD_CONSUME = "total_award_consume";

    /**
     * pk记录流水
     * %s==yyyyMMddHH
     */
    private final static String USER_PK_RESULT = "pk_result:%s";


    /**
     * pk记录流水
     * %s==uid
     */
    private final static String USER_PK_RESULT_RECORD = "pk_result_record:%s";



    @Override
    public long getActId() {
        return ComponentId.ANCHOR_HOUR_PK;
    }

    @Override
    public Long getComponentId() {
        return ComponentId.ANCHOR_HOUR_PK;
    }

    @PostConstruct
    @Scheduled(cron = "0/1 * * * * ? ")
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    public void settle() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            log.info("actIds is empty");
            return;
        }
        for (Long actId : actIds) {
            Date now = commonService.getNow(actId);
            ActivityInfoVo actInfo = hdztRankingThriftClient.queryActivityInfo(actId);
            long startTime = actInfo.getBeginTime();
            //结束时间故意延迟1分钟给活动结束后pk结算
            long endTime = actInfo.getEndTime() + DateUtil.ONE_MIN_MILL_SECONDS;
            if (now.getTime() < startTime || now.getTime() > endTime) {
                continue;
            }
            AnchorHourPkComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            timerSupport.work(makeKey(attr, "settleTimer"), Const.TEN, () -> {
                try {
                    //初始化pk信息
                    initPkData(attr, now);
                } catch (Exception e) {
                    log.error("initPkData error,e:{}", e.getMessage(), e);
                }
                try {
                    //重放更新榜单事件
                    reUpdatePkRank(attr, now);
                } catch (Exception e) {
                    log.error("reUpdatePkRank error,e:{}", e.getMessage(), e);
                }
                try {
                    //结算每小时pk信息
                    settlePkData(attr, now);
                } catch (Exception e) {
                    log.error("settlePkData error,e:{}", e.getMessage(), e);
                }
            });
        }
    }


    /**
     * 更新pk小时榜
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChangedAddPkScore(RankingScoreChanged event, AnchorHourPkComponentAttr attr) throws Exception {
        if (!(attr.getRankId() == event.getRankId() && attr.getPhaseId() == event.getPhaseId())) {
            return;
        }
        log.info("onRankingScoreChangedAddPkScore,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        Date occurTime = DateUtil.getDate(event.getOccurTime());
        if (!pkTimeBegin(attr, occurTime)) {
            log.info("onRankingScoreChanged not time,uid:{},score:{},time:{}", event.getMember(), event.getItemScore(), event.getOccurTime());
            return;
        }
        boolean notInit = pushUpdateRankingBeforeInit(event, attr);
        if (!notInit) {
            updatePkRank(event, attr);
        }
    }

    /**
     * pk分值触发年度祝福
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChangedAddChatLottery(RankingScoreChanged event, AnchorHourPkComponentAttr attr) throws Exception {
        if (!(attr.getPkRankId() == event.getRankId() && attr.getPkRankPhaseId() == event.getPhaseId())) {
            return;
        }
        log.info("onRankingScoreChangedAddChatLottery,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));

        addChatLottery(event, attr);
    }


    @RequestMapping("/queryPkInfo")
    public Response<PkListInfo> queryPkInfo(Long actId
            , @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx
            , @RequestParam(name = "time") String timeCode
            , @RequestParam(name = "sid", required = false) long sid
            , @RequestParam(name = "ssid", required = false) long ssid) {

        AnchorHourPkComponentAttr attr = getComponentAttr(actId, cmptInx);

        PkListInfo pkListInfo = new PkListInfo();
        Set<Long> allUids = Sets.newHashSet();

        Date now = commonService.getNow(actId);
        pkListInfo.setServerTime(now.getTime());

        int pkState = calPkState(attr, now, timeCode);
        pkListInfo.setStatus(pkState);


        //全服top pk列表
        List<PkPariItem> pkPariItems = queryTopPkList(attr, timeCode);

        //当前频道uid
        List<Long> anchorUids = null;
        if (sid > 0 && ssid > 0) {
            OnlineChannelInfo onlineChannelInfo = onlineChannelService.get(sid, ssid);
            if (onlineChannelInfo != null) {
                allUids.addAll(onlineChannelInfo.getEffectAnchorId());
                anchorUids = onlineChannelInfo.getEffectAnchorId();
            }
        }

        //提取全服榜单主播uid
        List<Long> memberAUids = pkPariItems.stream().map(p -> Convert.toLong(p.getItem1().getKey())).collect(Collectors.toList());
        allUids.addAll(memberAUids);
        List<Long> memberBUids = pkPariItems.stream().map(p -> Convert.toLong(p.getItem2().getKey())).collect(Collectors.toList());
        allUids.addAll(memberBUids);

        //当前频道PK信息
        List<PkPariItem> channelPkPair = null;
        if (!CollectionUtils.isEmpty(anchorUids)) {
            channelPkPair = queryAnchorPkItems(attr, timeCode, anchorUids.stream().map(Convert::toString).toList());

            List<Long> cahnnelMemberAUids = channelPkPair.stream().map(p -> Convert.toLong(p.getItem1().getKey())).collect(Collectors.toList());
            allUids.addAll(cahnnelMemberAUids);
            List<Long> channelMemberBUids = channelPkPair.stream().map(p -> Convert.toLong(p.getItem2().getKey())).collect(Collectors.toList());
            allUids.addAll(channelMemberBUids);
        }

        //主播多昵称
        Map<String, Map<String, MultiNickItem>> nickExtUsers = Maps.newHashMap();
        Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(allUids), nickExtUsers, false, Template.all.getCode());
        pkListInfo.setNickExtUsers(nickExtUsers);

        //填充全服排行pk列表
        fillPkPariItemData(pkPariItems, userInfoVoMap);
        pkListInfo.setPkList(pkPariItems);

        //填充当前频道pk列表
        if (channelPkPair != null) {
            fillPkPariItemData(channelPkPair, userInfoVoMap);
            pkListInfo.setCurPkList(channelPkPair);
        }

        //填充当前频道主播信息
        if (!CollectionUtils.isEmpty(anchorUids)) {
            List<UserInfoVo> anchorList = Lists.newArrayList();
            for (Long uid : anchorUids) {
                if (userInfoVoMap.containsKey(uid)) {
                    anchorList.add(userInfoVoMap.get(uid));
                }
            }
            pkListInfo.setAnchorList(anchorList);
        }

        //倒计时
        long pkLeftSeconds = calPkLeftSeconds(attr, now, pkState);
        pkListInfo.setLeftSeconds(pkLeftSeconds);

        pkListInfo.setTotalAwardLimit(attr.getTotalPool());
        pkListInfo.setTotalAwardLeft(getAwardPoolLeft(attr));


        return Response.success(pkListInfo);
    }

    @RequestMapping("/queryPkRecord")
    public Response<Map<String, Object>> queryPkRecord(Long actId
            , @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx
            , @RequestParam(name = "56637EAD0B6CA194498BFFD27ADBB94A", required = false) Long userId) {
        long uid = getLoginYYUid();
        if (uid <= 0L && userId == null) {
            return Response.fail(400, "未登录");
        }
        AnchorHourPkComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-1, "not config");
        }

        Map<String, Object> result = Maps.newHashMap();

        String redisCode = getRedisGroupCode(actId);
        String pkRecordKey = buildUserPkRecordKey(attr, uid);
        final int showRecord = 49;
        List<String> records = actRedisDao.lrange(redisCode, pkRecordKey, 0, showRecord);
        if (!CollectionUtils.isEmpty(records)) {
            List<PkResultRecord> pkResultRecords = records.stream()
                    .filter(Objects::nonNull)
                    .map(p -> JSON.parseObject(p, PkResultRecord.class))
                    .collect(Collectors.toList());
            result.put("records", pkResultRecords);

        }
        return Response.success(result);
    }


    private List<PkPariItem> queryTopPkList(AnchorHourPkComponentAttr attr, String timeCode) {
        List<PkPariItem> result = Lists.newArrayList();
        //预读取topN数据，这里可以查询的N可大于显示的N，这样如果可以将pk对手的分值覆盖到就可以少查一次
        List<Rank> preLoadRanks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getPkRankId(), attr.getPkRankPhaseId(), timeCode, attr.getTopPkRankShowPreLoad(), Maps.newHashMap());

        String redisCode = getRedisGroupCode(attr.getActId());
        Map<String, Rank> preLoadRankMap = preLoadRanks.stream().collect(Collectors.toMap(Rank::getMember, p -> p));
        List<String> topMember = preLoadRanks.stream().map(Rank::getMember).collect(Collectors.toList());
        String pkInfoKey = buildPkInfoKey(attr, timeCode);
        List<Object> pkPair = actRedisDao.hmGet(redisCode, pkInfoKey, MyListUtils.toObjectList(topMember));
        Set<String> alreadySet = Sets.newHashSet();
        Set<String> memberBNotInTop = Sets.newHashSet();
        for (int i = 0; i < preLoadRanks.size() && i < attr.getTopPkRankShow(); i++) {
            String memberA = topMember.get(i);
            if (alreadySet.contains(memberA)) {
                continue;
            }
            Object pkMemberB = pkPair.get(i);
            if (pkMemberB == null) {
                continue;
            }
            String memberB = Convert.toString(pkMemberB);

            PkPariItem pkPariItem = new PkPariItem();

            PkListItem item1 = new PkListItem();
            item1.setKey(memberA);
            if (preLoadRankMap.containsKey(memberA)) {
                item1.setValue(preLoadRankMap.get(memberA).getScore());
                item1.setRank(preLoadRankMap.get(memberA).getRank());
            }

            PkListItem item2 = new PkListItem();
            item2.setKey(memberB);
            if (preLoadRankMap.containsKey(memberB)) {
                item2.setValue(preLoadRankMap.get(memberB).getScore());
                item2.setRank(preLoadRankMap.get(memberB).getRank());
            } else {
                memberBNotInTop.add(memberB);
            }

            pkPariItem.setItem1(item1);
            pkPariItem.setItem2(item2);
            result.add(pkPariItem);

            alreadySet.add(memberA);
            alreadySet.add(memberB);
        }

        //如果预加载的数据，比参数传入个个数还要少，说明整个榜单的数据都全量数据load出来了，就没必要再补数据了
        boolean alreadyLoadAllData = attr.getTopPkRankShowPreLoad() > preLoadRanks.size();
        if (!alreadyLoadAllData && !CollectionUtils.isEmpty(memberBNotInTop)) {
            log.info("load memberB score");
            //查询pk另一半的数据
            List<ActorQueryItem> para = buildActorQueryItem(attr, timeCode, memberBNotInTop);
            List<ActorInfoItem> actorInfoItems = hdztRankingThriftClient.queryActorRankingInfo(attr.getActId(), para);
            Map<String, ActorInfoItem> memberBRank = actorInfoItems.stream().collect(Collectors.toMap(ActorInfoItem::getActorId, p -> p));
            for (PkPariItem pkPariItem : result) {
                String memberB = pkPariItem.getItem2().getKey();
                if (pkPariItem.getItem2().getValue() == 0 && memberBRank.containsKey(memberB)) {
                    pkPariItem.getItem2().setValue(memberBRank.get(memberB).getScore());
                    pkPariItem.getItem2().setRank(memberBRank.get(memberB).getRank());
                }
            }
        }


        return result;
    }

    private List<PkPariItem> queryAnchorPkItems(AnchorHourPkComponentAttr attr, String timeCode, List<String> members) {
        List<PkPariItem> result = Lists.newArrayList();

        Set<String> allMembers = Sets.newHashSet(members);

        String redisCode = getRedisGroupCode(attr.getActId());
        String pkInfoKey = buildPkInfoKey(attr, timeCode);
        List<Object> pkPair = actRedisDao.hmGet(redisCode, pkInfoKey, MyListUtils.toObjectList(members));
        List<String> memberB = pkPair.stream().filter(Objects::nonNull).map(Convert::toString).toList();
        allMembers.addAll(memberB);

        List<ActorQueryItem> para = buildActorQueryItem(attr, timeCode, allMembers);
        List<ActorInfoItem> actorInfoItems = hdztRankingThriftClient.queryActorRankingInfo(attr.getActId(), para);
        Map<String, ActorInfoItem> memberRank = actorInfoItems.stream().collect(Collectors.toMap(ActorInfoItem::getActorId, p -> p));

        for (int i = 0; i < members.size(); i++) {
            String memberA = members.get(i);
            Object pkMemberBObj = pkPair.get(i);
            if (pkMemberBObj == null) {
                continue;
            }

            String pkMemberB = Convert.toString(pkMemberBObj);

            PkPariItem pkPariItem = new PkPariItem();

            PkListItem item1 = new PkListItem();
            item1.setKey(memberA);
            if (memberRank.containsKey(memberA)) {
                item1.setValue(Math.max(memberRank.get(memberA).getScore(), 0));
                item1.setRank(memberRank.get(memberA).getRank());
            }

            PkListItem item2 = new PkListItem();
            item2.setKey(pkMemberB);
            if (memberRank.containsKey(pkMemberB)) {
                item2.setValue(Math.max(memberRank.get(pkMemberB).getScore(), 0));
                item2.setRank(memberRank.get(pkMemberB).getRank());
            }

            pkPariItem.getItemMembers().add(item1.getKey());
            pkPariItem.getItemMembers().add(item2.getKey());

            pkPariItem.setItem1(item1);
            pkPariItem.setItem2(item2);

            result.add(pkPariItem);
        }
        return result;
    }

    private void fillPkPariItemData(List<PkPariItem> result, Map<Long, UserInfoVo> userInfoVoMap) {

        for (PkPariItem pkPariItem : result) {

            long totalScore = pkPariItem.getItem1().getValue() + pkPariItem.getItem2().getValue();

            long rank1 = pkPariItem.getItem1().getRank();
            if (rank1 < 0) {
                rank1 = Integer.MAX_VALUE;
            }
            long rank2 = pkPariItem.getItem2().getRank();
            if (rank2 < 0) {
                rank2 = Integer.MAX_VALUE;
            }
            if (rank1 < rank2) {
                pkPariItem.setWinnerKey(pkPariItem.getItem1().getKey());
            } else {
                pkPariItem.setWinnerKey(pkPariItem.getItem2().getKey());
            }

            ChannelInfoVo memberAChannel = onlineChannelService.getChannelInfoVo(Convert.toLong(pkPariItem.getItem1().getKey()));
            if (memberAChannel != null) {
                pkPariItem.getItem1().setSid(memberAChannel.getSid());
                pkPariItem.getItem1().setSsid(memberAChannel.getSsid());
            }

            ChannelInfoVo memberBChannel = onlineChannelService.getChannelInfoVo(Convert.toLong(pkPariItem.getItem2().getKey()));
            if (memberBChannel != null) {
                pkPariItem.getItem2().setSid(memberBChannel.getSid());
                pkPariItem.getItem2().setSsid(memberBChannel.getSsid());
            }

            Long item1Uid = Convert.toLong(pkPariItem.getItem1().getKey());
            if (userInfoVoMap.containsKey(item1Uid)) {
                pkPariItem.getItem1().setAvatarInfo(userInfoVoMap.get(item1Uid).getAvatarUrl());
            }

            Long item2Uid = Convert.toLong(pkPariItem.getItem2().getKey());
            if (userInfoVoMap.containsKey(item2Uid)) {
                pkPariItem.getItem2().setAvatarInfo(userInfoVoMap.get(item2Uid).getAvatarUrl());
            }

            pkPariItem.setTotalScore(totalScore);

            pkPariItem.getItemMembers().add(pkPariItem.getItem1().getKey());
            pkPariItem.getItemMembers().add(pkPariItem.getItem2().getKey());

            //都是0分，双输
            if (pkPariItem.getTotalScore() == 0) {
                pkPariItem.setWinnerKey("");
            }
        }
    }

    private List<ActorQueryItem> buildActorQueryItem(AnchorHourPkComponentAttr attr, String timeCode, Set<String> memberIds) {
        List<ActorQueryItem> result = Lists.newArrayList();
        for (String memberId : memberIds) {
            ActorQueryItem queryItem = new ActorQueryItem();
            queryItem.setActorId(memberId);
            queryItem.setRankingId(attr.getPkRankId());
            queryItem.setPhaseId(attr.getPkRankPhaseId());
            queryItem.setDateStr(timeCode);
            queryItem.setWithStatus(false);
            result.add(queryItem);
        }

        return result;
    }

    private int calPkState(AnchorHourPkComponentAttr attr, Date now, String queryTimeCode) {
        String nowTimeCode = DateUtil.format(now, DateUtil.PATTERN_TYPE7);
        boolean queryTodayData = nowTimeCode.equals(queryTimeCode);
        if (queryTodayData && !pkHourBegin(attr, now)) {
            return PkState.PK_NOT_BEGIN;
        } else if (queryTodayData && !pkHourSecondsBegin(attr, now)) {
            return PkState.PK_MATCH_ING;
        } else {
            String tagKey = buildPkSettleTagKey(attr);
            String redisCode = getRedisGroupCode(attr.getActId());
            String settle = actRedisDao.hget(redisCode, tagKey, queryTimeCode);
            return StringUtil.isNotBlank(settle) ? PkState.PK_CLOSE : PkState.PK_ING;
        }
    }

    /**
     * 获取pk匹配中以及pk中的倒计时
     */
    private long calPkLeftSeconds(AnchorHourPkComponentAttr attr, Date now, int pkState) {
        if (PkState.PK_CLOSE == pkState || PkState.PK_NOT_BEGIN == pkState) {
            return 0;
        }
        if (PkState.PK_MATCH_ING == pkState) {
            long beginPkTime = (DateUtil.getHourBeginTime(now).getTime() / 1000 + attr.getPkHourStartTime());
            long beginPkLeftSeconds = beginPkTime - now.getTime() / 1000;
            return Math.max(0, beginPkLeftSeconds);
        }
        if (PkState.PK_ING == pkState) {
            long pkEndLeftSeconds = (DateUtil.getHourEndTime(now).getTime() - now.getTime()) / 1000;
            return Math.max(0, pkEndLeftSeconds);
        }

        return 0;
    }


    /**
     * 初始化每小时的pk对阵信息
     */
    public void initPkData(AnchorHourPkComponentAttr attr, Date now) {
        long actId = attr.getActId();
        String redisCode = getRedisGroupCode(actId);
        String pkTagKey = buildPkInitTagKey(attr);
        String timeCode = DateUtil.format(now, DateUtil.PATTERN_TYPE7);
        String dayCode = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        String alreadyInit = actRedisDao.hget(redisCode, pkTagKey, timeCode);

        if (!pkTimeBegin(attr, now)) {
            return;
        }

        if (StringUtil.isNotBlank(alreadyInit)) {
            return;
        }

        List<PkAnchor> anchors = queryPkAnchor(timeCode, attr);
        //不可能整个模板没人在线
        if (CollectionUtils.isEmpty(anchors)) {
            throw new RuntimeException("initPkData anchors is empty");
        }
        Map<Long, Set<Long>> alreadyPkMap = queryDayAlreadyPkMap(attr, redisCode, dayCode);

        Clock clock = new Clock();
        List<PkInfo> pkInfos = calPkResult(attr, anchors, alreadyPkMap);
        byte[] pkInfoKey = buildPkInfoKey(attr, timeCode).getBytes();
        byte[] pkInfoDayKey = buildPkDayInfoKey(attr, dayCode).getBytes();
        //测试环境测试，3000数据92ms
        actRedisDao.getRedisTemplate(redisCode).executePipelined((RedisConnection connection) -> {
            //pk关系
            for (PkInfo pkInfo : pkInfos) {
                connection.hSetNX(pkInfoKey, Convert.toString(pkInfo.getUid1()).getBytes(), Convert.toString(pkInfo.getUid2()).getBytes());
                connection.hSetNX(pkInfoKey, Convert.toString(pkInfo.getUid2()).getBytes(), Convert.toString(pkInfo.getUid1()).getBytes());

                String pkContent = pkInfo.getUid1() >= pkInfo.getUid2() ? pkInfo.getUid1() + "_" + pkInfo.getUid2() : pkInfo.getUid2() + "_" + pkInfo.getUid1();
                connection.sAdd(pkInfoDayKey, pkContent.getBytes());
            }
            //pk初始化标记
            connection.hSet(pkTagKey.getBytes(), timeCode.getBytes(), Convert.toString(now.getTime()).getBytes());
            return null;
        });
        log.info("init PkData success,timeCode:{},size:{},clock:{}", timeCode, pkInfos.size(), clock.tag());

        //通知挂件更新
        broActLayerService.beginBroLayerInfo(actId, Template.skillcard);
    }


    private Map<Long, Set<Long>> queryDayAlreadyPkMap(AnchorHourPkComponentAttr attr, String redisCode, String dayCode) {
        Map<Long, Set<Long>> alreadyPkMap = Maps.newHashMap();
        String key = buildPkDayInfoKey(attr, dayCode);
        Clock clock = new Clock();
        Set<String> allPkMembers = actRedisDao.sMembers(redisCode, key);
        log.info("queryDayAlreadyPkMap dayCode:{},size:{},clock:{}", dayCode, allPkMembers.size(), clock.tag());
        for (String pkMember : allPkMembers) {
            if (StringUtil.isBlank(pkMember)) {
                continue;
            }
            String[] uidArray = pkMember.split("_");
            long uid1 = Convert.toLong(uidArray[0]);
            long uid2 = Convert.toLong(uidArray[1]);

            Set<Long> uid1Pairs = alreadyPkMap.getOrDefault(uid1, Sets.newHashSet());
            uid1Pairs.add(uid2);
            alreadyPkMap.put(uid1, uid1Pairs);

            Set<Long> uid2Pairs = alreadyPkMap.getOrDefault(uid2, Sets.newHashSet());
            uid2Pairs.add(uid1);
            alreadyPkMap.put(uid2, uid2Pairs);
        }


        return alreadyPkMap;
    }

    /**
     * 获取需要参与pk的主持信息
     */
    private List<PkAnchor> queryPkAnchor(String timeCode, AnchorHourPkComponentAttr attr) {
        Clock clock = new Clock();
        List<PkAnchor> result = Lists.newArrayList();
        List<OnlineChannelInfo> onlineChannelInfos = onlineChannelService.queryOnlineChannelInfoNoCache(com.yy.thrift.broadcast.Template.SkillCard);

        if (attr.isFilterBlackRoomList()) {
            Set<Long> ssid = onlineChannelInfos.stream().map(OnlineChannelInfo::getSsid).collect(Collectors.toSet());
            List<RoomInfo> roomInfos = zhuiwanRoomInfoClient.listRoomInfoBySsid(Lists.newArrayList(ssid));

            List<ComponentWhitelist> blackListRoom = whitelistComponent.queryAllMemberList(attr.getActId(), 0);
            Set<Integer> blackRoom = blackListRoom.stream().map(p -> Convert.toInt(p.getMember(), 0)).collect(Collectors.toSet());
            Set<Long> blackSsid = roomInfos
                    .stream()
                    .filter(p -> blackRoom.contains(p.getRoomId()))
                    .map(RoomInfo::getSsid).collect(Collectors.toSet());
            long oldSize = onlineChannelInfos.size();
            onlineChannelInfos = onlineChannelInfos.stream().filter(p -> !blackSsid.contains(p.getSsid())).toList();
            log.info("queryPkAnchor onlineChannelInfos,timeCode:{},oldSize:{},blackListSize:{},newSize:{},clock:{}", timeCode, oldSize, blackListRoom.size(), onlineChannelInfos.size(), clock.tag());
        }

        Set<Long> allAnchorUid = onlineChannelInfos.stream().map(OnlineChannelInfo::getEffectAnchorId).flatMap(List::stream).collect(Collectors.toSet());

        List<ComponentWhitelist> whitelists = whitelistComponent.queryAllMemberList(attr.getActId(), attr.getPkAnchorScoreWhiteListIndex());
        Map<String, String> scoreMap = whitelists.stream()
                .collect(Collectors.toMap(ComponentWhitelist::getMember, p -> p.getConfigValue() == null ? "" : p.getConfigValue()));
        Map<Long, Long> anchorFamily = turnoverFamilyThriftClient.batchQueryContractFamilyIds(Lists.newArrayList(allAnchorUid), QUERY_FAMILY_BATCH_SIZE);
        BigDecimal randomFactor = new BigDecimal(attr.getPkAnchorScoreRandomFactor());
        Random random = new Random();
        for (Long uid : allAnchorUid) {
            if (!anchorFamily.containsKey(uid)) {
                log.error("queryPkAnchor, anchor not sign,uid:{}", uid);
                continue;
            }

            PkAnchor pkAnchor = new PkAnchor();
            pkAnchor.setFamily(anchorFamily.get(uid));
            pkAnchor.setUid(uid);
            String member = Convert.toString(uid);
            if (scoreMap.containsKey(member)) {
                long score = Convert.toLong(scoreMap.get(member), 0L);
                int addMax = randomFactor.multiply(new BigDecimal(score)).intValue();
                long addScore = addMax > 0 ? random.nextInt(addMax) : addMax;
                long newScore = score + addScore;
                pkAnchor.setScore(newScore);
                log.info("build anchorScore,timeCode:{},uid:{},configScore:{},newScore:{}", timeCode, member, score, newScore);
            }

            result.add(pkAnchor);
        }

        log.info("queryPkAnchor done,sourceAnchorSize:{},familyAnchorSize:{},memberScoreSize:{},pkAnchorResultSize:{},clock:{}"
                , allAnchorUid.size(), anchorFamily.keySet().size(), whitelists.size(), result.size(), clock.tag());
        return result;
    }

    private List<PkInfo> calPkResult(AnchorHourPkComponentAttr attr, List<PkAnchor> anchors, Map<Long, Set<Long>> alreadyPkMap) {
        List<PkInfo> result = Lists.newArrayList();

        boolean useTestUid = SysEvHelper.isDev() || commonService.isGrey(attr.getActId());
        if (useTestUid && !CollectionUtils.isEmpty(attr.getPkTestUid())) {
            log.warn("calPkResult use test uid");
            anchors = anchors.stream().filter(p -> attr.getPkTestUid().contains(p.getUid())).collect(Collectors.toList());
        } else {
            log.info("calPkResult begin uid size:{}", anchors.size());
        }

        anchors = anchors.stream().sorted(Comparator.comparing(PkAnchor::getScore).reversed()).collect(Collectors.toList());

        for (int i = 0; i < anchors.size(); i++) {
            PkAnchor pkAnchorA = anchors.get(i);
            if (pkAnchorA.isRemove()) {
                continue;
            }
            PkAnchor pkAnchorB = findPkTarget(anchors, alreadyPkMap, pkAnchorA, true, true);
            if (pkAnchorB == null) {
                pkAnchorB = findPkTarget(anchors, alreadyPkMap, pkAnchorA, false, true);
            }
            if (pkAnchorB == null) {
                pkAnchorB = findPkTarget(anchors, alreadyPkMap, pkAnchorA, true, false);
            }
            if (pkAnchorB == null) {
                pkAnchorB = findPkTarget(anchors, alreadyPkMap, pkAnchorA, false, false);
            }
            if (pkAnchorB == null) {
                pkAnchorB = getDefaultPkAnchor(attr);
            }

            PkInfo pkResult = new PkInfo();
            pkResult.setUid1(pkAnchorA.getUid());
            pkResult.setUid2(pkAnchorB.getUid());
            result.add(pkResult);

            pkAnchorA.setRemove(true);
            pkAnchorB.setRemove(true);
        }
        return result;
    }

    private PkAnchor findPkTarget(List<PkAnchor> anchors, Map<Long, Set<Long>> alreadyPkMap, PkAnchor pkAnchorA, boolean diffFamily, boolean diffPk) {
        for (PkAnchor pkAnchor : anchors) {
            if (pkAnchor.getUid() == pkAnchorA.getUid() || pkAnchor.isRemove()) {
                continue;
            }
            if (diffFamily && pkAnchor.getFamily() == pkAnchorA.getFamily()) {
                continue;
            }
            if (diffPk && alreadyPkMap.containsKey(pkAnchorA.getUid()) && alreadyPkMap.get(pkAnchorA.getUid()).contains(pkAnchor.getUid())) {
                continue;
            }
            return pkAnchor;
        }
        return null;
    }

    /**
     * 如果主播有单数，使用兜底的产品提供的主播账号来pk
     *
     * @return
     */
    private PkAnchor getDefaultPkAnchor(AnchorHourPkComponentAttr attr) {
        PkAnchor pkAnchor = new PkAnchor();
        pkAnchor.setUid(attr.getPkAnchorStandbyUid());
        return pkAnchor;
    }

    /**
     * 是否到了pk开始时间
     */
    private boolean pkTimeBegin(AnchorHourPkComponentAttr attr, Date now) {
        return pkHourBegin(attr, now) && pkHourSecondsBegin(attr, now);

    }

    private boolean pkHourBegin(AnchorHourPkComponentAttr attr, Date now) {
        int dayHour = DateUtil.getHours(now);
        return dayHour >= attr.getPkDayStartTime();
    }

    private boolean pkHourSecondsBegin(AnchorHourPkComponentAttr attr, Date now) {
        long hourSeconds = DateUtil.getHourTotalSeconds(now);
        return hourSeconds >= attr.getPkHourStartTime();
    }

    private boolean inPkShowResultTime(AnchorHourPkComponentAttr attr, Date now) {
        long hourSeconds = DateUtil.getHourTotalSeconds(now);
        int dayHour = DateUtil.getHours(now);
        return hourSeconds < attr.getPkShowResultSeconds() && (dayHour > attr.getPkDayStartTime() || dayHour == 0);
    }

    /**
     * 结算每小时的pk对阵信息
     */
    public void settlePkData(AnchorHourPkComponentAttr attr, Date now) {
        long hourSeconds = DateUtil.getHourTotalSeconds(now);
        if (hourSeconds < attr.getPkHourSettleDelaySeconds()) {
            return;
        }
        String time = DateUtil.format(now);
        String redisCode = getRedisGroupCode(attr.getActId());
        Date preHourHour = DateUtil.addHours(now, -1);
        String settleTimeCode = DateUtil.format(preHourHour, DateUtil.PATTERN_TYPE7);

        String settleTagKey = buildPkSettleTagKey(attr);
        String settle = actRedisDao.hget(redisCode, settleTagKey, settleTimeCode);
        if (StringUtil.isNotBlank(settle)) {
            return;
        }


        String pkInfoKey = buildPkInfoKey(attr, settleTimeCode);
        Map<Object, Object> pkInfo = actRedisDao.hGetAll(redisCode, pkInfoKey);
        Clock clock = new Clock();
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getPkRankId(), attr.getPkRankPhaseId(), settleTimeCode, QUERY_PK_RANK_COUNT, Maps.newHashMap());
        log.info("queryRanking actId:{},rankId:{},settleTimeCode:{}, ranks:{},clock:{}", attr.getActId(), attr.getPkRankId(), settleTimeCode, ranks.size(), clock.tag());
        List<PkResult> pkResults = calPkResult(pkInfo, ranks);
        String awardSourceSeq = "settleHourRank:" + settleTimeCode;

        //如果没有pk场次的时间段，这里数据为空，不影响
        for (PkResult pkResult : pkResults) {
            if (pkResult.getWinnerScore() == 0) {
                log.warn("deal pk result skip,pkResult:{}", JSON.toJSONString(pkResult));
                continue;
            }
            AwardAttrConfig awardConfig = calAwardAttrConfig(attr, pkResult.getWinnerUid(), pkResult.getWinnerScore());
            log.info("settlePkData user begin,timeCode:{},uid:{},score:{},awardConfig:{}", settleTimeCode, pkResult.getWinnerUid(), pkResult.getWinnerScore(), JSON.toJSONString(awardConfig));
            AwardPackageAttrConfig packageConfig = null;
            String awardUserSeq = makeKey(attr, awardSourceSeq + ":user:" + pkResult.getWinnerUid());
            String awardUserSeqMd5 = MD5SHAUtil.getMD5(awardUserSeq);
            //奖池不足时发奖
            if (awardConfig.getTAwardPkgId() > 0) {
                Map<Long, Integer> packageIdAmount = ImmutableMap.of(awardConfig.getTAwardPkgId(), awardConfig.getNum());
                hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.GAME_ECOLOGY.getValue(), pkResult.getWinnerUid(), awardConfig.getTAwardTskId(), ImmutableMap.of(awardConfig.getTAwardTskId(), packageIdAmount), awardUserSeqMd5, Maps.newHashMap());

            } else {
                BatchLotteryResult result = hdztAwardServiceClient.doBatchLottery(awardUserSeqMd5, BusiId.GAME_ECOLOGY.getValue(), pkResult.getWinnerUid(), awardConfig.getTAwardTskId(), Const.ONE
                        , Maps.newHashMap()
                        , ImmutableMap.of(LotteryExtParaName.NOT_FLOW_CONTROL, "1", LotteryExtParaName.LOAD_RECORD_SEQ_DUP, "1")
                        , Const.TOW);
                boolean lotteryError = result == null || (result.getCode() != 0 && result.getCode() != LotteryException.E_LOTTERY_COMPLETED);
                if (lotteryError) {
                    log.error("doBatchLottery error, player:{}, seq:{}, result:{}", pkResult.getWinnerUid(), awardUserSeqMd5, result);
                    throw new RuntimeException("doBatchLottery error");
                }
                log.info("doBatchLottery result,player:{},result:{}", pkResult.getWinnerUid(), result);
                if (CollectionUtils.isEmpty(result.getRecordPackages())) {
                    throw new RuntimeException("doBatchLottery return empty");
                }

                //扣除奖池
                for (Long recordId : result.getRecordPackages().keySet()) {
                    long recordPackageId = result.getRecordPackages().get(recordId);
                    long taskId = awardConfig.getTAwardTskId();
                    if (MapUtils.isNotEmpty(result.getExtData())) {
                        //失败重放的时候，分数会变，导致taskid不一样？
                        long oldTaskId = Convert.toLong(result.getExtData().get("taskId"), 0);
                        if (oldTaskId != awardConfig.getTAwardTskId()) {
                            log.warn("reDoBatchLottery score change? current request taskId:{},seqTaskId:{},uid:{},timeCode:{},seq:{},score:{}"
                                    , awardConfig.getTAwardTskId(), oldTaskId, pkResult.getWinnerUid(), settleTimeCode, awardUserSeqMd5, pkResult.getWinnerScore());
                        }
                        if (oldTaskId > 0) {
                            taskId = oldTaskId;
                        }
                    }
                    //本次需求是单抽，只有1条记录
                    packageConfig = queryPackageInfo(taskId, recordPackageId);
                    if (packageConfig != null && packageConfig.getAwardAmount() > 0) {
                        String reduceSeq = makeKey(attr, "seq:reduce_pool:" + awardSourceSeq + ":" + pkResult.getWinnerUid());
                        String consumeKey = buildTotalAwardConsumeKey(attr);
                        //List<Long> addResult = actRedisDao.incrValueWithSeq(redisCode, reduceSeq, consumeKey, packageConfig.getAwardAmount(), DateUtil.ONE_WEEK_SECONDS);
                        List<Long> addResult;
                        //TODO 如果次组件要重新启用，则需要改造此处
                        if (true) {
                            throw new RuntimeException("incrValueWithSeq.lua,lua forbid");
                        }
                        log.info("settleHourRank award pool incrValueWithSeq  actId:{},uid:{},reduceSeq:{},addResult:{}", attr.getActId(), pkResult.getWinnerUid(), reduceSeq, JSON.toJSONString(addResult));
                    }
                }
            }
            AwardResult awardResult = toAwardResult(awardConfig, packageConfig);

            //保存个人pk记录
            String winnerKey = buildUserPkRecordKey(attr, pkResult.getWinnerUid());
            PkResultRecord winResultRecord = new PkResultRecord();
            winResultRecord.setUnit(awardResult.getUnit());
            winResultRecord.setAmount(awardResult.getNum());
            winResultRecord.setAwardName(awardResult.getAwardName());
            winResultRecord.setGiftIcon(awardResult.getAwardIcon());
            winResultRecord.setScore1(pkResult.getWinnerScore());
            winResultRecord.setScore2(pkResult.getFailedScore());
            winResultRecord.setTimeCode(settleTimeCode);
            winResultRecord.setIsWin(1);
            String winRecordSeq = makeKey(attr, "seq:add_win_record:" + settleTimeCode + ":" + pkResult.getWinnerUid());
            String winResultRecordData = JSON.toJSONString(winResultRecord);
            //actRedisDao.lPushWithSeq(redisCode, winRecordSeq, winnerKey, winResultRecordData, DateUtil.ONE_WEEK_SECONDS);
            //TODO 如果次组件要重新启用，则需要改造此处
            if (true) {
                throw new RuntimeException("lPushWithSeq,lua forbid");
            }

            String settleResultKey = buildUserPkResult(attr, settleTimeCode);
            actRedisDao.hset(redisCode, settleResultKey, Convert.toString(pkResult.getWinnerUid()), winResultRecordData);

            String failedKey = buildUserPkRecordKey(attr, pkResult.getFailedUid());
            PkResultRecord failedResultRecord = new PkResultRecord();
            failedResultRecord.setScore1(pkResult.getFailedScore());
            failedResultRecord.setScore2(pkResult.getWinnerScore());
            failedResultRecord.setTimeCode(settleTimeCode);
            failedResultRecord.setIsWin(0);
            String failedRecordSeq = makeKey(attr, "seq:add_failed_record:" + settleTimeCode + ":" + pkResult.getWinnerUid());
            String failedRecordData = JSON.toJSONString(failedResultRecord);
            //actRedisDao.lPushWithSeq(redisCode, failedRecordSeq, failedKey, failedRecordData, DateUtil.ONE_WEEK_SECONDS);
            //TODO 如果次组件要重新启用，则需要改造此处
            if (true) {
                throw new RuntimeException("lPushWithSeq,lua forbid");
            }

            actRedisDao.hset(redisCode, settleResultKey, Convert.toString(pkResult.getFailedUid()), failedRecordData);

            //单播通知
            threadPoolManager.get(Const.GENERAL_POOL).execute(() -> noticePkResult(attr, awardSourceSeq, pkResult, awardResult));

            log.info("settlePkData user done,timeCode:{},uid:{},score:{},awardConfig:{}", settleTimeCode, pkResult.getWinnerUid(), pkResult.getWinnerScore(), JSON.toJSONString(awardConfig));

        }

        //结算标记（打上这个标记意味着结算成功）
        actRedisDao.hset(redisCode, settleTagKey, settleTimeCode, DateUtil.format(new Date()));


        //通知挂件更新（放在最后面，失败不重试）
        broActLayerService.beginBroLayerInfo(attr.getActId(), Template.skillcard);

    }

    public AwardPackageAttrConfig queryPackageInfo(long taskId, long packageId) {
        AwardPackageAttrConfig awardPackageAttrConfig = new AwardPackageAttrConfig();
        try {
            Map<Long, AwardModelInfo> awardModelInfoMap = hdztAwardServiceClient.queryAwardTasks(taskId);
            AwardModelInfo awardModelInfo = awardModelInfoMap.get(packageId);
            if (awardModelInfo == null) {
                throw new RuntimeException("not found taskId:" + taskId + ",packageId:" + packageId);
            }
            awardPackageAttrConfig.setTAwardPkgId(packageId);
            awardPackageAttrConfig.setAwardName(awardModelInfo.getPackageName());
            awardPackageAttrConfig.setAwardIcon(awardModelInfo.getPackageImage());
            awardPackageAttrConfig.setUnit(awardModelInfo.getUnit());
            if (StringUtil.isNotBlank(awardModelInfo.getViewExtjson())) {
                JSONObject jsonObject = JSON.parseObject(awardModelInfo.getViewExtjson());
                awardPackageAttrConfig.setNum(jsonObject.getIntValue("num"));
                awardPackageAttrConfig.setAwardAmount(jsonObject.getLongValue("awardAmount"));
            }

        } catch (Exception e) {
            log.error("queryPackageInfo error,taskId:{},packageId:{},e:{}", taskId, packageId, e.getMessage(), e);
            throw new RuntimeException("queryAwardTasks error");
        }

        return awardPackageAttrConfig;
    }


    private void noticePkResult(AnchorHourPkComponentAttr attr, String awardSourceSeq, PkResult pkResult, AwardResult awardResult) {
        String noticeSeq = makeKey(attr, "seq:notice:" + awardSourceSeq + ":" + pkResult.getWinnerUid());

        RetryTool.withRetryCheck(attr.getActId(), noticeSeq, DateUtil.ONE_DAY_SECONDS, () -> {

            Set<Long> uids = Sets.newHashSet(pkResult.getWinnerUid(), pkResult.getFailedUid());
            Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
            Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids), multiNickUsers, true, Template.all.getCode());


            long winnerUid = pkResult.getWinnerUid();
            Map<String, Object> winnNotice = Maps.newHashMap();
            winnNotice.put("uid", winnerUid);
            winnNotice.put("win", 1);
            winnNotice.put("giftCount", awardResult.getNum());
            winnNotice.put("giftName", awardResult.getAwardName());
            winnNotice.put("giftUnit", awardResult.getUnit());
            winnNotice.put("giftIcon", awardResult.getAwardIcon());
            winnNotice.put("nickExtUsers", multiNickUsers);
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), "5116_pk_result", JSON.toJSONString(winnNotice), "", winnerUid);

            long failedUid = pkResult.getFailedUid();
            Map<String, Object> failedNotice = Maps.newHashMap();
            failedNotice.put("uid", failedUid);
            failedNotice.put("win", 0);
            failedNotice.put("nickExtUsers", multiNickUsers);
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), "5116_pk_result", JSON.toJSONString(failedNotice), "", failedUid);

        });
    }

    private AwardResult toAwardResult(AwardAttrConfig awardConfig, AwardPackageAttrConfig packageConfig) {
        AwardResult awardResult = new AwardResult();

        if (packageConfig == null) {
            awardResult.setNum(awardConfig.getNum());
            awardResult.setAwardName(awardConfig.getAwardName());
            awardResult.setAwardIcon(awardConfig.getAwardIcon());
            awardResult.setUnit(awardConfig.getUnit());
        } else {
            awardResult.setNum(packageConfig.getNum());
            awardResult.setAwardName(packageConfig.getAwardName());
            awardResult.setAwardIcon(packageConfig.getAwardIcon());
            awardResult.setUnit(packageConfig.getUnit());
        }

        return awardResult;
    }


    private List<PkResult> calPkResult(Map<Object, Object> pkInfo, List<Rank> ranks) {
        List<PkResult> pkResults = Lists.newArrayList();
        Set<Long> alreadySettle = Sets.newHashSet();
        for (Object obj : pkInfo.keySet()) {
            long uid1 = Convert.toLong(obj);
            long uid2 = Convert.toLong(pkInfo.get(obj));
            //1个uid中存在2个 pkInfo中， a vs b , b vs a
            if (alreadySettle.contains(uid1) || alreadySettle.contains(uid2)) {
                continue;
            }


            PkResult pkResult = new PkResult();

            long score1 = 0;
            long rank1 = Integer.MAX_VALUE;
            Optional<Rank> rankItem1 = ranks.stream().filter(p -> p.getMember().equals(Convert.toString(uid1))).findFirst();
            if (rankItem1.isPresent()) {
                score1 = rankItem1.get().getScore();
                rank1 = rankItem1.get().getRank();
            } else {
                log.info("settlePkData get score empty,member:{}", uid1);
            }


            long score2 = 0;
            long rank2 = Integer.MAX_VALUE;
            Optional<Rank> rankItem2 = ranks.stream().filter(p -> p.getMember().equals(Convert.toString(uid2))).findFirst();
            if (rankItem2.isPresent()) {
                score2 = rankItem2.get().getScore();
                rank2 = rankItem2.get().getRank();
            } else {
                log.info("settlePkData get score empty,member:{}", uid2);
            }


            if (rank1 < rank2) {
                pkResult.setWinnerUid(uid1);
                pkResult.setWinnerScore(score1);
                pkResult.setFailedUid(uid2);
                pkResult.setFailedScore(score2);
            } else {
                pkResult.setWinnerUid(uid2);
                pkResult.setWinnerScore(score2);
                pkResult.setFailedUid(uid1);
                pkResult.setFailedScore(score1);
            }

            alreadySettle.add(uid1);
            alreadySettle.add(uid2);
            pkResults.add(pkResult);
        }

        return pkResults;
    }

    private AwardAttrConfig calAwardAttrConfig(AnchorHourPkComponentAttr attr, long uid, long score) {
        List<Long> scoreConfigs = attr.getHourRankAward().keySet().stream().sorted().toList();
        long targetScoreConfig = 0;
        for (long scoreConfig : scoreConfigs) {
            if (score >= scoreConfig) {
                targetScoreConfig = scoreConfig;
            }
        }
        AwardAttrConfig awardAttrConfig = attr.getHourRankAward().get(targetScoreConfig);
        boolean awardPoolOut = getAwardPoolLeft(attr) <= 0;
        //要有价值的抽奖奖池才会转成按照奖池不足发放
        if (awardPoolOut && awardAttrConfig.getAwardAmount() > 0) {
            log.info("calAwardAttrConfig awardPoolOut,uid:{},score:{},awardPoolOut:{}", uid, score, awardPoolOut);
            return attr.getHourRankAward().get(-1L);
        }
        return awardAttrConfig;
    }

    private long getAwardPoolLeft(AnchorHourPkComponentAttr attr) {
        long send = getAwardPoolConsume(attr);
        return Math.max(attr.getTotalPool() - send, 0);
    }

    private long getAwardPoolConsume(AnchorHourPkComponentAttr attr) {
        String redisCode = getRedisGroupCode(attr.getActId());
        String totalAwardKey = buildTotalAwardConsumeKey(attr);
        return Convert.toLong(actRedisDao.get(redisCode, totalAwardKey), 0);
    }

    public boolean pushUpdateRankingBeforeInit(RankingScoreChanged event, AnchorHourPkComponentAttr attr) {
        //本小时pk数据没初始化好，要先hold住数据不上报
        //等初始化好后再上报。因为没匹配上pk的数据不能进入pk榜，否则下游无法实现准确取出参与pk top N数据
        Date occurTime = DateUtil.getDate(event.getOccurTime());
        String redisCode = getRedisGroupCode(attr.getActId());
        String timeCode = DateUtil.format(occurTime, DateUtil.PATTERN_TYPE7);
        String pkInitTagKey = buildPkInitTagKey(attr);
        String init = actRedisDao.hget(redisCode, pkInitTagKey, timeCode);
        if (StringUtil.isBlank(init)) {
            String key = buildUpdateRankingQueueKey(attr);
            actRedisDao.lpush(redisCode, key, JSON.toJSONString(event));
            log.info("updatePkRank pk not init,add queue,uid:{},timeCode:{},score:{}", event.getMember(), timeCode, event.getItemScore());
            return true;
        }
        return false;
    }


    public void updatePkRank(RankingScoreChanged event, AnchorHourPkComponentAttr attr) {
        Date occurTime = DateUtil.getDate(event.getOccurTime());


        String timeCode = DateUtil.format(occurTime, DateUtil.PATTERN_TYPE7);

        //没匹配上pk,不累榜
        if (!checkExistPk(attr, event.getMember(), timeCode)) {
            log.info("updatePkRank no match pk,uid:{},timeCode:{},score:{}", event.getMember(), timeCode, event.getItemScore());
            return;
        }


        String seq = makeKey(attr, event.getSeq());
        String md5Seq = "anchorPk_" + MD5SHAUtil.getMD5(seq);

        UpdateRankingRequest request = new UpdateRankingRequest();
        request.setBusiId(event.getBusiId());
        request.setActId(attr.getActId());
        request.setSeq(md5Seq);
        request.setActors(event.getActors());
        request.setItemId(attr.getUpdatePkRankItem());
        request.setCount(event.getItemCount());
        request.setScore(event.getItemScore());
        request.setTimestamp(occurTime.getTime());
        request.setExtData(Maps.newHashMap());


        boolean result = hdztRankingThriftClient
                .updateRankingWithRetry(request, Const.TOW);
        if (!result) {
            log.error("update rank error,invoke retry,update ranking done,member:{},score:{},md5Seq:{}", event.getMember(), event.getItemScore(), md5Seq);
            throw new RuntimeException("update rank error,invoke retry");
        }

        log.info("update ranking done,member:{},score:{},sourceSeq:{},seq:{},md5Seq:{}", event.getMember(), event.getItemScore(), event.getSeq(), seq, md5Seq);
    }

    /**
     * pk对阵关系初始化完成，重放更新榜单事件
     */
    public void reUpdatePkRank(AnchorHourPkComponentAttr attr, Date now) {
        String timeCode = DateUtil.format(now, DateUtil.PATTERN_TYPE7);
        String redisCode = getRedisGroupCode(attr.getActId());
        String init = actRedisDao.hget(redisCode, buildPkInitTagKey(attr), timeCode);
        if (StringUtil.isBlank(init)) {
            return;
        }
        String queueKey = buildUpdateRankingQueueKey(attr);
        String queue = actRedisDao.lpop(redisCode, queueKey);
        while (StringUtil.isNotBlank(queue)) {
            RankingScoreChanged event = JSON.parseObject(queue, RankingScoreChanged.class);
            log.info("begin reUpdatePkRank,event:{}", queue);
            updatePkRank(event, attr);

            queue = actRedisDao.lpop(redisCode, queueKey);
        }
    }

    /**
     * 触发年度祝福
     */
    private void addChatLottery(RankingScoreChanged event, AnchorHourPkComponentAttr attr) {
        ChannelChatLotteryComponentAttr chatAttr = channelChatLotteryComponent.getComponentAttr(attr.getActId(), attr.getChatLotteryIndex());
        if (chatAttr == null) {
            log.info("sendChatLottery return,not config");
            return;
        }

        long newLevelScore = HdzjHelper.findLevelScore(event.getRankScore(), attr.getChatLotteryScoreLevel());
        if (newLevelScore <= 0) {
            return;
        }

        String groupCode = redisConfigManager.getGroupCode(event.getActId());
        Date time = DateUtil.getDate(event.getOccurTime());
        String timeCode = DateUtil.format(time, DateUtil.PATTERN_TYPE7);
        //没有匹配上pk
        if (!checkExistPk(attr, event.getMember(), timeCode)) {
            log.info("addChatLottery return,not exist pk,seq:{},member:{},score:{}", event.getSeq(), event.getMember(), event.getItemScore());
            return;
        }

        // 若设置操作返回的老值大于或等于新值，则设置失败，直接返回
        String subName = HdzjHelper.getRankingScoreChangedSubKey(event, false);
        String hashKey = makeKey(attr, subName + ":CurrLevelScore");
        //long oldLevelScore = actRedisDao.hSetGrownReturnOld(groupCode, hashKey, event.getMember(), newLevelScore);
        long oldLevelScore = 0;
        if (true) {
            throw new RuntimeException("hset_grown_return_old.lua,lua forbid");
        }
        if (oldLevelScore >= newLevelScore) {
            return;
        }


        String seq = makeKey(attr, "seq:sendChatLottery:" + event.getSeq());
        long uid = Convert.toLong(event.getMember());
        ChannelChatLotteryComponent.ChatLotteryBox box = new ChannelChatLotteryComponent.ChatLotteryBox();
        box.setSeq(seq);
        box.setBabyUid(uid);
        box.setScore(newLevelScore);
        box.setBroType(BroadcastType.SUB_CHANNEL);

        //用户跳转的频道
        ChannelInfo channelInfo = resolveUserChannel(uid, event);
        if (channelInfo != null && channelInfo.getSid() > 0) {
            box.setSid(channelInfo.getSid());
            box.setSsid(channelInfo.getSsid());
        }
        log.info("addChatLotteryBox,uid:{},box:{}", uid, JSON.toJSONString(box));
        channelChatLotteryComponent.addChatLotteryBox(chatAttr, box);
    }

    private boolean checkExistPk(AnchorHourPkComponentAttr attr, String member, String timeCode) {
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String pkInfoKey = buildPkInfoKey(attr, timeCode);
        String existPk = actRedisDao.hget(groupCode, pkInfoKey, member);
        //没有匹配上pk
        if (StringUtil.isBlank(existPk)) {
            log.info("onRankingScoreChangedAddChatLottery return,not exist pk,uid:{},timeCode:{}", member, timeCode);
            return false;
        }
        return true;
    }

    @Override
    public LayerBroadcastInfo customBroadcastInTheEnd(LayerBroadcastInfo source) {
        long actId = source.getActId();
        AnchorHourPkComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return source;
        }
        List<LayerMemberItem> layerMemberItems = source.getExtMemberItem();
        if (CollectionUtils.isEmpty(layerMemberItems)) {
            return source;
        }
        List<String> anchorMembers = layerMemberItems
                .stream()
                .filter(p -> LayerItemTypeKey.CMPT_ITEM2.equals(p.getItemType()))
                .map(LayerMemberItem::getMemberId).toList();
        if (CollectionUtils.isEmpty(anchorMembers)) {
            return source;
        }
        Set<Long> anchorUids = layerMemberItems
                .stream()
                .filter(p -> LayerItemTypeKey.CMPT_ITEM2.equals(p.getItemType()))
                .map(p -> Convert.toLong(p.getMemberId(), 0))
                .filter(p -> p > 0).collect(Collectors.toSet());


        Date now = commonService.getNow(source.getActId());
        String redisCode = getRedisGroupCode(attr.getActId());
        String timeCode = DateUtil.format(now, DateUtil.PATTERN_TYPE7);

        boolean pkTimeBegin = pkTimeBegin(attr, now);
        boolean pkHourBegin = pkHourBegin(attr, now);
        boolean pkSecondsNotBegin = !pkHourSecondsBegin(attr, now);
        boolean pkShowResult = inPkShowResultTime(attr, now);
        //pk结果显示（优先级-高）
        if (pkShowResult) {
            setSettleLayerData(attr, layerMemberItems, anchorMembers, redisCode, now, timeCode);
        }
        //pk准备中（优先级-中）
        else if (pkHourBegin && pkSecondsNotBegin) {
            long leftSeconds = attr.getPkHourStartTime() - DateUtil.getHourTotalSeconds(now);
            setViewState(layerMemberItems, PKLayerViewState.PK_MATCH_ING, leftSeconds);

        }
        //pk中
        else if (pkTimeBegin) {
            Set<Long> pkMembersOutput = setPkIngData(attr, source.getActEndTime(), layerMemberItems, anchorMembers, redisCode, timeCode, now);
            anchorUids.addAll(pkMembersOutput);
        }
        //非PK时间段（优先级-低）
        else {
            setViewState(layerMemberItems, PKLayerViewState.HIDE, 0);
        }

        //用户信息、多昵称统一处理
        Map<String, Map<String, MultiNickItem>> multiNickUsers = Maps.newHashMap();
        Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(anchorUids), multiNickUsers, true, Template.unknown.getCode());
        setUserInfoData(layerMemberItems, userInfoVoMap, multiNickUsers);

        return source;
    }

    private Set<Long> setPkIngData(AnchorHourPkComponentAttr attr
            , long actEndTime
            , List<LayerMemberItem> layerMemberItems, List<String> anchorMembers, String redisCode, String timeCode, Date now) {
        Set<Long> pkMembersOutput = Sets.newHashSet();

        String initKey = buildPkInitTagKey(attr);
        String alreadyInit = actRedisDao.hget(redisCode, initKey, timeCode);
        //数据未初始化完成，显示匹配中
        if (StringUtil.isBlank(alreadyInit)) {
            long leftSeconds = attr.getPkHourStartTime() - DateUtil.getHourTotalSeconds(now);
            setViewState(layerMemberItems, PKLayerViewState.PK_MATCH_ING, leftSeconds);
            return pkMembersOutput;
        }
        List<PkPariItem> pkPariItems = queryAnchorPkItems(attr, timeCode, anchorMembers);
        for (LayerMemberItem layerMemberItem : layerMemberItems) {
            if (!LayerItemTypeKey.CMPT_ITEM2.equals(layerMemberItem.getItemType())) {
                continue;
            }
            Optional<PkPariItem> curPkPairItem = pkPariItems.stream()
                    .filter(p -> p.getItemMembers().contains(layerMemberItem.getMemberId()))
                    .findFirst();
            if (curPkPairItem.isEmpty()) {
                //没匹配上显示下一轮pk时间
                int nextHour = DateUtil.getHours(now) + 1;
                layerMemberItem.putExtInfo("nextPkTimes", nextHour >= 24 ? attr.getPkDayStartTime() : nextHour);
                String actLastHour = DateUtil.format(new Date(actEndTime), DateUtil.PATTERN_TYPE7);
                boolean isActLastHour = actLastHour.equals(timeCode);
                setViewState(layerMemberItem, isActLastHour ? PKLayerViewState.PK_NOT_MATCH_LAST_PK : PKLayerViewState.PK_NOT_MATCH);
                continue;
            }
            PkPariItem pkPariItem = curPkPairItem.get();
            boolean mySelfOnFirst = pkPariItem.getItem1().getKey().equals(layerMemberItem.getMemberId());
            PkListItem pkListItem1 = mySelfOnFirst ? pkPariItem.getItem1() : pkPariItem.getItem2();
            PkListItem pkListItem2 = mySelfOnFirst ? pkPariItem.getItem2() : pkPariItem.getItem1();


            PKInfo pkInfo = new PKInfo();
            List<PKItem> pkItems = Lists.newArrayList();
            PKItem pKItem1 = new PKItem();
            pKItem1.setMemberId(pkListItem1.getKey());
            pKItem1.setPkValue(pkListItem1.getValue());
            pkItems.add(pKItem1);

            PKItem pKItem2 = new PKItem();
            pKItem2.setMemberId(pkListItem2.getKey());
            pKItem2.setPkValue(pkListItem2.getValue());
            pkItems.add(pKItem2);

            pkInfo.setPkItems(pkItems);
            layerMemberItem.setPkInfo(pkInfo);

            long leftSeconds = calPkLeftSeconds(attr, now, PkState.PK_ING);
            pkInfo.setPkLeftSeconds(leftSeconds);
            layerMemberItem.putExtInfo("totalSeconds", DateUtil.ONE_HOUR_SECONDS - attr.getPkHourStartTime());
            layerMemberItem.setLeftSeconds(leftSeconds);

            setViewState(layerMemberItem, PKLayerViewState.PK_ING);

            pkMembersOutput.add(Convert.toLong(pkPariItem.getItem1().getKey()));
            pkMembersOutput.add(Convert.toLong(pkPariItem.getItem2().getKey()));
        }

        return pkMembersOutput;

    }

    private void setSettleLayerData(AnchorHourPkComponentAttr attr
            , List<LayerMemberItem> layerMemberItems, List<String> anchorMembers
            , String redisCode, Date now, String timeCode) {
        String settleTag = buildPkSettleTagKey(attr);
        String preTimeCode = DateUtil.format(DateUtil.addHours(now, -1), DateUtil.PATTERN_TYPE7);
        String alreadySettle = actRedisDao.hget(redisCode, settleTag, preTimeCode);
        if (StringUtil.isBlank(alreadySettle)) {
            //没结算出来，显示为结算中
            setViewState(layerMemberItems, PKLayerViewState.SETTLE, 0);
        } else {
            String pkResultKey = buildUserPkResult(attr, preTimeCode);
            List<Object> prePkResult = actRedisDao.hmGet(redisCode, pkResultKey, MyListUtils.toObjectList(anchorMembers));
            Map<String, PkResultRecord> pkResultMap = Maps.newHashMap();
            for (int i = 0; i < anchorMembers.size(); i++) {
                Object pkResult = prePkResult.get(i);
                if (pkResult == null) {
                    continue;
                }
                PkResultRecord pkResultRecord = JSON.parseObject(Convert.toString(pkResult), PkResultRecord.class);
                pkResultMap.put(Convert.toString(anchorMembers.get(i)), pkResultRecord);
            }
            for (LayerMemberItem layerMemberItem : layerMemberItems) {
                if (!LayerItemTypeKey.CMPT_ITEM2.equals(layerMemberItem.getItemType())) {
                    continue;
                }
                PkResultRecord pkResultRecord = pkResultMap.get(layerMemberItem.getMemberId());
                //本轮没匹配上的显示匹配中
                if (pkResultRecord == null) {
                    long leftSeconds = calPkLeftSeconds(attr, now, PkState.PK_MATCH_ING);
                    layerMemberItem.setLeftSeconds(leftSeconds);
                    setViewState(layerMemberItem, pkHourBegin(attr, now) ? PKLayerViewState.PK_MATCH_ING : PKLayerViewState.HIDE);
                }
                //匹配上了的显示pk结果
                else {
                    int viewState = pkResultRecord.getIsWin() == 1 ? PKLayerViewState.PK_CLOSE_WIN : PKLayerViewState.PK_CLOSE_FAILED;
                    layerMemberItem.putExtInfo("win", pkResultRecord.getIsWin());
                    if (pkResultRecord.getIsWin() == 1) {
                        layerMemberItem.putExtInfo("giftCount", pkResultRecord.getAmount());
                        layerMemberItem.putExtInfo("giftName", pkResultRecord.getAwardName());
                        layerMemberItem.putExtInfo("giftUnit", pkResultRecord.getUnit());
                        layerMemberItem.putExtInfo("giftIcon", pkResultRecord.getGiftIcon());
                    }
                    setViewState(layerMemberItem, viewState);
                }
            }
        }
    }

    private void setUserInfoData(List<LayerMemberItem> layerMemberItems, Map<Long, UserInfoVo> userInfoVoMap, Map<String, Map<String, MultiNickItem>> multiNickUsers) {
        for (LayerMemberItem layerMemberItem : layerMemberItems) {
            if (!LayerItemTypeKey.CMPT_ITEM2.equals(layerMemberItem.getItemType())) {
                continue;
            }
            Map<String, Map<String, MultiNickItem>> curItemMultiNickUsers = Maps.newHashMap();
            long uid = Convert.toLong(layerMemberItem.getMemberId(), 0);
            UserInfoVo userInfoVo = userInfoVoMap.get(uid);
            if (userInfoVo == null) {
                continue;
            }
            if (multiNickUsers.containsKey(layerMemberItem.getMemberId())) {
                curItemMultiNickUsers.put(layerMemberItem.getMemberId(), multiNickUsers.get(layerMemberItem.getMemberId()));
            }
            layerMemberItem.setLogo(userInfoVo.getAvatarUrl());
            if (layerMemberItem.getPkInfo() != null && layerMemberItem.getPkInfo().getPkItems() != null) {
                for (PKItem pkItem : layerMemberItem.getPkInfo().getPkItems()) {
                    long pkUid = Convert.toLong(pkItem.getMemberId(), 0);
                    UserInfoVo pkUserInfo = userInfoVoMap.get(pkUid);
                    if (pkUserInfo != null) {
                        pkItem.setAvatar(pkUserInfo.getAvatarUrl());
                    }
                    if (multiNickUsers.containsKey(pkItem.getMemberId())) {
                        curItemMultiNickUsers.put(pkItem.getMemberId(), multiNickUsers.get(pkItem.getMemberId()));
                    }
                }
            }

            layerMemberItem.putExtInfo("nickExtUsers", curItemMultiNickUsers);

        }
    }


    private void setViewState(List<LayerMemberItem> layerMemberItems, int viewState, long leftSeconds) {
        for (LayerMemberItem layerMemberItem : layerMemberItems) {
            if (!LayerItemTypeKey.CMPT_ITEM2.equals(layerMemberItem.getItemType())) {
                continue;
            }
            //结算中
            if (layerMemberItem.getViewStatus() == LayerViewStatus.SETTLE_900) {
                continue;
            }
            layerMemberItem.setViewStatus(viewState);
            if (leftSeconds > 0) {
                layerMemberItem.setLeftSeconds(leftSeconds);
            }
        }
    }

    private void setViewState(LayerMemberItem layerMemberItem, int viewState) {
        if (layerMemberItem.getViewStatus() == LayerViewStatus.SETTLE_900) {
            return;
        }
        layerMemberItem.setViewStatus(viewState);
    }

    private String buildPkInfoKey(AnchorHourPkComponentAttr attr, String timeCode) {
        return makeKey(attr, String.format(PK_INFO_KEY, timeCode));
    }

    private String buildPkInitTagKey(AnchorHourPkComponentAttr attr) {
        return makeKey(attr, PK_INIT_TAG);
    }

    private String buildPkSettleTagKey(AnchorHourPkComponentAttr attr) {
        return makeKey(attr, PK_SETTLE_TAG);
    }

    private String buildTotalAwardConsumeKey(AnchorHourPkComponentAttr attr) {
        return makeKey(attr, TOTAL_AWARD_CONSUME);
    }

    private String buildUserPkResult(AnchorHourPkComponentAttr attr, String timeCode) {
        return makeKey(attr, String.format(USER_PK_RESULT, timeCode));
    }

    private String buildUserPkRecordKey(AnchorHourPkComponentAttr attr, long uid) {
        return makeKey(attr, String.format(USER_PK_RESULT_RECORD, uid));
    }

    private String buildUpdateRankingQueueKey(AnchorHourPkComponentAttr attr) {
        return makeKey(attr, UPDATE_RANKING_QUEUE);
    }

    private String buildPkDayInfoKey(AnchorHourPkComponentAttr attr, String dayCode) {
        return makeKey(attr, String.format(PK_DAY_INFO_KEY, dayCode));
    }


    @Data
    static class PkAnchor {
        private long score;
        private long uid;
        private long family;

        private boolean remove;
    }

    @Data
    static class PkInfo {
        private long uid1;
        private long uid2;
    }

    @Data
    static class PkResult {
        private long winnerUid;

        private long winnerScore;

        private long failedUid;

        private long failedScore;
    }

    /**
     * 专题页pk信息查询用
     */
    @Data
    static class PkListInfo {
        private long serverTime;
        private long leftSeconds;
        private int status;
        private List<UserInfoVo> anchorList;
        private List<PkPariItem> pkList;
        private List<PkPariItem> curPkList;
        private Map<String, Map<String, MultiNickItem>> nickExtUsers;

        private long totalAwardLimit;
        private long totalAwardLeft;

    }

    @Data
    static class PkPariItem {
        private PkListItem item1;
        private PkListItem item2;
        private Set<String> itemMembers = Sets.newHashSet();
        private String winnerKey;
        private long totalScore;

    }

    @Data
    static class PkListItem {
        private String avatarInfo;
        private String key;
        private long sid;
        private long ssid;
        private long value;
        private long rank = Integer.MAX_VALUE;
    }

    @Data
    static class PkResultRecord {
        private String timeCode;
        private long score1;
        private long score2;
        private int isWin;
        private String awardName;
        private long amount;
        private String unit;
        private String giftIcon;
    }

    /**
     * 0===下轮pk乱斗将于17点开始   1===正在匹配对手  2===pk中  3===pk已结束
     */
    static class PkState {
        public static final int PK_NOT_BEGIN = 0;
        public static final int PK_MATCH_ING = 1;
        public static final int PK_ING = 2;
        public static final int PK_CLOSE = 3;
    }


    /**
     * viewStatue == -1 入口隐藏不显示; viewStatue==5116001 匹配中 ; viewStatue==5116002 pk中 ;
     * viewStatue==5116003 pk胜利;  viewStatue==5116004 pk失败; viewStatue==5116005 pk未匹配上;5116006==活动期间最后一场没匹配上
     */
    static class PKLayerViewState {
        public static final int HIDE = -1;
        public static final int SETTLE = 5116000;
        public static final int PK_MATCH_ING = 5116001;
        public static final int PK_ING = 5116002;
        public static final int PK_CLOSE_WIN = 5116003;
        public static final int PK_CLOSE_FAILED = 5116004;
        public static final int PK_NOT_MATCH = 5116005;
        public static final int PK_NOT_MATCH_LAST_PK = 5116006;

    }
}
