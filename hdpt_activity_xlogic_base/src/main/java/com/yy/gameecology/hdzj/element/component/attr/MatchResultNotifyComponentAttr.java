package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption: 赛点广播属性
 * @Date: 2021/9/26 14:39
 * @Modified:
 */
@Data
public class MatchResultNotifyComponentAttr extends ComponentAttr {
    /**
     * 逆序广播，默认是正序，按 calc_type, calc_value（名次升序）
     */
    @ComponentAttrField(labelText = "逆序广播", remark = "逆序广播，默认是正序，按 calc_type, calc_value（名次升序）")
    private boolean reversedOrder = false;

    @ComponentAttrField(labelText = "广播配置", useDialog = 1,
            subFields = {
                    @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = NotifyConfig.class)
            })
    private List<NotifyConfig> notifyConfigList;

    private long globalDelay = 0;

    /**
     * 延迟广播设置,使得不同类型广播有序播出 如主播 公会
     * <rankId,<phaseId,delay>> delay单位:毫秒
     */
    @ComponentAttrField(labelText = "延迟广播设置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "榜单ID"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "阶段ID"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "延迟时长")
            })
    private Map<Long, Map<Long, Long>> broDelayMap;

    @Data
    public static class NotifyConfig {
        @ComponentAttrField(labelText = "榜单id")
        private Long rankId;
        @ComponentAttrField(labelText = "阶段id")
        private Long phaseId;
        @ComponentAttrField(labelText = "广播模板", remark = "0=单业务广播（角色所属的模板）,400=宝贝,500=交友,600=约战,900=陪玩")
        private String broadcastType;
        @ComponentAttrField(labelText = "计算类型", remark = "0=普通榜单，1=pk胜方，2=pk负方，3=达成分值，4=贡献")
        private Integer calcType;
        @ComponentAttrField(labelText = "计算值", remark = "计算类型为3时是分值，其它是排名")
        private Integer calcValue;
        @ComponentAttrField(labelText = "贡献榜id")
        private Long contriRankId;
        @ComponentAttrField(labelText = "matchType", remark = "默认是0，使用roleType")
        private Integer matchType;
        @ComponentAttrField(labelText = "moduleType", remark = "默认是0")
        private Integer moduleType;
        @ComponentAttrField(labelText = "跳转标记", remark = "频道跳转标记,0=不跳转,非0=要跳转")
        private Integer skipFlag;
        @ComponentAttrField(labelText = "广播文本")
        private String text;
        @ComponentAttrField(labelText = "uri", remark = "榜单事件,1003=榜单开始,1004=榜单结束,1005=阶段开始,1006=阶段结束,1007=PK结算结束,1008=晋级结算结束")
        private Integer uri;
        @ComponentAttrField(labelText = "扩展信息")
        private String extJson;
    }
}
