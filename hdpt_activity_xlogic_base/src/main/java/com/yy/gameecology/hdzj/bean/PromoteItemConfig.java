package com.yy.gameecology.hdzj.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2022-07-07 19:17
 **/
@Data
public class PromoteItemConfig {
    @ComponentAttrField(labelText = "最终晋级目标榜单")
    private long destRankId;

    @ComponentAttrField(labelText = "最终晋级目标阶段")
    private long destPhaseId;

    @ComponentAttrField(labelText = "需要加分，用于晋级的来源榜单",remark = "如果没有中间过渡榜，可填写小于等于0")
    private long addScoreSrcRankId;

    @ComponentAttrField(labelText = "预设晋级成员Id，优先级大于expectedMemberRank")
    private String expectedMemberId;

    @ComponentAttrField(labelText = "预设晋级成员排名,如果expectedMemberId未晋级，则按照剩余晋级成员，按照排名从小到大优先级填充")
    private long leftMemberRankSort;


    @ComponentAttrField(labelText = "pk设置排序,按照pk图顺序排列")
    private long pkSort;
}
