package com.yy.gameecology.hdzj.element.component.attr;
import com.google.common.collect.Maps;
import com.yy.gameecology.common.consts.PBCommonBannerId;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvagConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class CpMultiPuzzleComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "强厅角色=用于获取cp过任务所在房间")
    private long tingActor;

    @ComponentAttrField(labelText = "cp任务榜单id")
    private long cpTaskRankId;

    @ComponentAttrField(labelText = "cp任务阶段id")
    private long cpTaskPhaseId;

    @ComponentAttrField(labelText = "cp榜单id")
    private long cpRankId;

    @ComponentAttrField(labelText = "cp用户对主持贡献榜单id")
    private long cpContributeRankId;

    @ComponentAttrField(labelText = "cp 主持对用户贡献榜单id")
    private long cpAntiContributeRankId;

    @ComponentAttrField(labelText = "全程阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "cp榜单拉取数量")
    private long rankLimit;

    @ComponentAttrField(labelText = "万能碎片图片")
    private String jackPointPic;

    @ComponentAttrField(labelText = "产生万能碎片时间")
    private long jackPointTimestamp;

    @ComponentAttrField(labelText = "真实产生万能碎片等级")
    private long jackPointTaskIndex;

    @ComponentAttrField(labelText = "对外产生万能碎片等级")
    private long jackPointShowTaskIndex;

    @ComponentAttrField(labelText = "任务抽奖奖池id",remark = "任务抽奖奖池id",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "第几套"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "抽奖奖池id")
            })
    private Map<Integer, Long> lotteryTaskIdMap = Maps.newHashMap();


    @ComponentAttrField(labelText = "口令task id")
    private long chatTaskId;

    @ComponentAttrField(labelText = "大奖奖池task id")
    private long jackPointTaskId;

    @ComponentAttrField(labelText = "大奖奖池package id")
    private long jackPointPackageId;

    @ComponentAttrField(labelText = "大奖奖品名")
    private String jackPointAwardName;

    @ComponentAttrField(labelText = "大奖图片")
    private String jackPointAwardPic;

    @ComponentAttrField(labelText = "大奖发放数限额")
    private long jackPointLimit;

    @ComponentAttrField(labelText = "大奖单价")
    private long jackPointPrice;

    @ComponentAttrField(labelText = "外显限额")
    private long priceLimit;

    @ComponentAttrField(labelText = "大奖兜底奖池")
    private String jackPointBackUp;

    @ComponentAttrField(labelText = "大奖兜底奖品名")
    private String jackPointBackUpName;

    @ComponentAttrField(labelText = "大奖兜底奖品图片")
    private String jackPointBackUpPic;


    @ComponentAttrField(labelText = "任务阈值", remark = ",多个逗号分隔", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
    })
    private List<Long> missions;

    @ComponentAttrField(labelText = "完成拼图显示文案",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "第几套"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "文案")
            })
    private Map<Integer, String> puzzleCompleteText =  Maps.newHashMap();

    @ComponentAttrField(labelText = "拼图位置",remark = "拼图位置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "第几套"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = RowCol.class)
            })
    private Map<Integer, List<RowCol>> cidRowColMap = Maps.newHashMap();

    @ComponentAttrField(labelText = "行集齐拼图列表",remark = "行集齐拼图列表",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "第几套"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = RowPuzzle.class, labelText = "拼图列表")
            })
    private Map<Integer, List<RowPuzzle>> rowPuzzles = Maps.newHashMap();

    @ComponentAttrField(labelText = "列集齐拼图列表",remark = "列集齐拼图列表",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "第几套"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = ColPuzzle.class, labelText = "拼图列表")
            })
    private Map<Integer, List<ColPuzzle>> colPuzzles = Maps.newHashMap();

    @ComponentAttrField(labelText = "抽奖packageId对应的拼图",remark = "抽奖packageId对应的拼图",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "lottery packageId"),
                    @SubField(fieldName = Constant.VALUE, type = String.class)
            })
    private Map<Long, String> lotteryPuzzleMap =  Maps.newHashMap();

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    private long busiId = 200;

    @ComponentAttrField(labelText = "完成广播类型", dropDownSourceBeanClass = BroadcastTypeSource.class)
    private int bannerBroType = 2;

    @ComponentAttrField(labelText = "bannerId")
    private long bannerId = PBCommonBannerId.SUNSHINE_TASK;

    @ComponentAttrField(labelText = "bannerType")
    private long bannerType = 0;

    @ComponentAttrField(labelText = "横幅svga配置",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "svgaConfigCode"),
                    @SubField(fieldName = Constant.VALUE, type = BannerSvagConfig.class, labelText = "svga配置")})
    private Map<String, BannerSvagConfig> bannerSvag;

    @ComponentAttrField(labelText = "svgaText文案配置",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "svga文案配置编码"),
                    @SubField(fieldName = Constant.VALUE, type = BannerSvgaTextConfig.class, labelText = "svga文案配置")})
    private Map<String, BannerSvgaTextConfig> svgaText;

    @ComponentAttrField(labelText = "text动态文案", remark = "可用于替换svgaText文案配置-富文本消息 中的占位符",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "文案中的占位符"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "占位符对应的值")})
    private Map<String, String> textDynamicValue;

    @ComponentAttrField(labelText = "svga图片key",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "imgkey"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "图片地址")})
    private Map<String, String> svgaImgLayers;


    @ComponentAttrField(labelText = "循环次数", remark = "循环播放次数, 0-无限循环(勿填0)")
    private int loops = 1;

    @ComponentAttrField(labelText = "布局", remark = " 横幅类型(contentType)==6使用 可选 动画播放位置（左右充满，垂直对齐类型）0：全屏播放；1：居中对齐播放；2：顶部对齐播放；3：底部对齐播放")
    private int layoutType;

    @ComponentAttrField(labelText = "布局边距", remark = "相对父布局的间距 2个元素的数组，分别对应顶部和底部的间距；对应位置为[top, bottom]。通常top、bottom为0。配置例子：{\"android\":[10,0],\"ios\":[0,0]}")
    private String layoutMargin = "{\"android\":[10,0],\"ios\":[0,0]}";


    @ComponentAttrField(labelText = "宽高比", remark = "必填 宽高比 客户端默认宽为全屏，svga高度根据宽高比计算,填写实例：6:9")
    private String whRatio;

    @ComponentAttrField(labelText = "广播业务ID", remark = "1 -语音房 2 -交友房 4 -其他 8 -宝贝 位域表示 支持组合：交友and宝贝：2+8")
    private int broBusiId;


    @Data
    public static class RowCol {
        @ComponentAttrField(labelText = "行")
        private int row;

        @ComponentAttrField(labelText = "列")
        private int col;

        @ComponentAttrField(labelText = "图片")
        private String pic;

        @ComponentAttrField(labelText = "发奖taskId")
        private long taskId;

        @ComponentAttrField(labelText = "发奖packageId")
        private long packageId;

        @ComponentAttrField(labelText = "虚拟货币id")
        private String cid;
    }

    @Data
    public static class RowPuzzle {
        @ComponentAttrField(labelText = "行")
        private int row;

        @ComponentAttrField(labelText = "拼图列表‘,’分割")
        private String puzzles;

        @ComponentAttrField(labelText = "奖励图片")
        private String pic;

        @ComponentAttrField(labelText = "奖励名称")
        private String name;

        @ComponentAttrField(labelText = "奖励发奖taskId")
        private long taskId;

        @ComponentAttrField(labelText = "奖励发奖packageId")
        private long packageId;
    }

    @Data
    public static class ColPuzzle {
        @ComponentAttrField(labelText = "列")
        private int row;

        @ComponentAttrField(labelText = "拼图列表‘,’分割")
        private String puzzles;

        @ComponentAttrField(labelText = "奖励图片")
        private String pic;

        @ComponentAttrField(labelText = "奖励名称")
        private String name;

        @ComponentAttrField(labelText = "奖励发奖taskId")
        private long taskId;

        @ComponentAttrField(labelText = "奖励发奖packageId")
        private long packageId;
    }

}
