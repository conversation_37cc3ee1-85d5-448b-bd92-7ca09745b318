package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.KnockoutAnchorTransferComponentAttr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * ai desc:
 * KnockoutAnchorTransferMysqlComponent是1个组件
 * 需要实现的目标是要从KnockoutAnchorTransferComponent的逻辑中改写过来
 * 变更的内容是把原有使用redis存储的改成mysql存储，请按照以下步骤完成KnockoutAnchorTransferMysqlComponent组件的编写
 * 1、先分析好KnockoutAnchorTransferComponent实现的功能的逻辑
 * 2、设计好KnockoutAnchorTransferMysqlComponent组件的mysql存储表结构
 * 3、把KnockoutAnchorTransferComponent的逻辑，存储使用mysql替换掉redis,并重构到KnockoutAnchorTransferMysqlComponent新组件中
 * 4、实现的过程中需要考虑并发的问题
 *
 * <AUTHOR>
 * @date 2025-07-11 11:29
 **/
@UseRedisStore
@Component
public class KnockoutAnchorTransferMysqlComponent extends BaseActComponent<KnockoutAnchorTransferComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Override
    public Long getComponentId() {
        return ComponentId.KNOCKOUT_ANCHOR_TRANSFER_MYSQL;
    }
}
