package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import com.yy.thrift.hdztranking.RoleType;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022.10.20 10:21
 */
@Data
public class SupperPCBannerComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "榜单id")
    private long rankId;

    /**
     * 主播 -> 200 公会->400 角色类型,主播or公会 对应这个类,目前只有主播和公会 com.yy.gameecology.common.protocol.thrift.hdztranking.RoleType
     **/
    @ComponentAttrField(labelText = "角色类型", dropDownSourceBeanClass = RoleTypeSource.class)
    private int roleType = RoleType.ANCHOR.getValue();

    /**
     * 等级对应的分值描述,如一级为 7千万(前端需要中文表述) level对应中台的任务等级
     **/
    @ComponentAttrField(labelText = "等级对应的分值描述", remark = "如一级为 7千万(前端需要中文表述) level对应中台的任务等级", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务等级"),
            @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "分值描述")
    })
    private Map<Long, String> level2ScoreDesc;

    @Deprecated
    @ComponentAttrField(labelText = "广播的bannerId", remark = "已弃用,默认17")
    private long bannerId = 17;

    @ComponentAttrField(labelText = "广播的bannertype", remark = "区分多个相同类型的广播")
    private long bannerType;

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    private int busiId;

    /**
     * 等级对应广播范围
     **/
    @ComponentAttrField(labelText = "等级对应广播范围", remark = "可不填,默认单业务广播", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务等级"),
            @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "广播类型,默认单业务广播")})
    private Map<Long, Integer> level2BroType;

    /**
     * 等级对应广播时长
     **/
    @ComponentAttrField(labelText = "等级对应广播时长,单位秒", remark = "可不填,默认8秒", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务等级"),
            @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "广播时长")})
    private Map<Long, Integer> level2TimeLong = Maps.newHashMap();

    @ComponentAttrField(labelText = "黑名单组件序号", remark = "默认0,不开启黑名单")
    private long blackListCmptUseIndex = 0;
}
