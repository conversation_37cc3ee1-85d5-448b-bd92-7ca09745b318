package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.*;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.event.*;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.bean.hdzt.WelfareValuePair;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.exception.LotteryException;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.activity.service.layer.LayerSupport;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.NickExt;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.db.model.gameecology.ComponentDataList;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.AwardRoll;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.*;
import com.yy.java.webdb.BatchUserInfoWithNickExt;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.protocol.pb.layer.LayerInfo;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztranking.*;
import lombok.Data;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-07-14 16:14
 **/
@UseRedisStore
@Component
@RequestMapping("/allUserTask")
@RestController
public class AllUserTaskComponent extends BaseActComponent<AllUserTaskComponentAttr> implements LayerSupport {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 每天抽奖次数记录
     */
    private final static String LOTTERY_COUNT = "lottery_count_%s";

    /**
     * 全服中奖记录
     */
    private final static String ALL_AWARD_LIST = "all_award_list";

    private final static String LOTTERY_PACKAGE_REPORT = "lottery_package_report";

    private final static String LOTTERY_PACKAGE_DAY_REPORT = "lottery_package_day_report_%s";

    //发放礼物金额
    private final static String LOTTERY_PACKAGE_VALUE_FILED = "lottery_value";

    private static final String ROBOT_TOKEN_PREFIX = "https://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=";

    private final static long GGFS_BANNER_TYPE = 4;

    private final static long GGFS_BANNER_ID = PBCommonBannerId.ALL_USER_BANNER;

    private static final int TOP_BANNER_CONTENT_TYPE = 3;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private ActLayerInfoService actLayerInfoService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    protected UserInfoService userInfoService;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private OnlineChannelService onlineChannelService;

    @Autowired
    private CommonDataDao commonDataDao;

    @Override
    public Long getComponentId() {
        return ComponentId.ALL_USER_TASK;
    }

    @Override
    public long getActId() {
        return ComponentId.ALL_USER_TASK;
    }

    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = true)
    public void onTaskProgressChanged(TaskProgressChanged event, AllUserTaskComponentAttr attr) throws Exception {
        if (event.getRankId() == attr.getTaskRankId() && event.getPhaseId() == attr.getTaskPhaseId()) {
            handleWxTaskCompleted(event, attr);
        } else {
            log.info("onTaskProgressChanged no handler to process!");
        }
    }

    public void handleWxTaskCompleted(TaskProgressChanged event, AllUserTaskComponentAttr attr) throws Exception {
        log.info("handleWxTaskCompleted event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        Map<Long, String> actors = event.getActors();

        long userUid = getUser(actors, attr.getTaskUserRoleId());
        if (userUid == 0) {
            log.warn("handleWxTaskCompleted user uid invalid.");
            return;
        }

        long anchorUid = getAnchor(actors, attr.getTaskAnchorRoleIds());
        if (anchorUid == 0) {
            log.warn("handleWxTaskCompleted anchor uid invalid.");
            return;
        }

        Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfo(Lists.newArrayList(userUid, anchorUid), com.yy.gameecology.common.bean.Template.skillcard);
        String dayCode = DateUtil.format(DateUtil.getDate(event.getOccurTime()), DateUtil.PATTERN_TYPE2);

        //---抽奖（抽个占位记录）
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        int lotteryCount = (int) event.getRoundComplete();
        //抽奖必中逻辑
        Map<Integer, Long> assignHit = calAssignHit(attr, seq, dayCode, lotteryCount);
        Date now = commonService.getNow(attr.getActId());
        long lotteryTaskId = attr.getLotteryTaskId();
        long welfareTaskId = attr.getWelfareTaskId();
        if(attr.getLotteryTaskIdDateMap() != null && attr.getLotteryTaskIdDateMap().containsKey(dayCode)) {
            lotteryTaskId = attr.getLotteryTaskIdDateMap().get(dayCode).getLotteryTaskId();
            welfareTaskId = attr.getLotteryTaskIdDateMap().get(dayCode).getWelfareTaskId();
            log.info("doLottery date:{}, lotteryTaskId:{}, welfareTaskId:{}", dayCode, lotteryTaskId, welfareTaskId);
        }
        BatchLotteryResult result = hdztAwardServiceClient.doBatchLottery(seq, BusiId.MAKE_FRIEND.getValue(), userUid, lotteryTaskId, lotteryCount
                , assignHit
                , ImmutableMap.of(LotteryExtParaName.NOT_FLOW_CONTROL, "1", LotteryExtParaName.LOAD_RECORD_SEQ_DUP, "1")
                , 3);
        boolean lotteryError = result == null || (result.getCode() != 0 && result.getCode() != LotteryException.E_LOTTERY_COMPLETED);
        if (lotteryError) {
            log.error("doBatchLottery error, player:{}, seq:{}, result:{}", userUid, seq, result);
            throw new RuntimeException("doBatchLottery error");
        }
        log.info("doBatchLottery result,player:{},result:{}", userUid, result);

        //用于提取抽中的礼物列表
        List<AwardRoll> taskPackageInfos = Lists.newArrayList();
        Map<Long, AwardModelInfo> awardModelMap = hdztAwardServiceClient.queryAwardTasks(welfareTaskId);
        //---发奖（根据抽奖结果，同时给主播和用户发奖）
        Map<Long, Long> recordPackageMap = result.getRecordPackages();
        Map<Long, Integer> packageIds = new HashMap<>(recordPackageMap.size());
        for (Long recordId : recordPackageMap.keySet()) {
            long lPackageId = recordPackageMap.get(recordId);
            long welfarePackageId = attr.getLotteryPackageIdMap().get(lPackageId).get(event.getBusiId()).getWelfarePackageId();
            packageIds.compute(welfarePackageId, (k, v) -> v == null ? 1 : v + 1);

            AwardRoll taskPackageInfo = new AwardRoll();
            taskPackageInfo.setPrize(awardModelMap.get(welfarePackageId).getPackageName());
            taskPackageInfo.setLogo(awardModelMap.get(welfarePackageId).getPackageImage());
            taskPackageInfo.setPackageId(lPackageId);
            taskPackageInfos.add(taskPackageInfo);
            taskPackageInfo.setGiftUnit(awardModelMap.get(welfarePackageId).getUnit());
        }
        String time = DateUtil.format(now);

        String playerSeq = "laward|p|" + seq;
        String anchorSeq = "laward|a|" + seq;
        // 用户发奖
        for (Long packageId : packageIds.keySet()) {
            int num = packageIds.get(packageId);
            if(num == 1) {
                Map<Long, Integer> packageTmps = new HashMap<>();
                packageTmps.put(packageId, 1);
                Map<String, String> extData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(anchorUid));
                hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.MAKE_FRIEND.getValue(), userUid, welfareTaskId, ImmutableMap.of(welfareTaskId, packageTmps), playerSeq + "_" + packageId, extData);
                extData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(userUid));
                hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.MAKE_FRIEND.getValue(), anchorUid, welfareTaskId, ImmutableMap.of(welfareTaskId, packageTmps), anchorSeq + "_" + packageId, extData);
            } else {
                for(int i=0; i<num; i++) {
                    Map<Long, Integer> packageTmps = new HashMap<>();
                    packageTmps.put(packageId, 1);
                    Map<String, String> extData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(anchorUid));
                    hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.MAKE_FRIEND.getValue(), userUid, welfareTaskId, ImmutableMap.of(welfareTaskId, packageTmps), playerSeq + "_" + packageId+"_" +i, extData);
                    extData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(userUid));
                    hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.MAKE_FRIEND.getValue(), anchorUid, welfareTaskId, ImmutableMap.of(welfareTaskId, packageTmps), anchorSeq + "_" + packageId+"_"+i, extData);
                }
            }
        }
        //保存全服中奖记录信息
        List<AwardRoll> awardRolls = toAwardRoll(attr.getBigAwardPackageId(), taskPackageInfos, userInfoVoMap, anchorUid, userUid, attr.getUnitMap(), attr.getCountMap());
        AtomicInteger t = new AtomicInteger();
        awardRolls.forEach(roll -> {
            String lPushSeq = "lpush:" + "|" + roll.getPackageId() + "|" + seq + "|" + t;
            commonDataDao.listInsertIgnore(event.getActId(), ComponentId.ALL_USER_TASK,
                    Convert.toInt(attr.getCmptUseInx()), lPushSeq, buildAllAwardKey(attr), JSON.toJSONString(roll));
            t.getAndIncrement();
            long value = attr.getLotteryPackageIdMap()
                    .getOrDefault(roll.getPackageId(), new HashMap<>())
                    .getOrDefault((long)attr.getBusiId(), new WelfareValuePair()).getValue();

            String reportSeq = Const.addActivityPrefix(attr.getActId(), "report:" + seq+"|"+t);
            String reportKey = makeKey(attr, LOTTERY_PACKAGE_REPORT);
//            actRedisDao.hIncrByKeyWithSeq(getRedisGroupCode(event.getActId()), reportSeq, reportKey, roll.getPackageId() + "", 1, 20 * DateUtil.ONE_DAY_SECONDS);
//            actRedisDao.hIncrByKeyWithSeq(getRedisGroupCode(event.getActId()), reportSeq + LOTTERY_PACKAGE_VALUE_FILED, reportKey, LOTTERY_PACKAGE_VALUE_FILED, value * 2, 20 * DateUtil.ONE_DAY_SECONDS);
            //TODO 如果次组件要重新启用，则需要改造此处
            if (true) {
                throw new RuntimeException("hIncrByKeyWithSeq,lua forbid");
            }
            if(event.getActors().containsKey(attr.getTingRoleId())) {
                String ting = event.getActors().get(attr.getTingRoleId());
                String newValue = actRedisDao.hget(getRedisGroupCode(event.getActId()), reportKey, LOTTERY_PACKAGE_VALUE_FILED);
                sendNotice(attr, userUid, anchorUid, ting, roll.getPrize(), Convert.toLong(newValue));
            }
            String reportDaySeq = Const.addActivityPrefix(attr.getActId(), "report_day:" + seq + "|" + t);
            String dayKey = String.format(makeKey(attr, LOTTERY_PACKAGE_DAY_REPORT), dayCode);
//            actRedisDao.hIncrByKeyWithSeq(getRedisGroupCode(event.getActId()), reportDaySeq, dayKey, roll.getPackageId() + "", 1, DateUtil.ONE_DAY_SECONDS);
//            actRedisDao.hIncrByKeyWithSeq(getRedisGroupCode(event.getActId()), reportDaySeq + LOTTERY_PACKAGE_VALUE_FILED, dayKey, LOTTERY_PACKAGE_VALUE_FILED, value * 2, DateUtil.ONE_DAY_SECONDS);
            //TODO 如果次组件要重新启用，则需要改造此处
            if (true) {
                throw new RuntimeException("hIncrByKeyWithSeq,lua forbid");
            }
        });
        if (StringUtils.isNotBlank(attr.getZkGiftId())) {
            updateRankData(attr.getActId(), attr.getBusiId(), attr.getZkGiftId(), event, event.getActors());
        }

        //广播横幅[全频道广播]
        Map<String, Object> broadBannerExt = Maps.newHashMap();
        UserInfoVo userInfoVo = userInfoVoMap.get(userUid);
        UserInfoVo anchorVo = userInfoVoMap.get(anchorUid);
        broadBannerExt.put("userNick", Base64.encodeBase64String(userInfoVo.getNick().getBytes()));
        broadBannerExt.put("anchorNick", Base64.encodeBase64String(anchorVo.getNick().getBytes()));
        broadBannerExt.put("userLogo", userInfoVo.getAvatarUrl());
        broadBannerExt.put("anchorLogo", anchorVo.getAvatarUrl());
        broadBannerExt.put("awardInfo", removeUserInfo(awardRolls));
        broadBannerExt.put("babyLogo", anchorVo.getAvatarUrl());
        broadBannerExt.put("babyNick", Base64.encodeBase64String(anchorVo.getNick().getBytes()));
        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(Lists.newArrayList(userUid, anchorUid));
        Map<String, Map<String, MultiNickItem>> multiNickUsers = null;
        if(batched != null) {
            if (org.apache.commons.lang3.StringUtils.startsWith(batched.getNickExt(), StringUtil.OPEN_BRACE)) {
                NickExt nickExt = JSON.parseObject(batched.getNickExt(), NickExt.class);
                multiNickUsers = nickExt.getUsers();
            }
        }
        broadBannerExt.put("nickExtUsers", multiNickUsers);
        if(event.getActors().containsKey(attr.getTingRoleId())) {
            String ting = event.getActors().get(attr.getTingRoleId());
            broadBannerExt.put("sid", ting.split("_")[0]);
            broadBannerExt.put("ssid", ting.split("_")[1]);
        }
        RetryTool.withRetryCheck(attr.getActId(), seq, () -> {
            commonBroadCastService.commonBannerBroadcast(event.getActId(), userUid, 0, "", GGFS_BANNER_ID, GGFS_BANNER_TYPE, broadBannerExt, Template.findByValue(attr.getRefreshLayerTemplate()));
        });

        //触发挂件更新
        refreshLayer(event, attr, now, userInfoVo, anchorVo);
        if (attr.isBroAppBanner()) {
            int i = 0;
            for (AwardRoll awardRoll : awardRolls) {
                List<AwardRoll> rolls = new ArrayList<>();
                rolls.add(awardRoll);
                RetryTool.withRetryCheck(attr.getActId(), seq + ":appbanner" + "_" + i, () -> {
                    bottomBroadcastApp(event, attr, userUid, anchorUid, rolls);
                });
                i++;
            }
        }

        //单播获奖动画
        String noticeType = "awardNotice";
        int i = 0;
        for (AwardRoll taskPackageInfo : taskPackageInfos) {
            Map<String, Object> noticeExt = Maps.newHashMap();
            List<AwardRoll> infos = new ArrayList<>();
            infos.add(taskPackageInfo);
            noticeExt.put("awardList", infos);
            String extInfo = JSON.toJSONString(noticeExt);
            RetryTool.withRetryCheck(attr.getActId(), seq + ":notice_user" + "_"+ i, () -> {
                commonBroadCastService.commonNoticeUnicast(event.getActId(), noticeType, noticeType, extInfo, userUid);
            });
            RetryTool.withRetryCheck(attr.getActId(), seq + ":notice_anchor"+ "_"+ i, () -> {
                commonBroadCastService.commonNoticeUnicast(event.getActId(), noticeType, noticeType, extInfo, anchorUid);
            });
            i++;
        }
        log.info("onTaskProgressChanged done -> event:{}, attr:{}", event, attr);
    }

    @Override
    public Map<String, Object> buildItemMemberExtInfo(long actId, LayerMemberItem layerMemberItem, Map<String, Object> ext) {
        AllUserTaskComponentAttr attr = getUniqueComponentAttr(actId);
        if (attr == null) {
            return ext;
        }

        if (!LayerItemTypeKey.CMPT_ITEM.equals(layerMemberItem.getItemType())) {
            return ext;
        }

        if (ext == null) {
            ext = new HashMap<>(8);
        }

        long score = 0;
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, attr.getTaskRankId(), attr.getTaskPhaseId(), StringUtils.EMPTY, 1, null);
        if (CollectionUtils.isNotEmpty(ranks)) {
            score = ranks.get(0).getScore();
        }

        ext.put("score", score%attr.getTaskThreshold());
        ext.put("taskConfig", attr.getTaskThreshold());

        return ext;
    }

    private void refreshLayer(TaskProgressChanged event, AllUserTaskComponentAttr attr, Date now, UserInfoVo userInfoVo, UserInfoVo anchorVo) {
        //手动触发一个包含中奖信息的挂件广播并添加碰杯动画数据
        List<ChannelInfo> onlineChannels = commonService.queryBroChannels(attr.getActId(), Template.findByValue(attr.getRefreshLayerTemplate()));
        if (!CollectionUtils.isEmpty(onlineChannels)) {
            String from = "manual";
            log.info("refreshLayer begin,size:{}", onlineChannels.size());
            for (ChannelInfo onlineChannel : onlineChannels) {
                threadPoolManager.get(ThreadPoolNames.CMPT_BUILD_LAYER).execute(() -> {
                    refreshLayerSubChannel(event, attr, now, userInfoVo, anchorVo, onlineChannel, from);
                });
            }
        }
    }

    private void refreshLayerSubChannel(TaskProgressChanged event, AllUserTaskComponentAttr attr, Date now, UserInfoVo userInfoVo, UserInfoVo anchorVo,ChannelInfo onlineChannel ,String from) {
        LayerBroadcastInfo layerBroadcastInfo = actLayerInfoService.buildLayerInfo(attr.getActId(), onlineChannel.getSid(), onlineChannel.getSsid(), now, from);
        LayerMemberItem memberItem = getCmptLayerMemberItem(layerBroadcastInfo);
        if (memberItem == null) {
            log.warn("caught layerBroadcastInfo illegal null value:{}", layerBroadcastInfo);
            return;
        }

        Map<String, Object> ext = memberItem.getExt();
        if (ext == null) {
            ext = new HashMap<>(8);
            memberItem.setExt(ext);
        }

        ext.put("taskRound", event.getCurrRound() - 1);
        ext.put("userNick", Base64.encodeBase64String(userInfoVo.getNick().getBytes()));
        ext.put("anchorNick", Base64.encodeBase64String(anchorVo.getNick().getBytes()));
        ext.put("userLogo", userInfoVo.getAvatarUrl());
        ext.put("anchorLogo", anchorVo.getAvatarUrl());

        LayerInfo.LayerBroadcast broadcastInfo = layerBroadcastInfo.toBroadcastInfoPb();
        log.info("broadcast layer info with ext:{}", ext);

        //广播信息
        GameecologyActivity.GameEcologyMsg msg =
                GameecologyActivity.GameEcologyMsg.newBuilder()
                        .setUri(GameecologyActivity.PacketType.LayerBroadcast_VALUE)
                        .setLayerBroadcast(broadcastInfo).build();

        if (onlineChannel.getSsid() != 0) {
            svcSDKService.broadcastSub(onlineChannel.getSid(), onlineChannel.getSsid(), msg);
        }
        //顶级频道广播
        else {
            log.warn("ssid is empty");
        }
    }

    private LayerMemberItem getCmptLayerMemberItem(LayerBroadcastInfo layerBroadcastInfo) {
        if (layerBroadcastInfo == null || CollectionUtils.isEmpty(layerBroadcastInfo.getExtMemberItem())) {
            return null;
        }

        return layerBroadcastInfo.getExtMemberItem().stream()
                .filter(layerMemberItem -> LayerItemTypeKey.CMPT_ITEM.equals(layerMemberItem.getItemType()))
                .findAny().orElse(null);
    }

    private long getUser(Map<Long, String> actors, long roleId) {
        if (StringUtils.isNumeric(actors.get(roleId))) {
            return Long.parseLong(actors.get(roleId));
        }

        return 0;
    }

    private long getAnchor(Map<Long, String> actors, List<Long> anchorRoleIds) {
        for (long roleId : anchorRoleIds) {
            if (StringUtils.isNumeric(actors.get(roleId))) {
                return Long.parseLong(actors.get(roleId));
            }
        }

        return 0;
    }

    /**
     * 必中逻辑运算
     */
    private Map<Integer, Long> calAssignHit(AllUserTaskComponentAttr attr, String eventSeq, String dayCode, int lotteryCount) {
        String lotteryCountKey = buildDayLotteryKey(attr, dayCode);
        String seq = makeKey(attr, "assign:" + eventSeq);
        List<Long> incResult;
        //List<Long> incResult = actRedisDao.incrValueWithLimitSeq(getRedisGroupCode(attr.getActId()), seq, lotteryCountKey, lotteryCount, Integer.MAX_VALUE, false, DateUtil.ONE_DAY_SECONDS);
        //TODO 如果次组件要重新启用，则需要改造此处
        if (true) {
            throw new RuntimeException("string_incr_with_limit_seq.lua,lua forbid");
        }
        long afterInc = incResult.get(1);

        Map<Integer, Long> assignHit = Maps.newHashMap();
        for (int i = 0; i < lotteryCount; i++) {
            Long packageId = attr.getAssignHit().getOrDefault(dayCode, Collections.emptyMap()).get(afterInc - i);
            if (packageId != null) {
                //必中的参数规范索引号是从1开始的
                assignHit.put(i + 1, packageId);
                log.info("assignHit,seq:{},afterInc:{},i:{},packageId:{}", eventSeq, afterInc, i, packageId);
            }
        }
        return assignHit;
    }

    private List<AwardRoll> toAwardRoll(Set<Long> bigAwardPackageIds, List<AwardRoll> taskPackageInfos, Map<Long, UserInfoVo> userInfoVoMap, long anchorUid, long playerUid, Map<Long, String> unitMap, Map<Long, Long> countMap) {
        List<AwardRoll> awardRolls = Lists.newArrayList();
        String time = DateUtil.format(new Date(), DateUtil.DEFAULT_PATTERN);
        for (AwardRoll taskPackageInfo : taskPackageInfos) {
            AwardRoll roll = new AwardRoll();
            roll.setTime(time);
            roll.setPrize(taskPackageInfo.getPrize());
            roll.setType(bigAwardPackageIds.contains(taskPackageInfo.getPackageId()) ? 1 : 0);
            roll.setPackageId(taskPackageInfo.getPackageId());
            roll.setLogo(taskPackageInfo.getLogo());
            roll.setUnit(unitMap.getOrDefault(taskPackageInfo.getPackageId(), ""));
            roll.setGiftCount(countMap.getOrDefault(roll.getPackageId(), 0L));
            roll.setGiftUnit(taskPackageInfo.getGiftUnit());
            roll.setUser(Lists.newArrayList(userInfoVoMap.get(playerUid), userInfoVoMap.get(anchorUid)));
            awardRolls.add(roll);
        }

        return awardRolls;
    }

    private List<AwardRoll> removeUserInfo(List<AwardRoll> awardRolls) {
        List<AwardRoll> result = Lists.newArrayList();
        for (AwardRoll roll : awardRolls) {
            AwardRoll resultItem = new AwardRoll();
            resultItem.setType(roll.getType());
            resultItem.setPrize(roll.getPrize());
            resultItem.setGiftName(roll.getGiftName());
            resultItem.setGiftIcon(roll.getLogo());
            resultItem.setGiftUnit(roll.getGiftUnit());
            resultItem.setGiftCount(roll.getGiftCount());
            result.add(resultItem);
        }
        return result;
    }


    /**
     * app横幅广播
     */
    public void bottomBroadcastApp(TaskProgressChanged event, AllUserTaskComponentAttr attr, long playerUid, long anchorUid, List<AwardRoll> awardRolls) {


        String giftName = buildBigGiftName(awardRolls);
        //非大奖，不广播
        if (StringUtil.isBlank(giftName)) {
            log.info("content empty");
            return;
        }
        String template =attr.getBroTemplate();
        String content = template.replace("{gift}", giftName);

        String seq = UUID.randomUUID().toString().replaceAll("-", "");

        content = content.replace("{userNick}", String.format("{%s:n}", playerUid));
        content = content.replace("{anchorNick}", String.format("{%s:n}", anchorUid));

        AppBannerSvgaConfig2 svgaConfig = new AppBannerSvgaConfig2();
        svgaConfig.setDuration(13);
        svgaConfig.setLoops(Const.ONE);
        svgaConfig.setSvgaURL(attr.getSvga());
        svgaConfig.setHeight(120);
        svgaConfig.setJump(1);
        svgaConfig.setClickLayerName("button");
        svgaConfig.setMiniURL("https://res.yy.com/fts/mobile/plane/cs_mini_1.svga");

        Map<String, AppBannerSvgaText> svgaText = Maps.newHashMap();
        AppBannerSvgaText text = new AppBannerSvgaText();
        text.setText(content);
        //text.setImgs(images);
        svgaText.put(attr.getBroSvgaTextKey(), text);
        svgaConfig.setContentLayers(Collections.singletonList(svgaText));

        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, attr.getAppBannerBusiId(),
                attr.getBcType(), 0, 0, "",
                Lists.newArrayList());
        appBannerEvent.setContentType(TOP_BANNER_CONTENT_TYPE);
        appBannerEvent.setAppId(getTurnoverAppId(Convert.toInt(event.getBusiId())));
        appBannerEvent.setSvgaConfig(svgaConfig);
        appBannerEvent.setUid(playerUid);
        appBannerEvent.setUidList(Lists.newArrayList(playerUid, anchorUid));
        kafkaService.sendAppBannerKafka(appBannerEvent);
    }

    private void updateRankData(long actId, int busiId, String itemId, TaskProgressChanged event, Map<Long, String> actors) {
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        UpdateRankingRequest rankDataEvent = new UpdateRankingRequest();
        rankDataEvent.setBusiId(busiId);
        rankDataEvent.setActId(actId);
        rankDataEvent.setSeq(seq);
        rankDataEvent.setActors(actors);
        rankDataEvent.setItemId(itemId);
        rankDataEvent.setCount(event.getRoundComplete());
        rankDataEvent.setScore(event.getRoundComplete());
        rankDataEvent.setTimestamp(DateUtil.getDate(event.getTimestamp(), DateUtil.DEFAULT_PATTERN).getTime());
        boolean result = hdztRankingThriftClient.updateRankWithRetry(rankDataEvent, 3);
        if(!result) {
            log.error("update rank error, invoke retry seq:{}", seq);
            throw new RuntimeException("update rank error, invoke retry");
        }
        log.info("updateRankData done: result:{}, request:{}", result, rankDataEvent.toString());
    }

    private String buildBigGiftName(List<AwardRoll> rolls) {
        List<String> bigGiftNames = rolls.stream().filter(p -> p.getType() == 1).map(AwardRoll::getPrize).collect(Collectors.toList());
        return StringUtils.join(bigGiftNames, ",");
    }

    private String buildAllAwardKey(AllUserTaskComponentAttr attr) {
        return makeKey(attr, ALL_AWARD_LIST);
    }

    private String buildDayLotteryKey(AllUserTaskComponentAttr attr, String dayCode) {
        return makeKey(attr, String.format(LOTTERY_COUNT, dayCode));
    }

    /**
     * 抽奖统计查询
     */
    @RequestMapping(value = "/queryLotteryReport")
    public Response<Map<String, Object>> queryLotteryReport(Long actId, String day) throws TException {
        Map<String, Object> result = Maps.newHashMap();

        AllUserTaskComponentAttr att = getUniqueComponentAttr(actId);
        String reportKey = makeKey(att, LOTTERY_PACKAGE_REPORT);
        String dayKey = String.format(makeKey(att, LOTTERY_PACKAGE_DAY_REPORT), day);
        String group = getRedisGroupCode(actId);
        Map<Object, Object> lotteryCount = actRedisDao.hGetAll(group, reportKey);
        result.put("lotteryCount", lotteryCount);

        Map<Object, Object> dayLotteryCount = actRedisDao.hGetAll(group, dayKey);
        result.put("dayLotteryCount", dayLotteryCount);

        List<Long> taskIds = new ArrayList<>();
        att.getLotteryTaskIdDateMap().forEach((key, value) -> {
            taskIds.add(value.getWelfareTaskId());
        });
        taskIds.add(att.getWelfareTaskId());
        Map<Long, AwardModelInfo> awardModelInfoMap = hdztAwardServiceClient.queryAwardTasks(taskIds, true);
        result.put("lotteryInfo", awardModelInfoMap);
        return Response.success(result);
    }

    //全服礼物滚动
    @RequestMapping("/getAwardRoll")
    public Response<List<AwardRoll>> getAwardRoll(long actId, int top) {
        List<AwardRoll> result = Lists.newArrayList();
        if (SysEvHelper.isHistory()) {
            return Response.success(result);
        }
        AllUserTaskComponentAttr attr = getUniqueComponentAttr(actId);
        top = Math.min(top, 300) - 1;
        String key = buildAllAwardKey(attr);
        String redisGroup = getRedisGroupCode(actId);
        List<ComponentDataList> ret =
                commonDataDao.listSelect(actId, ComponentId.ALL_USER_TASK, (int)attr.getCmptUseInx(), 0, top, true);
        List<String> awardInfo = new ArrayList<>();
        for (ComponentDataList componentDataList : ret) {
            awardInfo.add(componentDataList.getValue());
        }
        if (!CollectionUtils.isEmpty(awardInfo)) {
            awardInfo.forEach(content -> {
                if (StringUtil.isNotBlank(content)) {
                    AwardRoll roll = JSON.parseObject(content, AwardRoll.class);
                    result.add(roll);
                }
            });
        }

        return Response.success(result);
    }

    @RequestMapping("/getAwardRollV2")
    public Response<AwardRollResponse> getAwardRollV2(long actId, int top) {
        AwardRollResponse rollResponse = new AwardRollResponse();
        List<AwardRoll> result = Lists.newArrayList();
        if (SysEvHelper.isHistory()) {
            return Response.success(rollResponse);
        }
        AllUserTaskComponentAttr attr = getUniqueComponentAttr(actId);
        top = Math.min(top, 300) - 1;
        List<ComponentDataList> ret =
                commonDataDao.listSelect(actId, ComponentId.ALL_USER_TASK, (int)attr.getCmptUseInx(), 0, top, true);
        List<String> awardInfo = new ArrayList<>();
        for (ComponentDataList componentDataList : ret) {
            awardInfo.add(componentDataList.getValue());
        }
        if (!CollectionUtils.isEmpty(awardInfo)) {
            awardInfo.forEach(content -> {
                if (StringUtil.isNotBlank(content)) {
                    AwardRoll roll = JSON.parseObject(content, AwardRoll.class);
                    roll.setUnit(attr.getUnitMap().getOrDefault(roll.getPackageId(), ""));
                    roll.setGiftCount(attr.getCountMap().getOrDefault(roll.getPackageId(), 0L));
                    roll.setGiftIcon(roll.getLogo());
                    result.add(roll);
                }
            });
        }
        List<Long> uids = new ArrayList<>();
        for (AwardRoll awardRoll : result) {
            ChannelInfoVo channelInfoVo = null;
            for (UserInfoVo userInfoVo : awardRoll.getUser()) {
                uids.add(userInfoVo.getUid());
                channelInfoVo = onlineChannelService.getChannelInfoVo(userInfoVo.getUid());
            }
            if(channelInfoVo != null) {
                awardRoll.setSid(channelInfoVo.getSid());
                awardRoll.setSsid(channelInfoVo.getSsid());
            }
        }
        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(uids);
        if(batched != null) {
            Map<String, Map<String, MultiNickItem>> multiNickUsers = null;
            if (org.apache.commons.lang3.StringUtils.startsWith(batched.getNickExt(), StringUtil.OPEN_BRACE)) {
                NickExt nickExt = JSON.parseObject(batched.getNickExt(), NickExt.class);
                multiNickUsers = nickExt.getUsers();
            }
            rollResponse.setMultiNickUsers(MapUtils.isEmpty(multiNickUsers) ?
                    org.apache.commons.lang3.StringUtils.EMPTY : JSON.toJSONString(multiNickUsers));
        }
        rollResponse.setList(result);
        return Response.success(rollResponse);
    }

    @GetMapping("/queryTaskProgress")
    public Response<JSONObject> queryTaskProgress(long actId) {
        AllUserTaskComponentAttr attr = getUniqueComponentAttr(actId);
        if(attr == null) {
            return Response.success(null);
        }
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, attr.getTaskRankId(), attr.getTaskPhaseId(), StringUtils.EMPTY, 1, Collections.emptyMap());
        long score = 0;
        if (!CollectionUtils.isEmpty(ranks)) {
            score = ranks.stream().findFirst().get().score;
        }

        JSONObject json = new JSONObject(2);
        json.put("taskThreshold", attr.getTaskThreshold());
        json.put("taskProgress", score % attr.getTaskThreshold());
        boolean show = false;
        if(actInfoService.inActTime(attr.getActId())) {
            show = true;
        }
        json.put("show", show);
        return Response.success(json);
    }

    private void sendNotice(AllUserTaskComponentAttr attr, long uid, long anchorUid, String ting,
                            String awardName, long amount) {
        // 优先使用配置的如流群,否则使用默认
        String msg = buildRuliuMsg(attr, uid, anchorUid, ting, awardName, amount);
        if (attr.getGroupId() > 0 && org.apache.commons.lang3.StringUtils.isNotEmpty(attr.getRobotToken())) {
            String robotToken = attr.getRobotToken().trim();
            if (!robotToken.contains(ROBOT_TOKEN_PREFIX)) {
                robotToken = ROBOT_TOKEN_PREFIX + robotToken;
            }
            baiduInfoFlowRobotService.sendNotify(attr.getGroupId(), robotToken, msg, null);
        } else {
            baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT, msg, null);
        }

        log.info("settleHourRankAward sendNotice done@msg:{},groupId:{}", msg, attr.getGroupId());
    }

    private String buildRuliuMsg(AllUserTaskComponentAttr attr, long uid, long anchorUid, String ting,
                                 String awardName, long amount) {
        String tmp = "【为你爆灯玩法】 \n" +
                "活动id:"+attr.getActId()+ "\n" +
                "神豪:%s 主播:%s" + "\n" +
                "奖励:%s：" + "\n"+
                "频道:%s"+ "\n"+
                "截止当前，玩法累计发放礼物金额："+ amount + "\n";

        return String.format(tmp, uid, anchorUid, awardName, ting);
    }

    /**
     * 用于标识哪个业务触发
     */
    private int getTurnoverAppId(int busiId) {
        BusiId value = BusiId.findByValue(busiId);
        int appId = 0;
        switch (value) {
            case GAME_BABY:
                appId = 36;
                break;
            case MAKE_FRIEND:
                appId = 2;
                break;
            case SKILL_CARD:
                appId = 34;
                break;
            default:
                break;
        }
        return appId;

    }

    @Data
    public class AwardRollResponse {
        private String multiNickUsers;
        private List<AwardRoll> list;
    }

}
