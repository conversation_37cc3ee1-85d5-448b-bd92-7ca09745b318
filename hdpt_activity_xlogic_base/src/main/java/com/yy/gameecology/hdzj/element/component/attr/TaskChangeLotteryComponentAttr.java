package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.activity.bean.acttask.TaskPackageIdConfig;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * desc:过任务抽奖组件
 *
 * <AUTHOR>
 * @date 2023-02-22 19:29
 **/
@Data
public class TaskChangeLotteryComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "抽奖业务Id")
    private Long busiId;

    @ComponentAttrField(labelText = "任务榜单id",
            subFields = {
                    @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
            })
    private List<Long> rankId;

    @ComponentAttrField(labelText = "任务阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "任务完成抽奖", remark = "场景一：任务完成后自动抽奖, 场景二：礼物发放优先级控制：A礼物库存不足改成发B礼物，这个时候设置A为必中即可。",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "当前过的任务等级索引,从0开始"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "抽奖奖池taskId"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "必中packageId", remark = "大于0时有效")
            })
    private Map<Long, Map<Long, Long>> levelLotteryMap = Collections.emptyMap();

    @ComponentAttrField(labelText = "二次抽奖", remark = "根据第一次抽奖结果，触发第二次抽奖，适用于双重限额场景。第一层控制概率：例如第一层奖池，每个用户7样奖品，每个奖品只能中次;第二层控制发放优先级：如果第一层中了Q币，但是Q币不足时，要改成发玩豆。",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "来源奖池taskId"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "来源奖包packageId"),
                    @SubField(fieldName = Constant.VALUE, type = TaskPackageIdConfig.class, labelText = "二次抽奖信息", remark = "奖包信息代表必中奖包，大于0时有效")
            })
    private Map<Long, Map<Long, TaskPackageIdConfig>> towLottery = Collections.emptyMap();

    @ComponentAttrField(labelText = "抽奖失败重试次数")
    private int retry;
}
