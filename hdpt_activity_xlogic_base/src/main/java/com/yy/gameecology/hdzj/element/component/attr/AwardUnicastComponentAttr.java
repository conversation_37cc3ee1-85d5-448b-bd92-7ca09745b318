package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import lombok.Data;

import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption: 奖品单播属性
 * @Date: 2021/4/15 19:19
 * @Modified:
 */
@Data
public class AwardUnicastComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    private Long busiId;

    @ComponentAttrField(labelText = "奖池id", remark = "多个时用逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private long[] taskIds;
    /**
     * 空表示所有taskid下的奖品
     */
    @ComponentAttrField(labelText = "奖包id", remark = "空表示所有taskid下的奖品,多个时用逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private long[] packageIds;
    /**
     * 是否合并奖品
     */
    @ComponentAttrField(labelText = "是否合并奖品")
    private boolean isMageAward = true;
    /**
     * isMageAward = true 时的分隔符
     */
    @ComponentAttrField(labelText = "分隔符", remark = "合并奖品= true 时的分隔符")
    private String separator = "+";

    @ComponentAttrField(labelText = "宝箱类型", dropDownSourceBeanClass = BoxTypeSource.class)
    private int type = 1;

    @ComponentAttrField(labelText = "奖包对应的等级", remark = "先触发高等级,再触发低等级,则低等级无效",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "奖包id"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "等级")
            })
    private Map<Long, Integer> packageLevelMap = Maps.newHashMap();
}
