package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-06-29 20:40
 **/
@Data
public class RankingShowComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "列表配置",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Config.class)})
    private List<Config> configList;
    @ComponentAttrField(labelText = "榜单配置",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = RankConfig.class)})
    private List<RankConfig> rankConfigs;

    @Data
    public static class RankConfig {
        @ComponentAttrField(labelText = "配置id")
        private String configId;
        @ComponentAttrField(labelText = "榜单id")
        private Long rankId;
        @ComponentAttrField(labelText = "阶段id")
        private Long phaseId;
        @ComponentAttrField(labelText = "榜单名称")
        private String name;
        @ComponentAttrField(labelText = "时间格式", remark = "时间格式:日榜则对应为yyyyMMdd")
        private String dateStrFormat;
        @ComponentAttrField(labelText = "是否需要登录")
        private Integer needLogin;
    }

    @Data
    private static class Config {
        @ComponentAttrField(labelText = "配置id", remark = "配置id需要与列表配置中的id对应;一条配置可以对应到多个榜单;")
        private String configId;
        @ComponentAttrField(labelText = "开始时间")
        private Date startTime;
        @ComponentAttrField(labelText = "结算时间")
        private Date endTime;
        @ComponentAttrField(labelText = "排序")
        private Integer sort;
        @ComponentAttrField(labelText = "跳转url")
        private String moreLink;
        @ComponentAttrField(labelText = "tab名称")
        private String name;
        @ComponentAttrField(labelText = "分值描述")
        private String scoreDesc;
        @ComponentAttrField(labelText = "专题页分值描述")
        private String zhuanTiPageDesc;
    }
}
