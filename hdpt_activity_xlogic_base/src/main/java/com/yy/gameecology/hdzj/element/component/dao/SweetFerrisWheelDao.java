package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5149StarTaskRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Slf4j
@Repository
public class SweetFerrisWheelDao {

    public static final String INSERT_IGNORE = "insert ignore into cmpt_5149_star_task_record (act_id, task_level, member_id, uid, award_type, `state`, create_time, update_time) values (?, ?, ?, ?, ?, ?, ?, ?)";

    @Autowired
    private GameecologyDao gameecologyDao;

    @Transactional(rollbackFor = Throwable.class)
    public int saveStarTaskRecord(long actId, long taskLevel, String memberId, long userUid, long anchorUid, int awardType) {
        Date now = new Date();
        int rs = gameecologyDao.getJdbcTemplate().update(INSERT_IGNORE, actId, taskLevel, memberId, userUid, awardType, 0, now, now);
        log.info("saveStarTaskRecords with memberId:{} taskLevel:{} rs:{}", memberId, taskLevel, rs);
        if (rs <= 0) {
            return 0;
        }

        if (userUid != anchorUid) {
            rs = gameecologyDao.getJdbcTemplate().update(INSERT_IGNORE, actId, taskLevel, memberId, anchorUid, awardType, 0, now, now);
            log.info("saveStarTaskRecords with memberId:{} taskLevel:{} rs:{}", memberId, taskLevel, rs);
            if (rs <= 0) {
                throw new IllegalStateException("add anchor's record fail");
            }
        }

        return 1;
    }

    public List<Cmpt5149StarTaskRecord> getStarTaskRecords(long actId, String memberId, long uid) {
        Cmpt5149StarTaskRecord me = new Cmpt5149StarTaskRecord();
        me.setActId(actId);
        me.setMemberId(memberId);
        me.setUid(uid);

        return gameecologyDao.select(Cmpt5149StarTaskRecord.class, me, StringUtils.EMPTY);
    }

    public Cmpt5149StarTaskRecord getStarTaskRecord(long actId, long taskLevel, String memberId, long uid) {
        Cmpt5149StarTaskRecord me = new Cmpt5149StarTaskRecord();
        me.setActId(actId);
        me.setTaskLevel(taskLevel);
        me.setMemberId(memberId);
        me.setUid(uid);

        return gameecologyDao.selectOne(Cmpt5149StarTaskRecord.class, me, StringUtils.EMPTY);
    }

    public int updateStarTaskRecordReceived(long recordId, long taskId, long packageId) {
        Cmpt5149StarTaskRecord me = new Cmpt5149StarTaskRecord();
        me.setId(recordId);
        me.setState(0);
        Cmpt5149StarTaskRecord to = new Cmpt5149StarTaskRecord();
        to.setState(1);
        to.setTaskId(taskId);
        to.setPackageId(packageId);
        to.setUpdateTime(new Date());
        return gameecologyDao.update(Cmpt5149StarTaskRecord.class, me, to);
    }
}
