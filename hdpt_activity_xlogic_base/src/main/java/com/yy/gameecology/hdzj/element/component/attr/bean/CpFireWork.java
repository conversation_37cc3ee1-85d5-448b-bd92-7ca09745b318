package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

@Data
public class CpFireWork {

    @ComponentAttrField(labelText = "烟花秀id")
    protected long fid;

    @ComponentAttrField(labelText = "烟花秀名称")
    protected String name;

    @ComponentAttrField(labelText = "烟花秀logo")
    protected String logo;

    @ComponentAttrField(labelText = "broBusiness")
    protected int jyBusiness;

    @ComponentAttrField(labelText = "mp4Url")
    protected String mp4Url;

    @ComponentAttrField(labelText = "broLevel")
    protected int broLevel;

    @ComponentAttrField(labelText = "烟花有效秒数")
    private int validSec;
}
