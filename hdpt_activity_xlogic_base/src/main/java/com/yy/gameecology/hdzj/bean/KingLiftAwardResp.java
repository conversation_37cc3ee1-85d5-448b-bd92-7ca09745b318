package com.yy.gameecology.hdzj.bean;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022.03.11 14:49
 */
@Data
public class KingLiftAwardResp {

    /**
     * 返回0时处理成功,失败情况[10001 -> 该手机号已经超过领取限制])
     */
    private String code;

    /**
     * 成功时返回成功,其他返回错误描述
     */
    private String msg;
    /**
     * 签名,必填
     */
    private String sign;
    /**
     * 参考: [{"couponInstanceCode":"C88888888888","couponValidSTime":1570868525,"couponValidETime":1570868526}]
     */
    private List<CouponInfo> couponInfoList;

    public KingLiftAwardResp() {
        this.code = "0";
        this.msg = "成功";
    }

    public static class CouponInfo{
        /**
         * 优惠券编码,必填
         */
        private String couponInstanceCode;
        /**
         * 券生效开始时间,非必填,单位:s
         */
        private long couponValidSTime;

        /**
         * 券生效结束时间,非必填, 单位:s
         */
        private long couponValidETime;

        public CouponInfo(String couponInstanceCode) {
            this.couponInstanceCode = couponInstanceCode;
        }

        public String getCouponInstanceCode() {
            return couponInstanceCode;
        }

        public void setCouponInstanceCode(String couponInstanceCode) {
            this.couponInstanceCode = couponInstanceCode;
        }

        public long getCouponValidSTime() {
            return couponValidSTime;
        }

        public void setCouponValidSTime(long couponValidSTime) {
            this.couponValidSTime = couponValidSTime;
        }

        public long getCouponValidETime() {
            return couponValidETime;
        }

        public void setCouponValidETime(long couponValidETime) {
            this.couponValidETime = couponValidETime;
        }
    }
}
