package com.yy.gameecology.hdzj.element.doc;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/15 14:16
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MethodCommentBean {
    private String methodName;
    private String commentText;

    private ParamCommentBean returnParam;

    private List<ParamCommentBean> inputParams;

    private boolean isEventHandler;

    public void addParamBean(ParamCommentBean paramBean) {
        if (inputParams == null) {
            inputParams = new ArrayList<>();
        }
        inputParams.add(paramBean);
    }
}
