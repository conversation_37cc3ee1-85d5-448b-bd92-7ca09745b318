package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableSortedMap;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.db.model.gameecology.ComponentWhitelist;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.RankScoreNoticeComponentAttr;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.Base64Utils;

import java.util.List;
import java.util.Map;


/**
 * 1. 监听日榜榜单,判断是否达到门槛
 * 2. 附加上总榜的数据
 *
 * @Author: CXZ
 * @Desciption: 榜单分数通知组件
 * @Date: 2022/4/20 10:25
 * @Modified:
 */
@Component
public class RankScoreNoticeComponent extends BaseActComponent<RankScoreNoticeComponentAttr> {
    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private WhitelistComponent whitelistComponent;


    private static final String QUERY_RANK_KEY = "query_rank_key";

    private static final String ROBOT_TOKEN_PREFIX = "https://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=";

    @Override
    public Long getComponentId() {
        return ComponentId.RANK_SCORE_NOTICE;
    }

    /**
     * 响应榜单变化事件
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, RankScoreNoticeComponentAttr attr) {
        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }
        String memberId = event.getMember();
        ImmutableSortedMap<Long, String> sortedMap = ImmutableSortedMap.copyOf(attr.getScoreMap());

        // 当前日榜的分数
        long score = attr.getPhaseId() > 0 ? event.getPhaseScore() : event.getRankScore();
        long lastScore = score - event.getItemScore();

        Map.Entry<Long, String> currentEntry = sortedMap.floorEntry(score);
        Map.Entry<Long, String> lastEntry = sortedMap.floorEntry(lastScore);

        boolean isIgnore = (currentEntry == null || currentEntry.equals(lastEntry));
        if (isIgnore) {
            log.info("onRankingScoreChanged ignore");
            return;
        }

        long extScore = 0L;
        if (attr.getExtQueryRankId() > 0) {
            Map<String, String> ext = Maps.newHashMap();
            if (StringUtils.isNotBlank(attr.getExtQueryKey())) {
                ext.put(attr.getExtQueryKey(), "");
            }
            Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(attr.getActId(), attr.getExtQueryRankId(), attr.getExtQueryPhaseId(), "", memberId, ext);
            extScore = rank.getScore();
        }

        RoleType roleType = RoleType.findByValue(attr.getRoleType());
        Assert.notNull(roleType, "角色配置错误");
        String format = attr.getNoticeFormat();
        Assert.hasText(format, "消息模板配置错误");

        MemberInfo memberInfo = memberInfoService.getMemberInfo(event.getBusiId(), roleType, event.getMember());
        String whiteName = whitelistComponent.getConfigValue(attr.getActId(), attr.getWhitelistCmptInx(), event.getMember());
        if(StringUtils.isNotEmpty(whiteName)) {
            memberInfo.setName(whiteName);
        }

        //替换宏
        String msg = format
                .replace("{{memberId}}", event.getMember())
                .replace("{{logo}}", Convert.toString(memberInfo.getLogo()))
                .replace("{{name}}", Convert.toString(memberInfo.getName()))
                .replace("{{asid}}", Convert.toLong(memberInfo.getAsid()) + "")
                .replace("{{sid}}", Convert.toLong(memberInfo.getSid()) + "")

                .replace("{{score}}", score + "")
                .replace("{{addScore}}", event.getItemScore() + "")
                .replace("{{scoreThreshold}}", currentEntry.getKey() + "")
                .replace("{{thresholdValue}}", currentEntry.getValue())

                .replace("{{extScore}}", extScore + "")
                .replace("{{date}}", DateUtil.getNowYyyyMMddHHmmss())
                .replace("<br/>", "\r\n");

        log.info("onRankingScoreChanged msg:{},attr:{}", msg, JSON.toJSON(attr));

        if(StringUtils.isNotBlank(attr.getGgbpLink())) {
            String ggbpLink = getGgbpLink(attr,Convert.toString(memberInfo.getName()),Convert.toString(memberInfo.getLogo()),currentEntry.getKey(), currentEntry.getValue());
            msg = msg.replace("{{rankScoreBannerLink}}", ggbpLink);
        }

        String subChannel = event.getActors().values().stream().filter(i -> i.contains("_")).findFirst().orElse("");
        if(StringUtils.isNotBlank(subChannel)){
            String[] channelArr = subChannel.split("_");
            String fly = String.format("yy://pd-[sid=%s&subid=%s]", channelArr[0], channelArr[1]);
            msg = msg.replace("{{fly}}", fly);
        }

        sendNotice(msg, attr);
        log.info("onRankingScoreChanged done@event:{}", JSON.toJSONString(event));
    }

    private String getGgbpLink(RankScoreNoticeComponentAttr attr,String name,String logo,long scoreThreshold,String scoreText) {
        long nowTime = commonService.getNow(attr.getActId()).getTime();
        String link = attr.getGgbpLink();
        int noticeType = 1;
        if(scoreThreshold>=attr.getBpScoreMinLimit()){
            noticeType = 2;
        }
        log.info("getGgbpLink noticeType:{},name:{},logo:{},thresholdValue:{}",noticeType,name,logo,scoreText);
        link = link+"?"+"noticeType="+noticeType+"&name="+ Base64Utils.encodeToString(name.getBytes()) +"&logo="+Base64Utils.encodeToString(logo.getBytes())
                + "&thresholdValue="+Base64Utils.encodeToString(scoreText.getBytes()) +"&nowTime="+nowTime;
        return link;

    }
    private void sendNotice(String msg, RankScoreNoticeComponentAttr attr) {

        // 优先使用配置的如流群,否则使用默认
        if (attr.getGroupId() > 0 && StringUtils.isNotEmpty(attr.getRobotToken())) {
            String robotToken = attr.getRobotToken().trim();
            if (!robotToken.contains(ROBOT_TOKEN_PREFIX)) {
                robotToken = ROBOT_TOKEN_PREFIX + robotToken;
            }
            baiduInfoFlowRobotService.sendNotify(attr.getGroupId(), robotToken, msg, attr.getUserIds());
        } else {
            baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT, msg, attr.getUserIds());
        }

        log.info("sendNotice done@msg:{},groupId:{}", msg, attr.getGroupId());
    }




}
