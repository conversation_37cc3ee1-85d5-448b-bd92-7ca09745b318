package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.yrpc.DanmakuActivityClient;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.common.db.model.gameecology.BroadcastTimerConfig;
import com.yy.gameecology.common.db.model.gameecology.HdzjComponent;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 赛点通知组件
 * 注意：一个活动只能配置本组件，且cmptInxUse 必须为1，不满足此条件将拒绝服务！
 *
 * <AUTHOR>
 * @date 2021/4/20 19:40
 */
@Component
public class MatchPointNotifyComponent extends BaseActComponent<ComponentAttr> {

    @Autowired
    private CommonDataDao commonDataDao;

    private long counter = 0;

    private String EXCLUDE_DANMAKU = "excludeDanmaku";

    @Autowired
    private DanmakuActivityClient danmakuActivityClient;

    @Override
    public Long getComponentId() {
        return ComponentId.MATCH_POINT_NOTIFY;
    }

    @Override
    public boolean isUniq1UseIndex() {
        return true;
    }

    @Override
    public ComponentAttr getComponentAttr(long actId, long cmptUseInx) {
        long cmptId = this.getComponentId();
        HdzjComponent hdzjComponent = cacheService.getHdzjComponent(actId, cmptId, cmptUseInx);
        if (hdzjComponent == null) {
            return null;
        }

        ComponentAttr attr = new ComponentAttr();
        attr.setCmptId(cmptId);
        attr.setActId(actId);
        attr.setCmptUseInx(cmptUseInx);
        attr.setExtjson(StringUtil.trim(hdzjComponent.getExtjson()));
        return attr;
    }

    /***
     * 赛点广播
     */
    @Scheduled(cron = "3/25 * * * * ?")
    @NeedRecycle(author = "guoliping", notRecycle = true)
    public void exec() {
        if (SysEvHelper.isLocal()) {
            return;
        }
        counter++;
        Long cmptId = this.getComponentId();
        Clock clock = new Clock();
        try {
            // 优雅停机：已关闭，不再去抢锁, 直接返回
            if (ShutdownHolder.isShuttingDown()) {
                log.warn("{}) exec fail@cmptId:{} application is shutdown. stop timer!", counter, cmptId);
                return;
            }

            Set<Long> actIds = this.getComponentEffectActIds();
            if (CollectionUtils.isEmpty(actIds)) {
                log.info("{}) exec skip@cmptId:{}, no activity need process", counter, cmptId);
                return;
            }

            List<ActivityInfoVo> effectActInfos = hdztRankingThriftClient.queryEffectActInfos();
            if (CollectionUtils.isEmpty(effectActInfos)) {
                log.info("{}) exec skip@cmptId:{}, hdzt no activity need process", counter, cmptId);
                return;
            }

            //乱序，让机器能相对均匀的处理任务
            List<Long> effectActIds = effectActInfos.stream().map(ActivityInfoVo::getActId).collect(Collectors.toList());
            Collections.shuffle(effectActIds);
            for (Long actId : effectActIds) {
                if (actIds.contains(actId)) {
                    ComponentAttr attr = this.getUniqueComponentAttr(actId);
                    if (attr == null) {
                        log.warn("{}) exec skip@actId:{} no uniq cmptId:{} cmptUseInx:1 attribute!", counter, actId, cmptId);
                    } else {
                        doMathPointBroadcast(actId, attr);
                    }
                }
            }

            log.info("{}) exec done@cmptId:{}, effectActIds:{}, actIds:{} {}", counter, cmptId, effectActIds, actIds.size(), clock.tag());
        } catch (Throwable t) {
            log.error("{}) exec exception@cmptId:{}, err:{} {}", counter, cmptId, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 做赛点广播
     */
    protected void doMathPointBroadcast(Long actId, ComponentAttr attr) {
        if(!actInfoService.inActShowTime(actId)){
            log.info("not in act show time");
            return;
        }
        // 对活动当前分钟锁定 3 分钟，锁失败直接返回(定时器执行频度要够小如1分钟才安全）
        String curTime = commonService.getNowDateTime(actId).format(DateUtil.YYYY_MM_DD_HH_MM);
        String value = DateUtil.today() + ", " + SystemUtil.getWorkerBrief();
        boolean rs = commonDataDao.hashValueSetNX(actId, attr.getCmptId(), attr.getCmptUseInx(), "MatchPointNotifyComponent:doMathPointBroadcast", curTime, value);
        if (!rs) {
            return;
        }

        // 取出当前分钟的所有广播配置
        BroadcastTimerConfig where = new BroadcastTimerConfig();
        where.setBroadcastTime(curTime);
        where.setStatus(1);
        where.setActId(actId);
        List<BroadcastTimerConfig> configs = gameecologyDao.getBroadcastTimerConfigs(where);
        log.info("{}) doMathPointBroadcast start@actId:{}, curTime:{}, size:{}", counter, actId, curTime, configs.size());
        if (CollectionUtils.isEmpty(configs)) {
            return;
        }

        // 遍历发出每一个广播
        int inx = 0;
        int size = configs.size();
        for (BroadcastTimerConfig config : configs) {
            processOneConfig(curTime, ++inx, size, config);
        }
    }

    /**
     * 处理一个广播配置
     */
    protected void processOneConfig(String curTime, int inx, int size, BroadcastTimerConfig config) {
        Clock clock = new Clock();
        long actId = config.getActId();
        long id = config.getId();
        String ranges = config.getBroadcastRanges();
        try {
            GameecologyActivity.Act202010_MatchPointNotify.Builder task = GameecologyActivity.Act202010_MatchPointNotify.newBuilder()
                    .setId(id).setActId(config.getActId()).setContent(config.getBroadcastTxt()).setExtjson("");
            GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.kAct202010_MatchPointNotify_VALUE)
                    .setAct202010MatchPointNotify(task).build();
            for (String range : ranges.split(StringUtil.COMMA)) {
                if ("4".equals(ranges)) {
                    svcSDKService.broadcastAllChanelsInPW(actId, msg);
                } else if ("5".equals(ranges)) {
                    svcSDKService.broadcastAllChanelsInSkillCard(msg);
                } else {
                    Template tpl = Template.findByValue(Integer.parseInt(range));
                    if (tpl != null) {
                        List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
                        Set<String> exclude = danmakuChannel.stream().map(channel -> channel.getSid() + StringUtil.UNDERSCORE + channel.getSsid()).collect(Collectors.toSet());
                        if(!StringUtil.isEmpty(config.getExtJson()) && config.getExtJson().contains(EXCLUDE_DANMAKU)) {
                            svcSDKService.broadcastTemplateExclude(tpl, msg, exclude);
                        } else {
                            svcSDKService.broadcastTemplate(tpl, msg);
                        }
                    } else {
                        log.error("{}) processOneConfig fail@inx:{}/{}, actId:{}, id:{}, curTime:{}, range:{}", counter,
                                inx, size, actId, id, curTime, range);
                    }
                }
            }
            log.info("{}) processOneConfig done@inx:{}/{}, actId:{}, id:{}, curTime:{}, ranges:{} {}", counter,
                    inx, size, actId, id, curTime, ranges, clock.tag());
        } catch (Throwable t) {
            log.error("{}) processOneConfig exception@inx:{}/{}, actId:{}, id:{}, curTime:{}, ranges:{}, err:{} {}",
                    counter, inx, size, actId, id, curTime, ranges, t.getMessage(), clock.tag(), t);
        }
    }
}
