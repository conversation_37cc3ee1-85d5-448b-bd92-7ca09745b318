package com.yy.gameecology.hdzj.element.attrconfig;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/25 10:02
 **/
@Component
public class FrequencySource implements DropDownSource {
    // [{"code":"0","desc":"不限制(0)"},{"code":"1","desc":"活动期间只弹一次(1)"},{"code":"2","desc":"每日只弹一次(2)"}]
    @Override
    public List<DropDownVo> listDropDown() {
        return Arrays.asList(
                new DropDownVo("0", "不限制(0)"),
                new DropDownVo("1", "活动期间只弹一次(1)"),
                new DropDownVo("2", "每日只弹一次(2)")
        );
    }
}
