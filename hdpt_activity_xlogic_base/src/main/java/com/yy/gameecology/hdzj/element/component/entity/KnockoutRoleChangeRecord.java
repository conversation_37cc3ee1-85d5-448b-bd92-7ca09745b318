package com.yy.gameecology.hdzj.element.component.entity;

import com.yy.gameecology.common.db.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * 淘汰赛角色变更记录表
 * 用于存储主播角色变更记录，替代Redis的ROLE_CHANGE_RECORD集合
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@TableColumn(underline = true)
public class KnockoutRoleChangeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5159_role_change_";

    public static String getTableName(long actId) {
        return TABLE_NAME + actId;
    }

    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<KnockoutRoleChangeRecord> ROW_MAPPER = (rs, rowNum) -> {
        KnockoutRoleChangeRecord result = new KnockoutRoleChangeRecord();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getLong("cmpt_use_inx"));
        result.setMemberId(rs.getString("member_id"));
        result.setOldRoleId(rs.getLong("old_role_id"));
        result.setNewRoleId(rs.getLong("new_role_id"));
        result.setChangeSeq(rs.getString("change_seq"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        return result;
    };

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 组件使用索引
     */
    private Long cmptUseInx;

    /**
     * 主播ID
     */
    private String memberId;

    /**
     * 原角色ID
     */
    private Long oldRoleId;

    /**
     * 新角色ID
     */
    private Long newRoleId;

    /**
     * 变更序列号
     */
    private String changeSeq;

    /**
     * 创建时间
     */
    private Date createTime;
}
