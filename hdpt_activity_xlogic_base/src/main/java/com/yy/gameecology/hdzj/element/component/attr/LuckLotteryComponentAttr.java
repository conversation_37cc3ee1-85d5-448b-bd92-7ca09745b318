package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class LuckLotteryComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "礼物数和钥匙数的比配置", subFields = {
            @SubField(labelText = "活动礼物id", type = String.class, fieldName = Constant.KEY1, remark = "活动礼物id"),
            @SubField(labelText = "礼物数和钥匙数的比", type = String.class, fieldName = Constant.VALUE, remark = "礼物数/钥匙数(1)")
    })
    private Map<String, String> giftNum2LuckyKeyNum = Maps.newHashMap();

    @ComponentAttrField(labelText = "任务榜单Id", remark = "多个时逗号分隔", subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private List<Long> rankIds;

    /**
     * 金宝箱 taskId
     */
    @ComponentAttrField(labelText = "金宝箱奖池id")
    private long goldTaskId;

    /**
     * 银宝箱taskId
     */
    @ComponentAttrField(labelText = "银宝箱奖池id")
    private long silverTaskId;

    @ComponentAttrField(labelText = "广播跳转链接")
    private String broJumpUrl;

    @ComponentAttrField(labelText = "钥匙不足提示文本")
    private String errorMsg;

    @ComponentAttrField(labelText = "是否允许抽奖")
    private boolean canLottery = true;

}
