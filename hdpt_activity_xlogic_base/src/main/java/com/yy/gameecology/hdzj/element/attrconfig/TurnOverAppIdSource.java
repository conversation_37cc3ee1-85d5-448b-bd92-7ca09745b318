package com.yy.gameecology.hdzj.element.attrconfig;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/10 10:55
 **/
@Component
public class TurnOverAppIdSource implements DropDownSource {
    // [{\"code\":\"2\",\"desc\":\"交友(2)\"},{\"code\":\"14\",\"desc\":\"约战(14)\"},{\"code\":\"36\",\"desc\":\"宝贝(36)\"}]
    @Override
    public List<DropDownVo> listDropDown() {
        return Arrays.asList(
                new DropDownVo("2", "交友(2)"),
                new DropDownVo("14", "约战(14)"),
                new DropDownVo("36", "宝贝(36)")
        );
    }
}
