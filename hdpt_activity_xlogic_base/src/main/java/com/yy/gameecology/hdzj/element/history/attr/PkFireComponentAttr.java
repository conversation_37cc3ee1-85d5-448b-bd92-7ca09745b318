package com.yy.gameecology.hdzj.element.history.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;

import java.util.Date;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-04-26 18:18
 **/
public class PkFireComponentAttr extends ComponentAttr {

    /**
     * 累pk值的数据来源榜
     */
    @ComponentAttrField(labelText = "累pk值的数据来源榜")
    private long addPkScoreHdztRankId;

    /**
     * 榜单top 几可以获取主动发起pk资格
     */
    @ComponentAttrField(labelText = "榜单topN", remark = "榜单top 几可以获取主动发起pk资格")
    private int awardStartPkChanceTopN;


    /**
     * 可以参与pk玩法的成员， awardPkTopN - awardStartPkChanceTopN 剩余的人只能被动接受pk
     */
    @ComponentAttrField(labelText = "参与玩法的TopN", remark = "减去榜单topN剩余的人只能被动接受pk")
    private int awardPkTopN;

    /**
     * 结算奖励pk卡的榜单
     */
    @ComponentAttrField(labelText = "结算奖励pk卡的榜单")
    private long awardCardSourceRankId;

    /**
     * 结算奖励pk卡的来源阶段
     */
    @ComponentAttrField(labelText = "结算奖励pk卡阶段")
    private long awardCardSourcePhaseId;


    /**
     * pk对象的角色
     */
    @ComponentAttrField(labelText = "pk对象的角色")
    private int pkRoleType;

    /**
     * 奖励的pk次数
     */
    @ComponentAttrField(labelText = "奖励的pk次数")
    private int awardPkChanceAmount;

    /**
     * 获取pk数量的最大限制
     */
    @ComponentAttrField(labelText = "pk数的最大限制")
    private int queryPkHisMaxCount = 1000;

    /**
     * pk时长（毫秒）
     */
    @ComponentAttrField(labelText = "pk时长（毫秒）")
    private long pkMillSeconds = 60 * 60 * 1000;

    /**
     * 发起pk结束时间
     */
    @ComponentAttrField(labelText = "发起pk结束时间")
    private Date startPkEndTime;

    /**
     * 挂件展示开始时间,闭区间 yyyy-MM-dd
     */
    @ComponentAttrField(labelText = "挂件展示开始时间", remark = "闭区间 yyyy-MM-dd")
    private String showLayerBeginDate;

    /**
     * 挂件展示开始小时，闭区间
     */
    @ComponentAttrField(labelText = "挂件展示开始小时", remark = "闭区间")
    private int showLayerBeginHour;

    /**
     * 挂件展示结束时间,闭区间  yyyy-MM-dd
     */
    @ComponentAttrField(labelText = "挂件展示结束时间", remark = "闭区间  yyyy-MM-dd")
    private String showLayerEndDate;

    /**
     * 挂件展示结束小时，闭区间，跨天的时候，结束时间小于开时间
     */
    @ComponentAttrField(labelText = "挂件展示结束小时", remark = "闭区间，跨天的时候，结束时间小于开时间")
    private int showLayeyEndHour;


    public int getAwardStartPkChanceTopN() {
        return awardStartPkChanceTopN;
    }

    public void setAwardStartPkChanceTopN(int awardStartPkChanceTopN) {
        this.awardStartPkChanceTopN = awardStartPkChanceTopN;
    }

    public int getAwardPkTopN() {
        return awardPkTopN;
    }

    public void setAwardPkTopN(int awardPkTopN) {
        this.awardPkTopN = awardPkTopN;
    }

    public int getPkRoleType() {
        return pkRoleType;
    }

    public void setPkRoleType(int pkRoleType) {
        this.pkRoleType = pkRoleType;
    }

    public int getAwardPkChanceAmount() {
        return awardPkChanceAmount;
    }

    public void setAwardPkChanceAmount(int awardPkChanceAmount) {
        this.awardPkChanceAmount = awardPkChanceAmount;
    }

    public int getQueryPkHisMaxCount() {
        return queryPkHisMaxCount;
    }

    public void setQueryPkHisMaxCount(int queryPkHisMaxCount) {
        this.queryPkHisMaxCount = queryPkHisMaxCount;
    }

    public long getAddPkScoreHdztRankId() {
        return addPkScoreHdztRankId;
    }

    public void setAddPkScoreHdztRankId(long addPkScoreHdztRankId) {
        this.addPkScoreHdztRankId = addPkScoreHdztRankId;
    }

    public long getPkMillSeconds() {
        return pkMillSeconds;
    }

    public void setPkMillSeconds(long pkMillSeconds) {
        this.pkMillSeconds = pkMillSeconds;
    }

    public String getShowLayerBeginDate() {
        return showLayerBeginDate;
    }

    public void setShowLayerBeginDate(String showLayerBeginDate) {
        this.showLayerBeginDate = showLayerBeginDate;
    }

    public int getShowLayerBeginHour() {
        return showLayerBeginHour;
    }

    public void setShowLayerBeginHour(int showLayerBeginHour) {
        this.showLayerBeginHour = showLayerBeginHour;
    }

    public String getShowLayerEndDate() {
        return showLayerEndDate;
    }

    public void setShowLayerEndDate(String showLayerEndDate) {
        this.showLayerEndDate = showLayerEndDate;
    }

    public int getShowLayeyEndHour() {
        return showLayeyEndHour;
    }

    public void setShowLayeyEndHour(int showLayeyEndHour) {
        this.showLayeyEndHour = showLayeyEndHour;
    }

    public long getAwardCardSourceRankId() {
        return awardCardSourceRankId;
    }

    public void setAwardCardSourceRankId(long awardCardSourceRankId) {
        this.awardCardSourceRankId = awardCardSourceRankId;
    }

    public long getAwardCardSourcePhaseId() {
        return awardCardSourcePhaseId;
    }

    public void setAwardCardSourcePhaseId(long awardCardSourcePhaseId) {
        this.awardCardSourcePhaseId = awardCardSourcePhaseId;
    }

    public Date getStartPkEndTime() {
        return startPkEndTime;
    }

    public void setStartPkEndTime(Date startPkEndTime) {
        this.startPkEndTime = startPkEndTime;
    }
}
