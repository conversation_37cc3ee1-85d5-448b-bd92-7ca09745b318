package com.yy.gameecology.hdzj.delay;

import com.yy.gameecology.activity.bean.mq.DelayedEvent;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;

/**
 * desc:接入振宇的funDelay
 *
 * <AUTHOR>
 * @date 2025-07-15 16:32
 **/
@Component
public class ComponentFunDelay {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 记录被注册了哪些组件延迟队列
     */
    private static final ConcurrentHashMap<DelayKey, DelayComponent<?>> DELAY_COMPONENTS = new ConcurrentHashMap<>();

    /**
     * 延时事件发布
     */
    public void publishDelayEvent(Long actId, Long componentId, Long componentIndex, String key, Object event, long expiredMills) {

    }

    public <T extends ComponentAttr> void register(BaseActComponent<T> component, String key, BiConsumer<T, Object> consumer) {
        DelayKey delayKey = new DelayKey(component.getComponentId(), key);
        DELAY_COMPONENTS.computeIfAbsent(delayKey, k -> {
            DelayComponent<T> delayComponent = new DelayComponent<>();
            delayComponent.setComponent(component);
            delayComponent.setConsumer(consumer);
            log.info("registered delayKey:{}", delayKey);
            return delayComponent;
        });
    }

    public void invokeDelayEvent(DelayedEvent event) {
        String body = event.getBody();

        //TODO
        DelayKey delayKey = new DelayKey();
        if (!DELAY_COMPONENTS.containsKey(delayKey)) {
            log.error("invokeDelayEvent delayKey:{} not exist", delayKey);
            return;
        }
        DelayComponent<?> delayComponent = DELAY_COMPONENTS.get(delayKey);
        var attr = delayComponent.getComponent().getComponentAttr(1L,1L);
        delayComponent.getConsumer().accept(attr, body);

    }


}
