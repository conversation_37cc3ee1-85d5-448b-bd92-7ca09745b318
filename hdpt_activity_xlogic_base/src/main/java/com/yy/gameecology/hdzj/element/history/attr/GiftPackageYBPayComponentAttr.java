package com.yy.gameecology.hdzj.element.history.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;

/**
 * @Author: CXZ
 * @Desciption: 礼包属性
 * @Date: 2021/4/15 19:19
 * @Modified:
 */
public class GiftPackageYBPayComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    private long busiId;

    /**
     * 是否需要限制用户的购买资格
     */
    @ComponentAttrField(labelText = "限制购买资格", remark = "是否需要限制用户的购买资格")
    private boolean isLimitQualification = true;

    /**
     * 限制允许购买或者不允许购买
     */
    @ComponentAttrField(labelText = "是否白名单", remark = "true：启用白名单,false:启用白名单;当开启了限制购买资格时,才需要进行黑白名单检验")
    private boolean allow = true;

    /**
     * 存放限制用户名单的redis set key ,需要直接制定key的全称，不会再加前缀了
     */
    @ComponentAttrField(labelText = "用户名单缓存key", remark = "当启用白名单时,这里存放的是白名单用户;否则这里存放的是黑名单用户;存放限制用户名单的redis set key ,需要直接制定key的全称，不会再加前缀了")
    private String limitUidRedisKey = "";
    /**
     * 被限制的提醒
     */
    @ComponentAttrField(labelText = "被限制的提醒", remark = "没有购买资格时的提醒文案")
    private String limitQualificationTip = "";

    /**
     * 限制用户购买数量，小于等于0是不限制
     */
    @ComponentAttrField(labelText = "用户购买数量", remark = "限制用户购买数量，小于等于0是不限制")
    private int limitBugCount = 0;
    /**
     * 购买数量限制提示
     */
    @ComponentAttrField(labelText = "购买数量限制提示", remark = "超过购买数量限制时,提示该文案")
    private String limitBugCountTip = "";

    /**
     * 同个ip限制购买的秒数
     */
    @ComponentAttrField(labelText = "同个ip限制购买的秒数", remark = "限制通过ip多少秒内只能请求一次")
    private long ipLimitSec;
    /**
     * 营收扣费标识，找营收配
     */
    @ComponentAttrField(labelText = "营收扣费标识", remark = "营收扣费标识，找营收配")
    private int pType;

    /**
     * 礼包扣费价格（以紫宝石单位，1:1000）
     */
    @ComponentAttrField(labelText = "礼包扣费价格", remark = "礼包扣费价格（以紫宝石单位，1:1000）")
    private long price = 100;
    /**
     * 礼包详情，传给营收
     */
    @ComponentAttrField(labelText = "礼包详情", remark = "礼包详情，传给营收")
    private String giftDes = "";


    /**
     * 礼包发放的taskId
     */
    @ComponentAttrField(labelText = "奖池id", remark = "礼包发放的奖池id")
    private long taskId;
    /**
     * 礼包发放的packageId
     */
    @ComponentAttrField(labelText = "奖包id", remark = "礼包发放的packageId")
    private long packageId;
    /**
     * 礼包发放的数量
     */
    @ComponentAttrField(labelText = "礼包发放的数量")
    private int count;

    public long getBusiId() {
        return busiId;
    }

    public void setBusiId(long busiId) {
        this.busiId = busiId;
    }

    public boolean isLimitQualification() {
        return isLimitQualification;
    }

    public void setLimitQualification(boolean limitQualification) {
        isLimitQualification = limitQualification;
    }

    public boolean isAllow() {
        return allow;
    }

    public void setAllow(boolean allow) {
        this.allow = allow;
    }

    public String getLimitUidRedisKey() {
        return limitUidRedisKey;
    }

    public void setLimitUidRedisKey(String limitUidRedisKey) {
        this.limitUidRedisKey = limitUidRedisKey;
    }

    public String getLimitQualificationTip() {
        return limitQualificationTip;
    }

    public void setLimitQualificationTip(String limitQualificationTip) {
        this.limitQualificationTip = limitQualificationTip;
    }

    public int getLimitBugCount() {
        return limitBugCount;
    }

    public void setLimitBugCount(int limitBugCount) {
        this.limitBugCount = limitBugCount;
    }

    public String getLimitBugCountTip() {
        return limitBugCountTip;
    }

    public void setLimitBugCountTip(String limitBugCountTip) {
        this.limitBugCountTip = limitBugCountTip;
    }

    public long getIpLimitSec() {
        return ipLimitSec;
    }

    public void setIpLimitSec(long ipLimitSec) {
        this.ipLimitSec = ipLimitSec;
    }

    public int getpType() {
        return pType;
    }

    public void setpType(int pType) {
        this.pType = pType;
    }

    public long getPrice() {
        return price;
    }

    public void setPrice(long price) {
        this.price = price;
    }

    public String getGiftDes() {
        return giftDes;
    }

    public void setGiftDes(String giftDes) {
        this.giftDes = giftDes;
    }

    public long getTaskId() {
        return taskId;
    }

    public void setTaskId(long taskId) {
        this.taskId = taskId;
    }

    public long getPackageId() {
        return packageId;
    }

    public void setPackageId(long packageId) {
        this.packageId = packageId;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
