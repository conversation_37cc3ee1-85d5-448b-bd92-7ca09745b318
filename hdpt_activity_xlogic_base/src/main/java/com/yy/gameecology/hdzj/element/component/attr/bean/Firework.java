package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrCollector;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

@Data
public class Firework {

    @ComponentAttrField(labelText = "烟花秀id")
    protected int fid;

    @ComponentAttrField(labelText = "烟花秀名称")
    protected String name;

    @ComponentAttrField(labelText = "烟花秀logo", propType = ComponentAttrCollector.PropType.IMAGE)
    protected String logo;

    @ComponentAttrField(labelText = "资产id")
    protected String cid;

    @ComponentAttrField(labelText = "taskId")
    protected long taskId;

    @ComponentAttrField(labelText = "packageId")
    protected long packageId;

    @ComponentAttrField(labelText = "broBusiness")
    protected int jyBusiness;

    @ComponentAttrField(labelText = "mp4Url")
    protected String mp4Url;

    @ComponentAttrField(labelText = "broLevel")
    protected int broLevel;
}
