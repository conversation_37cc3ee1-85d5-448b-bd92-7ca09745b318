package com.yy.gameecology.hdzj.bean;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.utils.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class SuperWinnerBox {
    private String time = DateUtil.today();

    private String boxId = null;

    private int template = -1;

    private long busiId = -1;

    private long cmptUseInx = -1;

    private long uid;

    private long sid;

    private long ssid;

    private String seq;

    private String giftId;

    private long giftNum;

    private long combo;

    public SuperWinnerBox() {
    }

    public SuperWinnerBox(String seq, int template, long busiId, long cmptUseInx, long uid, long sid, long ssid, String giftId, long giftNum, long combo) {
        String uuid = java.util.UUID.randomUUID().toString().replace("-", "");
        this.template = template;
        this.busiId = busiId;
        this.cmptUseInx = cmptUseInx;
        this.uid = uid;
        this.sid = sid;
        this.ssid = ssid;
        this.seq = seq;
        this.giftId = giftId;
        this.giftNum = giftNum;
        this.combo = combo;
        this.boxId = cmptUseInx + "_" + template + "_" + uuid;
    }

    public String toString() {
        return JSON.toJSONString(this);
    }

    public static void main(String[] args) {
        String uuid = java.util.UUID.randomUUID().toString();
        System.out.println(uuid);
        System.out.println(uuid.replace("-", ""));
    }
}
