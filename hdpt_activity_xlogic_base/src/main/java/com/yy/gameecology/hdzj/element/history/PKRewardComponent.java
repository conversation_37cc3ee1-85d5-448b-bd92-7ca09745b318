package com.yy.gameecology.hdzj.element.history;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.rank.SubGuildRankItem;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.processor.ranking.ComponentRankingExtHandle;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.PKMembers;
import com.yy.gameecology.hdzj.element.history.attr.PKRewardComponentAttr;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import com.yy.thrift.hdztranking.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yy.gameecology.hdzj.consts.ComponentId.PK_REWARD;

/**
 * <AUTHOR>
 * @since 2021/7/2
 */
@Deprecated
@Component
@Slf4j
public class PKRewardComponent extends BaseActComponent<PKRewardComponentAttr> implements
        ComponentRankingExtHandle<PKRewardComponentAttr> {

    public static final String UNDERLINE = "_";

    /**
     * 榜单变化事件
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void onRankingScoreChanged(RankingScoreChanged event, PKRewardComponentAttr attr) {
        log.info("PKRewardComponent RankingScoreChangedEvent:{}", JSONUtils.toJsonString(event));
        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }
        String groupCode = redisConfigManager.getGroupCode(event.getActId());
        List<String> keys = Lists
                .newArrayList(getSettleFenceKey(event.getMember(), event.getOccurTime(), attr),
                        getPKRewardInfoKey(event.getMember(), attr));
        List<String> args = Lists.newArrayList(getPKRewardHashField(event.getOccurTime(), attr),
                calculatePKRewardByRankScore(event.getRankScore(), attr));
        actRedisDao.executeLua(groupCode, attr.getAddRewardLua(), Long.class, keys, args);
    }

    /**
     * pk结束，计算赏金
     */
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = false)
    public void onPKRankEnd(PhaseTimeEnd event, PKRewardComponentAttr attr) {
        log.info("PKRewardComponent PhaseTimeEnd:{}", JSONUtils.toJsonString(event));
        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        if (idempotent(groupCode, event.getEkey(), attr)) {
            log.info("PhaseTimeEnd had been fired,eky:{},rankId:{}", event.getEkey(), event.getRankId());
            return;
        }

        Table<String, String, String> rewards = getPKMembers(event, attr)
                .stream()
                .filter(pk -> !pk.isTie())
                .map(pk -> {
                    setSettleFence(event, attr, pk);
                    Pair<String, Map<String, String>> win = getRewards(groupCode, attr, pk.getWinner().getMemberId(),
                            getEventTime(event.getEndTime()));
                    Pair<String, Map<String, String>> loser = getRewards(groupCode, attr, pk.getLoser().getMemberId(),
                            getEventTime(event.getEndTime()));
                    return calculatePKReward(win, loser, attr);
                })
                .reduce(HashBasedTable.create(), (t1, t2) -> {
                    t1.putAll(t2);
                    return t1;
                });
        rewards.rowMap()
                .forEach((key, value) -> actRedisDao.getRedisTemplate(groupCode).opsForHash().putAll(key, value));
    }

    private boolean idempotent(String groupCode, String ekey, PKRewardComponentAttr attr) {
        if (StringUtils.isNotBlank(ekey)) {
            final boolean rs = actRedisDao.setNX(groupCode, makeKey(attr, ekey), "1");
            if (!rs) {
                return true;
            }
        }
        return false;
    }

    protected Table<String, String, String> calculatePKReward(
            Pair<String, Map<String, String>> winnerReward, Pair<String, Map<String, String>> loserReward,
            PKRewardComponentAttr attr) {
        Table<String, String, String> rewardTables = HashBasedTable
                .create(2, winnerReward.getRight().size());

        winnerReward.getRight().keySet().forEach(key -> {
            Pair<Long, Long> rewards = robPKReward(winnerReward.getRight().get(key),
                    loserReward.getRight().get(key), attr);
            rewardTables.put(winnerReward.getKey(), key, String.valueOf(rewards.getLeft()));
            rewardTables.put(loserReward.getKey(), key, String.valueOf(rewards.getRight()));
        });
        return rewardTables;
    }

    protected Pair<Long, Long> robPKReward(String winnerReward, String loserReward,
                                           PKRewardComponentAttr attr) {
        long wr = NumberUtils.toLong(winnerReward);
        long lr = NumberUtils.toLong(loserReward);
        long rob = lr / attr.getRewardRobRatio();
        return Pair.of(wr + rob, lr - rob);
    }

    private LocalDateTime getEventTime(String eventTime) {
        return LocalDateTime.parse(eventTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }


    private Pair<String, Map<String, String>> getRewards(String groupCode, PKRewardComponentAttr attr, String memberId,
                                                         LocalDateTime now) {
        Map<String, String> rewards = Maps.newHashMap();
        LocalDate calculateDate = now.toLocalDate();
        String key = getPKRewardInfoKey(memberId, attr);
        actRedisDao.hGetAll(groupCode, key)
                .entrySet().stream()
                .filter(Objects::nonNull)
                .filter(e -> Objects.nonNull(e.getKey()) && Objects.nonNull(e.getValue()) && e
                        .getKey() instanceof String && e.getValue() instanceof String)
                .forEach(e -> {
                    LocalDate date = LocalDate.parse((String) e.getKey(), DateTimeFormatter.BASIC_ISO_DATE);
                    if (date.isBefore(calculateDate) || date.isEqual(calculateDate)) {
                        rewards.put((String) e.getKey(), (String) e.getValue());
                    }
                });
        return Pair.of(key, rewards);
    }

    private void setSettleFence(PhaseTimeEnd event, PKRewardComponentAttr attr,
                                PKMembers<GroupMemberItem> pk) {
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        pk.getMembers().forEach(item -> {
            String memberId = item.getMemberId();
            String settleFenceKey = getSettleFenceKey(memberId, event.getEndTime(), attr);
            Boolean setIfAbsent = actRedisDao.getRedisTemplate(groupCode).opsForValue()
                    .setIfAbsent(settleFenceKey, "1", 10 * 60, TimeUnit.SECONDS);
            if (BooleanUtils.toBoolean(setIfAbsent)) {
                String infoKey = getPKRewardInfoKey(item.getMemberId(), attr);
                String infoField = getPKRewardHashField(event.getEndTime(), attr);
                if (item.getScore() <= 0) {
                    return;
                }
                String reward = calculatePKRewardByRankScore(item.getScore(), attr);
                actRedisDao.hset(groupCode, infoKey, infoField, reward);
            }
        });
    }

    private List<PKMembers<GroupMemberItem>> getPKMembers(PhaseTimeEnd event,
                                                          PKRewardComponentAttr attr) {
        String dateStr = getPKConfigDateStr(event);
        PkInfo pkInfo = hdztRankingThriftClient
                .queryPhasePkgroup(attr.getActId(), attr.getRankId(), attr.getPhaseId(), dateStr, dateStr,
                        true,
                        true, Collections.emptyMap());
        return pkInfo.getPkGroupItems().stream().map(PkGroupItem::getMemberPkItems)
                .flatMap(Collection::stream)
                .filter(groupMemberItems -> groupMemberItems.size() >= 2)
                .map(memberPkItems -> new PKMembers<>(memberPkItems.get(0), memberPkItems.get(1),
                        Comparator.comparingLong(GroupMemberItem::getScore)))
                .collect(Collectors.toList());
    }

    private String getPKConfigDateStr(PhaseTimeEnd event) {
        String dateStr = "00000000";
        if (TimeKeyHelper.isSplitByHour(event.getTimeKey())) {
            Date date = DateUtil.getDate(event.getEndTime());
            dateStr = DateUtil.format(date, DateUtil.PATTERN_TYPE7);

        } else if (TimeKeyHelper.isSplitByDay(event.getTimeKey())) {
            Date date = DateUtil.getDate(event.getEndTime());
            dateStr = DateUtil.format(date, DateUtil.PATTERN_TYPE2);
        }
        return dateStr;
    }


    @Override
    public Long getComponentId() {
        return PK_REWARD;
    }

    protected String getPKRewardInfoKey(String member, PKRewardComponentAttr attr) {
        return makeKey(attr, attr.getRewardKey() + UNDERLINE + member);
    }

    protected String calculatePKRewardByRankScore(long rankScore, PKRewardComponentAttr attr) {
        long reward = rankScore / attr.getRewardRatio() * attr.getUnitReward();
        if (reward > attr.getRewardLimit()) {
            reward = attr.getRewardLimit();
        }
        return String.valueOf(reward);
    }


    protected String getPKRewardHashField(String eventTime, PKRewardComponentAttr attr) {
        return DateUtil.changeDateTimeFormat("yyyy-MM-dd HH:mm:ss", eventTime, "yyyyMMdd");
    }

    protected String getSettleFenceKey(String member, String eventTime, PKRewardComponentAttr attr) {
        return makeKey(attr,
                attr.getSettleFenceKeySuffix() + UNDERLINE + member + UNDERLINE + getPKRewardHashField(
                        eventTime, attr));
    }

    @VisibleForTesting
    long getRewardValues(String groupCode, PKRewardComponentAttr attr, String memberId) {
        return getRewards(groupCode, attr, memberId, LocalDateTime.MAX)
                .getRight().values()
                .stream()
                .mapToLong(NumberUtils::toLong)
                .sum();
    }

    @Override
    public List<Object> handleExt(PKRewardComponentAttr attr, GetRankReq rankReq,
                                  RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        if (attr.getRankId() != rankReq.getRankId() || attr.getPhaseId() != rankReq.getPhaseId()) {
            return objectList;
        }
        String groupCode = redisConfigManager.getGroupCode(rankReq.getActId());
        if (objectList.get(0) instanceof SubGuildRankItem) {
            objectList.stream().map(rank -> ((SubGuildRankItem) rank))
                    .forEach(rank -> {
                                long values = getRewardValues(groupCode, attr, rank.getKey());
                                if (values <= 0) {
                                    return;
                                }
                                rank.getViewExt()
                                        .put(attr.getRewardKey(), String.valueOf(values));
                            }
                    );
        }

        return objectList;
    }
}
