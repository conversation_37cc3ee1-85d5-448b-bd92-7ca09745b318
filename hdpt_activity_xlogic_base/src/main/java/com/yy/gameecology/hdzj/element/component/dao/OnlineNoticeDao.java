package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt1019NoticeMsg;
import com.yy.gameecology.common.utils.DateUtil;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class OnlineNoticeDao {

    private static final String INSERT_IGNORE_SQL = "insert ignore into cmpt_1019_notice_msg (act_id, cmpt_use_inx, uid, seq, notice_type, notice_value, expired_time, create_time, update_time) values (?, ?, ?, ?, ?, ?, ?, ?, ?)";

    @Autowired
    private GameecologyDao gameecologyDao;

    public int addOnlineNotice(long actId, long cmptIndex, long uid, String seq, String noticeType, String noticeValue, Date expiredTime) {
        Date now = new Date();
        return gameecologyDao.getJdbcTemplate().update(INSERT_IGNORE_SQL, actId, cmptIndex, uid, seq, noticeType, noticeValue, expiredTime, now, now);
    }

    public List<Cmpt1019NoticeMsg> selectOnlineNotices(long actId, long cmptIndex, long uid, Date time) {
        Cmpt1019NoticeMsg where = new Cmpt1019NoticeMsg();
        where.setActId(actId);
        where.setCmptUseInx(cmptIndex);
        where.setUid(uid);

        String expiredTimeStr = DateFormatUtils.format(time, DateUtil.DEFAULT_PATTERN);
        return gameecologyDao.select(Cmpt1019NoticeMsg.class, where, " and expired_time > '" + expiredTimeStr + "' limit 100");
    }

    public int deleteOnlineNotice(long id) {
        Cmpt1019NoticeMsg where = new Cmpt1019NoticeMsg();
        where.setId(id);
        return gameecologyDao.delete(Cmpt1019NoticeMsg.class, where);
    }


}
