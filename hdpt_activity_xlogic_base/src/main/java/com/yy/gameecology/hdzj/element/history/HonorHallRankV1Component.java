package com.yy.gameecology.hdzj.element.history;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.service.ActResultService;
import com.yy.gameecology.common.db.model.gameecology.ActResult;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.PKMembers;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.HonorHallRankComponent;
import com.yy.gameecology.hdzj.element.history.attr.HonorHallV1ComponentAttr;
import com.yy.thrift.hdztranking.Rank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 已废弃,功能已被 5027组件替代
 * <p>
 * <p>
 * 可能存在用户自定义的信息 所以存在数据库表act_result 中
 * 使用此组件时,需配置组件属性和act_result表
 *
 * <AUTHOR>
 * @date 2021.09.22 10:13
 * 每个榜单都需要独立配置一个组件,比较繁琐,在V2中已改进
 * @see HonorHallRankComponent
 */
@Component
@Deprecated
public class HonorHallRankV1Component extends BaseActComponent<HonorHallV1ComponentAttr> {

    @Autowired
    private ActResultService actResultService;

    @Override
    public Long getComponentId() {
        return ComponentId.HONOR_HALL_RANK;
    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = false)
    public void honorHallPhaseTimeEnd(PhaseTimeEnd event, HonorHallV1ComponentAttr attr) {
        long actId = attr.getActId();
        if (event.getActId() != actId || !attr.getRankIds().contains(event.getRankId())) {
            return;
        }
        String groupCode = redisConfigManager.getGroupCode(actId);
        String key = makeKey(actId, attr.getCmptId(), attr.getCmptUseInx(), "");
        if (!actRedisDao.hsetnx(groupCode, key, event.getEkey(), DateUtil.getNowYyyyMMddHHmmss())) {
            return;
        }
        List<ActResult> actResults = new ArrayList<>();
        for (HonorHallV1ComponentAttr.HonorConfig config : attr.getConfigs()) {
            if (config.getPhaseId() != event.getPhaseId() || config.getRankId() != event.getRankId()) {
                continue;
            }
            //获取前n个
            List<Rank> ranks = hdztRankingThriftClient.queryRanking(event.getActId(), config.getRankId(), config.getPhaseId(),
                    "", 50, Maps.newHashMap());
            if ("pk".equals(config.getRankType())) {
                List<PKMembers<Rank>> pkMembers = setUpPK(actId, config.getRankId(), config.getPhaseId(), ranks);
                if (config.isWinner()) {
                    ranks = pkMembers.stream().map(PKMembers::getWinner).collect(Collectors.toList());
                } else {
                    ranks = pkMembers.stream().map(PKMembers::getLoser).collect(Collectors.toList());
                }
            }
            ranks.sort(Comparator.comparingInt(Rank::getRank));

            int startIndex = config.getStartIndex() - 1;
            int rankIndex = config.getRankIndex();
            for (int i = 0; i < config.getCount(); i++) {
                ActResult actResult = new ActResult();
                actResult.setActId(actId);
                actResult.setMemberId(ranks.get(startIndex).getMember());
                actResult.setType(attr.getType());
                actResult.setGroupId(attr.getGroupId());
                actResult.setRank(rankIndex);
                actResult.setStatus(1);
                actResults.add(actResult);

                startIndex++;
                rankIndex++;
            }
        }
        //批量更新act_result
        int[] ints = actResultService.batchUpdate(actResults);
        log.info("handle honorHallPhaseTimeEnd done, rankId:{}, phaseId:{}, update result:{}", event.getRankId(), event.getPhaseId(), ints);
    }

}
