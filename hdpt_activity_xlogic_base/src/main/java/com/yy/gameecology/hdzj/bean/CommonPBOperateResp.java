package com.yy.gameecology.hdzj.bean;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;

import java.util.Map;
import java.util.UUID;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-12-13 20:19
 **/
public class CommonPBOperateResp {

    public CommonPBOperateResp() {
    }

    public CommonPBOperateResp(long code, String content, String msg) {
        this.code = code;
        this.content = content;
        this.msg = msg;
    }

    private long code;

    private String content;

    private String msg;

    /**
     * 扩展json数据，约定使用
     */
    private Map<String, String> extjson = Maps.newHashMap();


    public GameecologyActivity.CommonOperateResp toPb(CommonPBOperateRequest request) {
        GameecologyActivity.CommonOperateResp.Builder builder = GameecologyActivity.CommonOperateResp.newBuilder();
        builder.setRetCode(this.getCode());
        builder.setRetContent(this.getContent());
        builder.setRetMsg(Convert.toString(msg));
        builder.setExtjson(JSON.toJSONString(this.getExtjson()));

        builder.setActId(request.getActId());
        builder.setSeq(Convert.toString(request.getSeq()));
        builder.setOpType(Convert.toString(request.getOpType()));
        builder.setOpId(Convert.toString(request.getOpId()));
        builder.setCmptId(request.getCmptId());
        builder.setCmptIndex(request.getCmptIndex());
        builder.setRespTime(System.currentTimeMillis());


        return builder.build();
    }

    public GameecologyActivity.CommonOperateResp toPb(ComponentAttr attr) {
        GameecologyActivity.CommonOperateResp.Builder builder = GameecologyActivity.CommonOperateResp.newBuilder();
        builder.setRetCode(this.getCode());
        builder.setRetContent(this.getContent());
        builder.setRetMsg(Convert.toString(msg));
        builder.setExtjson(JSON.toJSONString(this.getExtjson()));

        builder.setActId(attr.getActId());
        builder.setCmptId(attr.getCmptId());
        builder.setCmptIndex(attr.getCmptUseInx());
        builder.setRespTime(System.currentTimeMillis());
        builder.setSeq(UUID.randomUUID().toString());

        return builder.build();
    }

    public long getCode() {
        return code;
    }

    public void setCode(long code) {
        this.code = code;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Map<String, String> getExtjson() {
        return extjson;
    }

    public void setExtjson(Map<String, String> extjson) {
        this.extjson = extjson;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
