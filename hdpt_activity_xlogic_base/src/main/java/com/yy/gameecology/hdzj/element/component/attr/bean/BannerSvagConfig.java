package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrCollector;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-05-31 11:22
 **/
@Data
public class BannerSvagConfig {

    /**
     * svga点击按钮key name
     */
    @ComponentAttrField(labelText = "svga点击按钮key name")
    private String clickLayerName;
    /**
     * 横幅svga url
     */
    @ComponentAttrField(labelText = "不带围观按钮的横幅svga", remark = "不带围观按钮的svga", propType = ComponentAttrCollector.PropType.RESOURCE)
    private String svgaURL;

    /**
     * 横幅svga url
     * 需要跳转，就设置jump==1，然后把带围观和不带围观的url都填上，会处理当前频道不带围观，其他频道带围观
     */
    @ComponentAttrField(labelText = "带围观按钮的横幅svga", remark = "带围观按钮的svga需要跳转，就设置jump==1，然后把带围观和不带围观的url都填上，会处理当前频道不带围观，其他频道带围观", propType = ComponentAttrCollector.PropType.RESOURCE)
    private String jumpSvgaURL;

    /**
     * 礼物滚屏url
     */
    @ComponentAttrField(labelText = "不带围观的礼物滚屏svga", remark = "当前端限制横幅时,前端将使用这个svga播放在礼物滚屏", propType = ComponentAttrCollector.PropType.RESOURCE)
    private String miniURL;

    /**
     * 横幅svga url
     */
    @ComponentAttrField(labelText = "带围观按钮的礼物滚屏svga", remark = "带围观按钮的礼物滚屏svga,前端将使用这个svga播放在礼物滚屏", propType = ComponentAttrCollector.PropType.RESOURCE)
    private String jumpMiniURL;



    @ComponentAttrField(labelText = "svgaTextCode", remark = "svgaText文案配置编码，如有多个，逗号隔开")
    private String contentLayerCodes;

}
