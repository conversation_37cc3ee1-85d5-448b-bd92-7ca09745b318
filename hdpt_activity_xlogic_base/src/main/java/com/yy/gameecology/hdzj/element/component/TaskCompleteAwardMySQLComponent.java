package com.yy.gameecology.hdzj.element.component;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.acttask.TaskAwardConfig;
import com.yy.gameecology.activity.bean.acttask.TaskFinishInfoVo;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.common.db.model.gameecology.ComponentDataList;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.JsonUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.TaskCompleteAwardComponentAttr;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;


@RestController
@RequestMapping("5131")
public class TaskCompleteAwardMySQLComponent extends BaseActComponent<TaskCompleteAwardComponentAttr> {

    private static final String RED_DOT_KEY = "red_dot:";

    private static final String FINISH_TASK_LIST_KEY = "finishTask:";

    private static final String TASK_STATIC_KEY = "task_static_";

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Override
    public Long getComponentId() {
        return ComponentId.TASK_COMPLETE_AWARD_NOTICE_MYSQL;
    }

    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = true)
    public void awardNotice(TaskProgressChanged event, TaskCompleteAwardComponentAttr attr) {
        log.info("TaskCompleteAwardComponent event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        if (event.getRankId() != attr.getTaskRankId()) {
            log.info("TaskCompleteAwardComponent rankId:{} not match", event.getRankId());
            return;
        }

        if (event.getRankId() == attr.getTaskRankId() && event.getPhaseId() == attr.getTaskPhaseId()) {
            List<TaskAwardConfig> awardList = attr.getAward();
            List<String> awardTexts = Lists.newArrayList();
            for( long startIndex = event.getStartTaskIndex()+1 ;startIndex<=event.getCurrTaskIndex();startIndex++) {
                long finalStartIndex = startIndex;
                TaskAwardConfig awardItem = awardList.stream().filter(v -> v.getTaskLevel() == finalStartIndex).findFirst().orElse(null);
                if (awardItem != null) {
                    awardTexts.add(awardItem.getAwardText());
                    log.info("TaskCompleteAwardComponent pc bro uid:{}, event:{}", event.getMember(), JSON.toJSONString(event));
                }else{
                    log.warn("TaskCompleteAwardComponent awardItem is null,event:{}", JSON.toJSONString(event));
                }
            }
            if(!awardTexts.isEmpty()) {
                JSONObject json = new JSONObject(6);
                json.put("awardTexts", awardTexts);
                json.put("moduleName", attr.getModuleName());
                commonBroadCastService.commonNoticeUnicast(attr.getActId(), "taskCompleteAwardNotice", JsonUtil.toJson(json), StringUtils.EMPTY, Convert.toLong(event.getMember()));
            }
        }

        if(attr.isAddFinishTaskList()){
            addUserFinishTask(event,attr);
        }

        if(attr.isShowRedDot()){
            addUserTaskRedDot(event,attr);
        }

        //更新每日任务用户完成数
        refreshTaskStaticRecord(event,attr);
    }


    private void addUserTaskRedDot(TaskProgressChanged event, TaskCompleteAwardComponentAttr attr) {
        commonDataDao.valueIncrIgnore(attr.getActId(), attr.getCmptId(), 500, event.getSeq(), RED_DOT_KEY + event.getMember(), 1);
    }

    //完成任务记录
    private void addUserFinishTask(TaskProgressChanged event, TaskCompleteAwardComponentAttr attr) {

        log.info("TaskCompleteAwardComponent addUserFinishTask event:{}",JSON.toJSONString(event));
        String eventMemberSeq = (StringUtil.isNotBlank(event.getEkey()) ? event.getEkey() : event.getSeq()) + ":" + event.getMember();
        for( long startIndex = event.getStartTaskIndex() + 1 ;startIndex <= event.getCurrTaskIndex(); startIndex++) {
            long finalStartIndex = startIndex;
            List<TaskAwardConfig> awardList = attr.getAward();
            TaskAwardConfig awardItem = awardList.stream().filter(v -> v.getTaskLevel() == finalStartIndex).findFirst().orElse(null);
            if (awardItem != null) {
                TaskFinishInfoVo item = new TaskFinishInfoVo();
                item.setMember(event.getMember());
                item.setTaskName(awardItem.getTaskName());
                item.setAwardName(awardItem.getAwardName());
                item.setAwardCount(awardItem.getAwardCount());
                item.setAwardTime(event.getOccurTime());

                String finishTaskSeq = makeKey(attr, "ft:" + eventMemberSeq + ":" + finalStartIndex);
                String seq = DigestUtil.sha256Hex(finishTaskSeq);
                commonDataDao.listInsertIgnore(attr.getActId(), attr.getCmptId(), 500, seq, FINISH_TASK_LIST_KEY + event.getMember(), JSON.toJSONString(item));
            }
        }
    }

    private void  refreshTaskStaticRecord(TaskProgressChanged event, TaskCompleteAwardComponentAttr attr) {
        try {
            Date now = DateUtils.parseDate(event.getOccurTime(),DateUtil.DEFAULT_PATTERN);
            String dayCode = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
            String key = TASK_STATIC_KEY + dayCode;
            for( long startIndex = event.getStartTaskIndex()+1 ;startIndex <= event.getCurrTaskIndex(); startIndex++) {
                String seq = event.getSeq() + ":" + startIndex;
                String hashKey = event.getRankId() + "|" + startIndex;
                commonDataDao.hashValueIncIgnore(attr.getActId(), attr.getCmptId(), 500, seq, key, hashKey, 1);
            }

        } catch (Exception e) {
            log.error("TaskCompleteAwardComponent refreshTaskStaticRecord event:{},error:{}", JSON.toJSON(event),e.getMessage());
        }
    }

    public Map<String, Long> getTaskStaticInfo(long actId, String dayCode) {
        String key = TASK_STATIC_KEY + dayCode;
        Map<String, String> entries = commonDataDao.hashGetAll(actId, getComponentId(), 500, key);
        if (MapUtils.isEmpty(entries)) {
            return Collections.emptyMap();
        }

        Map<String, Long> result = new HashMap<>(entries.size());
        entries.forEach((k, v) -> result.put(k, Convert.toLong(v)));

        return result;

    }

    /**
     * 查看所有已完成的历史任务
     * @param actId 活动id
     * @param cmptUseInx 序号可不填
     * @return
     */
    @GetMapping("/getFinishTaskList")
    public Response<List<TaskFinishInfoVo>> getFinishTaskList(@RequestParam(name = "actId") long actId,
                                                              @RequestParam(name = "cmptUseInx", defaultValue = "500") long cmptUseInx) {

        long loginUid = getLoginYYUid();
        if (loginUid <= 0) {
            return Response.fail(401, "未登录！");
        }


        TaskCompleteAwardComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        List<ComponentDataList> dataList = commonDataDao.listSelect(attr.getActId(), attr.getCmptId(), 500, FINISH_TASK_LIST_KEY + loginUid, 0, 1000);
        if (CollectionUtils.isEmpty(dataList)) {
            return Response.success(Collections.emptyList());
        }

        List<TaskFinishInfoVo> list = dataList.stream().map(item -> JSON.parseObject(item.getValue(), TaskFinishInfoVo.class)).toList();

        return Response.success(list);
    }

    /**
     * 任意任务完成红点
     * @param req
     * @param resp
     * @param actId
     * @param cmptUseInx  可不传
     * @return
     */
    @GetMapping("/queryRedDot")
    public Response<Long> queryRedDot(HttpServletRequest req, HttpServletResponse resp, long actId,
                                      @RequestParam(name = "cmptUseInx", defaultValue = "500") long cmptUseInx) {
        long uid = getLoginYYUid(req, resp);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        TaskCompleteAwardComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return Response.fail(-2, "参数错误");
        }
        if (!actInfoService.inActTime(actId)) {
            return Response.fail(-3, "未在活动时间内");
        }

        long value = commonDataDao.valueGet(attr.getActId(), attr.getCmptId(), 500, RED_DOT_KEY + uid);
        return Response.success(value);
    }

    /**
     * 点击红点
     * @param req
     * @param resp
     * @param actId
     * @param cmptUseInx
     * @return
     */
    @GetMapping("/clickRedDot")
    public Response<?> clickRedDot(HttpServletRequest req, HttpServletResponse resp, long actId,
                                @RequestParam(name = "cmptUseInx", defaultValue = "500") long cmptUseInx) {
        long uid = getLoginYYUid(req, resp);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        TaskCompleteAwardComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return Response.fail(-2, "参数错误");
        }
        if (!actInfoService.inActTime(actId)) {
            return Response.fail(-3, "未在活动时间内");
        }

        commonDataDao.valueDel(actId, attr.getCmptId(), 500, RED_DOT_KEY + uid);
        return Response.ok();
    }
}
