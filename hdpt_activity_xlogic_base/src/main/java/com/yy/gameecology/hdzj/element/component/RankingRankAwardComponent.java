package com.yy.gameecology.hdzj.element.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.hdzt.RankingTimeEnd;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.service.HdztRankService;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.HdzjHelper;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.RankingRankAwardComponentAttr;
import com.yy.thrift.hdztranking.Rank;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 按榜单所在名次做奖励的组件
 *
 * <AUTHOR>
 * @date 2021/4/14 17:23
 */
@Component
public class RankingRankAwardComponent extends BaseActComponent<RankingRankAwardComponentAttr> {

    @Override
    public Long getComponentId() {
        return ComponentId.RANKING_RANK_AWARD;
    }

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    public HdztRankService hdztRankService;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    /**
     * 响应榜单结束事件，按配置发放奖励， 本函数做了防重检查，只能执行一次
     */
    @HdzjEventHandler(value = RankingTimeEnd.class, canRetry = true)
    public void onRankingTimeEnd(RankingTimeEnd event, RankingRankAwardComponentAttr attr) {
        // 先检查是否要处理的
        long rankId = event.getRankId();
        if (!attr.isMyDuty(rankId)) {
            return;
        }
        log.info("onRankingTimeEnd done@event:{}, attr:{}", event, attr);
        String checkName = "onRankingTimeEnd:" + event.getEkey();
        String checkValue = event.getTimestamp() + ":" + event.getSeq();
        boolean set = commonDataDao.hashValueSetNX(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), checkName, StringUtils.EMPTY, checkValue);
        if (set) {
            doAward(event, attr);
            doMysql(event, attr);
        }
    }

    private void doAward(RankingTimeEnd event, RankingRankAwardComponentAttr attr) {
        Clock clock = new Clock();
        try {
            // 1. 先提取所有的排名奖励，若配置为空，则结束返回
            Map<Integer, Map<Long, Map<Long, Integer>>> awardConfig = HdzjHelper.parseRankAwardConfig(attr.getTAwardTskPkgConfig());
            if (CollectionUtils.isEmpty(awardConfig)) {
                log.warn("doAward ignore@invalid award config, ekey:{}, seq:{}, attr:{}", event.getEkey(), event.getSeq(), attr);
                return;
            }

            // 2. 取主榜的成员（用于形成贡献榜子key中的 成员 部分）
            String primaryRankMember = getPrimaryRankMember(event, attr);
            if (primaryRankMember == null) {
                log.error("doAward skip@not found primaryRankMember, event:{}, attr:{}", event, attr);
                return;
            }

            // 3. 找出从 1 ~ 最大排名位置 的所有排名清单
            int maxRank = Collections.max(Lists.newArrayList(awardConfig.keySet()));
            Map<Integer, Rank> rankReceivers = hdztRankService.readyRankReceivers(event.getActId(), event.getRankId(),
                    0, event.getTimeKey(), event.getEndTime(), maxRank, attr.getRankType(), primaryRankMember);
            clock.tag();
            if (rankReceivers == null) {
                log.error("doAward wrong@fail get rankReceivers, event:{}, attr:{}", event, attr);
                return;
            }

            // 4. 遍历位置奖励配置，为每个位置上的接收者发放奖励
            int retry = attr.getRetry();
            int receiverInx = attr.getReceiverInx();
            Date now = commonService.getNow(attr.getActId());
            String time = DateUtil.format(now);
            for (Integer rank : awardConfig.keySet()) {
                Rank bean = rankReceivers.get(rank);
                if (bean != null) {
                    Map<Long, Map<Long, Integer>> taskPackageIds = awardConfig.get(rank);
                    long receiver = Long.parseLong(bean.getMember().split("\\|")[receiverInx]);
                    //如果是公会则下发给公会的OW
                    if (attr.getIsChannel() == 1) {
                        receiver = webdbThriftClient.getOwUid(receiver);
                    }
                    String seq = event.getSeq() + StringUtil.UNDERSCORE + rank;
                    hdztAwardServiceClient.doBatchWelfare(seq, receiver, taskPackageIds, time, retry, Maps.newHashMap());
                }
            }

            log.info("doAward done@seq:{}, ekey:{}, attr:{} {}", event.getSeq(), event.getEkey(), attr, clock.tag());
        } catch (Throwable t) {
            log.error("doAward exception@event:{}, attr:{}, err:{} {}", event, attr, t.getMessage(), clock.tag(), t);
        }
    }

    private void doMysql(RankingTimeEnd event, RankingRankAwardComponentAttr attr) {
        Clock clock = new Clock();
        long actId = event.getActId();
        try {
            // 1. 准备sql操作配置
            Map<String, Set<Integer>> mysqlOperConfig = HdzjHelper.parseRankMysqlConfig(attr.getRankMysqlConfig());
            if (CollectionUtils.isEmpty(mysqlOperConfig)) {
                return;
            }

            // 2. 找出从 1 ~ 最大排名位置 的所有排名清单
            List<Integer> allRanks = Lists.newArrayList();
            for (String sql : mysqlOperConfig.keySet()) {
                allRanks.addAll(mysqlOperConfig.get(sql));
            }
            int maxRank = Collections.max(allRanks);
            Map<Integer, Rank> rankReceivers = hdztRankService.readyRankReceivers(actId, event.getRankId(),
                    0, event.getTimeKey(), event.getEndTime(), maxRank, attr.getRankType());
            clock.tag();
            if (rankReceivers == null) {
                log.error("doMysql wrong@event:{}, attr:{}", event, attr);
                return;
            }

            // 3. 遍历位置mysql配置，为每个位置执行该 sql
            for (String mysql : mysqlOperConfig.keySet()) {
                Set<Integer> set = mysqlOperConfig.get(mysql);
                if (CollectionUtils.isEmpty(set)) {
                    continue;
                }
                for (int rank : set) {
                    Rank bean = rankReceivers.get(rank);
                    if (bean != null) {
                        Map<String, String> replaceMap = HdzjHelper.makeReplaceMap(event, bean);
                        String sql = HdzjHelper.replace(mysql, replaceMap);
                        int ret = this.gameecologyDao.getJdbcTemplate().update(sql);
                        log.info("doMysql one ok@actId:{}, seq:{}, ekey:{}, sql:{}, ret:{}", actId, event.getSeq(), event.getEkey(), sql, ret);
                    }
                }
            }
            log.info("doMysql done@seq:{}, ekey:{}, attr:{} {}", event.getSeq(), event.getEkey(), attr, clock.tag());
        } catch (Throwable t) {
            log.error("doMysql exception@event:{}, attr:{}, err:{} {}", event, attr, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 查主榜指定位置的成员， 若返回 null 表明出现某种异常，调用者应该中断处理
     */
    private String getPrimaryRankMember(RankingTimeEnd event, RankingRankAwardComponentAttr attr) {
        long primaryRankId = attr.getPrimaryRankId();
        if (primaryRankId <= 0) {
            return "";
        }
        int primaryRankPos = attr.getPrimaryRankPos();
        String primaryRankType = attr.getPrimaryRankType();
        Map<Integer, Rank> primaryRankMembers = hdztRankService.readyRankReceivers(event.getActId(), primaryRankId,
                0, event.getTimeKey(), event.getEndTime(), primaryRankPos, primaryRankType);
        if (primaryRankMembers == null || !primaryRankMembers.containsKey(primaryRankPos)) {
            return null;
        }
        return primaryRankMembers.get(primaryRankPos).getMember();
    }
}
