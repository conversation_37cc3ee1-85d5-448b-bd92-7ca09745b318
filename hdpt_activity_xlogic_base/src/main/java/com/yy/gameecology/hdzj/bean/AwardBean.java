package com.yy.gameecology.hdzj.bean;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.Map;

@Getter
@Setter
@ToString
public class AwardBean {
    private String seq;
    private long busiId;
    private String receiver;
    // yyyy-MM-dd HH:mm:ss 格式的时间戳
    private String time;
    Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap();
    private String utime;

    public AwardBean(){
    }

    public AwardBean(String seq, long busiId, String receiver, String time, long taskId, long packageId, int packageNum) {
        Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap();
        Map<Long, Integer> packageIdNumMap = ImmutableMap.of(packageId, packageNum);
        taskPackageIds = ImmutableMap.of(taskId, packageIdNumMap);

        this.seq = seq;
        this.busiId = busiId;
        this.receiver = receiver;
        this.taskPackageIds =taskPackageIds;
        this.time = time;
    }

    public AwardBean(String seq, long busiId, String receiver, String time, Map<Long, Map<Long, Integer>> taskPackageIds) {
        this.seq = seq;
        this.busiId = busiId;
        this.receiver = receiver;
        this.time = time;
        this.taskPackageIds = taskPackageIds;
        this.utime = time;
    }

    public long getSeconds() {
        if (StringUtil.isBlank(time)) {
            return 0;
        }
        Date date = DateUtil.getDate(time);
        return DateUtil.getSeconds(date);
    }

    public String toString() {
        return JSON.toJSONString(this);
    }
}
