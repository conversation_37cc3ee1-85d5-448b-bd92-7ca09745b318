package com.yy.gameecology.hdzj.bean;

/**
 * <AUTHOR>
 * @date 2021.09.23 15:38
 */
public class PKRecord {
    private PkRankItem winner;
    private PkRankItem loser;

    //
    private long totalScore;

    private String date;

    public long getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(long totalScore) {
        this.totalScore = totalScore;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public PKRecord() {

    }

    public PKRecord(PkRankItem r1, PkRankItem r2, String date) {
        if (r1.getScore() > r2.getScore()) {
            winner = r1;
            loser = r2;
        } else {
            winner = r2;
            loser = r1;
        }
        this.totalScore = r1.getScore() + r2.getScore();
        this.date = date;
    }

    public PkRankItem getWinner() {
        return winner;
    }

    public void setWinner(PkRankItem winner) {
        this.winner = winner;
    }

    public PkRankItem getLoser() {
        return loser;
    }

    public void setLoser(PkRankItem loser) {
        this.loser = loser;
    }


    public static class PkRankItem {
        private String memberId;
        private long score;
        private int rank;
        private String nick;
        private String logo;
        private String asid;

        public String getAsid() {
            return asid;
        }

        public void setAsid(String asid) {
            this.asid = asid;
        }

        public String getNick() {
            return nick;
        }

        public void setNick(String nick) {
            this.nick = nick;
        }

        public String getLogo() {
            return logo;
        }

        public void setLogo(String logo) {
            this.logo = logo;
        }

        public String getMemberId() {
            return memberId;
        }

        public void setMemberId(String memberId) {
            this.memberId = memberId;
        }

        public long getScore() {
            return score;
        }

        public void setScore(long score) {
            this.score = score;
        }

        public int getRank() {
            return rank;
        }

        public void setRank(int rank) {
            this.rank = rank;
        }
    }

}
