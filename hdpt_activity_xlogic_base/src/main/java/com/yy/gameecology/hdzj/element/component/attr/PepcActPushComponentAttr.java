package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

import java.time.LocalTime;

@Data
public class PepcActPushComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "IM推送AppId", remark = "推送IM消息的AppId")
    protected int imAppId;

    @ComponentAttrField(labelText = "IM推送者UID", remark = "推送IM消息的官方号UID")
    protected long imSenderUid;

    @ComponentAttrField(labelText = "短信特殊AppId")
    protected String smsAppid;

    @ComponentAttrField(labelText = "短信特殊Secret")
    protected String smsSecret;

    @ComponentAttrField(labelText = "推送开始时间")
    protected LocalTime startTime;

    @ComponentAttrField(labelText = "推送结束时间")
    protected LocalTime endTime;

    @ComponentAttrField(labelText = "IM推送标题")
    protected String imMsgTitle = "和平精英巅峰赛";

    @ComponentAttrField(labelText = "预约推送文案")
    protected String subscribeMsg = "您预约的新一期巅峰赛已开始报名，快去参与吧！";

    @ComponentAttrField(labelText = "预约推送短信")
    protected String subscribeSms = "您预约的新一期巅峰赛已开始报名，快去参与吧！";

    @ComponentAttrField(labelText = "IM推送跳转链接")
    protected String imMsgLink = "https://hd-activity-test.yy.com/yo_2024_kingofglory_peak/pages/mobile/index.html";

    @ComponentAttrField(labelText = "审核不通过文案")
    protected String rejectedMsg = "您的战队宣言审核未通过，请重新编辑";

    @ComponentAttrField(labelText = "有待审批文案")
    protected String newApplyMsg = "您有一条入队申请待审批，点击前往审批";

    @ComponentAttrField(labelText = "待审批推送链接", remark = "需要拉起审批页的链接")
    protected String approveLink = "https://hd-activity-test.yy.com/yo_2024_kingofglory_peak/pages/mobile/index.html?type=approval";

    @ComponentAttrField(labelText = "队伍名称审核不通过文案")
    protected String teamNameRejectedMsg = "您的队伍名称审核未通过，请重新编辑";

    @ComponentAttrField(labelText = "审批通过文案")
    protected String approvedMsg = "您的入队审批已通过，快去看看吧！";

    @ComponentAttrField(labelText = "审批通过短信")
    protected String approvedSms = "您的和平精英巅峰赛赛入队审批已通过，快登录Yo语音参与吧！";

    @ComponentAttrField(labelText = "被T出队伍文案")
    protected String kickedOutMsg = "您已被队长踢出队伍，快去看看吧！";

    @ComponentAttrField(labelText = "被T出队伍短信")
    protected String kickedOutSms;

    @ComponentAttrField(labelText = "队长解散队伍文案")
    protected String disbandMsg = "您的队伍已经被队长解散，快去看看吧";

    @ComponentAttrField(labelText = "队长解散队伍短信")
    protected String disbandSms;

    @ComponentAttrField(labelText = "队员离开队伍文案")
    protected String quitTeamMsg = "您的队伍有一名队员离开了队伍，快去看看吧";

    @ComponentAttrField(labelText = "队员离开队伍短信")
    protected String quitTeamSms;

    @ComponentAttrField(labelText = "比赛开始文案")
    protected String startGameMsg = "您报名的巅峰赛已开赛，快去参与吧！";

    @ComponentAttrField(labelText = "比赛开始短信")
    protected String startGameSms = "您报名的和平精英巅峰赛已开赛，快登录Yo语音参与吧！";

    @ComponentAttrField(labelText = "进入游戏房间时间",remark = "从游戏开始时间往前倒推N分钟可以进入游戏H5房间")
    protected  int jumpGameMin;

//    @ComponentAttrField(labelText = "app弹窗次数")
//    protected int appPopupTimes;

//    @ComponentAttrField(labelText = "app弹窗周期")
//    protected int appPopupDays;

    @ComponentAttrField(labelText = "口令分享弹窗",remark = "动态替换字符串： {teamId} {shareNick}")
    protected String shareInviteUrl;

    @ComponentAttrField(labelText = "加入队伍公屏提醒",remark = "例:恭喜<font color='IM1' color_pc='#fff' action=''>{99999:n} </font>获得 <font color='IM1' color_pc='#fff' action=''>xxx勋章</font>")
    private String joinTeamRoomNotice;

    @ComponentAttrField(labelText = "申请加入队伍公屏提醒",remark = "例:恭喜<font color='IM1' color_pc='#fff' action=''>{99999:n} </font>获得 <font color='IM1' color_pc='#fff' action=''>xxx勋章</font>")
    private String applyTeamRoomNotice;

    @ComponentAttrField(labelText = "加入队伍IM提醒")
    protected String joinTeamIm = "有一名队员成功加入您的队伍！";

    @ComponentAttrField(labelText = "上一期活动id")
    protected  long prevActId = 0;

    public String getImMsgLink() {
        return imMsgLink.replace("{actId}", Convert.toString(this.getActId()));
    }

    public String getApproveLink(){
        return approveLink.replace("{actId}", Convert.toString(this.getActId()));
    }

    public String getShareInviteUrl(){
        return shareInviteUrl.replace("{actId}", Convert.toString(this.getActId()));
    }
}
