package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.bean.ChannelChatTextInnerEvent;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2062LotteryBox;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2062LotteryRecord;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.ChannelWatchwordLotteryComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.ChannelWatchwordLotteryDao;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;

/**
 * 频道公屏口令抽奖
 */
@Component
public class ChannelWatchwordLotteryComponent extends BaseActComponent<ChannelWatchwordLotteryComponentAttr> {

    @Autowired
    private ChannelWatchwordLotteryDao channelWatchwordLotteryDao;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Override
    public Long getComponentId() {
        return ComponentId.WATCHWORD_LOTTERY;
    }

    public void addWatchwordLotteryBox(long actId, long cmptUseInx, String seq, String memberId, long sid, long ssid, Date expiredTime) {
        var attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            log.warn("addWatchwordLotteryBox: attr is null");
            return;
        }
        log.info("actId:{} seq:{} memberId:{} sid:{} ssid:{} expiredTime:{}",actId,seq,memberId,sid,ssid,expiredTime);
        channelWatchwordLotteryDao.addChatBox(actId, cmptUseInx, seq, memberId, sid, ssid, expiredTime);
    }

    @HdzjEventHandler(value = ChannelChatTextInnerEvent.class, canRetry = true)
    public void handleChannelChatEvent(ChannelChatTextInnerEvent event, ChannelWatchwordLotteryComponentAttr attr) {
        final long actId = attr.getActId(), sid = event.getTopsid(), ssid = event.getSubsid(), uid = event.getUid();

        log.info("ChatEven in actId:{} event:{} ",actId,event);
        Date now = commonService.getNow(actId);
        var boxes = channelWatchwordLotteryDao.selectValidLotteryBoxes(actId, attr.getCmptUseInx(), sid, ssid, now);
        if (CollectionUtils.isEmpty(boxes)) {
            return;
        }

        if (!textMatch(event.getChat(), attr.getTargetWord())) {
            log.warn("addWatchwordLotteryBox: target word not match chat:{}", event.getChat());
            return;
        }

        String time = DateUtil.format(now);
        for (var box : boxes) {
            Cmpt2062LotteryRecord record = channelWatchwordLotteryDao.selectLotteryRecord(actId, attr.getCmptUseInx(), box.getId(), uid);
            if (record != null) {
                continue;
            }

            var lotteryResult = transactionTemplate.execute((status) -> {
                int rs = channelWatchwordLotteryDao.addLotteryRecord(actId, attr.getCmptUseInx(), box.getId(), uid);
                if (rs <= 0) {
                    return null;
                }

                String seq = String.format("wlt:%d:%d:%d", actId, box.getId(), uid);
                var result = hdztAwardServiceClient.doLottery(time, attr.getBusiId(), uid, attr.getTAwardTskId(), 1, 0, seq);
                log.info("handleChannelChatEvent lottery with boxId:{} uid:{} result:{}", box.getId(), uid, result);
                if (result.getCode() != 0) {
                    status.setRollbackOnly();
                    return null;
                }

//                rs = channelWatchwordLotteryDao.updateLotteryResult(actId, attr.getCmptUseInx(), box.getId(), uid, JSON.toJSONString(result.getRecordIds()));
//                log.info("updateLotteryResult with boxId:{} uid:{} result:{}", box.getId(), uid, rs);

                return result;
            });

            if (attr.isSingleLottery() && lotteryResult != null) {
                return;
            }
        }
    }

    public long getLotteryCount(long actId, long cmptUseInx, String seq) {
        return channelWatchwordLotteryDao.countLotteryRecord(actId, cmptUseInx, seq);
    }

    public ChatLotteryBoxInfo getLotteryCountAndExpiredTime(long actId, long cmptUseInx, String seq) {
        var attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return null;
        }
        ChatLotteryBoxInfo boxInfo = new ChatLotteryBoxInfo();
        boxInfo.setSeq(seq);
        boxInfo.setChatLotteryText(attr.getChatText());
        Pair<Long, Cmpt2062LotteryBox> info = channelWatchwordLotteryDao.getLotteryCountAndExpiredTime(actId, cmptUseInx, seq);
        if (info == null) {
            return boxInfo;
        }
        boxInfo.setLotteryNum(info.getLeft()==null ? 0 : info.getLeft());
        boxInfo.setExpireTime(info.getRight().getExpiredTime());
        boxInfo.setSid(info.getRight().getSid());
        boxInfo.setSsid(info.getRight().getSsid());
        return boxInfo;
    }

    public boolean textMatch(String chatText, String targetWord) {
        String cleanedInput = chatText.replaceAll("[\\p{P}\\p{S}\\s]+", "");
        return StringUtils.contains(cleanedInput, targetWord);
    }

    @Data
    public static class ChatLotteryBoxInfo {
        private String seq;
        private long lotteryNum;
        private Date expireTime;
        private String chatLotteryText;
        private long sid;
        private long ssid;
    }
}
