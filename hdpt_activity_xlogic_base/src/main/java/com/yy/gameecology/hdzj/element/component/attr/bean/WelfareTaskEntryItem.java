package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.google.common.collect.Sets;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

import java.util.Set;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-05-22 20:16
 **/
@Data
public class WelfareTaskEntryItem {

    @ComponentAttrField(labelText = "显示类型", remark = "1== webview 2==原生气泡 3==banner广告位")
    private int type;
    /**
     * 功能id,见 com.yy.gameecology.common.consts.AppIconItemId
     */
    @ComponentAttrField(labelText = "功能id")
    private long id;

    @ComponentAttrField(labelText = "显示去重 time key",remark = "给客户端去重显示key。例如如果一天显示1次，服务端会以日期组key,如果为空则常驻显示")
    private String dupTimeKey;

    /**
     * 宽度，单位为 dp
     */
    @ComponentAttrField(labelText = "宽度，单位为 dp", remark = "")
    private int width;

    /**
     * 宽度，单位为 dp
     */
    @ComponentAttrField(labelText = "高度，单位为 dp", remark = "")
    private int height;

    /**
     * 需要展示几秒，单位为 秒， 如果 -1 则常驻显示
     */
    @ComponentAttrField(labelText = "显示时长", remark = "需要展示几秒，单位为 秒， 如果 -1 则常驻显示")
    private int duration;

    @ComponentAttrField(labelText = "显示平台", remark = "3==安卓 4==ios")
    private String platform;

    @ComponentAttrField(labelText = "显示端", remark = "yomi==yo语音 dreamer==yo交友")
    private String hostName;

    @ComponentAttrField(labelText = "用户业务", remark = "与入白名单的时候的业务匹配到才显示")
    private String welfareNoticeInfoBusiness;

    /**
     * 是否需要展示红点
     */
    @ComponentAttrField(labelText = "是否需要展示红点", remark = "")
    private boolean showRed;

    /**
     * 气泡展示的位置， left、right、top、bottom， 相对于 view 的上下左右
     */
    @ComponentAttrField(labelText = "气泡展示的位置", remark = "left、right、top、bottom， 相对于 view 的上下左右")
    private String position;

    @ComponentAttrField(labelText = "webview地址", remark = "type==1时填写")
    private String webSrc;

    @ComponentAttrField(labelText = "原生文字", remark = "type==2时填写")
    private String nativeText;

    @ComponentAttrField(labelText = "原生背景颜色", remark = "type==2时填写")
    private String nativeBgColor;

    @ComponentAttrField(labelText = "字体颜色", remark = "type==2时填写")
    private String nativeTextColor;

    @ComponentAttrField(labelText = "原生字体大小", remark = "type==2时填写")
    private int nativeTextSize;

    @ComponentAttrField(labelText = "广告位链接", remark = "type==3时填写")
    private String bannerLink;

    @ComponentAttrField(labelText = "广告位背景图", remark = "type==3时填写")
    private String bannerImg;

    @ComponentAttrField(labelText = "备注", remark = "")
    private String remark;
}
