package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.RankAwardConfig;
import com.yy.gameecology.activity.bean.RankAwardItem;
import com.yy.gameecology.activity.bean.RankDataEvent;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankItem;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.common.bean.GuildInfoBean;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.RankCardComponentAttr;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * desc:榜单加成组件
 * <p>
 * 目前具体支撑玩法：
 * 1、陪玩520活动荣耀卡
 *
 * @createBy 曾文帜
 * @create 2021-04-22 10:50
 **/
@UseRedisStore
@Component
public class RankCardComponent extends BaseActComponent<RankCardComponentAttr> {

    //积分卡积分余额
    private final String cardScoreBalanceHashKey = "card_score_balance_%s";

    //积分卡流水记录
    private final String cardScoreAddRecordListKey = "card_score_record";


    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private EnrollmentNewService enrollmentService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private MemberInfoService memberInfoService;


    @Override
    public Long getComponentId() {
        return ComponentId.RANKING_CARD;
    }


    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = false)
    public void onPhaseTimeEnd(PhaseTimeEnd event, RankCardComponentAttr attr) {
        log.info("onPhaseTimeStart start -> event:{}, attr:{}", event, attr);
        String checkName = "onPhaseTimeEnd:" + event.getEkey();
        String checkValue = event.getTimestamp() + ":" + event.getSeq();
        //请求去重
        String groupCode = getRedisGroupCode(event.getActId());
        if (!actRedisDao.setNX(groupCode, makeKey(attr, checkName), checkValue)) {
            log.warn("rankCardComponent onPhaseTimeEnd dup,return, event:{}, attr:{}", event, attr);
            return;
        }

        //奖励来源配置
        String awardSourceKey = event.getRankId() + "," + event.getPhaseId();
        List<RankAwardConfig> awardConfigList = attr.getRankAwardConfig().get(awardSourceKey);
        if (CollectionUtils.isEmpty(awardConfigList)) {
            log.info("award source not config,return,actId:{},key:{}", event.getActId(), awardSourceKey);
            return;
        }

        //奖励信息
        List<RankAwardItem> needAwardResult = Lists.newArrayList();

        String dateStr = TimeKeyHelper.getTimeCode(event.getTimeKey(), DateUtil.getDate(event.getEndTime(), DateUtil.DEFAULT_PATTERN));
        int maxRank = awardConfigList.stream().map(x -> x.getRank()).max(Integer::compareTo).orElse(0);
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(event.getActId(), event.getRankId(), event.getPhaseId(), dateStr, maxRank, null);
        for (Rank rank : ranks) {
            for (RankAwardConfig config : awardConfigList) {
                if (rank.getRank() != config.getRank()) {
                    continue;
                }
                RankAwardItem item = new RankAwardItem();
                item.setAwardScore(config.getAwardValue());
                item.setMemberId(rank.getMember());
                item.setAwardMemberRoleType(config.getAwardMemberRoleType());
                item.setSaveKey(buildBalanceKey(attr, rank.getMember(), config.getAwardMemberRoleType()));
                needAwardResult.add(item);
            }
        }

        awardCard(attr, needAwardResult);

        log.info("onPhaseTimeStart done -> event:{}, attr:{}", event, attr);
    }


    /**
     * 保存积分卡余额和流水记录
     */
    public void awardCard(RankCardComponentAttr attr, List<RankAwardItem> needAwardResult) {
        String groupCode = getRedisGroupCode(attr.getActId());
        actRedisDao.getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            for (RankAwardItem item : needAwardResult) {

                String transKey = makeKey(attr, cardScoreAddRecordListKey);
                String trans = String.format("add:memberId:%s:score:%s:time:%s", item.getMemberId(), item.getAwardScore(), DateUtil.format(new Date()));

                connection.hIncrBy(item.getSaveKey().getBytes(), item.getMemberId().getBytes(), item.getAwardScore());
                connection.lPush(transKey.getBytes(), trans.getBytes());
                log.info("award card,actId:{},memberId:{},score:{},save key:{}", attr.getActId(), item.getMemberId(), item.getAwardScore(), item.getSaveKey());
            }

            return null;
        });
    }

    /**
     * 构建保存积分余额 key
     */
    private String buildBalanceKey(RankCardComponentAttr attr, String memberId, int roleType) {
        String sid = memberId;
        ActivityInfoVo activityInfo = hdztRankingThriftClient.queryActivityInfo(attr.getActId());
        //陪玩团的key以公会id作为后缀，否则难以获取某个公会下的团
        if (roleType == RoleType.PWTUAN.getValue()) {
            EnrollmentInfo enrollmentInfo = enrollmentService.getFirstEnrolMember(attr.getActId(), activityInfo.getBusiId(), roleType, memberId);
            if (enrollmentInfo == null) {
                throw new RuntimeException(String.format("enrollmentInfo not found,actId:%s,memberId:%s", attr.getActId(), memberId));
            }
            sid = enrollmentInfo.getSignSid() + "";
        }

        return buildBalanceKeySuffix(attr, sid, roleType);
    }

    private String buildBalanceKeySuffix(RankCardComponentAttr attr, String sid, int roleType) {
        String suffix = sid;
        if (roleType == RoleType.PWTUAN.getValue()) {
            suffix = sid + "_Tuan";
        }
        return makeKey(attr, String.format(cardScoreBalanceHashKey, suffix));
    }


    /**
     * 使用荣耀卡
     *
     * @param busiId       业务id
     * @param uid          登录的uid
     * @param cardRoleType 给榜单加分的角色类型
     * @param cardMember   给榜单加分的成员，陪玩团的格式特别点，是 运营公会sid,团id
     */
    public void useCard(RankCardComponentAttr attr, long busiId, long uid, int cardRoleType, long roleId, String cardMember) {
        log.info("useCard begin,actId:{},busiId:{},uid:{},cardRoleType:{},cardMember:{}", attr.getActId(), busiId, uid, cardRoleType, cardMember);
        long actId = attr.getActId();
        Date now = commonService.getNow(actId);

        //---各种校验

        if (cardRoleType != RoleType.PWTUAN.getValue() && cardRoleType != RoleType.GUILD.getValue()) {
            throw new SuperException("暂时只支持陪玩团和公会积分卡", SuperException.E_PARAM_ILLEGAL);
        }

        String[] cardMemberArray = cardMember.split(",");
        long sid = Convert.toLong(cardMemberArray[0], 0);
        String memberId = cardMemberArray.length == 1 ? cardMemberArray[0] : cardMemberArray[1];

        //公会ow权限验证
        Map<Long, GuildInfoBean> guildInfoBeanMap = webdbThriftClient.getSessionListByOwnerid(uid);
        if (RoleType.GUILD.getValue() == cardRoleType) {
            adjustGuildInfo(guildInfoBeanMap, uid, attr);
            if (!guildInfoBeanMap.containsKey(sid)) {
                throw new SuperException("请选择正确的公会", SuperException.E_PARAM_ILLEGAL);
            }
        }

        //团身份验证
        if (RoleType.PWTUAN.getValue() == cardRoleType) {
            MemberInfo memberInfo = memberInfoService.getPwTeam(memberId);
            if (memberInfo == null || !(sid + "").equals(memberInfo.getSid())) {
                throw new SuperException("请选择正确的团", SuperException.E_PARAM_ILLEGAL);
            }
        }

        //使用时间验证
        if (attr.getUseCardStartTime() != null) {
            if (now.getTime() < attr.getUseCardStartTime().getTime()) {
                throw new SuperException("使用时间未开始", SuperException.E_PARAM_ILLEGAL);
            }
        }
        if (attr.getUseCardEndTime() != null) {
            if (now.getTime() > attr.getUseCardEndTime().getTime()) {
                throw new SuperException("使用时间已结束", SuperException.E_PARAM_ILLEGAL);
            }
        }

        // 角色ID为0，取报名时的角色
        if (roleId <= 0) {
            EnrollmentInfo enrollmentInfo = enrollmentService.getFirstEnrolMember(attr.getActId(), busiId, cardRoleType, memberId);
            if (enrollmentInfo != null) {
                roleId = enrollmentInfo.getDestRoleId();
            }
        }

        //---以下是数据操作

        //扣减余额
        String balanceKey = buildBalanceKeySuffix(attr, sid + "", cardRoleType);
        String groupCode = getRedisGroupCode(actId);
        long balance = Convert.toLong(actRedisDao.hget(groupCode, balanceKey, memberId));
//        List<Long> incResult = actRedisDao.hIncrWithLimit(groupCode, balanceKey, memberId, -balance, 0);
        List<Long> incResult;
        if (true) {
            throw new RuntimeException("hash_incr_with_limit.lua,lua forbid");
        }
        log.info("dec done,key:{},member:{},balance:{},ret:{}", balanceKey, memberId, balance, JSON.toJSONString(incResult));
        if (incResult.get(0) != 1) {
            throw new SuperException("积分余额不足", SuperException.E_PARAM_ILLEGAL);
        }

        //中台累榜增加相应榜单积分
        String seq = UUID.randomUUID().toString();
        Map<Long, String> actors = Maps.newHashMap();
        actors.put(roleId, memberId);
        RankDataEvent rankDataEvent = new RankDataEvent();
        rankDataEvent.setBusiId(busiId);
        rankDataEvent.setActId(attr.getActId());
        rankDataEvent.setSeq(seq);
        rankDataEvent.setActors(actors);
        rankDataEvent.setItemId(attr.getRankAwardItem());
        rankDataEvent.setCount(1);
        rankDataEvent.setScore(balance);
        rankDataEvent.setTimestamp(System.currentTimeMillis());
        kafkaService.updateRanking(rankDataEvent);
        log.info("useCard update rank,event:{}", JSON.toJSONString(rankDataEvent));

        log.info("useCard done,actId:{},busiId:{},uid:{},cardRoleType:{},cardMember:{}", actId, busiId, uid, cardRoleType, cardMember);
    }

    /**
     * 使用荣耀卡
     *
     * @param actId      活动id
     * @param uid        登录的uid
     * @param cardMember 榜单加分的成员
     */
    public void useCard(long actId, long uid, String cardMember) {
        RankCardComponentAttr attr = getComponentAttr(actId, 1);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=1");
        useCard(attr, attr.getBusiId(), uid, attr.getRoleType(), attr.getRoleId(), cardMember);
    }

    /**
     * 团使用荣耀卡
     */
    public void useCardForTuan(RankCardComponentAttr attr, long uid, String cardMember) {
        String[] cardMemberArray = cardMember.split(",");
        String memberId = cardMemberArray.length == 1 ? cardMemberArray[0] : cardMemberArray[1];

        EnrollmentInfo enrollmentInfo = enrollmentService.getFirstEnrolMember(attr.getActId(), attr.getBusiId()
                , RoleType.PWTUAN.getValue(), memberId);
        if (enrollmentInfo == null) {
            throw new SuperException("请选择正确的公会", SuperException.E_PARAM_ILLEGAL);
        }

        useCard(attr, attr.getBusiId(), uid, RoleType.PWTUAN.getValue(), enrollmentInfo.getDestRoleId(), cardMember);
    }

    public List<RankItem> queryCardScoreBalance(long actId, long uid) {
        RankCardComponentAttr attr = getComponentAttr(actId, 1);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=1");
        return queryCardScoreBalance(attr, attr.getRoleType(), uid);
    }

    /**
     * 查询积分卡余额
     */
    public List<RankItem> queryCardScoreBalance(RankCardComponentAttr attr, int cardRoleType, long uid) {
        List<RankItem> balances = Lists.newArrayList();

        //ow的公会
        Map<Long, GuildInfoBean> guildInfoBeanMap = webdbThriftClient.getSessionListByOwnerid(uid);
        adjustGuildInfo(guildInfoBeanMap, uid, attr);
        log.info("guildInfos={}", JSON.toJSONString(guildInfoBeanMap));
        if (MapUtils.isEmpty(guildInfoBeanMap)) {
            return balances;
        }

        List<String> balanceKey = Lists.newArrayList();
        for (Long sid : guildInfoBeanMap.keySet()) {
            String key = buildBalanceKeySuffix(attr, sid + "", cardRoleType);
            balanceKey.add(key);
        }
        log.info("balanceKeys={}", JSON.toJSONString(balanceKey));

        String groupCode = getRedisGroupCode(attr.getActId());
        List<Object> results = actRedisDao.getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            for (String key : balanceKey) {
                connection.hGetAll(key.getBytes());
            }
            return null;
        });
        if (CollectionUtils.isEmpty(results)) {
            return balances;
        }

        int index = 0;
        for (Long sid : guildInfoBeanMap.keySet()) {
            Map<String, String> cardScoreItem = (Map<String, String>) results.get(index);
            for (String tuanId : cardScoreItem.keySet()) {
                RankItem balanceItem = null;
                long score = Convert.toLong(cardScoreItem.get(tuanId));
                if (RoleType.PWTUAN.getValue() == cardRoleType) {
                    balanceItem = buildTuanInfo(sid, tuanId, score);
                } else if (RoleType.GUILD.getValue() == cardRoleType) {
                    balanceItem = buildGuildInfo(sid, score);
                }

                if (balanceItem == null) {
                    continue;
                }

                balances.add(balanceItem);
            }

            index++;
        }

        return balances;
    }

    private void adjustGuildInfo(Map<Long, GuildInfoBean> guildInfoBeanMap, long uid, RankCardComponentAttr attr) {
        try {
            if (guildInfoBeanMap == null) {
                guildInfoBeanMap = Maps.newHashMap();
            }
            Map<Long, Long> uid2Sid = attr.getUid2Sid();
            // 没有该用户的配置
            Long sid = uid2Sid.getOrDefault(uid, 0L);
            log.info("uid2Sid={},uid={}", uid2Sid, uid);
            if (sid == null || sid <= 0) {
                return;
            }
            // 配置的sid对应的ow已经是uid
            if (guildInfoBeanMap.containsKey(sid)) {
                return;
            }
            guildInfoBeanMap.put(sid, new GuildInfoBean(sid, sid, ""));
        } catch (Exception ex) {
            log.error("adjustGuildInfo error,uid={},actId={}", uid, attr.getActId(), ex);
        }
    }

    /**
     * 团长查询余额
     **/
    public List<RankItem> queryCardScoreBalanceForTuan(RankCardComponentAttr attr, long uid) {
        List<RankItem> balances = Lists.newArrayList();
        Integer tuanId = attr.getTuanMap().get(uid);
        if (tuanId == null || tuanId <= 0) {
            return balances;
        }
        String memberId = tuanId + "";
        EnrollmentInfo enrollmentInfo = enrollmentService.getFirstEnrolMember(attr.getActId(), attr.getBusiId()
                , RoleType.PWTUAN.getValue(), memberId);
        if (enrollmentInfo == null) {
            log.info("enrollmentInfo is empty,memberId={}", memberId);
            return balances;
        }
        MemberInfo memberInfo = memberInfoService.getPwTeam(memberId);
        if (memberInfo == null) {
            log.info("memberInfo is empty,memberId={}", memberId);
            return balances;
        }
        Long sid = Convert.toLong(memberInfo.getSid());
        String key = buildBalanceKeySuffix(attr, sid + "", RoleType.PWTUAN.getValue());
        log.info("key={}", key);

        String groupCode = getRedisGroupCode(attr.getActId());
        List<Object> results = actRedisDao.getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            connection.hGetAll(key.getBytes());
            return null;
        });
        if (CollectionUtils.isEmpty(results)) {
            return balances;
        }
        Map<String, String> cardScoreItem = (Map<String, String>) results.get(0);
        long score = Convert.toLong(cardScoreItem.get(tuanId + ""));
        RankItem balanceItem = buildTuanInfo(sid, tuanId + "", score);
        balances.add(balanceItem);

        return balances;
    }

    /**
     * 获取团信息
     **/
    private RankItem buildTuanInfo(Long sid, String tuanId, long score) {
        RankItem balanceItem = new RankItem();
        String memberId = sid + "," + tuanId;

        MemberInfo memberInfo = memberInfoService.getPwTeam(tuanId);
        if (memberInfo != null) {
            balanceItem.setItemDesc(memberInfo.getName());
        }
        balanceItem.setScore(score);
        balanceItem.setMember(memberId);

        return balanceItem;
    }

    /**
     * 获取公会信息
     **/
    private RankItem buildGuildInfo(long sid, long score) {
        RankItem balanceItem = new RankItem();
        String memberId = sid + "";

        MemberInfo memberInfo = memberInfoService.getPwChannel(memberId);
        if (memberInfo != null) {
            balanceItem.setItemDesc(memberInfo.getName());
        }
        if (StringUtil.isEmpty(balanceItem.getItemDesc())) {
            balanceItem.setItemDesc(memberId);
        }
        balanceItem.setScore(score);
        balanceItem.setMember(memberId);

        return balanceItem;
    }

    /**
     * 使用荣耀卡
     *
     * @param actId
     * @param index
     * @param busiId       业务id
     * @param uid          登录的uid
     * @param cardRoleType 给榜单加分的角色类型
     * @param roleId
     * @param cardMember   给榜单加分的成员，陪玩团的格式特别点，是 运营公会sid,团id
     */
    public void useCard(long actId, Integer index, int busiId, long uid, int cardRoleType, long roleId, String cardMember) {
        int cmptUseInx = Convert.toInt(index, 1);
        RankCardComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + cmptUseInx);
        useCard(attr, busiId, uid, cardRoleType, roleId, cardMember);
    }

    /**
     * 询积分卡余额
     *
     * @param actId
     * @param index
     * @param cardRoleType
     * @param uid
     * @return
     */
    public List<RankItem> queryCardScoreBalance(long actId, Integer index, int cardRoleType, long uid) {
        int cmptUseInx = Convert.toInt(index, 1);
        RankCardComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + cmptUseInx);
        return queryCardScoreBalance(attr, cardRoleType, uid);
    }
}

