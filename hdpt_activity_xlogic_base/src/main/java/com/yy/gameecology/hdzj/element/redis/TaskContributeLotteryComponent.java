package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UpdateTaskReq;
import com.yy.gameecology.activity.bean.UpdateTaskResult;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.exception.LotteryException;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.HdztActorInfoService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.activity.service.layer.LayerSupport;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.LotteryExtParaName;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.AwardRoll;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.TaskContributeLotteryComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.protocol.pb.layer.LayerInfo;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztranking.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-08-29 16:59
 **/
@UseRedisStore
@RequestMapping("/cmpt/taskContributeLotteryComponent")
@RestController
@Component
public class TaskContributeLotteryComponent extends BaseActComponent<TaskContributeLotteryComponentAttr> implements LayerSupport {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private final static Random RANDOM = new Random(System.currentTimeMillis());

    private final static String TASK_KEY_NAME_PREFIX = "task";

    /**
     * 今日忽略弹窗key
     */
    private final static String IGNORE_POP_DAY_KEY = "ignore_pop_%s";

    @Autowired
    private HdztActorInfoService hdztActorInfoService;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;


    @Override
    public Long getComponentId() {
        return ComponentId.TASK_CONTRIBUTE_LOTTERY;
    }

    /**
     * 挂件扩展信息 key，不和其他活动id以及业务id重复即可
     */
    @Override
    public long getActId() {
        return ComponentId.TASK_CONTRIBUTE_LOTTERY;
    }

    /**
     * 今日不再弹窗
     */
    @RequestMapping("/ignorePopToday")
    public Response<String> ignorePopToday(HttpServletRequest req, HttpServletResponse resp, Long actId, Long index) {
        long uid = getLoginYYUid(req, resp);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        setIgnoreNotice(getComponentAttr(actId, index), uid);
        return Response.ok();
    }

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, TaskContributeLotteryComponentAttr attr) throws Exception {
        //not my duty
        if (!(attr.getRankId() == event.getRankId() && attr.getPhaseId() == event.getPhaseId())) {
            return;
        }

        log.info("onRankingScoreChanged,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));

        //---组织过任务请求参数
        long actId = event.getActId();
        String group = getRedisGroupCode(actId);
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        UpdateTaskReq req = new UpdateTaskReq();
        String keyPrefix = makeKey(attr, TASK_KEY_NAME_PREFIX);
        String dataKeyPreFix = keyPrefix;
        if (StringUtil.isNotBlank(attr.getTaskTimeFormat())) {
            dataKeyPreFix = dataKeyPreFix + ":" + DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        }
        req.setKeyPrefix(dataKeyPreFix);
        req.setSeqKeyPrefix(keyPrefix + ":seq");
        req.setSeq(seq);
        req.setRepeatedCheckExpire(attr.getRepeatedCheckExpire());
        req.setSaveCurResult(Const.ONE);
        req.setScore(event.getItemScore());
        req.setMember(event.getMember());
        Map<String, String> contributeRole = calContributeRole(event, attr);
        req.setContributeMember(contributeRole);
        req.setTaskConfig(attr.getTaskConfig());
        req.setRecycleCount(attr.getRecycleCount());
//        String logCntlFlag = (SysEvHelper.isDev() || commonService.isGrey(actId)) ? Const.ONESTR : Const.ZEROSTR;
        req.setLogCntlFlag(Const.ONESTR);

        //---累任务数据
        Clock clock = new Clock();
        UpdateTaskResult updateTaskResult;
        //UpdateTaskResult updateTaskResult = hdzkTaskService.updateTask(group, req);
        //TODO 如果次组件要重新启用，则需要改造此处
        if (true) {
            throw new RuntimeException("update_task.lua,lua forbid");
        }
        clock.tag();

        //---过了任务，发奖、通知
        if (updateTaskResult.getRoundComplete() > 0) {
            log.info("oneMemberPassTask begin,roundComplete:{}", updateTaskResult.getRoundComplete());
            Map<Long, AwardModelInfo> awardModelMap = hdztAwardServiceClient.queryAwardTasks(attr.getLotteryTaskId());
            Map<String, Long> memberLotteryCount = calLotteryMember((int) updateTaskResult.getRoundComplete(), updateTaskResult, attr);
            clock.tag();
            for (String member : memberLotteryCount.keySet()) {
                String lotterySeq = makeKey(attr, "lottery|" + seq + "|" + member);
                oneMemberPassTask(MD5SHAUtil.getMD5(lotterySeq), member, memberLotteryCount.get(member), awardModelMap, event, attr);
                clock.tag();
            }
            log.info("oneMemberPassTask end,roundComplete:{},clock:{}", updateTaskResult.getRoundComplete(), clock);
        }

        //---累中台任务榜
        long rankScore = updateTaskResult.getRoundComplete();
        if (StringUtil.isNotBlank(attr.getUpdateHdztRankItem()) && rankScore > 0) {

            UpdateRankingRequest request = new UpdateRankingRequest();
            request.setBusiId(event.getBusiId());
            request.setActId(attr.getActId());
            request.setSeq(seq);
            request.setActors(event.getActors());
            request.setItemId(attr.getUpdateHdztRankItem());
            request.setCount(1);
            request.setScore(rankScore);
            request.setTimestamp(DateUtil.getDate(event.getOccurTime()).getTime());
            request.setExtData(Maps.newHashMap());

            boolean result = hdztRankingThriftClient
                    .updateRankingWithRetry(request, Const.TOW);
            if (!result) {
                log.error("update rank error,invoke retry,seq:{}", seq);
                throw new RuntimeException("update rank error,invoke retry");
            }
            log.info("update ranking done,seq:{},request:{}", seq, JSON.toJSONString(request));
        }
    }

    /**
     * 挂件任务进度
     */
    @Override
    public Map<String, Object> buildItemMemberExtInfo(long actId, LayerMemberItem layerMemberItem, Map<String, Object> ext) {
        TaskContributeLotteryComponentAttr attr = getUniqueComponentAttr(actId);
        if (attr == null) {
            return ext;
        }
        if (!attr.getLayerExtInfoItemType().equals(layerMemberItem.getItemType())) {
            return ext;
        }
        if (ext == null) {
            ext = Maps.newHashMap();
        }

        //这里先简单做，如果后续要支持多级任务，则需要结合中控过任务逻辑在HdzkTaskService实现
        long score = 0;

        ActorQueryItem queryItem = new ActorQueryItem();
        queryItem.setRankingId(attr.getRankId());
        queryItem.setPhaseId(attr.getPhaseId());
        queryItem.setActorId(layerMemberItem.getMemberId());
        if (StringUtil.isNotBlank(attr.getTaskTimeFormat())) {
            queryItem.setDateStr(DateUtil.format(commonService.getNow(actId), DateUtil.PATTERN_TYPE2));
        }

        queryItem.setWithStatus(false);
        ActorInfoItem infoItem = hdztRankingThriftClient.queryActorRankingInfo(actId, queryItem);
        if (infoItem != null) {
            score = infoItem.getScore();
        }

        ext.put("score", score);
        ext.put("taskConfig", attr.getTaskConfig().stream().mapToLong(Long::longValue).sum());
        return ext;
    }

    /**
     * 计算参与过任务的成员
     *
     * @return key===roleType value==memberid
     */
    private Map<String, String> calContributeRole(RankingScoreChanged event, TaskContributeLotteryComponentAttr attr) {
        Map<String, String> contributeRole = Maps.newHashMap();
        if (MapUtils.isNotEmpty(event.getActors())) {
            for (Long roleId : event.getActors().keySet()) {
                HdztActorInfo hdztActorInfo = hdztActorInfoService.getHdztActorInfo(roleId);
                if (hdztActorInfo == null) {
                    log.error("can not found role config,roleId:{}", roleId);
                    continue;
                }
                String roleType = Convert.toString(hdztActorInfo.getType());
                if (!attr.getContributeRoleType().contains(roleType)) {
                    continue;
                }
                contributeRole.put(roleType, event.getActors().get(roleId));
            }
        }

        return contributeRole;
    }

    /**
     * 运算能够参与抽奖的用户
     *
     * @return key=== memberid   value===抽奖次数
     */
    private Map<String, Long> calLotteryMember(int contributeCount, UpdateTaskResult updateTaskResult, TaskContributeLotteryComponentAttr attr) {
        Map<String, Long> lotteryMember = Maps.newHashMap();
        String group = getRedisGroupCode(attr.getActId());
        //注意：这里每过一级任务都会有1个贡献key，如果是后续有多级任务需求，注意根据根据变化需求调整此处逻辑！！！！！
        for (String memberInfo : updateTaskResult.getContributeKey().keySet()) {
            List<String> keys = updateTaskResult.getContributeKey().get(memberInfo);
            String[] passResult = memberInfo.split("@");
            if (!Const.TRUE_STR.equals(passResult[Const.TOW])) {
                log.warn("this key not pass,memberInfo:{},key:{}", memberInfo, JSON.toJSONString(keys));
                continue;
            }

            for (String key : keys) {
                Set<ZSetOperations.TypedTuple<String>> score = actRedisDao.zrevRange(group, key, attr.getLotteryTopNContribute());
                //优先拿分数符合门槛的抽奖
                List<String> members = resolveFirstLotteryMember(score, attr.getLotteryFirstScore());
                if (CollectionUtils.isEmpty(members)) {
                    members = score.stream().map(ZSetOperations.TypedTuple::getValue).collect(Collectors.toList());
                }

                //随机抽取1个，增加抽奖次数
                String member = members.get(RANDOM.nextInt(members.size()));
                long lotteryCount = lotteryMember.getOrDefault(member, 0L);
                lotteryMember.put(member, lotteryCount + 1);
            }
        }
        return lotteryMember;
    }

    /**
     * 提取优先抽奖的用户
     */
    private List<String> resolveFirstLotteryMember(Set<ZSetOperations.TypedTuple<String>> scores, long threshold) {
        List<String> members = Lists.newArrayList();
        for (ZSetOperations.TypedTuple<String> score : scores) {
            if (score.getScore().longValue() >= threshold) {
                members.add(score.getValue());
            }
        }
        return members;
    }

    private void oneMemberPassTask(String lotterySeq, String member, long lotteryCount, Map<Long, AwardModelInfo> awardModelInfoMap, RankingScoreChanged event, TaskContributeLotteryComponentAttr attr) throws Exception {
        log.info("oneMemberPassTask,seq:{},member:{},count:{}", lotterySeq, member, lotteryCount);
        //抽奖
        long userUid = Convert.toLong(member);
        BatchLotteryResult result = hdztAwardServiceClient
                .doBatchLottery(lotterySeq, BusiId.MAKE_FRIEND.getValue(), userUid, attr.getLotteryTaskId(), (int) lotteryCount
                        , Maps.newHashMap()
                        , ImmutableMap.of(LotteryExtParaName.NOT_FLOW_CONTROL, "1", LotteryExtParaName.LOAD_RECORD_SEQ_DUP, "1")
                        , 3);
        boolean lotteryError = result == null || (result.getCode() != 0 && result.getCode() != LotteryException.E_LOTTERY_COMPLETED);
        if (lotteryError) {
            log.error("doBatchLottery error, player:{}, seq:{}, result:{}", userUid, lotterySeq, result);
            throw new RuntimeException("doBatchLottery error");
        }
        log.info("doBatchLottery result,,seq:{},member:{},result:{}", lotterySeq, member, result);

        MemberInfo userInfoVo = memberInfoService.getMemberInfo(attr.getUserInfoBusiId(), RoleType.USER, member);
        List<AwardRoll> awardRolls = toAwardRoll(result, awardModelInfoMap);
        //---中奖弹窗
        if (!ignoreNotice(attr, userUid)) {
            commonBroadCastService.awardNoticeUnicastWithRetryCheck(lotterySeq + ":notice_user", event.getActId(), userUid, awardRolls);
        }
        //---中大奖，底部广播
        List<AwardRoll> bigAward = awardRolls.stream().filter(p -> attr.getBigAwardPackageIds().contains(p.getPackageId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(bigAward)) {
            Map<String, Object> broadBannerExt = Maps.newHashMap();

            broadBannerExt.put("userNick", Base64.encodeBase64String(userInfoVo.getName().getBytes()));
            broadBannerExt.put("userLogo", userInfoVo.getHdLogo());
            broadBannerExt.put("awardInfo", bigAward);
            RetryTool.withRetryCheck(attr.getActId(), lotterySeq, () -> {
                commonBroadCastService.commonBannerBroadcast(event.getActId(), userUid, 0, "", attr.getAwardBroBannerId(), attr.getAwardBroBannerType(), broadBannerExt, Template.findByValue(attr.getBroTemplate()));
            });
        }
        //GOTO 中大奖移动端广播(用com.yy.gameecology.hdzj.consts.ComponentId.AWARD_APP_BROADCAST组件实现)

        //中奖挂件广播
        RetryTool.withRetryCheck(attr.getActId(), lotterySeq + ":notice_layer", () -> {
            broLayerAwardInfo(userUid, userInfoVo, event, attr);
        });
    }

    /**
     * 挂件展示 用户XXXX成为调色师
     */
    private void broLayerAwardInfo(long userUid, MemberInfo userInfoVo, RankingScoreChanged event, TaskContributeLotteryComponentAttr attr) {
        LayerInfo.LayerPartialRefreshItemValue.Builder itemValue = LayerInfo.LayerPartialRefreshItemValue.newBuilder()
                .setItemKey("passTaskNickName")
                .setItemValue(Base64.encodeBase64String(userInfoVo.getName().getBytes()));

        LayerInfo.LayerPartialRefreshItem.Builder items = LayerInfo.LayerPartialRefreshItem.newBuilder()
                .setItemType(attr.getNoticeLayerItemType())
                .addItemValue(itemValue);

        LayerInfo.LayerPartialRefreshBroadcast.Builder partial = LayerInfo.LayerPartialRefreshBroadcast.newBuilder()
                .setActId(attr.getActId())
                .addItems(items);

        String subChannel = StringUtil.EMPTY;
        //只能在这里提取，因为随机抽奖，历史贡献当事人很有可能不在频道里面！！！
        for (Long roleId : event.getActors().keySet()) {
            if (attr.getNoticeLayerFilterRoleId().contains(roleId)) {
                subChannel = event.getActors().get(roleId);
            }
        }
        if (StringUtil.isBlank(subChannel)) {
            log.warn("not found sub channel,actId:{},uid:{}", attr.getActId(), userUid);
            return;
        }
        String[] channelContent = subChannel.split("_");
        long sid = Convert.toLong(channelContent[0]);
        long ssid = Convert.toLong(channelContent[1]);
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.LayerPartialRefreshBroadcastUri_VALUE)
                .setLayerPartialRefreshBroadcast(partial).build();
        svcSDKService.broadcastSub(sid, ssid, msg);

    }

    private List<AwardRoll> toAwardRoll(BatchLotteryResult lotteryResult, Map<Long, AwardModelInfo> awardModelInfoMap) {
        List<AwardRoll> awardRolls = Lists.newArrayList();
        for (long packageId : lotteryResult.getRecordPackages().values()) {
            AwardModelInfo awardModelInfo = awardModelInfoMap.get(packageId);

            AwardRoll awardRoll = new AwardRoll();
            awardRoll.setPrize(awardModelInfo.getPackageName());
            awardRoll.setLogo(awardModelInfo.getPackageImage());
            awardRoll.setPackageId(packageId);
            awardRolls.add(awardRoll);
        }
        return awardRolls;
    }

    /**
     * 设置今日不再出获奖弹窗提示
     */
    private void setIgnoreNotice(TaskContributeLotteryComponentAttr attr, long uid) {
        String key = makeIgnorePopKey(attr);
        actRedisDao.hsetnx(getRedisGroupCode(attr.getActId()), key, Convert.toString(uid), DateUtil.getNowYyyyMMddHHmmss());
        log.info("setIgnoreNotice ok,actId:{},uid:{}", attr.getActId(), uid);
    }

    /**
     * 如设置了今日不再提示，今日不弹窗
     */
    private boolean ignoreNotice(TaskContributeLotteryComponentAttr attr, long uid) {
        String key = makeIgnorePopKey(attr);
        String content = actRedisDao.hget(getRedisGroupCode(attr.getActId()), key, Convert.toString(uid));
        return StringUtil.isNotBlank(content);
    }

    private String makeIgnorePopKey(TaskContributeLotteryComponentAttr attr) {
        String dayCode = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        return makeKey(attr, String.format(IGNORE_POP_DAY_KEY, dayCode));
    }
}
