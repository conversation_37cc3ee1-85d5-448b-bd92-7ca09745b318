package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.mq.hdzk.DayTaskCompleteEvent;
import com.yy.gameecology.activity.bean.mq.hdzk.DayTaskPollOutEvent;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.common.bean.PairTemplateBean;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.model.gameecology.Cmpt1017DayTaskItemState;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.MD5SHAUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.bean.daytask.CurDayTask;
import com.yy.gameecology.hdzj.bean.daytask.CurDayTaskItem;
import com.yy.gameecology.hdzj.bean.daytask.DayTaskState;
import com.yy.gameecology.hdzj.bean.daytask.UpdateDayTaskReq;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.DayTaskComponent2Attr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardLimitConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.DayTaskConfig;
import com.yy.gameecology.hdzj.element.component.dao.DayTaskDao;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * desc:存储换成mysql
 *
 * <AUTHOR>
 * @date 2024-09-18 17:06
 **/
@RestController
@RequestMapping("/1017")
@Component
public class DayTaskComponent2 extends BaseActComponent<DayTaskComponent2Attr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private LimitControlComponent limitControlComponent;

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private WhitelistComponent whitelistComponent;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private CommonService commonService;

    @Autowired
    private DayTaskDao dayTaskDao;

    @Autowired
    private KafkaService kafkaService;


    @Override
    public Long getComponentId() {
        return ComponentId.DAY_TASK2;
    }


    @RequestMapping("/queryCurDayTaskInfo")
    public CurDayTask queryCurDayTaskInfo(long actId, long index, String member) {
        return queryCurDayTask(actId, index, member);
    }


    /**
     * 更新任务
     * 接口幂等、可重复调用，有问题抛出异常
     */
    public void updateTask(UpdateDayTaskReq req) {
        log.info("updateTask begin,req={}", JSON.toJSONString(req));
        Assert.hasLength(req.getSeq(), "seq不为空");
        Assert.hasLength(req.getItem(), "item 不为空");
        Assert.hasLength(req.getDayCode(), "dayCode 不为空");
        Assert.isTrue(req.getActId() > 0, "actId 0");

        var attr = req.getCmptIndex() > 0 ? getComponentAttr(req.getActId(), req.getCmptIndex()) : getUniqueComponentAttr(req.getActId());
        //白名单判断
        if (!checkWhiteList(attr, req)) {
            return;
        }

        //活动中台白名单判断
        if (SysEvHelper.isDeploy() && commonService.isGrey(attr.getActId())) {
            boolean inWhiteList = hdztRankingThriftClient.checkWhiteList(attr.getActId(), RoleType.USER, req.getMember());
            if (!inWhiteList) {
                log.info("updateTask grey not in whitelist uid:{},actId:{}", req.getMember(), attr.getActId());
                return;
            }
        }

        DayTaskState dayTaskState = dayTaskDao.queryDayTaskState(req.getActId(), attr.getCmptUseInx(), req.getMember());

        //判断是否完成了当日所有任务
        if (alreadyCompleteDayTask(dayTaskState, req)) {
            return;
        }
        //获取正在过的任务
        int curTaskDayIndex = calCurTaskDayIndex(dayTaskState, req.getMember());
        if (!hasTaskItem(attr, req, curTaskDayIndex)) {
            return;
        }

        //奖池判断
        if (!checkAwardPoolLimit(attr, curTaskDayIndex)) {
            log.warn("award pool limit");
            return;
        }

        //判断当前是否完成了任务，如已完成，则保存完任务数据
        boolean completeTask = calIsCompleteCurDayTask(attr, dayTaskState, req.getItem(), req.getMember(), curTaskDayIndex, req.getValue());
        //保存数据
        int newTaskDayIndex = getNextDayIndex(attr.getDayTaskConfig(), curTaskDayIndex);
        dayTaskDao.saveTaskData(attr, req, completeTask, curTaskDayIndex, newTaskDayIndex);

        if (!completeTask) {
            log.info("not pass task,member:{},itemSeq:{},add:{},curTaskDayIndex:{}", req.getMember(), req.getSeq(), req.getValue(), curTaskDayIndex);
            return;
        }

        //发奖(注意：里面有 天数+用户 组成的seq防重，确保每个用户1天只发1次奖品)
        AwardAttrConfig awardAttrConfig = releaseAward(attr, req, curTaskDayIndex);

        //发布完成任务事件
        completeTaskEventNotify(attr, req, curTaskDayIndex, awardAttrConfig);

        log.info("updateTask done,req={}", JSON.toJSONString(req));
    }

    public CurDayTask queryCurDayTask(DayTaskComponent2Attr attr, String member) {
        CurDayTask curDayTask = new CurDayTask();
        long actId = attr.getActId();
        String nowDayCode = DateUtil.format(commonService.getNow(actId), DateUtil.PATTERN_TYPE2);

        DayTaskState dayTaskState = dayTaskDao.queryDayTaskState(attr.getActId(), attr.getCmptUseInx(), member);
        int curDayIndex = calCurTaskDayIndex(dayTaskState, member);
        int dayShowIndex = curDayIndex;
        //如果是当天完成的任务，进度要停留在当天
        boolean isCompleteDayTask = calIsCompleteCurDayTask(attr, dayTaskState, null, member, curDayIndex, 0);
        String completeDayCode = calCompleteDayCode(dayTaskState);
        int maxIndex = getMaxDayIndex(attr);
        boolean completeLastTask = maxIndex == curDayIndex && isCompleteDayTask;
        if (StringUtil.isNotBlank(completeDayCode) && completeDayCode.equals(nowDayCode) && !completeLastTask) {
            dayShowIndex = curDayIndex - 1;
        }

        curDayTask.setDayShowIndex(dayShowIndex);
        curDayTask.setCompleteDayCode(completeDayCode);

        //已完成打卡天数
        int signed = isCompleteDayTask ? curDayIndex : curDayIndex - 1;
        curDayTask.setSignedDay(signed);

        int finalCurDayIndex = dayShowIndex;
        List<DayTaskConfig> configs = attr.getDayTaskConfig().stream().filter(p -> p.getTaskDayIndex() == finalCurDayIndex).toList();
        Map<String, Long> completeTaskItem = dayTaskState.getItemStates().stream()
                .filter(p -> p.getDayIndex().equals(finalCurDayIndex))
                .collect(Collectors.toMap(Cmpt1017DayTaskItemState::getItem, Cmpt1017DayTaskItemState::getValue));
        List<CurDayTaskItem> itemResult = Lists.newArrayList();
        for (DayTaskConfig dayTaskConfig : configs) {
            CurDayTaskItem curDayTaskItem = new CurDayTaskItem();
            curDayTaskItem.setTaskName(dayTaskConfig.getTaskName());
            curDayTaskItem.setPassItem(dayTaskConfig.getPassItem());
            long value = completeTaskItem.getOrDefault(dayTaskConfig.getPassItem(), 0L);
            int state = value > 0 ? 1 : 0;
            curDayTaskItem.setState(state);

            itemResult.add(curDayTaskItem);
        }
        curDayTask.setCurTaskItem(itemResult);


        return curDayTask;
    }


    /**
     * 用户当前任务信息
     */
    public CurDayTask queryCurDayTask(long actId, long cmptIndex, String member) {

        var attr = getComponentAttr(actId, cmptIndex);
        return queryCurDayTask(attr, member);
    }

    public PairTemplateBean<Integer, AwardAttrConfig> getNearAwardConfig(long actId, long cmptIndex, int curDayTask) {
        var attr = getComponentAttr(actId, cmptIndex);
        int maxDayIndex = getMaxDayIndex(attr);
        for (int i = curDayTask; i <= maxDayIndex; i++) {
            if (attr.getDayAward().containsKey(i)) {
                return new PairTemplateBean<>(curDayTask, attr.getDayAward().get(i));
            }
        }
        return null;
    }


    /**
     * 用户当前正在过的任务配置
     */
    public List<DayTaskConfig> queryDayTaskConfig(long actId, long cmptIndex, String member) {
        var attr = getComponentAttr(actId, cmptIndex);
        var state = dayTaskDao.queryDayTaskState(actId, cmptIndex, member);
        int curTaskDayIndex = calCurTaskDayIndex(state, member);
        return attr.getDayTaskConfig().stream().filter(p -> p.getTaskDayIndex() == curTaskDayIndex).collect(Collectors.toList());
    }


    public boolean checkAwardPoolLimit(DayTaskComponent2Attr attr, int curTaskDayIndex) {
        AwardAttrConfig awardConfig = attr.getDayAward().get(curTaskDayIndex);
        if (awardConfig == null) {
            return true;
        }

        if (attr.getLimitControlIndex() <= 0) {
            return true;
        }

        long balance = limitControlComponent.queryPoolBalance(attr.getActId(), attr.getLimitControlIndex(), awardConfig.getTAwardPkgId());
        return balance == -1 || balance > awardConfig.getNum();
    }


    private AwardAttrConfig releaseAward(DayTaskComponent2Attr attr, UpdateDayTaskReq req, int curTaskDayIndex) {
        String seqSuffix = MD5SHAUtil.getMD5(makeKey(attr, String.format("dayTaskAwardHandler:%s:%s", req.getMember(), curTaskDayIndex)));
        AwardAttrConfig awardConfig = attr.getDayAward().get(curTaskDayIndex);
        if (awardConfig != null) {
            AwardLimitConfig limitConfig = limitControlComponent.getLimitConfig(attr.getActId(), attr.getLimitControlIndex(), awardConfig.getTAwardPkgId());
            //奖池扣除金额
            long reduceAmount = awardConfig.getAwardAmount();
            if (limitConfig != null && limitConfig.getLimit() > 0 && reduceAmount > 0) {
                String limitSeq = "seq:award_limit:" + seqSuffix;
                var addResult = reduceAwardPool(attr, req.getMember(), awardConfig.getTAwardTskId(), awardConfig.getTAwardPkgId(), limitSeq, reduceAmount);
                if (addResult.isViolateLimit()) {
                    log.info("releaseAward pool out,req:{},curTaskDayIndex:{},awardAmount():{},limit:{},addResult:{}", JSON.toJSONString(req), curTaskDayIndex, reduceAmount, limitSeq, JSON.toJSONString(addResult));
                    return awardConfig;
                }
            }

            String awardSeq = "dayTaskAward:" + seqSuffix;
            String time = DateUtil.format(commonService.getNow(attr.getActId()));
            long userUid = Convert.toLong(req.getMember());
            Map<Long, Integer> packageIdAmount = ImmutableMap.of(awardConfig.getTAwardPkgId(), awardConfig.getNum());
            //异步发奖
            hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.GAME_ECOLOGY.getValue(), userUid, awardConfig.getTAwardTskId(), ImmutableMap.of(awardConfig.getTAwardTskId(), packageIdAmount), awardSeq, Maps.newHashMap());
            log.info("award done,actId:{},uid:{},awardSeq:{},awardConfig:{}", attr.getActId(), userUid, awardSeq, JSON.toJSONString(awardConfig));

        }

        return awardConfig;
    }

    /**
     * 奖池扣减
     */
    public CommonDataDao.ValueIncResult reduceAwardPool(DayTaskComponent2Attr attr, String member, long taskId, long packageId, String limitSeq, long amount) {
        log.info("reduceAwardPool,member:{},packageId:{},seq:{},amount:{}", member, packageId, limitSeq, amount);

        var valueIncResult = limitControlComponent.valueIncrIgnoreWithLimit(attr.getActId(), attr.getLimitControlIndex(), packageId, limitSeq, amount);


        long balance = limitControlComponent.queryPoolBalance(attr.getActId(), attr.getLimitControlIndex(), packageId);
        if (valueIncResult.isViolateLimit() || balance <= 0) {
            //发布奖池消耗完成事件
            DayTaskPollOutEvent event = new DayTaskPollOutEvent();
            event.setActId(attr.getActId());
            event.setSeq(makeKey(attr, "poll_out:" + limitSeq));
            event.setTaskId(taskId);
            event.setPackageId(packageId);
            kafkaService.sendHdzkCommonEvent(event);
            log.info("reduceAwardPool out,member:{},packageId:{},seq:{},amount:{}", member, packageId, limitSeq, amount);
        }

        log.info("reduceAwardPool done,member:{},packageId:{},seq:{},amount:{},addResult:{}", member, packageId, limitSeq, amount, JSON.toJSONString(valueIncResult));
        return valueIncResult;
    }


    /**
     * 任务通知
     */
    private void completeTaskEventNotify(DayTaskComponent2Attr attr, UpdateDayTaskReq req, int curTaskDayIndex, AwardAttrConfig awardAttrConfig) {
        List<Long> taskId = attr.getDayTaskConfig()
                .stream()
                .filter(p -> p.getTaskDayIndex() == curTaskDayIndex)
                .map(DayTaskConfig::getTaskId).toList();
        DayTaskCompleteEvent event = new DayTaskCompleteEvent();
        event.setActId(attr.getActId());
        event.setSeq(makeKey(attr, "complete:" + curTaskDayIndex + ":" + req.getMember()));
        event.setTaskId(taskId);
        event.setDayIndex(curTaskDayIndex);
        event.setMemberId(req.getMember());
        if (awardAttrConfig != null) {
            event.setAwardTaskId(Convert.toLong(awardAttrConfig.getTAwardTskId(), 0));
            event.setAwardPackageId(Convert.toLong(awardAttrConfig.getTAwardPkgId(), 0));
            event.setAwardNum(Convert.toInt(awardAttrConfig.getNum(), 0));
        }
        kafkaService.sendHdzkCommonEvent(event);
    }

    private boolean checkWhiteList(DayTaskComponent2Attr attr, UpdateDayTaskReq req) {
        if (attr.getWhiteListIndex() > 0) {
            boolean inWhiteList = whitelistComponent.inWhitelist(req.getActId(), attr.getCmptUseInx(), req.getMember());
            if (!inWhiteList) {
                log.info("updateTask not in white list,seq:{},actId:{},memberId:{},item:{}", req.getSeq(), req.getActId(), req.getMember(), req.getItem());
                return false;
            }
        }

        return true;
    }

    private boolean alreadyCompleteDayTask(DayTaskState dayTaskState, UpdateDayTaskReq req) {
        if (dayTaskState.getDayTaskState() == null) {
            return false;
        }
        //因为第二天自然日是新任务，所以这里不相等，肯定是没完成当前任务
        return req.getDayCode().equals(dayTaskState.getDayTaskState().getLastCompleteDay());
    }


    public int calCurTaskDayIndex(DayTaskState state, String member) {
        if (state == null || state.getDayTaskState() == null) {
            return Const.ONE;
        }
        return Convert.toInt(state.getDayTaskState().getCurDayIndex(), Const.ONE);
    }

    private boolean hasTaskItem(DayTaskComponent2Attr attr, UpdateDayTaskReq req, int curTaskDayIndex) {
        //设置当天子任务状态
        List<DayTaskConfig> curDayTaskConfig = attr.getDayTaskConfig()
                .stream()
                .filter(p -> p.getTaskDayIndex() == curTaskDayIndex)
                .toList();
        boolean noneItem = curDayTaskConfig.stream().noneMatch(p -> p.getPassItem().equals(req.getItem()));
        if (noneItem) {
            log.info("none item,actId:{},seq:{},dayIndex:{},item:{}", req.getActId(), req.getSeq(), curTaskDayIndex, req.getItem());
            return false;
        }
        return true;
    }

    private boolean calIsCompleteCurDayTask(DayTaskComponent2Attr attr, DayTaskState dayTaskState, String updateItem, String member, int dayIndex, long preAdd) {
        List<DayTaskConfig> curDayTaskConfig = attr.getDayTaskConfig()
                .stream()
                .filter(p -> p.getTaskDayIndex() == dayIndex).toList();
        for (DayTaskConfig config : curDayTaskConfig) {
            Optional<Cmpt1017DayTaskItemState> itemState = dayTaskState.getItemStates()
                    .stream()
                    .filter(p -> p.getDayIndex().equals(dayIndex) && p.getItem().equals(config.getPassItem())).findFirst();
            long value = itemState.isPresent() ? itemState.get().getValue() : 0;
            long itemPreAdd = config.getPassItem().equals(updateItem) ? preAdd : 0;
            if (value + itemPreAdd < config.getPassValue()) {
                return false;
            }
        }

        return true;
    }

    private static String calCompleteDayCode(DayTaskState dayTaskState) {
        return dayTaskState == null
                || dayTaskState.getDayTaskState() == null
                || StringUtil.isBlank(dayTaskState.getDayTaskState().getLastCompleteDay())
                ?
                StringUtil.EMPTY
                : dayTaskState.getDayTaskState().getLastCompleteDay();
    }


    private int getNextDayIndex(List<DayTaskConfig> dayTaskConfigs, int curDayIndex) {
        int maxIndex = dayTaskConfigs.stream().mapToInt(DayTaskConfig::getTaskDayIndex).max().orElse(0);
        return Math.min(maxIndex, curDayIndex + 1);
    }

    private int getMaxDayIndex(DayTaskComponent2Attr attr) {
        return attr.getDayTaskConfig().stream().mapToInt(DayTaskConfig::getTaskDayIndex).max().orElse(0);
    }


}
