package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2061LatestCp;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.RankUtils;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.LatestCpComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.LatestCpDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.Collection;
import java.util.Date;

/**
 * 记录给主持送礼的最后一个神豪，组成最后的CP
 */
@Slf4j
@Component
public class LatestCpComponent extends BaseActComponent<LatestCpComponentAttr> {

    @Autowired
    private LatestCpDao latestCpDao;

    @Override
    public Long getComponentId() {
        return ComponentId.LATEST_CP;
    }

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, LatestCpComponentAttr attr) {
        if (!attr.getRankIds().contains(event.getRankId())) {
            return;
        }

        if (attr.getPhaseId() > 0 && attr.getPhaseId() != event.getPhaseId()) {
            return;
        }

        final long actId = event.getActId();
        Date time;
        try {
            time = DateUtils.parseDate(event.getOccurTime(), DateUtil.DEFAULT_PATTERN);
        } catch (ParseException ignore) {
            time = commonService.getNow(actId);
        }

        final String dateCode;
        if (attr.isIgnoreDateCode()) {
            dateCode = StringUtils.EMPTY;
        } else {
            dateCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), time);
        }

        String member = event.getMember();
        Pair<Long, Long> cpUids = RankUtils.getCpUidByMemberId(member);
        if (cpUids == null) {
            log.error("onRankingScoreChanged cpUids is null");
            return;
        }

        Pair<Long, Long> hallChannel = RankUtils.getHallChannel(attr.getHallActorIds(), event.getActors());
        if (hallChannel == null) {
            log.error("onRankingScoreChanged hallChannel is null");
            return;
        }

        final long sid = hallChannel.getLeft(), ssid = hallChannel.getRight();

        Cmpt2061LatestCp record = new Cmpt2061LatestCp();
        record.setActId(attr.getActId());
        record.setCmptUseInx(attr.getCmptUseInx());
        record.setDateCode(dateCode);
        record.setRankId(event.getRankId());
        record.setAnchorUid(cpUids.getRight());
        record.setUserUid(cpUids.getLeft());
        record.setSid(sid);
        record.setSsid(ssid);
        record.setGiftTime(time);

        latestCpDao.saveLatestCp(record);
    }

    public Cmpt2061LatestCp getLatestCp(long actId, long cmptUseInx, String dateCode, long rankId, long anchorUid) {
        return latestCpDao.getLatestCp(actId, cmptUseInx, dateCode, rankId, anchorUid);
    }

    public Cmpt2061LatestCp getLatestCp(long actId, long cmptUseInx, String dateCode, Collection<Long> rankIds, long anchorUid) {
        return latestCpDao.getLatestCp(actId, cmptUseInx, dateCode, rankIds, anchorUid);
    }
}
