package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class ManualBannerComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    protected int busiId;

    @ComponentAttrField(labelText = "横幅类型", remark = "【目前只支持3和6】组件类型 1.alert 2.toast 3.svga横幅 4. 通用web弹窗 5. mp4特效 6. svga特效")
    protected int contentType;
    /**
     * 1 -支持跳转 0 -不支持跳转 注：svga需要有跳转按钮
     */
    @ComponentAttrField(labelText = "是否跳转", remark = "1 -支持跳转 0 -不支持跳转")
    protected int jump;

    @ComponentAttrField(labelText = "高度", remark = "横幅类型(contentType)==3使用 可选 高度设计，以设计图高度为准，客户端宽度为屏幕宽度，高度换算为机型高度")
    protected int height;

    @ComponentAttrField(labelText = "播放时间(秒)", remark = "【横幅类型==3时有效】播放总时长,若需要循环,填写循环n次的总时长。ios的逻辑是如果duration小于循环次数*n,则播放时长以duration为准，如duration大于循环次数*n则svga自己播放完成后就会结束")
    protected int duration;

    @ComponentAttrField(labelText = "循环次数", remark = "循环播放次数, 0-无限循环(勿填0)")
    protected int loops = 1;

    @ComponentAttrField(labelText = "优先级", remark = "可选 播放动效等候队列的优先级：优先级越高，从等候队列到播放的时机越早；值越小，优先级越高 目前默认全屏礼物特效的优先级为999，如果优先级低于全屏礼物特效优先级，需要该值大于999")
    protected int level;

    @ComponentAttrField(labelText = "布局", remark = " 横幅类型(contentType)==6使用 可选 动画播放位置（左右充满，垂直对齐类型）0：全屏播放；1：居中对齐播放；2：顶部对齐播放；3：底部对齐播放")
    protected int layoutType;

    @ComponentAttrField(labelText = "布局边距", remark = "相对父布局的间距 2个元素的数组，分别对应顶部和底部的间距；对应位置为[top, bottom]。通常top、bottom为0。配置例子：{\"android\":[10,0],\"ios\":[0,0]}")
    protected String layoutMargin = "{\"android\":[10,0],\"ios\":[0,0]}";


    @ComponentAttrField(labelText = "宽高比", remark = "横幅类型(contentType)==6使用 必填 宽高比 客户端默认宽为全屏，svga高度根据宽高比计算,填写实例：6:9")
    protected String whRatio;

    /**
     * svga点击按钮key name
     */
    @ComponentAttrField(labelText = "svga点击按钮key name")
    protected String clickLayerName;
    /**
     * 横幅svga url
     */
    @ComponentAttrField(labelText = "不带围观按钮的横幅svga", remark = "不带围观按钮的svga")
    protected String svgaURL;

    /**
     * 横幅svga url
     * 需要跳转，就设置jump==1，然后把带围观和不带围观的url都填上，会处理当前频道不带围观，其他频道带围观
     */
    @ComponentAttrField(labelText = "带围观按钮的横幅svga", remark = "带围观按钮的svga需要跳转，就设置jump==1，然后把带围观和不带围观的url都填上，会处理当前频道不带围观，其他频道带围观")
    protected String jumpSvgaURL;

    /**
     * 礼物滚屏url
     */
    @ComponentAttrField(labelText = "不带围观的礼物滚屏svga", remark = "当前端限制横幅时,前端将使用这个svga播放在礼物滚屏")
    protected String miniURL;

    /**
     * 横幅svga url
     */
    @ComponentAttrField(labelText = "带围观按钮的礼物滚屏svga", remark = "带围观按钮的礼物滚屏svga,前端将使用这个svga播放在礼物滚屏")
    protected String jumpMiniURL;

    @ComponentAttrField(labelText = "svgaText文案配置",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = BannerSvgaTextConfig.class, labelText = "富文本svga文案配置，{nick}非公会取实际昵称，公会取asid")})
    protected List<BannerSvgaTextConfig> svgaTexts;

    @ComponentAttrField(labelText = "svga图片key",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "imgkey"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "图片地址，{avatar}取实际头像")})
    protected Map<String, String> svgaImgLayers;

    @ComponentAttrField(labelText = "角色类型", dropDownSourceBeanClass = RoleTypeSource.class)
    protected int roleType;

    @ComponentAttrField(labelText = "发放对象", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class), remark = "多个英文逗号隔开；用户/主持填UID，公会填SID，厅填SID_SSID，语音房房间填房间ID，语音房家族填家族ID")
    protected List<String> members;

    @ComponentAttrField(labelText = "触发白名单", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class), remark = "多个使用英文逗号隔开")
    protected Set<Long> triggerWhitelist;

}
