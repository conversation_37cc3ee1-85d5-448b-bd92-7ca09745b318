package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.hdzt.ActShopExchangeResult;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> 2024/3/21
 */
@Getter
@Setter
public class ActShopComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "风控策略key")
    private String riskStrategyKey;

    @ComponentAttrField(labelText = "推荐的交友房间列表", remark = "多个房间时逗号分隔;格式:sid_ssid,sid_ssid",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class)})
    private Set<String> recommendChannels = Collections.emptySet();

    @ComponentAttrField(labelText = "商城分区", remark = "多个房间时逗号分隔;分区显示以此顺序显示",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class)})
    private List<String> areaInfo = Lists.newArrayList();

    @ComponentAttrField(labelText = "兑换商品信息",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = ActShopExchangeResult.class)})
    private List<ActShopExchangeResult> shopExchangeResult;

    @ComponentAttrField(labelText = "单价配置", subFields = {
            @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "货币CID", remark = "单价货币cid"),
            @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "货币名称", remark = "单价货币名称")
    })
    private Map<String, String> priceDetails;

    @ComponentAttrField(labelText = "兑换文案", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "返回码", remark = "兑换接口返回码"),
            @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "文案", remark = "返回码对应的文案")
    }, remark = "针对兑换返回码，配置返回前端展示的文案")
    private Map<Integer, String> exchangeMessages;
}
