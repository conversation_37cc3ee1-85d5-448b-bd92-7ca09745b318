package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-11-13 16:17
 **/
@Data
public class AwardReplaceConfig {
    @ComponentAttrField(labelText = "奖池id")
    private long taskId;

    @ComponentAttrField(labelText = "奖包id")
    private long packageId;

    @ComponentAttrField(labelText = "奖池不足替换的奖池id")
    private long replaceTaskId;

    @ComponentAttrField(labelText = "奖池不足替换的奖包id")
    private long replacePackageId;
}
