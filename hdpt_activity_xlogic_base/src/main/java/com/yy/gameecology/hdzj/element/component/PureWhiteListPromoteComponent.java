package com.yy.gameecology.hdzj.element.component;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeStart;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.utils.MyMapUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.CommonPBOperateRequest;
import com.yy.gameecology.hdzj.bean.CommonPBOperateResp;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.PureWhiteListPromoteComponentAttr;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
@Component
public class PureWhiteListPromoteComponent extends BaseActComponent<PureWhiteListPromoteComponentAttr> {

    @Autowired
    private EnrollmentNewService enrollmentNewService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Override
    public Long getComponentId() {
        return ComponentId.PURE_WHITE_LIST_PROMOTE;
    }

    @Override
    public CommonPBOperateResp commonOperatePbRequest(CommonPBOperateRequest request) {
        return super.commonOperatePbRequest(request);
    }

    @HdzjEventHandler(value = PhaseTimeStart.class, canRetry = false)
    public void handleRankingTimeStart(PhaseTimeStart event, PureWhiteListPromoteComponentAttr attr) {
        if (attr.getSrcRankId() != event.getRankId() || attr.getSrcPhaseId() != event.getPhaseId()) {
            log.info("handleRankingTimeStart rankId or phaseId not match!");
            return;
        }

        Map<Integer, String> whiteList = attr.getWhiteList();
        if (MapUtils.isEmpty(whiteList)) {
            log.info("handleRankingTimeStart white list is empty!");
            return;
        }

        long actId = attr.getActId(), rankId = attr.getSrcRankId(), phaseId = attr.getSrcPhaseId();
        Map<String, Long> roleIdMap = new HashMap<>(whiteList.size());
        for (String memberId : whiteList.values()) {
            EnrollmentInfo memberInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(actId, attr.getBusiId(), attr.getRoleType(), memberId);
            if (memberInfo == null) {
                roleIdMap.put(memberId, attr.getDefaultRoleId());
                continue;
            }

            roleIdMap.put(memberId, memberInfo.getDestRoleId());
        }

        for (int rank : whiteList.keySet()) {
            String memberId = whiteList.get(rank);
            String seq = makeKey(attr, String.format("pw-settle-update-%s-%s", rankId, memberId));
            long score = 10000 - rank;
            Map<Long, Long> rankScore = ImmutableMap.of(rankId, score);
            boolean updateResult = hdztRankingThriftClient.updateRankWithRetry(seq, actId, attr.getPromoteItem(), memberId, roleIdMap.get(memberId), score, rankScore, System.currentTimeMillis(), 3, BusiId.MAKE_FRIEND.getValue());
            if (!updateResult) {
                throw new RuntimeException("更新晋级榜单失败了，请触发重试");
            }
        }

        //通知结算
        for (Long promoteRankId : attr.getPromoteNotifySettle().keySet()) {
            String tag = attr.getPromoteNotifySettle().get(promoteRankId);
            boolean result = hdztRankingThriftClient.notifySettleWithRetry(attr.getActId(), promoteRankId, 1, tag, 3);
            if (!result) {
                throw new RuntimeException("结算严重错误,通知结算失败,请触发重试");
            }
            log.info("handleRankingTimeStart notify settle success");
        }
    }

    @HdzjEventHandler(value = PromotTimeEnd.class, canRetry = false)
    public void onPKFirstPhasePromoteTimeEnd(PromotTimeEnd event, PureWhiteListPromoteComponentAttr attr) {
        if (attr.getPromotePhaseId() <= 0) {
            log.info("onPKFirstPhasePromoteTimeEnd phaseId {} not set!", attr.getPromotePhaseId());
            return;
        }

        Map<Long, String> promoteNotify = attr.getPromoteNotifySettle();

        if (attr.getPromotePhaseId() != event.getPhaseId() || promoteNotify.size() != 1
                || !attr.getPromoteNotifySettle().containsKey(event.getRankId())) {
            log.info("onPKFirstPhasePromoteTimeEnd phaseId:{} rankId:{} not match!", event.getPhaseId(), event.getRankId());
            return;
        }

        Map<Integer, String> whiteList = attr.getWhiteList();
        LinkedHashMap<Integer, String> sortedWhiteList = MyMapUtil.sortByKey(whiteList, Comparator.comparingInt(o -> o));
        String pkMembers = StringUtils.join(sortedWhiteList.values(), ',');

        log.info("onPKFirstPhasePromoteTimeEnd setDynamicPkMembers with rankId:{}, phaseId:{}, members:{}", event.getRankId(), event.getPhaseId(), pkMembers);

        String seq = makeKey(attr, String.format("set_dynamic_pk-%d-%d", event.getRankId(), event.getPhaseId()));
        boolean result = hdztRankingThriftClient.setDynamicPkMembers(event.getActId(), event.getRankId(), event.getPhaseId(), pkMembers, attr.getSetPkOpUid(), seq, 3);
        if (!result) {
            throw new RuntimeException("结算严重错误,设置pk对阵失败,请触发重试");
        }

        String msg = buildRuliuMsg(attr.getActId(), false, attr.getSrcRankId(), attr.getSrcPhaseId(), "设置属性PK对阵成功");
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());
    }


    private String buildRuliuMsg(long actId, boolean error, long rankId, long phaseId, String msg) {
        ActivityInfoVo hdztActivity = hdztRankingThriftClient.queryActivityInfo(actId);

        return "### <font color=\"" + (error ? "red" : "green") + "\">【中控->晋级结算" + (error ? "错误告警" : "通知") + "】</font>\n" + "#### [" + actId + "]" + hdztActivity.getActName() + "\n" +
                "榜单：" + rankId + "\n" +
                "阶段：" + phaseId + "\n" +
                "信息：" + msg + "\n";
    }
}
