package com.yy.gameecology.hdzj.bean;

import com.yy.gameecology.activity.bean.hdzt.RankPhasePair;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;

/**
 * <AUTHOR>
 * @date 2021.10.23 18:36
 */
public class HonorConfig {
    @ComponentAttrField(labelText = "榜单id")
    private long rankId;
    @ComponentAttrField(labelText = "阶段id")
    private long phaseId;

    /**
     * 对应数据库表act_result type, 1-交友(默认) 2-约战 3-宝贝 4-互动全品类 5-陪玩 10001-挂件top n定制展示
     */
    @ComponentAttrField(labelText = "类型", remark = "1-交友(默认) 2-约战 3-宝贝 4-互动全品类 5-陪玩 6-技能卡 10001-挂件top n定制展示")
    private int type = 1;

    /**
     * 定位榜单位置，最终提取的名次是 [startIndex, startIndex+count] 如榜单第5-8名 startIndex=5
     */
    @ComponentAttrField(labelText = "开始名次",remark = "定位榜单位置，最终提取的名次是 [startIndex, startIndex+count] 如榜单第5-8名 startIndex=5")
    private int startIndex = 1;

    /**
     * 读取个数， 默认前三（冠、亚、季）
     */
    @ComponentAttrField(labelText = "提取数量")
    private long count = 3;

    /**
     * 榜单类型,有些是从pk榜结算，normal:常规榜单（默认）,  pk:PK榜
     */
    @ComponentAttrField(labelText = "榜单类型", remark = "normal:常规榜单（默认）,  pk:PK榜")
    private String rankType = "normal";

    /**
     * 当pk时,读取胜方还是败方，true:胜方（默认）， false：败方
     */
    @ComponentAttrField(labelText = "是否读取胜方", remark = "榜单类型为pk时,true:胜方（默认）， false：败方")
    private boolean winner = true;

    /**
     * 结算排名,在荣耀堂展示的名次起始值，最终展示的名次是 [rankIndex, rankIndex+count]
     */
    @ComponentAttrField(labelText = "结算排名", remark = "结算排名,在荣耀堂展示的名次起始值，最终展示的名次是 [rankIndex, rankIndex+count]")
    private int rankIndex = 1;

    private String rankGroupCode;

    private String groupCode;

    @ComponentAttrField(labelText = "贡献榜榜单id")
    private long contributeRankId = 0;
    @ComponentAttrField(labelText = "贡献榜阶段id")
    private long contributePhaseId = 0;

    @ComponentAttrField(labelText = "贡献榜角色类型", remark = "100用户,200主播，400公会,401厅,700家族")
    private Integer roleType;

    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(long phaseId) {
        this.phaseId = phaseId;
    }

    public int getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(int startIndex) {
        this.startIndex = startIndex;
    }

    public long getCount() {
        return count;
    }

    public void setCount(long count) {
        this.count = count;
    }

    public String getRankType() {
        return rankType;
    }

    public void setRankType(String rankType) {
        this.rankType = rankType;
    }

    public int getRankIndex() {
        return rankIndex;
    }

    public void setRankIndex(int rankIndex) {
        this.rankIndex = rankIndex;
    }

    public boolean isWinner() {
        return winner;
    }

    public void setWinner(boolean winner) {
        this.winner = winner;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getRankGroupCode() {
        return rankGroupCode;
    }

    public void setRankGroupCode(String rankGroupCode) {
        this.rankGroupCode = rankGroupCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public long getContributeRankId() {
        return contributeRankId;
    }

    public void setContributeRankId(long contributeRankId) {
        this.contributeRankId = contributeRankId;
    }

    public long getContributePhaseId() {
        return contributePhaseId;
    }

    public void setContributePhaseId(long contributePhaseId) {
        this.contributePhaseId = contributePhaseId;
    }

    public Integer getRoleType() {
        return roleType;
    }

    public void setRoleType(Integer roleType) {
        this.roleType = roleType;
    }
}
