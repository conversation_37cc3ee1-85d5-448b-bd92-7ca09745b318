package com.yy.gameecology.hdzj.element.event;

import lombok.Data;

/**
 * 年兽成功打败事件
 *
 * <AUTHOR>
 * @since 2022/12/29 10:34
 **/
@Data
public class NianShouSuccessFightEvent {
    private long actId;
    private String nsInfoId;
    private long time;
    private String seq;
    private long anchorUid;
    private long lastUserId;
    private long lackManUid;
    private int count;

    public NianShouSuccessFightEvent() {
    }

    public NianShouSuccessFightEvent(long actId, String nsInfoId, String seq, long anchorUid, long lastUserId, long lackManUid) {
        this.actId = actId;
        this.nsInfoId = nsInfoId;
        this.seq = "success:" + seq;
        this.anchorUid = anchorUid;
        this.lastUserId = lastUserId;
        this.lackManUid = lackManUid;
        this.time = System.currentTimeMillis();
        this.count = 1;
    }
}
