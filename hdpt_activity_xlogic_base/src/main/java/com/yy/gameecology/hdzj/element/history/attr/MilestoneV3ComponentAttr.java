package com.yy.gameecology.hdzj.element.history.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.thrift.hdztranking.BusiId;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021.06.21 11:18
 */
public class MilestoneV3ComponentAttr extends ComponentAttr {

    /**
     * 团数值日榜
     **/
    @ComponentAttrField(labelText = "团数值日榜Id")
    private long rankId;
    @ComponentAttrField(labelText = "团数值日榜阶段id")
    private long phaseId;

    /**
     * 贡献榜的主体
     */
    @ComponentAttrField(labelText = "贡献榜的主体")
    private long subjectActor;
    /**
     * 分值对应的阶段
     */
    private Map<Long, Long> score2Level;

    /**
     * 等级对应的奖励和贡献榜榜单配置
     */
    @ComponentAttrField(labelText = "等级对应的奖励", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "等级"),
            @SubField(fieldName = Constant.VALUE, type = LevelConfigV3.class, labelText = "奖励配置")
    })
    private Map<Long, LevelConfigV3> levelConfigMap;

    /**
     * 用于上报贡献榜分值
     */
    @ComponentAttrField(labelText = "贡献榜分值上报item")
    private String giftId = "PW_LCB";

    /**
     * 神豪的贡献榜
     */
    @ComponentAttrField(labelText = "神豪的贡献榜Id")
    private long playerRankId;

    /**
     * 主播的贡献榜
     */
    @ComponentAttrField(labelText = "主播的贡献榜Id")
    private long anchorRankId;

    /**
     * 公会团贡献榜
     */
    @ComponentAttrField(labelText = "公会团贡献榜Id")
    private long tuanContributionRankId = 0L;

    /**
     * 业务类型 默认陪玩
     */
    @ComponentAttrField(labelText = "业务类型", remark = "默认陪玩", dropDownSourceBeanClass = BizSource.class)
    private long busiId = BusiId.PEI_WAN.getValue();
    /**
     * 发奖重试次数
     */
    @ComponentAttrField(labelText = "发奖重试次数")
    private int retry = 2;

    public long getTuanContributionRankId() {
        return tuanContributionRankId;
    }

    public void setTuanContributionRankId(long tuanContributionRankId) {
        this.tuanContributionRankId = tuanContributionRankId;
    }

    public long getSubjectActor() {
        return subjectActor;
    }

    public void setSubjectActor(long subjectActor) {
        this.subjectActor = subjectActor;
    }

    public long getBusiId() {
        return busiId;
    }

    public void setBusiId(long busiId) {
        this.busiId = busiId;
    }

    public int getRetry() {
        return retry;
    }

    public void setRetry(int retry) {
        this.retry = retry;
    }

    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(long phaseId) {
        this.phaseId = phaseId;
    }

    public Map<Long, Long> getScore2Level() {
        return score2Level;
    }

    public void setScore2Level(Map<Long, Long> score2Level) {
        this.score2Level = score2Level;
    }

    public Map<Long, LevelConfigV3> getLevelConfigMap() {
        return levelConfigMap;
    }

    public void setLevelConfigMap(Map<Long, LevelConfigV3> levelConfigMap) {
        this.levelConfigMap = levelConfigMap;
    }

    public String getGiftId() {
        return giftId;
    }

    public void setGiftId(String giftId) {
        this.giftId = giftId;
    }

    public long getPlayerRankId() {
        return playerRankId;
    }

    public void setPlayerRankId(long playerRankId) {
        this.playerRankId = playerRankId;
    }

    public long getAnchorRankId() {
        return anchorRankId;
    }

    public void setAnchorRankId(long anchorRankId) {
        this.anchorRankId = anchorRankId;
    }

    /**
     * 角色 {player:123,anchor:21345}
     */
    private Map<String, Long> actors;

    public Map<String, Long> getActors() {
        return actors;
    }

    public void setActors(Map<String, Long> actors) {
        this.actors = actors;
    }

    public static class LevelConfigV3 {

        /**
         * 用户贡献榜ID 记录top5,必须使用指定榜单上报数据
         */
        @ComponentAttrField(labelText = "用户贡献榜ID", remark = "记录top5,必须使用指定榜单上报数据")
        private long playerRecordRankId;

        /**
         * 主播贡献榜ID 记录top5,必须使用指定榜单上报数据
         */
        @ComponentAttrField(labelText = "主播贡献榜ID", remark = "记录top5,必须使用指定榜单上报数据")
        private long anchorRecordRankId;

        /**
         * 记录top n, 默认五个,必须大于等于发奖个数
         */
        @ComponentAttrField(labelText = "记录topN", remark = "默认五个,必须大于等于发奖个数")
        private long recordTopN = 5;

        /**
         * 奖励的taskId
         */
        @ComponentAttrField(labelText = "奖池id")
        private long awardTaskId;

        /**
         * 奖励个数 默认1个
         */
        @ComponentAttrField(labelText = "奖励个数", remark = "默认1个")
        private int awardCount = 1;

        /**
         * 给用户top n 发奖
         */
        @ComponentAttrField(labelText = "topN用户发奖")
        private int playerTopN;
        /**
         * 给主播top n 发奖
         */
        @ComponentAttrField(labelText = "topN主播发奖")
        private int anchorTopN;

        /**
         * 贡献神豪奖励
         */
        @ComponentAttrField(labelText = "贡献神豪奖励奖包id")
        private long playerAwardPackageId;

        /**
         * 贡献陪陪奖励
         */
        @ComponentAttrField(labelText = "贡献陪陪奖励奖包id")
        private long anchorAwardPackageId;

        /**
         * 里程碑等级
         */
        @ComponentAttrField(labelText = "里程碑等级")
        private int level;

        /**
         * 是否全业务广播
         */
        @ComponentAttrField(labelText = "是否全业务广播")
        private boolean isBroadcast = false;

        public int getAwardCount() {
            return awardCount;
        }

        public void setAwardCount(int awardCount) {
            this.awardCount = awardCount;
        }

        public long getRecordTopN() {
            return recordTopN;
        }

        public void setRecordTopN(long recordTopN) {
            this.recordTopN = recordTopN;
        }

        public long getAwardTaskId() {
            return awardTaskId;
        }

        public void setAwardTaskId(long awardTaskId) {
            this.awardTaskId = awardTaskId;
        }

        public long getPlayerTopN() {
            return playerTopN;
        }

        public void setPlayerTopN(int playerTopN) {
            this.playerTopN = playerTopN;
        }

        public long getAnchorTopN() {
            return anchorTopN;
        }

        public void setAnchorTopN(int anchorTopN) {
            this.anchorTopN = anchorTopN;
        }

        public long getPlayerAwardPackageId() {
            return playerAwardPackageId;
        }

        public void setPlayerAwardPackageId(long playerAwardPackageId) {
            this.playerAwardPackageId = playerAwardPackageId;
        }

        public long getAnchorAwardPackageId() {
            return anchorAwardPackageId;
        }

        public void setAnchorAwardPackageId(long anchorAwardPackageId) {
            this.anchorAwardPackageId = anchorAwardPackageId;
        }

        public int getLevel() {
            return level;
        }

        public void setLevel(int level) {
            this.level = level;
        }

        public boolean isBroadcast() {
            return isBroadcast;
        }

        public void setBroadcast(boolean broadcast) {
            isBroadcast = broadcast;
        }

        public long getPlayerRecordRankId() {
            return playerRecordRankId;
        }

        public void setPlayerRecordRankId(long playerRecordRankId) {
            this.playerRecordRankId = playerRecordRankId;
        }

        public long getAnchorRecordRankId() {
            return anchorRecordRankId;
        }

        public void setAnchorRecordRankId(long anchorRecordRankId) {
            this.anchorRecordRankId = anchorRecordRankId;
        }
    }
}
