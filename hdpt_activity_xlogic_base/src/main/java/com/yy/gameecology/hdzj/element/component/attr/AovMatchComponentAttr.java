package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AovMatchComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "结算保留时间", remark = "轮次结束前X分钟需要完成轮次结算，这是保留的时间")
    protected int settleReserveMin;
}
