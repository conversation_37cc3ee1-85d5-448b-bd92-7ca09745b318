package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.annotation.SkipCheck;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022.03.11 14:13
 */
@Data
@SkipCheck
public class KingLiftComponentAttr extends ComponentAttr {

    /**
     * 任务对应奖励, {"任务名":{"taskId":taskId,"packageId":packageId, "count":123},
     * "任务名2":{"taskId":taskId,"packageId":packageId, "count":123}}
     * 必须与任务对应ID
     */
    private Map<String,Map<String,Long>> task2Award;

    private int retry = 3;

    private String appKey;

    //aes加密秘钥
    private String aesKey;

    //aes加密向量
    private String iv;

    private String appId;
}
