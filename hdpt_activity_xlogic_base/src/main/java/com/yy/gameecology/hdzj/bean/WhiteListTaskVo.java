package com.yy.gameecology.hdzj.bean;

import lombok.Data;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2022-07-15 22:23
 **/
@Data
public class WhiteListTaskVo {
    /**
     * 短位ID
     */
    private long asId;

    /**
     * 昵称
     */
    private String nick;

    /**
     * 头像
     */
    private String headerUrl;

    /**
     * 任务ID
     */
    private long taskId;

    /**
     * 当前分数
     */
    private long curScore;

    /**
     * 总分数
     */
    private long taskScore;

    /**
     * 剩余奖励名额
     */
    private long leftAward;

    /**
     * 奖励限额
     */
    private long awardLimit;

    /**
     * 是否实际完成任务（名额已占满，即使分数达到了，仍然是未完成，isRealComplete==false）
     */
    private boolean isRealComplete;


}
