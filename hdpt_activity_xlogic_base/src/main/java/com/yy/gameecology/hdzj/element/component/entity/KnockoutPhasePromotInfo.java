package com.yy.gameecology.hdzj.element.component.entity;

import com.yy.gameecology.common.db.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * 淘汰赛阶段晋级信息表
 * 用于存储阶段晋级状态和晋级主播信息，替代Redis的RANK_PHASE_PROMOT_KEY和CURRENT_PROMOT_ANCHOR
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@TableColumn(underline = true)
public class KnockoutPhasePromotInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5159_phase_promot_";

    public static String getTableName(long actId) {
        return TABLE_NAME + actId;
    }

    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<KnockoutPhasePromotInfo> ROW_MAPPER = (rs, rowNum) -> {
        KnockoutPhasePromotInfo result = new KnockoutPhasePromotInfo();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getLong("cmpt_use_inx"));
        result.setPhaseId(rs.getLong("phase_id"));
        result.setHdzkRankId(rs.getLong("hdzk_rank_id"));
        result.setPromotStatus(rs.getInt("promot_status"));
        result.setPromotAnchors(rs.getString("promot_anchors"));
        result.setRankCount(rs.getInt("rank_count"));
        result.setRequiredRankCount(rs.getInt("required_rank_count"));
        result.setVersion(rs.getLong("version"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        result.setUpdateTime(rs.getTimestamp("update_time"));
        return result;
    };

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 组件使用索引
     */
    private Long cmptUseInx;

    /**
     * 阶段ID
     */
    private Long phaseId;

    /**
     * 中控榜单ID
     */
    private Long hdzkRankId;

    /**
     * 晋级状态：0-未开始，1-已完成
     */
    private Integer promotStatus;

    /**
     * 晋级主播列表，逗号分隔
     */
    private String promotAnchors;

    /**
     * 当前已结算的榜单数量
     */
    private Integer rankCount;

    /**
     * 需要的榜单数量
     */
    private Integer requiredRankCount;

    /**
     * 版本号，用于乐观锁
     */
    private Long version;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    // 状态常量
    public static final int PROMOT_STATUS_NOT_STARTED = 0;
    public static final int PROMOT_STATUS_COMPLETED = 1;
}
