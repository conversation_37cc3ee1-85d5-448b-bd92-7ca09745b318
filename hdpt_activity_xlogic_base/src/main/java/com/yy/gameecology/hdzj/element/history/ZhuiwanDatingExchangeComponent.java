package com.yy.gameecology.hdzj.element.history;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.peiwan.PwResponse;
import com.yy.gameecology.activity.bean.peiwan.QueryBalanceResp;
import com.yy.gameecology.activity.client.http.PeiwanServiceHttpClient;
import com.yy.gameecology.activity.exception.BadRequestException;
import com.yy.gameecology.activity.exception.ParameterException;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.MyListUtils;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.ZhuiwanDatingExchangeComponentAttr;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 陪玩次接口已经下线
 * com.yy.gameecology.activity.client.http.PeiwanServiceHttpClient#exchangeCoupon
 *
 * @Author: CXZ
 * @Desciption: 追玩兑换专区组件
 * @Date: 2021/9/1 16:39
 * @Modified:
 */
@UseRedisStore
@Component
public class ZhuiwanDatingExchangeComponent extends BaseActComponent<ZhuiwanDatingExchangeComponentAttr> {

    @Autowired
    private PeiwanServiceHttpClient peiwanServiceHttpClient;
    /**
     * 兑换额度 hash key ，“0”是总额度
     */
    public static final String USER_QUOTA_KEY = "user_quota";


    /**
     * 兑换记录
     */
    public static final String USER_RECORD_KEY = "user_record:%s";

    /**
     * 兑换的用户限制key
     */
    private static final String EXCHANGE = "exchange:%s";


    /**
     * 兑换额度限制 hash key
     */
    public static final String QUOTA_LIMIT_KEY = "quota_limit";

    private int[] PW_EXCHANGE_COUPON_RESULT_CODE = {0, -1, -2};


    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private Locker locker;

    private Random random = new Random();


    private static ZhuiwanDatingExchangeComponentAttr.Addition NO_ADDITION_GIFT = new ZhuiwanDatingExchangeComponentAttr.Addition("", "", 0L);


    @Override
    public Long getComponentId() {
        return ComponentId.ZHUIWAN_DATING_EXCHANGE;
    }

    /**
     * 响应榜单变化事件，增加用户兑换额度
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void addUserQuota(RankingScoreChanged event, ZhuiwanDatingExchangeComponentAttr attr) {
        if (attr.getActId() != event.getActId() || attr.getRankId() != event.getRankId() || attr.getPhaseId() != event.getPhaseId()) {
            return;
        }
        long itemScore = event.getItemScore();
        long nowScore = event.getPhaseScore();
        long lastScore = nowScore - itemScore;

        long quota = Math.floorDiv(nowScore, 1000) - Math.floorDiv(lastScore, 1000);

        long uid = Convert.toLong(event.getMember());
        if (uid <= 0) {
            log.error("addUserQuota ignore uid zore@event:{}", JSON.toJSONString(event));
            return;
        }
        if (quota <= 0) {
            log.info("addUserQuota ignore quota zore@actId:{} rankId:{} uid:{} phaseId:{} lastScore:{} nowScore:{} ",
                    attr.getActId(), attr.getRankId(), attr.getPhaseId(), uid, lastScore, nowScore);
            return;
        }

        long time = commonService.getNow(attr.getActId()).getTime();
        boolean isDayLimit = isDayLimit(attr, time);
        String quotaLimitKey = getQuotaLimitKey(isDayLimit, time);
        long limitQuota = getLimitQuota(isDayLimit, quotaLimitKey, attr);

        String userQuotaKey = makeKey(attr, USER_QUOTA_KEY);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        List<Long> result;
        //List<Long> result = actRedisDao.hIncrWithLimit(groupCode, userQuotaKey, quotaLimitKey, quota, limitQuota, true);

        if (true) {
            throw new RuntimeException("hash_incr_with_limit.lua,lua forbid");
        }
        long addQuota = 0L;
        long code = result.get(0);
        final int code2 = 2;
        if (1 == code) {
            addQuota = quota;
            actRedisDao.hIncrByKey(groupCode, userQuotaKey, String.valueOf(uid), quota);
        } else if (code2 == code) {
            //部分满足
            addQuota = result.get(1);
            actRedisDao.hIncrByKey(groupCode, userQuotaKey, String.valueOf(uid), addQuota);
        }


        bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.ZHUI_WAN, time, String.valueOf(uid), RoleType.USER, addQuota, 17, quota + "", (int) itemScore, nowScore);

        log.info("addUserQuota done@actId:{} rankId:{} phaseId:{} uid:{} lastScore:{} nowScore:{} itemScore:{} quota:{} quotaLimitKey:{} limitQuota:{} result:{}",
                attr.getActId(), attr.getRankId(), attr.getPhaseId(), uid, lastScore, nowScore, itemScore, quota, quotaLimitKey, limitQuota, JSON.toJSONString(result));

    }

    /**
     * 查询剩余总兑换额度
     *
     * @param actId
     * @param cmptUseInx
     * @return
     */
    @Cached(timeToLiveMillis = 3 * 1000)
    public Response<String> queryQuotaBalance(long actId, long cmptUseInx) {
        ZhuiwanDatingExchangeComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + cmptUseInx);

        Date now = commonService.getNow(attr.getActId());
        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityCacheNoTime(actId);

        String tip = "兑换额度发放完毕";

        if (activityInfoVo.getEndTime() > now.getTime()) {
            boolean isDayLimit = isDayLimit(attr, now.getTime());
            String quotaLimitKey = getQuotaLimitKey(isDayLimit, now.getTime());
            long limitQuota = getLimitQuota(isDayLimit, quotaLimitKey, attr);

            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            long useTotalQuota = Convert.toLong(actRedisDao.hget(groupCode, makeKey(attr, USER_QUOTA_KEY), quotaLimitKey));

            long quotaBalance = Math.max(limitQuota - useTotalQuota, 0L);
            double occupancyRate = quotaBalance * 100f / limitQuota;

            tip = occupancyRate <= 10 ? "剩余" + quotaBalance + "元可兑换额度获得" : "当前可兑换额度充足";
            if (occupancyRate <= 0 && isDayLimit) {
                tip = "今日兑换额度已发放完毕";
            }
        }

        return Response.success(tip);
    }

    /**
     * 是否日是日限
     *
     * @param attr
     * @param now
     * @return
     */
    private boolean isDayLimit(ZhuiwanDatingExchangeComponentAttr attr, long now) {
        Date dayLimitStartDate = attr.getDayLimitStartDate();
        return dayLimitStartDate != null && dayLimitStartDate.getTime() <= now;
    }

    /**
     * 获取限制key
     *
     * @param isDayLimit
     * @param dateTime
     * @return
     */
    private String getQuotaLimitKey(boolean isDayLimit, long dateTime) {
        return !isDayLimit ? "0" : getDayLimitKey(DateUtil.getPattenStrFromTime(dateTime, DateUtil.PATTERN_TYPE2));
    }

    private String getDayLimitKey(String day) {
        return "d-" + day;
    }

    /**
     * 限制额度
     *
     * @param isDayLimit
     * @param attr
     * @return
     */
    private long getLimitQuota(boolean isDayLimit, String timeKey, ZhuiwanDatingExchangeComponentAttr attr) {
        long totalQuota = attr.getTotalQuota();
        if (isDayLimit) {
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            String quotaString = actRedisDao.hget(groupCode, makeKey(attr, QUOTA_LIMIT_KEY), timeKey);
            totalQuota = Convert.toLong(quotaString, attr.getDayTotalQuota());
        }
        return totalQuota;
    }

    /**
     * 设置每日限额
     *
     * @param actId
     * @param cmptUseInx
     * @param limitCount
     * @param opUid
     * @return
     */
    public Response<List<JSONObject>> setDailyLimit(long actId, long cmptUseInx, long limitCount, long opUid) {
        ZhuiwanDatingExchangeComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + cmptUseInx);
        checkOpUid(opUid, attr);

        if (limitCount <= 0) {
            return Response.fail(500, "限制额度不能小于0");
        }

        Date now = commonService.getNow(actId);

        Date nextDate = DateUtil.add(now, 1);
        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityCacheNoTime(actId);

        if (activityInfoVo.getEndTime() < nextDate.getTime()) {
            return Response.fail(500, "不能设置活动结束后的时间");
        }


        List<String> dateStrs = DateUtil.getDateStrList(nextDate, new Date(activityInfoVo.getEndTime()), DateUtil.PATTERN_TYPE2);
        List<String> keys = dateStrs.stream().map(this::getDayLimitKey).collect(Collectors.toList());
        Map<String, String> map = keys.stream().collect(Collectors.toMap(Function.identity(), item -> String.valueOf(limitCount)));

        String groupCode = redisConfigManager.getGroupCode(attr.getActId());

        String quotaLimtKey = makeKey(attr, QUOTA_LIMIT_KEY);
        actRedisDao.getRedisTemplate(groupCode).opsForHash().putAll(quotaLimtKey, map);
        List<Object> limits = actRedisDao.hmGet(groupCode, quotaLimtKey, MyListUtils.toObjectList(keys));
        log.info("setDailyLimit info@actId:{} cmptUseInx:{} starDay:{} limitCount:{} opUid:{}"
                , actId, cmptUseInx, dateStrs.get(0), limitCount, opUid);

        String msgFormat = "兑换组件消息提醒\n" +
                "uid:%s 正在修改兑换专区的日兑换额度\n" +
                "日期:%s 日限制额度:%s";
        String msg = String.format(msgFormat, opUid, dateStrs.get(0), limitCount);
        //baiduInfoFlowRobotService.asyncSendNotifyByActAttrKey(actId, "zw_exchange_notice", msg, Lists.newArrayList());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());

        List<JSONObject> result = Lists.newArrayList();
        for (int i = 0; i < dateStrs.size(); i++) {
            JSONObject object = new JSONObject(true);
            object.put("day", dateStrs.get(i));
            object.put("limit", Convert.toLong(limits.get(i)));
            result.add(object);
        }

        return Response.success(result);
    }

    /**
     * 获取当前的限额和消耗情况
     *
     * @param actId
     * @param cmptUseInx
     * @param opUid
     * @return
     */
    public Response<JSONObject> getDailyQuotaLimit(long actId, long cmptUseInx, long opUid) {
        ZhuiwanDatingExchangeComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + cmptUseInx);
        checkOpUid(opUid, attr);

        if (!actInfoService.inActTime(actId)) {
            return Response.fail(500, "不在活动期间");
        }
        Date now = commonService.getNow(actId);
        String nowString = DateUtil.format(now, DateUtil.PATTERN_TYPE2);

        List<String> keys = Lists.newArrayList(nowString, DateUtil.format(DateUtil.add(now, 1), DateUtil.PATTERN_TYPE2));
        keys = keys.stream().map(this::getDayLimitKey).collect(Collectors.toList());

        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String quotaLimitKey = makeKey(attr, QUOTA_LIMIT_KEY);

        List<Object> limits = actRedisDao.hmGet(groupCode, quotaLimitKey, MyListUtils.toObjectList(keys));
        long useTotalQuota = Convert.toLong(actRedisDao.hget(groupCode, makeKey(attr, USER_QUOTA_KEY), getDayLimitKey(nowString)));
        JSONObject result = new JSONObject(true);

        long nowDayLimitQuota = Convert.toLong(limits.get(0));
        long nextDayLimitQuota = Convert.toLong(limits.get(1));
        result.put("当前时间", nowString);
        result.put("是否启用日限制", isDayLimit(attr, now.getTime()));
        result.put("当天限制额度", nowDayLimitQuota);
        result.put("当天消耗额度", useTotalQuota);
        result.put("当天剩余额度", nowDayLimitQuota - useTotalQuota);
        result.put("明天限制额度", nextDayLimitQuota);

        return Response.success(result);
    }

    private void checkOpUid(long opUid, ZhuiwanDatingExchangeComponentAttr attr) {
        if (!ArrayUtils.contains(attr.getOpAdminUids().split(StringUtil.COMMA), String.valueOf(opUid))) {
            throw new BadRequestException("权限不足");
        }
    }

    /**
     * 兑换
     *
     * @param actId
     * @param cmptUseInx
     * @param uid
     * @param count
     * @param currencyType
     * @param authCode
     * @return
     */
    public Response exchange(long actId, long cmptUseInx, long uid, int count, int currencyType, String authCode) {

        ZhuiwanDatingExchangeComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + cmptUseInx);
        //货币类型错误
        final boolean matchCurrencyType = currencyType == 1 || currencyType == 2;
        if (!matchCurrencyType) {
            throw new ParameterException("currencyType error");
        }
        if (count <= 0) {
            throw new ParameterException("count error");
        }

        String seq = UUID.randomUUID().toString();

        log.info("exchange start@actId:{} cmptUseInx:{} seq:{} uid:{} count:{} currencyType:{} authCode:{}"
                , actId, cmptUseInx, seq, uid, count, currencyType, authCode);
        //验证码
        final int currencyType2 = 2;
        if (currencyType == currencyType2 && StringUtils.isBlank(authCode)) {
            return exchangeFail(102, "请输入验证码", attr);
        }

        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityCacheNoTime(actId);
        if (activityInfoVo == null) {
            return exchangeFail(4, "活动未开始，敬请期待。", attr);
        }

        Date now = commonService.getNow(actId);

        long starTime = Optional.ofNullable(attr.getStartDate()).map(Date::getTime).orElse(activityInfoVo.getBeginTime());
        long endTime = Optional.ofNullable(attr.getEndDate()).map(Date::getTime).orElse(activityInfoVo.getEndTime());

        if (starTime > now.getTime()) {
            return exchangeFail(4, "活动未开始，敬请期待。", attr);
        }
        if (endTime < now.getTime()) {
            return exchangeFail(5, "活动已结束。", attr);
        }

        //抽奖
        ZhuiwanDatingExchangeComponentAttr.Addition additionGift = NO_ADDITION_GIFT;
        final int ten = 10;
        if (attr.getAdditionGifts().size() > 0 && count >= ten) {
            int r = random.nextInt(attr.getAdditionGifts().size());
            additionGift = attr.getAdditionGifts().get(r);
        }

        long price = attr.getPrice();
        long amount = count * price;

        String groupCode = redisConfigManager.getGroupCode(actId);

        String lockName = makeKey(attr, String.format(EXCHANGE, uid));
        int second = 60;
        Secret lock = null;
        try {
            lock = locker.lock(lockName, second);
            if (lock != null) {

                String quotaString = actRedisDao.hget(groupCode, makeKey(attr, USER_QUOTA_KEY), String.valueOf(uid));
                if (Convert.toLong(quotaString) < amount) {
                    return exchangeFail(103, "兑换额度不足。", attr);
                }

                PwResponse pwResponse = peiwanServiceHttpClient.exchangeCoupon(uid, currencyType, amount * 1000L, authCode, seq);

                if (pwResponse == null || !ArrayUtils.contains(PW_EXCHANGE_COUPON_RESULT_CODE, pwResponse.getResult())) {
                    return exchangeFail(104, Code.E_SYS_BUSY.getReason(), attr);
                }

                if (pwResponse.getResult() != 0) {
                    int pwCode = pwResponse.getResult();
                    //-1: 验证码有误 -2: 余额不足
                    int code = pwCode == -1 ? 105 : 106;
                    return exchangeFail(code, pwResponse.getMsg(), attr);
                }

                //Long code = actRedisDao.hIncrWithLimit(groupCode, makeKey(attr, USER_QUOTA_KEY), String.valueOf(uid), -amount, 0, true).get(0);
                Long code;
                if (true) {
                    throw new RuntimeException("hash_incr_with_limit.lua,lua forbid");
                }
                if (code != 1) {
                    log.error("exchange user quota error@actId:{} cmptUseInx:{} seq:{} uid:{} count:{} currencyType:{} amount:{}"
                            , actId, cmptUseInx, seq, uid, count, currencyType, amount);
                }

                Map<Long, Integer> packageIds = Maps.newHashMap(Collections.singletonMap(attr.getPackageId(), count));
                if (additionGift.getPackageId() > 0) {
                    packageIds.put(additionGift.getPackageId(), 1);
                }

                long taskId = attr.getTaskId();
                Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap(Collections.singletonMap(taskId, packageIds));
                hdztAwardServiceClient.doBatchWelfare(DateUtil.getNowYyyyMMddHHmmss(), BusiId.PEI_WAN.getValue(), uid, taskId, taskPackageIds, seq, 3, Maps.newHashMap());

                log.info("exchange success done@actId:{} cmptUseInx:{} seq:{} uid:{} count:{} currencyType:{} amount:{} addGift:{}"
                        , actId, cmptUseInx, seq, uid, count, currencyType, amount, additionGift.getName());

                Record record = new Record(count, price, currencyType, additionGift.getName(), seq, now.getTime());
                addUserRecord(uid, record, attr);


                JSONObject commodityBase = new JSONObject();
                commodityBase.put("count", record.getCount());
                commodityBase.put("price", record.getPrice());
                commodityBase.put("icon", attr.getIcon());
                commodityBase.put("additionGiftName", record.getAddName());
                commodityBase.put("additionGiftIcon", additionGift.getIcon());

                return Response.success(commodityBase);
            }
        } catch (Exception e) {
            log.error("exchange error {}", e.getMessage(), e);
        } finally {
            if (lock != null) {
                locker.unlock(lockName, lock);
            }
        }
        return Response.fail(2, "当前兑换人数较多，请稍后再试");
    }

    /**
     * 兑换错误返回
     *
     * @param code
     * @param message
     * @param attr
     * @param <T>
     * @return
     */
    private <T> Response<T> exchangeFail(int code, String message, ZhuiwanDatingExchangeComponentAttr attr) {
        log.warn("exchangeFail@actId:{} cmptUseInx:{}code:{} message:{}", attr.getActId(), attr.getCmptUseInx(), code, message);
        return Response.fail(code, Convert.toString(attr.getTipMsgMap().get("exchange-" + code), message));
    }

    private <T> Response<T> queryFail(int code, String message, ZhuiwanDatingExchangeComponentAttr attr) {
        return Response.fail(code, Convert.toString(attr.getTipMsgMap().get("query-" + code), message));
    }


    /**
     * 增加用户钱币
     *
     * @param actId
     * @param cmptUseInx
     * @param uid
     * @param count
     */
    public void addUserQuoteByTest(long actId, long cmptUseInx, long uid, int count) {

        ZhuiwanDatingExchangeComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "not find attr by cmptUseInx = " + cmptUseInx);
        Assert.isTrue(uid > 0, "uid error,uid=" + uid);
        Assert.isTrue(count > 0, "count error,count=" + count);

        String groupCode = redisConfigManager.getGroupCode(actId);
        actRedisDao.hIncrByKey(groupCode, makeKey(attr, USER_QUOTA_KEY), String.valueOf(uid), count);
    }


    /**
     * 增加兑换记录
     *
     * @param uid
     * @param record
     * @param attr
     */
    private void addUserRecord(long uid, Record record, ZhuiwanDatingExchangeComponentAttr attr) {
        String key = makeKey(attr, String.format(USER_RECORD_KEY, uid));
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        actRedisDao.getRedisTemplate(groupCode).opsForList().leftPush(key, JSON.toJSONString(record));
    }


    /**
     * 查询很购买记录
     *
     * @param actId
     * @param cmptUseInx
     * @param uid
     * @param count
     * @return
     */
    public List<JSONObject> queryUserRecord(long actId, long cmptUseInx, long uid, Integer count) {
        ZhuiwanDatingExchangeComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + cmptUseInx);

        count = Convert.toInt(count, Integer.MAX_VALUE);
        if (count <= 0) {
            return Lists.newArrayList();
        }
        String key = makeKey(attr, String.format(USER_RECORD_KEY, uid));
        String groupCode = redisConfigManager.getGroupCode(actId);
        List<String> recordStringList = actRedisDao.getRedisTemplate(groupCode).opsForList().range(key, 0, count - 1);
        if (recordStringList.isEmpty()) {
            return Lists.newArrayList();
        }
        String json = "[" + StringUtils.join(recordStringList, ",") + "]";
        List<Record> records = JSONObject.parseArray(json, Record.class);

        return records.stream().map(record -> {
            JSONObject item = new JSONObject();
            item.put("count", record.getCount());
            item.put("price", record.getPrice());
            item.put("currencyType", record.getCurrencyType());
            item.put("additionGiftName", record.getAddName());
            item.put("time", record.getcTime());
            return item;
        }).collect(Collectors.toList());
    }


    /**
     * 查询用户的商城
     *
     * @param actId
     * @param cmptUseInx
     * @param uid
     * @return
     */
    public Response queryUserInfo(long actId, long cmptUseInx, long uid) {

        ZhuiwanDatingExchangeComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + cmptUseInx);

        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityCacheNoTime(actId);
        if (activityInfoVo == null) {
            log.error("queryUserInfo error@not find activityInfoVo,actId:{}", actId);
            return queryFail(4, "活动未开始，敬请期待。", attr);
        }

        Date now = commonService.getNow(actId);
        Date starShowDate = attr.getStartShowDate();
        if (starShowDate != null) {
            if (starShowDate.after(now)) {
                return queryFail(2, "活动未开始，敬请期待。", attr);
            }
        }

        long starTime = Optional.ofNullable(attr.getStartDate()).map(Date::getTime).orElse(activityInfoVo.getBeginTime());
        long endTime = Optional.ofNullable(attr.getEndDate()).map(Date::getTime).orElse(activityInfoVo.getEndTime());
        if (starTime > now.getTime()) {
            return queryFail(4, "活动未开始，敬请期待。", attr);
        }
        if (endTime < now.getTime()) {
            return queryFail(5, "活动已结束。", attr);
        }


        JSONObject data = new JSONObject();

        UserBaseInfo userInfo = commonService.getUserInfo(uid, false);
        data.put("logo", userInfo.getLogo());
        data.put("nick", userInfo.getNick());

        String groupCode = redisConfigManager.getGroupCode(actId);

        String quotaString = actRedisDao.hget(groupCode, makeKey(attr, USER_QUOTA_KEY), String.valueOf(uid));
        long quota = Convert.toLong(quotaString);

        data.put("quota", quota);
        long price = attr.getPrice();


        //查询余额
        QueryBalanceResp queryBalanceResp = peiwanServiceHttpClient.queryBalance(uid);
        if (queryBalanceResp == null) {
            return new Response(Code.E_SYS_BUSY);
        }

        long quotaExchangeCount = Math.floorDiv(quota, price);


        JSONObject jinbeiCurrency = new JSONObject();
        long jinbei = queryBalanceResp.getJinbei() / 1000;
        jinbeiCurrency.put("type", 1);
        jinbeiCurrency.put("balance", jinbei);
        jinbeiCurrency.put("maxExchangeCount", Math.min(Math.floorDiv(jinbei, price), quotaExchangeCount));

        JSONObject lanbeiCurrency = new JSONObject();
        long lanbei = queryBalanceResp.getLanbei() / 1000;
        lanbeiCurrency.put("type", 1);
        lanbeiCurrency.put("balance", lanbei);
        lanbeiCurrency.put("maxExchangeCount", Math.min(Math.floorDiv(lanbei, price), quotaExchangeCount));

        List<JSONObject> currency = Lists.newArrayList(jinbeiCurrency, lanbeiCurrency);

        data.put("currency", currency);

        return Response.success(data);
    }


    public static class Record {


        /**
         * 优惠券数量
         */
        private int count;

        /**
         * 优惠券价格
         */
        private long price;
        /**
         * 货币类型，1=金贝，2=蓝贝
         */
        private int currencyType;

        /**
         * 附加的奖品名称
         */
        private String addName;
        /**
         * seq
         */
        private String seq;
        /**
         * 记录产生时间
         */
        private long cTime;

        public Record(int count, long price, int currencyType, String addName, String seq, long cTime) {
            this.count = count;
            this.price = price;
            this.currencyType = currencyType;
            this.addName = addName;
            this.seq = seq;
            this.cTime = cTime;
        }

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public long getPrice() {
            return price;
        }

        public void setPrice(long price) {
            this.price = price;
        }

        public int getCurrencyType() {
            return currencyType;
        }

        public void setCurrencyType(int currencyType) {
            this.currencyType = currencyType;
        }

        public String getAddName() {
            return addName;
        }

        public void setAddName(String addName) {
            this.addName = addName;
        }

        public String getSeq() {
            return seq;
        }

        public void setSeq(String seq) {
            this.seq = seq;
        }

        public long getcTime() {
            return cTime;
        }

        public void setcTime(long cTime) {
            this.cTime = cTime;
        }
    }
}
