package com.yy.gameecology.hdzj.bean;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yy.gameecology.activity.bean.GiftSourceChannel;
import com.yy.thrift.broadcast.Template;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2021/6/30
 */
@Getter
@Builder
@ToString
public class SendGiftBO {

  /**
   * fix Jackson builder Deserialization bug https://github.com/FasterXML/jackson-databind/issues/921
   */
  @JsonCreator
  public static SendGiftBO create(
      @JsonProperty("template") String template,
      @JsonProperty("seq") String seq,
      @JsonProperty("sendUid") Long sendUid,
      @JsonProperty("recvUid") Long recvUid,
      @JsonProperty("giftId") String giftId,
      @JsonProperty("giftNum") Long giftNum,
      @JsonProperty("sid") Long sid,
      @JsonProperty("ssid") Long ssid,
      @JsonProperty("eventTime") Date eventTime,
      @JsonProperty("sourceChannel") String sourceChannel,
      @JsonProperty("signSid") Long signSid,
      @JsonProperty("jsonMap") JSONObject jsonMap) {

    return SendGiftBO.builder()
        .template(Template.valueOf(template))
        .seq(seq)
        .sendUid(sendUid)
        .recvUid(recvUid)
        .giftId(giftId)
        .giftNum(giftNum)
        .sid(sid)
        .ssid(ssid)
        .eventTime(eventTime)
        .sourceChannel(GiftSourceChannel.valueOf(sourceChannel))
        .signSid(signSid)
        .jsonMap(jsonMap)
        .build();
  }


  /**
   * 模板类型
   */
  private final Template template;
  /**
   * 消息序列号
   */
  private final String seq;
  /**
   * 送礼uid
   */
  private final Long sendUid;
  /**
   * 收礼uid
   */
  private final Long recvUid;
  /**
   * 礼物id
   */
  private final String giftId;
  /**
   * 礼物数量
   */
  private final Long giftNum;
  /**
   * 顶级频道号
   */
  private final Long sid;
  /**
   * 子频道号
   */
  private final Long ssid;
  /**
   * 送礼时间
   */
  private final Date eventTime;
  /**
   * 礼物来源渠道
   */
  private final GiftSourceChannel sourceChannel;

  private final Long signSid;
  /**
   * 拓展字段
   */
  private final JSONObject jsonMap;

}
