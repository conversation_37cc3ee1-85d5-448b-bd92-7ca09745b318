package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import com.yy.thrift.hdztranking.RoleType;
import lombok.Data;

import java.util.List;

@Data
public class RankMilestoneReportComponentAttr extends ComponentAttr {

    /**
     * 生产报告的页面H5链接
     */
    @ComponentAttrField(labelText = "战报H5链接")
    protected String gloryLink;

    /**
     * 见{@link com.yy.thrift.hdztranking.BusiId}
     */
    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    protected int busiId;

    /**
     * 见{@link RoleType}
     */
    @ComponentAttrField(labelText = "角色类型", dropDownSourceBeanClass = RoleTypeSource.class)
    protected int roleType;

    /**
     * roleType是主播的话，是否返回签约工会信息
     */
    @ComponentAttrField(labelText = "返回签约工会信息", remark = "角色类型是主播时")
    protected boolean signedGuild = false;

    /**
     * 关注的榜单ID
     */
    @ComponentAttrField(labelText = "榜单ID")
    protected long rankId;

    @ComponentAttrField(labelText = "总榜榜单ID")
    protected long totalRankId = 0;

    /**
     * 关注的阶段ID
     */
    @ComponentAttrField(labelText = "阶段ID")
    protected long phaseId;

    /**
     * 关注的榜单分节点
     */
    @ComponentAttrField(labelText = "榜单分值节点", remark = "多个时逗号分隔"
            , subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    protected List<Long> milestoneScores;

    /**
     * freemarker文本模板
     */
    @ComponentAttrField(labelText = "freemarker文本模板")
    protected String template;

    protected String extHandler;
}
