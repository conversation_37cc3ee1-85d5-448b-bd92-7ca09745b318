package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.layer.BroActLayerService;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.HdztRankType;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.AwardBean;
import com.yy.gameecology.hdzj.bean.TiGuanGroupDetail;
import com.yy.gameecology.hdzj.bean.TiGuanMemberInfo;
import com.yy.gameecology.hdzj.bean.TiGuanMyDetail;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.TiGuanComponentAttr;
import com.yy.thrift.hdztranking.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 踢馆组件
 *
 * <AUTHOR>
 * @date 2022/03/30 17:42
 */
@UseRedisStore
@Component
@RestController
@RequestMapping("/cmpt/TiGuan")
public class TiGuanComponent extends BaseActComponent<TiGuanComponentAttr> {

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private BroActLayerService broActLayerService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    // 挑战错误码
    // 名额已满
    public static final int E_CHLN_QUOTA_EXHAUSTED = -1;
    // 已挑战选定的擂主
    public static final int E_CHLN_HAS_CHALLENGED_THIS = -2;
    // 已挑战其他的擂主
    public static final int E_CHLN_HAS_CHALLENGED_OTHER = -3;
    // 已超过主动挑战截止时间
    public static final int E_CHLN_TIME_END = -4;
    // 不能挑战自己
    public static final int E_CHLN_NOT_CHALLENG_ME = -5;
    // 被挑战的不是擂主
    public static final int E_CHLN_NOT_LEIZHU = -6;
    // 没有挑战资格
    public static final int E_CHLN_NO_QUALIFICATION = -7;
    // 数据准备中,请稍候!
    public static final int E_CHLN_READY_DATA = -8;
    // 不可挑战同OW旗下的主播
    public static final int E_SAME_OW_ANCHOR = -9;
    //不可挑战同公会的主播
    public static final int E_SAME_CHANNEL_ANCHOR = -10;
    //不可挑战上轮主播
    public static final int E_SAME_PK_ANCHOR = -11;

    // 报名 zset key = enroll:${phaseId}:${memberId}, value:成员ID， score：加入时的毫秒时间戳
    private static final String ENROLL_SET_KEY = "enroll:%s:%s";

    private static final String PROMOT_RESULT_KEY = "promot_result";
    private static final String PROMOT_SAVED_KEY = "promot_saved";

    // 结算防重 string key
    public static final String SETTLE_DONE_KEY = "settle_done";

    //主播对应ow map 防止相同ow pk 需要活动开始前导入白名单 hash {uid:owUid}
    // 活动级 act:2022064001:hdzj_cmpt:2033:anchor_ow_map
    public static final String ANCHOR_OW_MAP = "act:%s:hdzj_cmpt:2033:anchor_ow_map";

    // 擂台组详情缓存，给非踢馆参与者使用（数据可以不及时）
    private Cache<String, Response> groupDetailCache = CacheBuilder.newBuilder().
            initialCapacity(10).
            maximumSize(100).
            expireAfterWrite(10, TimeUnit.SECONDS).
            build();

    @Override
    public Long getComponentId() {
        return ComponentId.TI_GUAN;
    }

    /**
     * 发放奖励定时器
     */
    @Scheduled(cron = "*/5 * * * * ? ")
    @NeedRecycle(author = "guoliping", notRecycle = true)
    public void giveAwards() {
        this.giveAwards(2, 3600, 60 * 24);
    }

    /**
     * 响应当前榜结算事件，将擂主 和 打雷全员（擂主+挑战者） 分别保存在本系统的redis中（避免rpc调用），提高性能
     */
    @HdzjEventHandler(value = PromotTimeEnd.class, canRetry = false)
    public void onPromotTimeEnd(PromotTimeEnd event, TiGuanComponentAttr attr) {
        if (!attr.isNowDuty(event.getRankId(), event.getPhaseId())) {
            return;
        }

        Clock clock = new Clock();
        try {
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            LinkedHashMap<String, Long> promotedMembers = getPromotedMembers(event.getRankId(), attr);
            List<String> keys = Lists.newArrayList();
            List<String> values = Lists.newArrayList();
            List<Double> scores = Lists.newArrayList();

            int len = promotedMembers.size();
            String key = makePromotResultKey(event.getRankId(), attr);
            for (String memmberId : promotedMembers.keySet()) {
                keys.add(key);
                values.add(memmberId);
                // 加小数点达到保序目的，认为不可能超过100万
                String score = promotedMembers.get(memmberId) + "." + (1000000 + len - values.size());
                scores.add(new Double(score));
            }

            List<Object> results = actRedisDao.zBatchAddDouble(groupCode, keys, values, scores);

            // 标记踢馆赛的晋级处理完成
            String promotSavedKey = makePromotSavedKey(event.getRankId(), attr);
            actRedisDao.set(groupCode, promotSavedKey, event.getSeq() + "|" + DateUtil.today());

            log.info("onPromotTimeEnd ok@event:{}, attr:{}, promotedMembers:{}, result size:{} {}",
                    event, attr, JSON.toJSONString(promotedMembers), results.size(), clock.tag());
        } catch (Throwable t) {
            log.error("onPromotTimeEnd exception@event:{}, attr:{}, err:{} {}", event, attr, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 新辅助榜阶段结束，触发进行打擂结算，新擂主就保存在辅助榜的当前阶段（作为擂主榜下个阶段的晋级来源）
     */
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = false)
    public void onPhaseTimeEnd(PhaseTimeEnd event, TiGuanComponentAttr attr) {
        if (!attr.isNewDuty(event.getRankId(), event.getPhaseId())) {
            return;
        }

        Clock clock = new Clock();
        try {
            //当前擂主
            Set<String> nowWinnerSet = getNowWinnerSet(attr);
            //所有参赛者分数
            LinkedHashMap<String, Long> nowAllMap = getNowAllMap(attr);
            //获取分组信息
            Map<String, Set<String>> nowWinnerGroupMap = getNowWinnerGroupMap(nowWinnerSet, attr);
            String eseq = event.getEkey() + "|" + event.getSeq();
            settle(eseq, nowWinnerGroupMap, nowAllMap, event, attr);
        } catch (Throwable t) {
            log.error("onPhaseTimeEnd exception@event:{}, attr:{}, err:{} {}", event, attr, t.getMessage(), clock.tag(), t);
        }
    }


    /**
     * 系统自动报名处理
     */
    @Scheduled(cron = "*/10 * * * * ? ")
    @NeedRecycle(author = "guoliping", notRecycle = true)
    public void sysEnroll() {
        Clock clock = new Clock();
        Set<Long> activityIds = getComponentEffectActIds();
        for (Long actId : activityIds) {
            if (actInfoService.inActTime(actId)) {
                List<TiGuanComponentAttr> attrs = this.getAllComponentAttrs(actId);
                for (TiGuanComponentAttr attr : attrs) {
                    String lockName = this.makeKey(attr, "locker");
                    timerSupport.work(lockName, 180, () -> {
                        this.doSysEnroll(attr);
                    });
                }
            }
        }
        log.info("sysEnroll Scheduled done@act size:{} {}", activityIds.size(), clock.tag());
    }

    @RequestMapping("/getGroupDetail")
    public Response getGroupDetail(HttpServletRequest req, HttpServletResponse resp, long actId, long cmptUseInx) {
        TiGuanComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return Response.badRequest();
        }

        Clock clock = new Clock();
        try {
            long myUid = getLoginYYUid(req, resp);
            if (judgeUseCacheForGetGroupDetail(myUid, attr)) {
                return groupDetailCache.get(actId + ":" + cmptUseInx, () -> getGroupDetail(actId, cmptUseInx));
            } else {
                return getGroupDetail(actId, cmptUseInx);
            }
        } catch (ExecutionException e) {
            log.error("getGroupDetail fail@actId:{}, cmptUseInx:{}, err:{} {}", actId, cmptUseInx, e.getMessage(), clock.tag(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    /**
     * 发起挑战
     */
    @RequestMapping("/challenge")
    public Response challenge(HttpServletRequest req, HttpServletResponse resp, long actId, long cmptUseInx, long lzUid) {
        long myUid = getLoginYYUid(req, resp);
        Response response = challenge(actId, cmptUseInx, lzUid, myUid);
        log.info("challenge info@actId:{},cmptUseInx:{},lzUid:{}myUid:{},resp:{}", actId, cmptUseInx, lzUid, myUid, JSON.toJSONString(response));
        return response;
    }

    public Response challenge(long actId, long cmptUseInx, long lzUid, long myUid) {
        Clock clock = new Clock();
        try {
            TiGuanComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);
            if (attr == null) {
                return Response.badRequest();
            }

            if (myUid == 0) {
                return Response.fail(-1, "未登录");
            }

            Response checkResult = checkChallengeCondition(attr, lzUid, myUid);
            if (checkResult != null) {
                return checkResult;
            }

            Response response = enroll(attr, String.valueOf(lzUid), String.valueOf(myUid), "manual");
            //挑战成功更新挂件
            if (response.getResult() == 0) {
                UserCurrentChannel userCurrentChannel = commonService.getNoCacheUserCurrentChannel(myUid, 3);
                if (userCurrentChannel != null) {
                    OnlineChannelInfo onlineChannel = new OnlineChannelInfo();
                    onlineChannel.setSid(userCurrentChannel.getTopsid());
                    onlineChannel.setSsid(userCurrentChannel.getSubsid());
                    broActLayerService.broChannel(actId, onlineChannel);


                    log.info("challenge  bro done!uid:{},sid:{},ssid:{}", myUid, userCurrentChannel.getTopsid(), userCurrentChannel.getSubsid());
                }

            }
            return response.getResult() == 0 ? Response.ok("选择成功，挑战开始") : response;
        } catch (Exception e) {
            log.error("me exception@actId:{}, cmptUseInx:{}, myUid:{}, err:{} {}", actId, cmptUseInx, myUid, e.getMessage(), clock.tag(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    /**
     * 获取我的信息
     */
    @RequestMapping("/getMyDetail")
    public Response getMyDetail(HttpServletRequest req, HttpServletResponse resp, long actId, long cmptUseInx) {
        Clock clock = new Clock();
        long uid = -1;
        try {
            uid = getLoginYYUid(req, resp);
            if (uid == 0) {
                return Response.fail(-1, "未登录");
            }

            TiGuanMyDetail member = getMyDetail(actId, cmptUseInx, uid);
            return Response.success(member);
        } catch (Exception e) {
            log.error("getMyDetail exception@actId:{}, cmptUseInx:{}, uid:{}, err:{} {}", actId, cmptUseInx, uid, e.getMessage(), clock.tag(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    /**
     * 自动报名处理，本函数实现确保没有完全成功前，可以多次尝试自动报名，直到全部完成
     */
    private void doSysEnroll(TiGuanComponentAttr attr) {
        Clock clock = new Clock();
        try {
            // 检查时间是否合适，特意挑在手动挑战截止后的10秒开始（防止手动挑战和系统自动完成挑战的并发冲突），超过1800秒后也不再尝试
            Date now = commonService.getNow(attr.getActId());
            Date endTime = DateUtil.getDate(attr.getManualChallengeEndTime());
            boolean between = DateUtil.between(now, endTime, 10, 1800);
            if (!between) {
                log.info("doSysEnroll skip@not sys auto enroll time, actId:{}, cmptUseInx:{}, end:{}",
                        attr.getActId(), attr.getCmptUseInx(), attr.getManualChallengeEndTime());
                return;
            }

            // 检查是否已经做过
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            String doneKey = this.makeKey(attr, "sysEnrollDone");
            if (actRedisDao.hasKey(groupCode, doneKey)) {
                log.info("doSysEnroll ignore@has done, doneKey:{}", doneKey);
                return;
            }

            // 取出所有打擂成员
            String keyAll = makePromotResultKey(attr.getNowAllRankId(), attr);
            Set<String> all = actRedisDao.zrevRangeNoScores(groupCode, keyAll, 0);
            clock.tag();

            // 得到所有主播的签约公会
            Map<String, Long> sids = getAnchorSingedSids(attr.getActId(), attr.getBusiId(), 200, all);
            clock.tag();

            // 对分组好的尚未报名成员，一一做报名，任一处理失败，则整个过程失败
            boolean bAllOk = true;
            Map<String, Set<String>> appendSets = makeSysEnrollMap(attr, all, sids);
            for (String lzUid : appendSets.keySet()) {
                for (String tzUid : appendSets.get(lzUid)) {
                    Response<?> response = enroll(attr, lzUid, tzUid, "sys");
                    if (response.getResult() == Response.OK) {
                        Long lzSid = sids.get(lzUid);
                        Long tzSid = sids.get(tzUid);
                        log.info("doSysEnroll one ok@lzUid:{}, lzSid:{}, tzUid:{}, tzSid:{}, bAllOk:{}", lzUid, lzSid, tzUid, tzSid, bAllOk);
                    } else {
                        bAllOk = false;
                        log.error("doSysEnroll one fail@lzUid:{}, tzUid:{}, response:{}", lzUid, tzUid, response);
                    }
                }
            }

            // 标记已经完成系统自动报名，防止重复执行
            String content = JSON.toJSONString(appendSets);
            if (bAllOk) {
                boolean flag = actRedisDao.setNX(groupCode, doneKey, DateUtil.today() + ", " + content);
                log.info("doSysEnroll ok@doneKey:{}, flag:{}, attr:{}, content:{} {}", doneKey, flag, attr, content, clock.tag());
            } else {
                log.error("doSysEnroll fail@doneKey:{}, attr:{}, content:{} {}", doneKey, attr, content, clock.tag());
            }
        } catch (Throwable t) {
            log.error("doSysEnroll exception@attr:{}, err:{} {}", attr, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 获取主播的签约频道, 用于 不同频道的主播有些踢馆挑战匹配，
     * 鉴于此，对异常情况统一让签约频道为0，用于防止这里的失败导致后续的失败！
     */
    private Map<String, Long> getAnchorSingedSids(long actId, long busiId, int roleType, Set<String> anchorUids) {
        Map<String, Long> anchorSignedSids = Maps.newHashMap();
        for (String anchorUid : anchorUids) {
            try {
                EnrollmentInfo ei = enrollmentService.tryGetFirstEnrolMemberCache(actId, busiId, roleType, anchorUid);
                if (ei != null) {
                    anchorSignedSids.put(anchorUid, ei.getSignSid());
                }
            } catch (Throwable t) {
                log.error("getAnchorSingedSids exception@actId:{}, busiId:{}, roleType:{}, anchorUid:{}, err:{} {}", actId, busiId, roleType, anchorUid, t.getMessage(), t);
            }
        }
        return anchorSignedSids;
    }

    /**
     * 计算公会数相同的数量， 两个 List参数数量必须相同
     * 这里绝对地容忍错误，有问题时就当相同公会数量为 0
     * 计算
     */
    private int calcSameV2(int len, List<String> lzUids, List<String> tzUids, Map<String, Long> sids,
                           Map<Object, Object> lastPKMap, Map<Object, Object> uid2OW) {
        try {
            int count = 0;
            for (int i = 0; i < len; i++) {
                String lzUid = lzUids.get(i);
                String tzUid = tzUids.get(i);

                Long lzSid = sids.get(lzUid);
                Long tzSid = sids.get(tzUid);
                if (lzSid != null && lzSid.equals(tzSid)) {
                    count += 5;
                }

                Object lzOw = uid2OW.get(lzUid);
                Object tzOW = uid2OW.get(tzUid);
                if (lzOw != null && lzOw.equals(tzOW)) {
                    count += 3;
                }

                Object lzPK = lastPKMap.get(lzUid);
                Object tzPk = lastPKMap.get(tzUid);
                if (lzPK != null && lzPK.equals(tzPk)) {
                    count += 1;
                }
            }
            return count;
        } catch (Throwable t) {
            log.error("calcSameV2 exception@lzUids:{}, tzUids:{}, sids:{}, err:{}",
                    JSON.toJSONString(lzUids), JSON.toJSONString(tzUids), JSON.toJSONString(sids), t.getMessage(), t);
            // 发生异常，无法再做判断了， 直接返回0
            return 0;
        }
    }

    /**
     * 计算公会数相同的数量， 两个 List参数数量必须相同
     * 这里绝对地容忍错误，有问题时就当相同公会数量为 0
     */
    private int calcSameSid(int len, List<String> lzUids, List<String> tzUids, Map<String, Long> sids) {
        try {
            int count = 0;
            for (int i = 0; i < len; i++) {
                Long lzSid = sids.get(lzUids.get(i));
                Long tzSid = sids.get(tzUids.get(i));
                if (lzSid != null && tzSid != null && lzSid.equals(tzSid)) {
                    count++;
                }
            }
            return count;
        } catch (Throwable t) {
            log.error("calcSameSid exception@lzUids:{}, tzUids:{}, sids:{}, err:{}",
                    JSON.toJSONString(lzUids), JSON.toJSONString(tzUids), JSON.toJSONString(sids), t.getMessage(), t);
            // 发生异常，无法再做判断了， 直接返回0
            return 0;
        }
    }

    /**
     * 准备补充报名的数据， key是擂主uid， value是分配的挑战uid
     */
    private Map<String, Set<String>> makeSysEnrollMap(TiGuanComponentAttr attr, Set<String> all, Map<String, Long> sids) {
        Clock clock = new Clock();

        // 从打雷成员中去除擂主
        Set<String> nowWinners = getNowWinnerSet(attr);
        all.removeAll(nowWinners);

        // 和未报名人数一样长度的主播uid, 由于后面做非同公会优先挑战匹配
        List<String> lzUids = new ArrayList<String>(all.size());

        // 从打雷成员中去除已报名的成员
        int groupLen = attr.getGroupLen();
        Map<String, Set<String>> groupEnrolledMap = this.getNowWinnerGroupMap(nowWinners, attr);
        for (String winner : groupEnrolledMap.keySet()) {
            Set<String> groupEnrolled = groupEnrolledMap.get(winner);
            all.removeAll(groupEnrolled);
            for (int i = 0; i < groupLen - groupEnrolled.size(); i++) {
                lzUids.add(winner);
            }
        }
        clock.tag();


        String anchorOwMapKey = String.format(ANCHOR_OW_MAP, attr.getActId());
        Map<Object, Object> uid2Ow = actRedisDao.hGetAll(getRedisGroupCode(attr.getActId()), anchorOwMapKey);
        Map<Object, Object> allPkMap = allPkMap(attr.getActId());
        if (uid2Ow == null) {
            log.error("uid2Ow isEmpty,please check config, redis key:{}", anchorOwMapKey);
            uid2Ow = Maps.newHashMap();
        }

        clock.tag();
        // 定义最小的相同公会数量 和 找到的最好的挑战者排列list
        int min = Integer.MAX_VALUE;
        List<String> goodList = null;

        // 遍历10万次找出最好的
        int len = lzUids.size();
        int k = 0;
        for (; k < attr.getGoodMateTryNum(); k++) {
            List<String> unenrolled = Lists.newArrayList(all);
            Collections.shuffle(unenrolled);
            int count = calcSameV2(len, lzUids, unenrolled, sids, allPkMap, uid2Ow);

            if (count < min) {
                min = count;
                goodList = unenrolled;
            }
            if (min == 0) {
                break;
            }
        }

        // 做挑战匹配
        Map<String, Set<String>> appendSets = Maps.newLinkedHashMap();
        for (int i = 0; i < len; i++) {
            String lzUid = lzUids.get(i);
            String tzUid = goodList.get(i);
            appendSets.computeIfAbsent(lzUid, k1 -> Sets.newLinkedHashSet()).add(tzUid);
        }

        log.info("makeSysEnrollMap done@k:{}, min:{}, len:{} {}", k, min, len, clock.tag());

        return appendSets;
    }

    /**
     * 获取各分组情况， 保持擂主顺序， 保持报名顺序
     */
    List<TiGuanGroupDetail> getGroupDetail(TiGuanComponentAttr attr, Map<String, Set<String>> groupList, LinkedHashMap<String, Long> nowAllMap) {
        List<TiGuanGroupDetail> list = Lists.newArrayList();
        Map<String, Integer> nowAllRankMap = Maps.newHashMap();
        int rank = 1;
        for (String menber : nowAllMap.keySet()) {
            nowAllRankMap.put(menber, rank);
            rank++;
        }
        int groupLen = attr.getGroupLen();
        for (String winner : groupList.keySet()) {
            List<TiGuanMemberInfo> challengers = groupList.get(winner).stream().map(c -> new TiGuanMemberInfo(c, nowAllMap.get(c)))
                    .sorted(Comparator.comparingInt(item -> nowAllRankMap.get(item.getMemberId()))).collect(Collectors.toList());
            TiGuanMemberInfo leizhu = new TiGuanMemberInfo(winner, nowAllMap.get(winner));
            long status = challengers.size() < groupLen ? 0 : 1;
            list.add(new TiGuanGroupDetail(leizhu, challengers, status));
        }
        return list;
    }

    /**
     * 填充昵称、logo、胜利状态、获得奖励数量 等
     *
     * @param attr
     * @param nowAllMap
     * @param list
     */
    private void fillSettleInfo(TiGuanComponentAttr attr, LinkedHashMap<String, Long> nowAllMap, List<TiGuanGroupDetail> list) {
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String doneKey = makeKey(attr, SETTLE_DONE_KEY);
        boolean bHasDone = actRedisDao.hasKey(groupCode, doneKey);
        List<String> sortedMembers = nowAllMap.keySet().stream().map(memberId -> memberId).collect(Collectors.toList());
        Map<Long, UserBaseInfo> userInfoMap = commonService.batchGetUserInfos(sortedMembers.stream().map(i -> new Long(i)).collect(Collectors.toList()), true);

        for (TiGuanGroupDetail detail : list) {
            TiGuanMemberInfo leizhu = detail.getLeizhu();
            List<TiGuanMemberInfo> groupMembers = Lists.newArrayList(leizhu);
            groupMembers.addAll(detail.getChallengers());

            long lzUid = Long.valueOf(leizhu.getMemberId());
            UserBaseInfo ui1 = userInfoMap.get(lzUid);
            leizhu.setNickname(ui1.getNick());
            leizhu.setLogo(ui1.getLogo());
            if (bHasDone) {
                leizhu.setStatus(isVictory(leizhu, groupMembers, sortedMembers, detail) ? 1 : -1);
                leizhu.setAwardNum(attr.getAwardNum());
            }

            leizhu.setChannelInfo(onMicService.getOnMicChannel(lzUid));
            for (TiGuanMemberInfo challenger : detail.getChallengers()) {
                long challengerUid = Long.valueOf(challenger.getMemberId());
                UserBaseInfo ui2 = userInfoMap.get(challengerUid);
                challenger.setNickname(ui2.getNick());
                challenger.setLogo(ui2.getLogo());
                if (bHasDone) {
                    challenger.setStatus(isVictory(challenger, groupMembers, sortedMembers, detail) ? 1 : -1);
                }
                challenger.setChannelInfo(onMicService.getOnMicChannel(challengerUid));
            }
        }
    }

    /**
     * 判断是否胜利（已考虑了同分先到排前）
     */
    private boolean isVictory(TiGuanMemberInfo me, List<TiGuanMemberInfo> groupMembers, List<String> sortedMembers, TiGuanGroupDetail detail) {
        //调整这第一名的分数
        Long firstChallengerScore = detail.getChallengers().stream()
                .sorted(Comparator.comparingLong(TiGuanMemberInfo::getScore).reversed())
                .findFirst().map(TiGuanMemberInfo::getScore).orElse(0L);
        //同分主播获胜
        if (me == detail.getLeizhu() && me.getScore() >= firstChallengerScore) {
            return true;
        }
        for (TiGuanMemberInfo other : groupMembers) {
            String meId = me.getMemberId();
            String otherId = other.getMemberId();
            if (meId.equals(otherId)) {
                continue;
            }
            int i = sortedMembers.indexOf(meId);
            int j = sortedMembers.indexOf(otherId);
            if (i > j) {
                return false;
            }
        }
        //挑战者要大于主播的分数才能获胜
        if (me != detail.getLeizhu() && me.getScore() <= detail.getLeizhu().getScore()) {
            return false;
        }
        return true;
    }

    /**
     * @param eseq
     * @param nowWinnerGroupMap 当前分组
     * @param nowAllMap         所有参赛者分数
     * @param event
     * @param attr
     */
    private void settle(String eseq, Map<String, Set<String>> nowWinnerGroupMap, LinkedHashMap<String, Long> nowAllMap, PhaseTimeEnd event, TiGuanComponentAttr attr) {
        Clock clock = new Clock();
        Map<String, List<TiGuanMemberInfo>> nowWinnerGroupSortedList = getNowWinnerGroupSortedList(nowWinnerGroupMap, nowAllMap);
        List<TiGuanMemberInfo> newWinnerList = getNewWinnerList(nowWinnerGroupSortedList);
        List<UpdateRankingRequest> requests = makeUpdateRankingRequests(newWinnerList, event, attr);

        // 计算守擂成功擂主的奖励
        List<TiGuanMemberInfo> holdVictoryLeiZhus = getHoldVictoryLeiZhu(nowWinnerGroupSortedList);
        List<AwardBean> awards = calcAward(holdVictoryLeiZhus, attr);

        // 防重处理
        long actId = attr.getActId();
        String groupCode = redisConfigManager.getGroupCode(actId);
        String doneSummary = DateUtil.today() + ", " + eseq;
        String doneKey = makeKey(attr, SETTLE_DONE_KEY);
        if (!actRedisDao.setNX(groupCode, doneKey, doneSummary)) {
            log.error("settle fail@duplicated settle, ekey:{}, doneKey:{} {}", eseq, doneKey, clock.tag());
            return;
        }

        // 保存奖励
        this.saveAward(attr, eseq, awards);

        String content = "\nekey:" + event.getEkey() + "\nseq:" + event.getSeq() + "\nendTime:" + event.getEndTime() + "\nsize:" + requests.size();
        ArrayList<String> userIds = Lists.newArrayList();

        // 尝试3次保存数据
        final int three = 3;
        for (int i = 1; i <= three; i++) {
            if (save(requests)) {
                break;
            }

            if (i == three) {
                baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS,
                        "<font color=\"red\">TiGuan-Settle-Save FAIL! </font>\n" + i + content, userIds);
                log.error("settle fail@save try 3 time, eseq:{}, attr:{}, groupLists:{}, newWinnerList:{} {}", eseq, attr,
                        JSON.toJSONString(nowWinnerGroupSortedList), JSON.toJSONString(newWinnerList), clock.tag());
                return;
            }

            SysEvHelper.waiting(200);
        }

        // 尝试3次通知下阶段结算
        for (int i = 1; i <= three; i++) {
            if (notify(attr)) {
                break;
            }

            if (i == three) {
                baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS,
                        "<font color=\"red\">【TiGuan-Settle-notify FAIL!】-</font>" + i + content, userIds);
                log.error("settle fail@notify try 3 time, eseq:{}, attr:{}, groupLists:{}, newWinnerList:{} {}", eseq, attr,
                        JSON.toJSONString(nowWinnerGroupSortedList), JSON.toJSONString(newWinnerList), clock.tag());
                return;
            }

            SysEvHelper.waiting(200);
        }

        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS,
                "<font color=\"green\">【TiGuan-Settle OK】</font>" + content, userIds);

        log.info("settle ok@eseq:{}, attr:{}, groupLists:{}, newWinnerList:{} {}", eseq, attr,
                JSON.toJSONString(nowWinnerGroupSortedList), JSON.toJSONString(newWinnerList), clock.tag());
    }

    private List<AwardBean> calcAward(List<TiGuanMemberInfo> holdVictoryLeiZhus, TiGuanComponentAttr attr) {
        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        List<AwardBean> list = Lists.newArrayList();
        for (TiGuanMemberInfo info : holdVictoryLeiZhus) {
            Map<Long, Integer> packageIdNumMap = ImmutableMap.of(attr.getPackageId(), attr.getAwardNum());
            Map<Long, Map<Long, Integer>> taskPackageIds = ImmutableMap.of(attr.getTaskId(), packageIdNumMap);
            String myseq = java.util.UUID.randomUUID() + "#TG" + (list.size() + 1);
            String receiver = info.getMemberId();
            AwardBean personalTaskAward = new AwardBean(myseq, attr.getBusiId(), receiver, time, taskPackageIds);
            list.add(personalTaskAward);
        }
        return list;
    }

    private boolean notify(TiGuanComponentAttr attr) {
        // 找出所有依赖本打雷的结果的晋级赛段， 找出配置的控制tag ，若没有还要报错！
        // 生成所有的控制tag，批量执行打标记
        try {
            NotifySettleRequest request = new NotifySettleRequest();
            request.setActId(attr.getActId());
            request.setRankId(attr.getNowWinnerRankId());
            request.setType(1L);
            request.setTag(attr.getPromotCtrlTag());
            NotifySettleResponse response = hdztRankingThriftClient.getProxy().notifySettle(request);
            return response.getCode() == 0;
        } catch (Throwable t) {
            log.error("notifySettle fail:", t);
            return false;
        }
    }

    private boolean save(List<UpdateRankingRequest> requests) {
        Clock clock = new Clock();
        LinkedHashSet<FutureTask<Boolean>> initFtSet = Sets.newLinkedHashSet();
        int count = 0;
        for (UpdateRankingRequest request : requests) {
            final int inx = ++count;
            FutureTask<Boolean> futureTask = new FutureTask<Boolean>(new Callable<Boolean>() {
                @Override
                public Boolean call() throws Exception {
                    return doUpdateRanking(request, inx, 2);
                }
            });
            threadPoolManager.get(Const.GENERAL_POOL).submit(futureTask);
            initFtSet.add(futureTask);
        }

        // 将set倒序执行get方法
        int inx = 0;
        boolean flag = true;
        try {
            for (FutureTask<Boolean> task : initFtSet) {
                Boolean ret = task.get(10, TimeUnit.SECONDS);
                if (!ret) {
                    flag = false;
                }
                log.info("save FutureTask get ok@inx:{}, ret:{}, flag:{}", ++inx, ret, flag);
            }
        } catch (Throwable e) {
            log.error("save get exception@count:{}, inx:{}, flag:{}, err:{} {}", count, inx, flag, e.getMessage(), clock.tag(), e);
            flag = false;
        }
        return flag;
    }

    private String makePromotSavedKey(long rankId, TiGuanComponentAttr attr) {
        return makeKey(attr, PROMOT_SAVED_KEY + ":" + rankId + ":" + attr.getPhaseId());
    }

    private String makePromotResultKey(long rankId, TiGuanComponentAttr attr) {
        return makeKey(attr, PROMOT_RESULT_KEY + ":" + rankId + ":" + attr.getPhaseId());
    }

    private Boolean doUpdateRanking(UpdateRankingRequest request, int inx, int retry) {
        Throwable le = null;
        Clock clock = new Clock();
        for (int i = 1; i <= retry + 1; i++) {
            try {
                UpdateRankingResult response = hdztRankingThriftClient.getProxy().updateRanking(request);
                log.info("save done@inx:{}, try:{}, request:{}, response:{} {}", inx, i, request, response, clock.tag());
                return true;
            } catch (Throwable t) {
                log.warn("save exception@inx:{}, try:{}, request:{}, err:{} {}", inx, i, request, t.getMessage(), clock.tag(), t);
                le = t;
            }
        }

        if (le != null) {
            log.error("save fail@inx:{}, try:{}, request:{}, err:{} {}", inx, retry + 1, request, le.getMessage(), clock.tag(), le);
        }

        return false;
    }

    private List<UpdateRankingRequest> makeUpdateRankingRequests(List<TiGuanMemberInfo> newWinnerList, PhaseTimeEnd event, TiGuanComponentAttr attr) {
        List<UpdateRankingRequest> list = Lists.newArrayList();
        for (int i = 0; i < newWinnerList.size(); i++) {
            TiGuanMemberInfo ms = newWinnerList.get(i);
            String memberId = ms.getMemberId();
            String seq = "TG:" + event.getSeq() + ":" + memberId;
            UpdateRankingRequest request = new UpdateRankingRequest();
            request.setBusiId(attr.getBusiId());
            request.setActId(attr.getActId());
            request.setSeq(seq);
            request.setActors(ImmutableMap.of(attr.getNewWinerRoleId(), memberId));
            request.setItemId(attr.getNewWinerItemId());
            request.setCount(1);
            request.setScore(ms.getScore());
            request.setTimestamp(DateUtil.getDate(event.getEndTime()).getTime());
            request.setExtData(ImmutableMap.of("not_adjust_event_time_delay", "1"));
            list.add(request);
        }
        return list;
    }

    private List<TiGuanMemberInfo> getNewWinnerList(Map<String, List<TiGuanMemberInfo>> nowWinnerGroupSortedList) {
        List<TiGuanMemberInfo> result = Lists.newArrayList();

        for (Map.Entry<String, List<TiGuanMemberInfo>> entry : nowWinnerGroupSortedList.entrySet()) {
            List<TiGuanMemberInfo> list = entry.getValue();
            String lz = entry.getKey();
            TiGuanMemberInfo newWinner = list.get(0);
            if (!newWinner.getMemberId().equals(lz)) {
                Optional<TiGuanMemberInfo> lzInfo = list.stream().filter(item -> item.getMemberId().equals(lz)).findFirst();
                if (lzInfo.isPresent()) {
                    Long lzScore = lzInfo.map(TiGuanMemberInfo::getScore).orElse(0L);
                    //平分擂主
                    newWinner = newWinner.getScore() == lzScore ? lzInfo.get() : newWinner;
                } else {
                    throw new RuntimeException(lz + "擂主分数无法找到，" + JSON.toJSONString(entry));
                }
            }
            result.add(newWinner);
        }
        return result;
    }

    /**
     * 获取守擂成的擂主
     */
    private List<TiGuanMemberInfo> getHoldVictoryLeiZhu(Map<String, List<TiGuanMemberInfo>> nowWinnerGroupSortedList) {
        List<TiGuanMemberInfo> result = Lists.newArrayList();
        for (String oldWinnerId : nowWinnerGroupSortedList.keySet()) {
            TiGuanMemberInfo newWinner = nowWinnerGroupSortedList.get(oldWinnerId).get(0);
            if (oldWinnerId.equals(newWinner.getMemberId())) {
                result.add(newWinner);
            }
        }
        return result;
    }

    private Map<String, List<TiGuanMemberInfo>> getNowWinnerGroupSortedList(Map<String, Set<String>> nowWinnerGroupMap, LinkedHashMap<String, Long> nowAllMap) {
        Map<String, List<TiGuanMemberInfo>> map = Maps.newLinkedHashMap();
        for (String winner : nowWinnerGroupMap.keySet()) {
            List<TiGuanMemberInfo> list = Lists.newArrayList();
            for (String memberId : nowAllMap.keySet()) {
                if (nowWinnerGroupMap.get(winner).contains(memberId) || winner.equals(memberId)) {
                    list.add(new TiGuanMemberInfo(memberId, nowAllMap.get(memberId)));
                }
            }
            map.put(winner, list);
        }
        return map;
    }

    private Map<String, Set<String>> getNowWinnerGroupMap(Set<String> nowWinnerSet, TiGuanComponentAttr attr) {
        // 准备好所有擂主报名key
        List<String> keys = Lists.newArrayList();
        for (String winner : nowWinnerSet) {
            keys.add(makeEnrollKey(attr, winner)[0]);
        }

        // 批量查询
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        List<Set<String>> groupChallengers = actRedisDao.zBatchRevRangeNoScores(groupCode, keys, 0L);

        // 按擂主分组返回挑战者
        int inx = 0;
        Map<String, Set<String>> map = Maps.newLinkedHashMap();
        for (String winner : nowWinnerSet) {
            Set<String> challengers = groupChallengers.get(inx++);
            map.put(winner, challengers == null ? Sets.newLinkedHashSet() : challengers);
        }

        return map;
    }

    private String[] makeEnrollKey(TiGuanComponentAttr attr, String memberId) {
        // 每个擂主 memberId 的报名 zset key
        String lzKey = makeKey(attr, String.format(ENROLL_SET_KEY, attr.getPhaseId(), memberId));
        // 全局防重报名检查 zset key
        String duplicatedKey = makeKey(attr, String.format(ENROLL_SET_KEY, attr.getPhaseId(), "duplicatedCheck"));
        return new String[]{lzKey, duplicatedKey};
    }

    private Map<Object, Object> allPkMap(long actId) {
        List<TiGuanComponentAttr> attrs = getAllComponentAttrs(actId);
        Map<Object, Object> pkMap = Maps.newHashMap();
        for (TiGuanComponentAttr attr : attrs) {
            String duplicatedKey = makeKey(attr, String.format(ENROLL_SET_KEY, attr.getPhaseId(), "duplicatedCheck"));
            Map<Object, Object> hGetAll = actRedisDao.hGetAll(getRedisGroupCode(actId), duplicatedKey);
            if (!CollectionUtils.isEmpty(hGetAll)) {
                pkMap.putAll(hGetAll);
            }
        }
        return pkMap;
    }

    private LinkedHashMap<String, Long> getNowAllMap(TiGuanComponentAttr attr) {
        return getNowAllMap(attr, false);
    }

    /**
     * 获取当所有参赛者（胜者+败者）Map，保持了排名顺序
     */
    private LinkedHashMap<String, Long> getNowAllMap(TiGuanComponentAttr attr, boolean bUseCache) {
        long actId = attr.getActId();
        long rankId = attr.getNowAllRankId();
        long phaseId = attr.getPhaseId();
        QueryRankingByScoreResponse response = bUseCache ?
                hdztRankService.queryRankingByScoreCache(actId, rankId, phaseId, null, HdztRankType.CONTEST) :
                hdztRankService.queryRankingByScore(actId, rankId, phaseId, null, HdztRankType.CONTEST);
        if (response == null || response.code != 0) {
            throw new SuperException("获取All失败", SuperException.E_UNKNOWN);
        }

        // rpc返回的是从小到大，这里要转成从大到小
        LinkedHashMap<String, Long> map = Maps.newLinkedHashMap();
        for (int i = response.rankingByScoreInfos.size() - 1; i >= 0; i--) {
            RankingByScoreInfo item = response.rankingByScoreInfos.get(i);
            map.put(item.memberId, item.score);
        }

        return map;
    }

    /**
     * 从活动中台ranking系统查最新的晋级成员
     */
    private LinkedHashMap<String, Long> getPromotedMembers(long rankId, TiGuanComponentAttr attr) {
        long actId = attr.getActId();
        long phaseId = attr.getPhaseId();
        QueryRankingByScoreResponse response = hdztRankService.queryRankingByScore(actId, rankId, phaseId, null, HdztRankType.PROMOT);
        if (response == null || response.code != 0) {
            throw new SuperException("获取晋级名单失败", SuperException.E_UNKNOWN);
        }

        LinkedHashMap<String, Long> map = Maps.newLinkedHashMap();
        for (RankingByScoreInfo item : response.rankingByScoreInfos) {
            map.put(item.memberId, item.score);
        }
        return map;
    }

    /**
     * 获取当前主赛道上的获胜者Map
     */
    private Set<String> getNowWinnerSet(TiGuanComponentAttr attr) {
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String winnerPromotKey = makePromotResultKey(attr.getNowWinnerRankId(), attr);
        return actRedisDao.zrevRangeNoScores(groupCode, winnerPromotKey, 0);
    }

    /**
     * 报名，本函数保证：
     * 1）对一个明确的分组：不会超额报名
     * 2）全局范围未不会重复报名
     * 基于上面2点，本函数是可幂等执行的
     */
    private Response enroll(TiGuanComponentAttr attr, String lzMemberId, String myMemberId, String tag) {
        String[] keys = makeEnrollKey(attr, String.valueOf(lzMemberId));
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        //long result = actRedisDao.zAddWithLimitLen(groupCode, keys[0], String.valueOf(myMemberId), System.currentTimeMillis(), attr.getGroupLen(), keys[1], lzMemberId);
        long result;
        //TODO 如果次组件要重新启用，则需要改造此处
        if (true) {
            throw new RuntimeException("zadd_with_limit_len.lua,lua forbid");
        }
        log.info("enroll done@tag:{}, key:{}, lz:{}, my:{}, maxLen:{}, result:{}", tag, keys[0], lzMemberId, myMemberId, attr.getGroupLen(), result);

        final int mOne = -1, mTwo = -2, mThree = -3;
        if (result == mOne) {
            return new Response(E_CHLN_QUOTA_EXHAUSTED, "该主播的挑战者已满");
        }

        if (result == mTwo) {
            return new Response(E_CHLN_HAS_CHALLENGED_THIS, "已挑战选定的擂主");
        }

        if (result == mThree) {
            return new Response(E_CHLN_HAS_CHALLENGED_OTHER, "已挑战其他的擂主");
        }

        return Response.ok("报名成功:" + result);
    }

    private Response checkChallengeCondition(TiGuanComponentAttr attr, long lzUid, long myUid) {
        // 防并发:先确保晋级数据本地保存就绪
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String nowAllRankPromotSavedKey = makePromotSavedKey(attr.getNowAllRankId(), attr);
        String nowWinnerRankPromotSavedKey = makePromotSavedKey(attr.getNowWinnerRankId(), attr);
        if (!actRedisDao.hasKey(groupCode, nowAllRankPromotSavedKey) || !actRedisDao.hasKey(groupCode, nowWinnerRankPromotSavedKey)) {
            return new Response(E_CHLN_READY_DATA, "数据准备中,请稍候!");
        }

        // 检查是否可打雷成员
        String myKey = makePromotResultKey(attr.getNowAllRankId(), attr);
        if (actRedisDao.zRevRank(groupCode, myKey, String.valueOf(myUid)) == null) {
            return new Response(E_CHLN_NO_QUALIFICATION, "只有上轮被淘汰的主播才可进行挑战");
        }

        Date endTime = DateUtil.getDate(attr.getManualChallengeEndTime());
        if (commonService.getNow(attr.getActId()).after(endTime)) {
            return new Response(E_CHLN_TIME_END, "已超过主动挑战截止时间");
        }

        // 擂主和挑战者必须不同
        if (lzUid == myUid) {
            return new Response(E_CHLN_NOT_CHALLENG_ME, "不能挑战自己");
        }

        // 检查是否擂主
        String lzKey = makePromotResultKey(attr.getNowWinnerRankId(), attr);
        String lzMemberId = String.valueOf(lzUid);
        if (actRedisDao.zRevRank(groupCode, lzKey, lzMemberId) == null) {
            return new Response(E_CHLN_NOT_LEIZHU, "被挑战的不是擂主");
        }

        if (actRedisDao.zRevRank(groupCode, lzKey, String.valueOf(myUid)) != null) {
            return new Response(E_CHLN_NOT_LEIZHU, "擂主不能挑战别人");
        }

        String duplicatedKey = makeKey(attr, String.format(ENROLL_SET_KEY, attr.getPhaseId(), "duplicatedCheck"));
        if (actRedisDao.hExists(groupCode, duplicatedKey, String.valueOf(myUid))) {
            return new Response(E_CHLN_HAS_CHALLENGED_OTHER, "已挑战其他的擂主");
        }
        //todo 添加已选擂主判断 done

        //1.未满员的擂主
        //2.计算擂主的积分
        List<String> canSelectLz = Lists.newArrayList();
        //擂主
        Set<String> nowWinnerSet = getNowWinnerSet(attr);

        Map<String, Set<String>> groupList = getNowWinnerGroupMap(nowWinnerSet, attr);
        Map<String, Integer> lzScore = Maps.newHashMap();
        for (Map.Entry<String, Set<String>> entry : groupList.entrySet()) {
            Set<String> value = entry.getValue();
            if (value.size() < attr.getGroupLen()) {
                canSelectLz.add(entry.getKey());
                lzScore.put(entry.getKey(), 0);
            }
        }


        //计算所有擂主的分值,来判断当前选择是否合理
        String anchorOwMapKey = String.format(ANCHOR_OW_MAP, attr.getActId());
        Map<Object, Object> all = actRedisDao.hGetAll(groupCode, anchorOwMapKey);
        Map<Object, Object> lastPKMap = allPkMap(attr.getActId());

        EnrollmentInfo tzInfo = enrollmentService.tryGetFirstEnrolMemberCache(attr.getActId(), attr.getBusiId(), 200, myUid + "");
        for (String lz : canSelectLz) {
            EnrollmentInfo info = enrollmentService.tryGetFirstEnrolMemberCache(attr.getActId(), attr.getBusiId(), 200, lz);
            if (info.getSignSid() != tzInfo.getSignSid()) {
                //非同签约公会加5分
                lzScore.put(lz, lzScore.get(lz) + 5);
            }

            long lzOw = Convert.toLong(all.get(lz));
            long tzOW = Convert.toLong(all.get(String.valueOf(myUid)));
            //ow为0时不限制,
            if (tzOW == 0 || lzOw != tzOW) {
                //非OW签约公会加3分
                lzScore.put(lz, lzScore.get(lz) + 3);
            }
            Object o = lastPKMap.get(String.valueOf(myUid));
            if (o == null || !String.valueOf(o).equals(lz)) {
                lzScore.put(lz, lzScore.get(lz) + 1);
            }
        }

        int score = lzScore.getOrDefault(lzMemberId, 0);

        boolean canSelected = true;
        for (Map.Entry<String, Integer> entry : lzScore.entrySet()) {
            if (entry.getValue() > score) {
                canSelected = false;
            }
        }
        log.info("canSelected:{} score:{}, lzScore:{}", canSelected, score, lzScore);

        final int score5 = 5, score6 = 6;
        if (!canSelected) {
            if (score < score5 && score > 0) {
                return new Response(E_SAME_CHANNEL_ANCHOR, "不可挑战同公会或同OW旗下的主播");
            }
            if (score == score5 || score == score6) {
                return new Response(E_SAME_OW_ANCHOR, "不可挑战同公会或同OW旗下的主播");
            }
            if (lastPKMap != null && lzMemberId.equals(lastPKMap.get(String.valueOf(myUid)))) {
                return new Response(E_SAME_PK_ANCHOR, "不可连续两轮选择相同主播挑战");
            }
        }
        return null;
    }


    private TiGuanMemberInfo find(List<TiGuanMemberInfo> list, String memberId) {
        for (TiGuanMemberInfo memberScore : list) {
            if (memberScore.getMemberId().equals(memberId)) {
                return memberScore;
            }
        }
        return null;
    }

    /**
     * 访问量大且不需要及时信息的地方应该访问这个方法，以免给系统造成压力
     */
    //@Cached(timeToLiveMillis = 3 * 1000L)
    public TiGuanMyDetail getMyDetailByCache(long actId, long cmptUseInx, long uid) {
        return getMyDetail(actId, cmptUseInx, uid);
    }

    /**
     * 获取我的信息
     */
    public TiGuanMyDetail getMyDetail(long actId, long cmptUseInx, long uid) {
        TiGuanComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String nickName = commonService.getNickName(uid, true);

        String winner = null;
        String lzKey = makePromotResultKey(attr.getNowWinnerRankId(), attr);
        if (actRedisDao.zRevRank(groupCode, lzKey, String.valueOf(uid)) != null) {
            // 我是擂主
            winner = String.valueOf(uid);
        } else {
            String myKey = makePromotResultKey(attr.getNowAllRankId(), attr);
            if (actRedisDao.zRevRank(groupCode, myKey, String.valueOf(uid)) == null) {
                // 无打擂资格
                return null;
            }

            // 先找出 uid 挑战的擂主, 若没有说明改uid还没发出挑战
            String duplicatedCheckKey = makeEnrollKey(attr, "0")[1];
            winner = actRedisDao.hget(groupCode, duplicatedCheckKey, String.valueOf(uid));
            if (StringUtil.isBlank(winner)) {
                return new TiGuanMyDetail(uid, nickName, 0, -1, 0, false);
            }
        }

        TiGuanMyDetail info = null;
        Set<String> nowWinnerSet = Sets.newHashSet(winner);
        LinkedHashMap<String, Long> nowAllMap = getNowAllMap(attr);
        Map<String, Set<String>> groupList = getNowWinnerGroupMap(nowWinnerSet, attr);
        List<TiGuanMemberInfo> list = getNowWinnerGroupSortedList(groupList, nowAllMap).get(winner);

        TiGuanMemberInfo wms = find(list, String.valueOf(uid));
        int rank = list.indexOf(wms) + 1;
        boolean bHost = String.valueOf(uid).equals(winner);

        if (rank == 1) {
            long gap = wms.getScore() - (list.size() > 1 ? list.get(1).getScore() : 0);
            info = new TiGuanMyDetail(uid, nickName, wms.getScore(), rank, gap, bHost);
        } else {
            long gap = wms.getScore() - list.get(0).getScore();
            info = new TiGuanMyDetail(uid, nickName, wms.getScore(), rank, gap, bHost);
        }

        return info;
    }


    /**
     * 判断 /getGroupDetail 请求是否需要使用缓存
     */
    private boolean judgeUseCacheForGetGroupDetail(long myUid, TiGuanComponentAttr attr) {
        // 没有登录的，直接判断使用缓存
        //long myUid = getLoginYYUid(req, resp);
        if (myUid == 0) {
            return true;
        }
        // 登录的用户，若不在打雷成员集合中， 判定使用缓存
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String keyAll = makePromotResultKey(attr.getNowAllRankId(), attr);
        Double score = actRedisDao.zScore(groupCode, keyAll, String.valueOf(myUid));
        return score == null;
    }

    /**
     * 获取所有小组信息, 返回两层有序map（擂主排名顺序 和 各组报名顺序）
     */
    private Response getGroupDetail(long actId, long cmptUseInx) {
        Clock clock = new Clock();
        try {
            TiGuanComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);
            if (attr == null) {
                return Response.badRequest();
            }
            Set<String> nowWinnerSet = getNowWinnerSet(attr);
            LinkedHashMap<String, Long> nowAllMap = getNowAllMap(attr);
            Map<String, Set<String>> groupList = getNowWinnerGroupMap(nowWinnerSet, attr);

            List<TiGuanGroupDetail> list = getGroupDetail(attr, groupList, nowAllMap);

            fillSettleInfo(attr, nowAllMap, list);

            Date endTime = DateUtil.getDate(attr.getManualChallengeEndTime());

            // 随机打一点日志，以便观察耗时情况
            int random = Const.SYS.RD.nextInt(100);
            if (random == 0) {
                log.info("getGroupDetail ok@{}", clock.tag());
            }

            return Response.success(ImmutableMap.of("manualChallengeEndTime", endTime.getTime(), "groups", list));
        } catch (Throwable t) {
            log.error("getGroupDetail exception@actId:{}, cmptUseInx:{}, err:{} {}", actId, cmptUseInx, t.getMessage(), clock.tag(), t);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    public Map<String, List<TiGuanMemberInfo>> nowWinnerGroupSortedList(long actId, long cmptUseInx) {
        TiGuanComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "not find attr by cmptUseInx = " + cmptUseInx);
        //当前擂主
        Set<String> nowWinnerSet = getNowWinnerSet(attr);
        //所有参赛者分数
        LinkedHashMap<String, Long> nowAllMap = getNowAllMap(attr);
        //获取分组信息
        Map<String, Set<String>> nowWinnerGroupMap = getNowWinnerGroupMap(nowWinnerSet, attr);
        return getNowWinnerGroupSortedList(nowWinnerGroupMap, nowAllMap);
    }


    public Response getGroupDetailOL(long actId, long cmptUseInx, long uid) {
        TiGuanComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return Response.badRequest();
        }

        Clock clock = new Clock();
        try {
            if (judgeUseCacheForGetGroupDetail(uid, attr)) {
                return groupDetailCache.get(actId + ":" + cmptUseInx, () -> getGroupDetail(actId, cmptUseInx));
            } else {
                return getGroupDetail(actId, cmptUseInx);
            }
        } catch (ExecutionException e) {
            log.error("getGroupDetail fail@actId:{}, cmptUseInx:{}, err:{} {}", actId, cmptUseInx, e.getMessage(), clock.tag(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }
}
