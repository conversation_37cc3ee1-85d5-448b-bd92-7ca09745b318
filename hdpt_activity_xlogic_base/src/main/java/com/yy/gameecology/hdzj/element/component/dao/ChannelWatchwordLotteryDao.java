package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2062LotteryBox;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2062LotteryRecord;
import com.yy.gameecology.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Slf4j
@Repository
public class ChannelWatchwordLotteryDao {

    private static final String BOX_INSERT_IGNORE_SQL = "insert ignore into cmpt_2062_lottery_box (act_id, cmpt_use_inx, seq, member_id, sid, ssid, create_time, expired_time) values (?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String RECORD_INSERT_IGNORE_SQL = "insert ignore into cmpt_2062_lottery_record (act_id, cmpt_use_inx, box_id, uid, create_time) values (?, ?, ?, ?, ?)";

    @Autowired
    private GameecologyDao gameecologyDao;

    public void addChatBox(long actId, long cmptUseInx, String seq, String memberId, long sid, long ssid, Date expiredTime) {
        int rs = gameecologyDao.update(BOX_INSERT_IGNORE_SQL, actId, cmptUseInx, seq, memberId, sid, ssid, new Date(), expiredTime);
        log.warn("addChatBox: seq:{} rs:{}", seq, rs);
    }

    public List<Cmpt2062LotteryBox> selectValidLotteryBoxes(long actId, long cmptUseInx, long sid, long ssid, Date time) {
        Cmpt2062LotteryBox me = new Cmpt2062LotteryBox();
        me.setActId(actId);
        me.setCmptUseInx(cmptUseInx);
        me.setSid(sid);
        me.setSsid(ssid);

        return gameecologyDao.select(Cmpt2062LotteryBox.class, me, " and expired_time > '" + DateFormatUtils.format(time, DateUtil.DEFAULT_PATTERN) + "' order by create_time asc");
    }

    public Cmpt2062LotteryRecord selectLotteryRecord(long actId, long cmptUseInx, long boxId, long uid) {
        Cmpt2062LotteryRecord me = new Cmpt2062LotteryRecord();
        me.setActId(actId);
        me.setCmptUseInx(cmptUseInx);
        me.setBoxId(boxId);
        me.setUid(uid);

        return gameecologyDao.selectOne(Cmpt2062LotteryRecord.class, me, StringUtils.EMPTY);
    }

    public int addLotteryRecord(long actId, long cmptUseInx, long boxId, long uid) {
        return gameecologyDao.getJdbcTemplate().update(RECORD_INSERT_IGNORE_SQL, actId, cmptUseInx, boxId, uid, new Date());
    }

    public int updateLotteryResult(long actId, long cmptUseInx, long boxId, long uid, String result) {
        Cmpt2062LotteryRecord me = new Cmpt2062LotteryRecord();
        me.setActId(actId);
        me.setCmptUseInx(cmptUseInx);
        me.setBoxId(boxId);
        me.setUid(uid);
        Cmpt2062LotteryRecord to = new Cmpt2062LotteryRecord();
        to.setLotteryResult(result);
        return gameecologyDao.update(Cmpt2062LotteryRecord.class, me, to);
    }

    public long countLotteryRecord(long actId, long cmptUseInx, String seq) {
        Cmpt2062LotteryBox me = new Cmpt2062LotteryBox();
        me.setActId(actId);
        me.setCmptUseInx(cmptUseInx);
        me.setSeq(seq);
        var box = gameecologyDao.selectOne(Cmpt2062LotteryBox.class, me, StringUtils.EMPTY);
        if (box == null) {
            return 0;
        }

        Cmpt2062LotteryRecord record = new Cmpt2062LotteryRecord();
        record.setCmptUseInx(cmptUseInx);
        record.setActId(actId);
        record.setBoxId(box.getId());

        return gameecologyDao.count(Cmpt2062LotteryRecord.class, record);
    }

    public Pair<Long, Cmpt2062LotteryBox> getLotteryCountAndExpiredTime(long actId, long cmptUseInx, String seq) {
        Cmpt2062LotteryBox me = new Cmpt2062LotteryBox();
        me.setActId(actId);
        me.setCmptUseInx(cmptUseInx);
        me.setSeq(seq);
        var box = gameecologyDao.selectOne(Cmpt2062LotteryBox.class, me, StringUtils.EMPTY);
        if (box == null) {
            return null;
        }

        Cmpt2062LotteryRecord record = new Cmpt2062LotteryRecord();
        record.setCmptUseInx(cmptUseInx);
        record.setActId(actId);
        record.setBoxId(box.getId());

        long count = gameecologyDao.count(Cmpt2062LotteryRecord.class, record);

        return Pair.of(count, box);
    }

    public List<Cmpt2062LotteryBox> selectLotteryBoxes(long actId, Collection<Long> cmptUseInx, Date now, int size) {
        Cmpt2062LotteryBox me = new Cmpt2062LotteryBox();
        me.setActId(actId);
        String afterWhere = " and cmpt_use_inx in (" + StringUtils.join(cmptUseInx, ",") + ") and expired_time > '" + DateFormatUtils.format(now, DateUtil.DEFAULT_PATTERN) + "' order by create_time desc limit " + size;

        return gameecologyDao.select(Cmpt2062LotteryBox.class, me, afterWhere);
    }
}
