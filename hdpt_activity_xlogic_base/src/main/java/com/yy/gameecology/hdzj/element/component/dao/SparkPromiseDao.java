package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5123SparkRecord;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5123UserCard;
import com.yy.gameecology.common.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.PreparedStatementCreator;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Repository
public class SparkPromiseDao {

    private static final String INSERT_IGNORE_SQL = "insert ignore into cmpt_5123_spark_record (act_id, member_id, date_code, task_index, sid, ssid, package_id, effect, create_time) values (?, ?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String INCR_LOTTERY_COUNT_SQL = "update cmpt_5123_spark_record set lottery_count = lottery_count + 1 where id = ? and lottery_count < ?";

    private static final String INSERT_IGNORE_SQL2 = "insert ignore into cmpt_5123_user_card (act_id, record_id, uid, card_state, effect_state, create_time) values (?, ?, ?, ?, ?, ?)";

    private static final String SET_CARD_STATE_SQL = "update cmpt_5123_user_card set card_state = ? where id = ? and card_state = 0";

    private static final String SET_EFFECT_STATE_SQL = "update cmpt_5123_user_card set effect_state = ? where id = ? and effect_state = 0";

    private static final String SAVE_EFFECT_STATE_SQL = "insert into cmpt_5123_user_card (act_id, record_id, uid, effect_state, create_time) values (?, ?, ?, ?, ?) on duplicate key update effect_state = ?";

    @Autowired
    private GameecologyDao gameecologyDao;

    public int insertIgnore(Cmpt5123SparkRecord record) {
        PreparedStatementCreator creator = con -> {
            PreparedStatement statement = con.prepareStatement(INSERT_IGNORE_SQL, new String[]{"id"});
            statement.setLong(1, record.getActId());
            statement.setString(2, record.getMemberId());
            statement.setString(3, record.getDateCode());
            statement.setInt(4, record.getTaskIndex());
            statement.setLong(5, record.getSid());
            statement.setLong(6, record.getSsid());
            statement.setLong(7, record.getPackageId());
            statement.setInt(8, record.getEffect());
            statement.setTimestamp(9, Timestamp.from(record.getCreateTime().toInstant()));

            return statement;
        };

        KeyHolder keyHolder = new GeneratedKeyHolder();
        int rs = gameecologyDao.getJdbcTemplate().update(creator, keyHolder);
        if (keyHolder.getKey() != null) {
            record.setId(keyHolder.getKey().longValue());
        }

        return rs;
    }

    public Cmpt5123SparkRecord getSparkRecordByRecordId(long recordId) {
        Cmpt5123SparkRecord me = new Cmpt5123SparkRecord();
        me.setId(recordId);
        return gameecologyDao.selectByKey(Cmpt5123SparkRecord.class, me);
    }

    public int updateLotteryCount(long recordId, int lotteryLimit) {
        return gameecologyDao.getJdbcTemplate().update(INCR_LOTTERY_COUNT_SQL, recordId, lotteryLimit);
    }

    public Cmpt5123SparkRecord getLatestEffectSpark(long actId, long sid, long ssid, String dateCode, Date floorTime) {
        Cmpt5123SparkRecord me = new Cmpt5123SparkRecord();
        me.setActId(actId);
        me.setSid(sid);
        me.setSsid(ssid);
        me.setEffect(1);
        String floorTimeStr = DateFormatUtils.format(floorTime, DateUtil.DEFAULT_PATTERN);
        return gameecologyDao.selectOne(Cmpt5123SparkRecord.class, me, " and date_code <= '" + dateCode + "' and create_time > '" + floorTimeStr  + "' order by create_time desc, id desc limit 1");
    }

    public Cmpt5123SparkRecord getLatestEffectSpark(long actId, String memberId, String dateCode, Date floorTime) {
        Cmpt5123SparkRecord me = new Cmpt5123SparkRecord();
        me.setActId(actId);
        me.setMemberId(memberId);
        me.setEffect(1);
        String floorTimeStr = DateFormatUtils.format(floorTime, DateUtil.DEFAULT_PATTERN);
        return gameecologyDao.selectOne(Cmpt5123SparkRecord.class, me, " and date_code <= '" + dateCode +"' and create_time > '" + floorTimeStr  + "' order by create_time desc, id desc limit 1");
    }

    public List<Cmpt5123SparkRecord> getEffectSparkList(long actId, Date floorTime, int size) {
        Cmpt5123SparkRecord me = new Cmpt5123SparkRecord();
        me.setActId(actId);
        me.setEffect(1);
        String floorTimeStr = DateFormatUtils.format(floorTime, DateUtil.DEFAULT_PATTERN);
        return gameecologyDao.select(Cmpt5123SparkRecord.class, me, " and create_time > '" + floorTimeStr  + "' order by create_time desc, id desc limit " + size);
    }

    public Cmpt5123UserCard getUserCard(long actId, long recordId, long uid) {
        Cmpt5123UserCard me = new Cmpt5123UserCard();
        me.setActId(actId);
        me.setRecordId(recordId);
        me.setUid(uid);

        return gameecologyDao.selectOne(Cmpt5123UserCard.class, me, StringUtils.EMPTY);
    }

    public int insertIgnore(long actId, long recordId, long uid, int cardState, int effectState, Date time) {
        return gameecologyDao.getJdbcTemplate().update(INSERT_IGNORE_SQL2, actId, recordId, uid, cardState, effectState, time);
    }

    public int updateSetCardState(long cardId, int cardState) {
        return gameecologyDao.getJdbcTemplate().update(SET_CARD_STATE_SQL, cardState, cardId);
    }

    public int updateSetEffectState(long cardId, int effectState) {
        return gameecologyDao.getJdbcTemplate().update(SET_EFFECT_STATE_SQL, effectState, cardId);
    }

    public int saveEffectState(long actId, long recordId, long uid, int effectState, Date time) {
        return gameecologyDao.getJdbcTemplate().update(SAVE_EFFECT_STATE_SQL, actId, recordId, uid, effectState, time, effectState);
    }
}
