package com.yy.gameecology.hdzj.bean;

import com.yy.gameecology.activity.bean.ChannelInfoVo;

import java.util.List;

public class TiGuanGroupDetail {

    // 当前胜利者（擂主）
    private TiGuanMemberInfo leizhu;

    // 当前已发起挑战者
    private List<TiGuanMemberInfo> challengers;

    // 小组状态， 0：名额未满， 1：名额已满， 其它待定
    private long status = 0;

    // 擂主开播频道信息
    private ChannelInfoVo lzKaiBoChannel = null;

    public TiGuanGroupDetail() {
    }

    public TiGuanGroupDetail(TiGuanMemberInfo leizhu, List<TiGuanMemberInfo> challengers, long status) {
        this.leizhu = leizhu;
        this.challengers = challengers;
        this.status = status;
    }

    public long getStatus() {
        return status;
    }

    public void setStatus(long status) {
        this.status = status;
    }

    public TiGuanMemberInfo getLeizhu() {
        return leizhu;
    }

    public void setLeizhu(TiGuanMemberInfo leizhu) {
        this.leizhu = leizhu;
    }

    public List<TiGuanMemberInfo> getChallengers() {
        return challengers;
    }

    public void setChallengers(List<TiGuanMemberInfo> challengers) {
        this.challengers = challengers;
    }

    public ChannelInfoVo getLzKaiBoChannel() {
        return lzKaiBoChannel;
    }

    public void setLzKaiBoChannel(ChannelInfoVo lzKaiBoChannel) {
        this.lzKaiBoChannel = lzKaiBoChannel;
    }
}
