package com.yy.gameecology.hdzj.element.redis;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.JiaoyouComboEndEvent;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.AwardRecordVo;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardLotteryMsg;
import com.yy.gameecology.activity.bean.mq.GamebabyComboEndEvent;
import com.yy.gameecology.activity.exception.LotteryException;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.GiftToChance;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.ManualLotteryComponentAttr;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 手动抽奖，送礼等得到抽奖机会，消耗不同次数抽不同奖池
 *
 * <AUTHOR>
 * @date 2022/03/30 17:42
 */
@UseRedisStore
@Component
@RestController
@RequestMapping("/cmpt/ManualLottery")
public class ManualLotteryComponent extends BaseActComponent<ManualLotteryComponentAttr> {

    private static final String LOTTERY_CHANCE = "lottery_chance";

    @Override
    public Long getComponentId() {
        return ComponentId.MANUAL_LOTTERY;
    }

    /**
     * 响应奖励发放，针对指定的 gift_code, 做抽奖机会的发放
     */
    @HdzjEventHandler(value = HdztAwardLotteryMsg.class, canRetry = true)
    public void issueLotteryChance(HdztAwardLotteryMsg event, ManualLotteryComponentAttr attr) {
        long busiId = event.getBusiId();
        if (attr.getBusiId() != busiId) {
            return;
        }

        // 提取属于抽奖机会发放的记录
        List<HdztAwardLotteryMsg.Award> awardList = event.getData().stream()
                .filter(a -> a.getGiftCode().equals(attr.getAwardChanceCode()))
                .collect(Collectors.toList());
        if (awardList.isEmpty()) {
            return;
        }

        //seq控制
        String key = makeKey(attr, "seq:" + event.getSeq());
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        final boolean rs = actRedisDao.setNX(groupCode, key, DateUtil.today(), 1200);
        if (!rs) {
            log.warn("issueLotteryChance ignore@redo, key:{}, event:{}, attr:{}", key, event, attr);
            return;
        }

        // 准备好每个人的奖励数
        Map<String, Long> playerChances = Maps.newHashMap();
        for (HdztAwardLotteryMsg.Award award : awardList) {
            String player = String.valueOf(award.getUid());
            long playerChance = playerChances.getOrDefault(player, 0L);
            playerChances.put(player, playerChance + award.getPackageNum() * award.getGiftNum());
        }

        // 逐一发放
        String chanceKey = makeKey(attr, LOTTERY_CHANCE);
        for (String player : playerChances.keySet()) {
            long chance = playerChances.get(player);
            String seq = event.getSeq() + player;
            //long newChance = actRedisDao.hIncrByKeyWithSeq(groupCode, seq, chanceKey, player, chance, 0);
            long newChance;
            //TODO 如果次组件要重新启用，则需要改造此处
            if (true) {
                throw new RuntimeException("hIncrByKeyWithSeq,lua forbid");
            }
            log.info("issueLotteryChance one ok@chanceKey:{} add chance:{} to {}, seq:{}, player:{}", chanceKey, chance, newChance, event.getSeq(), player);
        }
    }

    /**
     * 响应礼物流水事件，和连送互斥
     */
    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = true)
    public void onSendGiftEvent(SendGiftEvent event, ManualLotteryComponentAttr attr) {
        if (attr.isCombo()) {
            return;
        }

        Clock clock = new Clock();
        long actId = attr.getActId();
        long cmptUseInx = attr.getCmptUseInx();

        Map<String, GiftToChance> giftToChanceMap = attr.getGiftToChanceMap();
        String giftId = event.getGiftId();
        if (!giftToChanceMap.containsKey(giftId)) {
            return;
        }

        int template = event.getTemplate().getValue();
        long giftNum = event.getGiftNum();
        long chance = giveChance(template, event.getSeq(), event.getSendUid(), event.getRecvUid(), giftId, giftNum, event.getSid(), event.getSsid(), attr);
        log.info("onSendGiftEvent done@actId:{}, cmptUseInx:{}, chance:{}, event:{} {}", actId, cmptUseInx, chance, event, clock.tag());

    }

    /**
     * 宝贝连送结束事件处理，和送礼流水互斥
     */
    @HdzjEventHandler(value = GamebabyComboEndEvent.class, canRetry = true)
    public void gamebabyComboEndEvent(GamebabyComboEndEvent event, ManualLotteryComponentAttr attr) {
        if (!attr.isCombo()) {
            return;
        }

        Clock clock = new Clock();
        long actId = attr.getActId();
        long cmptUseInx = attr.getCmptUseInx();

        Map<String, GiftToChance> giftToChanceMap = attr.getGiftToChanceMap();
        String giftId = event.getGiftId();
        if (!giftToChanceMap.containsKey(giftId)) {
            return;
        }

        int template = Template.Gamebaby.getValue();
        long giftNum = event.getGiftAmount() * event.getComboAmount();
        long chance = giveChance(template, event.getSeq(), event.getUid(), event.getBabyUid(), giftId, giftNum, event.getSid(), event.getSsid(), attr);
        log.info("gamebabyComboEndEvent done@actId:{}, cmptUseInx:{}, chance:{}, event:{} {}", actId, cmptUseInx, chance, event, clock.tag());

    }

    /**
     * 交友的用连送结束事件，和送礼流水互斥
     */
    @HdzjEventHandler(value = JiaoyouComboEndEvent.class, canRetry = true)
    public void jiaoyouComboEndEvent(JiaoyouComboEndEvent event, ManualLotteryComponentAttr attr) {
        if (!attr.isCombo()) {
            return;
        }

        Clock clock = new Clock();
        long actId = attr.getActId();
        long cmptUseInx = attr.getCmptUseInx();

        Map<String, GiftToChance> giftToChanceMap = attr.getGiftToChanceMap();
        String giftId = MapUtils.getString(event.getExpand(), "propId");
        if (!giftToChanceMap.containsKey(giftId)) {
            return;
        }

        int template = Template.Jiaoyou.getValue();
        long giftNum = MapUtils.getLongValue(event.getExpand(), "count");
        long chance = giveChance(template, event.getSeqId(), event.getSendUid(), event.getRecvUid(), giftId, giftNum, event.getSid(), event.getSsid(), attr);
        log.info("jiaoyouComboEndEvent done@actId:{}, cmptUseInx:{}, event:{}, chance:{} {}", actId, cmptUseInx, event, chance, clock.tag());

    }

    /**
     * 我的抽奖机会
     */
    @RequestMapping("/getMyChance")
    public Response getMyChance(HttpServletRequest req, HttpServletResponse resp, long actId, @RequestParam(required = false, defaultValue = "1") long cmptUseInx) {
        Clock clock = new Clock();
        long uid = -1;
        try {
            uid = getLoginYYUid(req, resp);
            if (uid == 0) {
                return Response.fail(-1, "未登录");
            }

            ManualLotteryComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);
            String chanceKey = makeKey(attr, LOTTERY_CHANCE);
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            String currChance = actRedisDao.hget(groupCode, chanceKey, String.valueOf(uid));
            long count = Convert.toLong(currChance, 0);
            return Response.success(ImmutableMap.of("name", attr.getChanceName(), "count", count));
        } catch (Exception e) {
            log.error("getMyChance exception@actId:{}, cmptUseInx:{}, uid:{}, err:{} {}", actId, cmptUseInx, uid, e.getMessage(), clock.tag(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    /**
     * 同步组件的属性定义,用于管理后台
     **/
    @RequestMapping("/draw")
    public Response draw(HttpServletRequest req, HttpServletResponse resp, long actId, @RequestParam(required = false, defaultValue = "1") long cmptUseInx, int boxId, @RequestParam(required = false, defaultValue = "1") int count) {
        Clock clock = new Clock();
        long uid = -1;
        try {
            uid = getLoginYYUid(req, resp);
            if (uid == 0) {
                return Response.fail(-1, "未登录");
            }

            ManualLotteryComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);
            if (attr == null) {
                return Response.fail(-2, "参数异常");
            }
            Integer drawCost = attr.getDrawCostMap().get(boxId);
            if (drawCost == null) {
                return Response.fail(-2, "非法奖箱：" + boxId);
            }

            ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityCacheNoTime(actId);
            if (activityInfoVo == null) {
                log.error("draw error@not find activityInfoVo,actId:{}", actId);
                return Response.fail(-2, "活动未开始");
            }


            Date time = commonService.getNow(actId);
            if (activityInfoVo.getBeginTime() > time.getTime()) {
                return Response.fail(4, "活动未开始");
            }
            long endTime = Optional.ofNullable(attr.getEndDate()).map(Date::getTime).orElse(activityInfoVo.getEndTime());
            if (endTime < time.getTime()) {
                return Response.fail(4, "活动已结束");
            }


            String memberId = String.valueOf(uid);
            String chanceKey = makeKey(attr, LOTTERY_CHANCE);
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());

            // 当count<=0, 说明是全抽，先取出剩余抽奖次数，再除以选定奖箱的单抽消耗抽奖机会数， 就是得到选定宝箱的 可抽次数
            int drawAmount = count;
            String chanceName = attr.getChanceName();
            if (count <= 0) {
                int currChanceCount = Convert.toInt(actRedisDao.hget(groupCode, chanceKey, memberId), 0);
                drawAmount = currChanceCount / drawCost;
                if (drawAmount <= 0) {
                    return Response.fail(-3, chanceName + "不足！");
                }
            }

            // 计算本次hash扣减步长，注意这里强制检查必须小于0，无条件防止意外发生
            int step = -1 * drawAmount * drawCost;
            if (step >= 0) {
                throw new SuperException(chanceName + "扣减值错误:" + step, SuperException.E_WRONG_PARAM);
            }

            // 可控制并发抽问题，若失败，不会有任何数据变化
            List<Long> result;
            if (true) {
                throw new RuntimeException("hash_incr_with_limit.lua,lua forbid");
            }
//            List<Long> result = actRedisDao.hIncrWithLimit(groupCode, chanceKey, memberId, step, 0);
            if (result.get(0) < 0) {
                return Response.fail(-4, chanceName + "不足！");
            }

            String now = DateUtil.today();
            String realIp = RequestUtil.getRealIp(req);
            String seq = java.util.UUID.randomUUID().toString();
            BatchLotteryResult response = hdztAwardServiceClient.doBatchLottery(seq, attr.getBusiId(), uid, boxId, drawAmount, now, null, realIp, attr.getRetry());

            log.warn("draw done@actId:{}, cmptUseInx:{}, uid:{}, boxId:{}, count:{}, drawAmount:{}, step:{}, result:{}, response:{} {}",
                    actId, cmptUseInx, uid, boxId, count, drawAmount, step, StringUtil.toString(result), response, clock.tag());

            final int code6000 = 6000;
            if (response != null && response.code == code6000) {
                return Response.fail(-5, "抽奖成功,请稍后查看抽奖记录！");
            }
            if (response == null || response.code != 0 || CollectionUtils.isEmpty(response.recordPackages)) {
                return Response.fail(LotteryException.E_NOT_HIT, "抱歉未抽中奖品！");

            }

            // 以 packageId 为 key，统计出现的次数
            Map<Long, Integer> packageIdNums = Maps.newHashMap();
            for (Long packageId : response.recordPackages.values()) {
                int pkgNum = packageIdNums.getOrDefault(packageId, 0);
                packageIdNums.put(packageId, pkgNum + 1);
            }
            List<String> list = hdztAwardService.toAwardNameList(boxId, packageIdNums);

            return Response.success(list);
        } catch (Exception e) {
            log.error("draw exception@actId:{}, cmptUseInx:{}, uid:{}, err:{} {}",
                    actId, cmptUseInx, uid, e.getMessage(), clock.tag(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    /**
     * 我的抽奖记录
     */
    @RequestMapping("/getMyAwards")
    public Response<List<AwardRecordVo>> getMyAwards(HttpServletRequest req, HttpServletResponse resp, long actId, @RequestParam(required = false, defaultValue = "1") long cmptUseInx) {
        Clock clock = new Clock();
        long uid = -1;
        try {
            uid = getLoginYYUid(req, resp);
            if (uid == 0) {
                return Response.fail(-1, "未登录");
            }

            ManualLotteryComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);
            List<Integer> taskIds = Lists.newArrayList(attr.getDrawCostMap().keySet());
            List<AwardRecordVo> awardRecords = hdztAwardService.getMyAwardRecords(uid, attr.getBusiId(), taskIds, null, false, false);

            /**
             * 聚合奖品
             */
            HashMap<String, Long> awardMap = awardRecords.stream()
                    .collect(Collectors.groupingBy(AwardRecordVo::getPrize, LinkedHashMap::new, Collectors.summingLong(AwardRecordVo::getAmount)));

            List<AwardRecordVo> awards = awardMap.entrySet().stream()
                    .map(item -> new AwardRecordVo("", item.getKey(), item.getValue()))
                    .collect(Collectors.toList());


            return Response.success(awards);
        } catch (Exception e) {
            log.error("getMyAwards exception@actId:{}, cmptUseInx:{}, uid:{}, err:{} {}",
                    actId, cmptUseInx, uid, e.getMessage(), clock.tag(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    /**
     * 发抽奖机会
     */
    private long giveChance(int template, String seq, long player, long anchor, String giftId, long giftNum, long sid, long ssid, ManualLotteryComponentAttr attr) {
        // 判断业务模板匹配
        if (attr.getTemplate() != template) {
            log.info("giveChance ignore1@not match, seq:{}, anchor:{}, player:{}, etpl:{}, atpl:{}", seq, anchor, player, template, attr.getTemplate());
            return 0;
        }

        //礼物控制
        GiftToChance giftToChance = attr.getGiftToChanceMap().get(giftId);
        if (giftToChance == null) {
            log.info("giveChance ignore2@attr no giftId:{}, seq:{}, anchor:{}, player:{}", giftId, seq, anchor, player);
            return 0;
        }

        //seq控制
        String key = makeKey(attr, "seq:" + seq);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        final boolean rs = actRedisDao.setNX(groupCode, key, DateUtil.today(), 1200);
        if (!rs) {
            log.warn("giveChance ignore3@redo, key:{}, anchor:{}, player:{}", key, anchor, player);
            return 0;
        }

        long chance = (giftNum / giftToChance.getCost()) * giftToChance.getChance();
        if (chance > 0) {
            String chanceKey = makeKey(attr, LOTTERY_CHANCE);
//            long newChance = actRedisDao.hIncrByKeyWithSeq(groupCode, seq, chanceKey, String.valueOf(player), chance, 0);
//            log.info("giveChance ok@key:{} add chance:{} to {}, seq:{}, anchor:{},  player:{}", chanceKey, chance, newChance, seq, anchor, player);
            //TODO 如果次组件要重新启用，则需要改造此处
            if (true) {
                throw new RuntimeException("hIncrByKeyWithSeq,lua forbid");
            }
        }
        return chance;
    }

}
