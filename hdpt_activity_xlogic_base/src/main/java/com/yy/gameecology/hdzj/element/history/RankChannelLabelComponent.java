package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableSortedMap;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.rank.RankValueItemBase;
import com.yy.gameecology.activity.processor.ranking.ComponentRankingExtHandle;
import com.yy.gameecology.activity.service.LoginService;
import com.yy.gameecology.common.bean.Page;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.element.component.attr.RankChannelLabelComponentAttr;
import com.yy.java.webdb.WebdbChannelInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

import static com.yy.gameecology.hdzj.consts.ComponentId.CHANNEL_COUNT_LABEL;

/**
 * 具体业务需求
 * 1. 活动期间,统计每个神豪在频道内的付费消费
 * 2. 在神豪榜单展示标签,对应规则如下：
 * A. 用户在1-3个顶级频道分别消费活动礼物满288800荣耀值是【专一】标签
 * B. 在4-8个顶级频道分别消费活动礼物满288800荣耀值才会变成【浪漫】标签
 * C. 在9个以上顶级频道分别消费活动礼物满288800荣耀值才会变成【潇洒】标签
 * <p>
 * 在中台的榜单属性添加发送 榜单改变事件 send_ranking_score_changed_event
 *
 * 新做的活动用RankChannelLabelComponent2（mysql版本）
 *
 * <AUTHOR>
 * @since 2021/6/28
 *
 */
@UseRedisStore
@Slf4j
@RestController
@RequestMapping("/hdzk/channel/label/")
@Deprecated
public class RankChannelLabelComponent extends
        BaseActComponent<RankChannelLabelComponentAttr> implements
        ComponentRankingExtHandle<RankChannelLabelComponentAttr> {

    @Autowired
    private LoginService loginService;

    @Value("${component.rank.channelCountLabel.redisKey:ccl}")
    private String channelCountLabelKey;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Override
    public Long getComponentId() {
        return CHANNEL_COUNT_LABEL;
    }

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void onRankingScoreChanged(RankingScoreChanged event, RankChannelLabelComponentAttr attr) {

        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }
        if (event.getItemScore() <= 0) {
            return;
        }
        log.info("RankChannelLabelComponent RankingScoreChangedEvent:{}",
                JSON.toJSONString(event));
        Pair<String, String> userIdAndChannelId = prepare(event, attr);
        if (StringUtils.isEmpty(userIdAndChannelId.getLeft()) || StringUtils
                .isEmpty(userIdAndChannelId.getRight())) {
            return;
        }
        action(attr, userIdAndChannelId.getLeft(), userIdAndChannelId.getRight(), event.getItemScore(), event.getSeq());
    }

    private void action(RankChannelLabelComponentAttr attr, String userId, String channelId, long itemScore, String seq) {
        String key = getKey(attr, userId);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());


//        long channelScore = actRedisDao.zIncrWithSeq(groupCode, seq, key, channelId, itemScore);
//        if (log.isInfoEnabled()) {
//            log.info("onRankingScoreChanged done,actId:{},rankId:{},userId:{},channelId:{}, channelScore:{}",
//                    attr.getActId(), attr.getRankId(), userId, channelId, channelScore);
        //TODO 如果次组件要重新启用，则需要改造此处
        if (true) {
            throw new RuntimeException("zIncr_with_seq.lua,lua forbid");
        }


    }

    @VisibleForTesting
    String getKey(RankChannelLabelComponentAttr attr, String userId) {
        return makeKey(attr, channelCountLabelKey + "_" + userId);
    }

    private Pair<String, String> prepare(RankingScoreChanged event,
                                         RankChannelLabelComponentAttr attr) {
        String channel = getChannel(event, attr.getChannelRoleIds()).orElse(StringUtils.EMPTY);
        String userId = StringUtils.trimToEmpty(event.getMember());
        return Pair.of(userId, channel);
    }

    private Optional<String> getChannel(RankingScoreChanged event, Set<Long> channelRoleIds) {
        if (CollectionUtils.isEmpty(channelRoleIds)) {
            return Optional.empty();
        }
        for (Long roleId : channelRoleIds) {
            if (event.getActors().containsKey(roleId)) {
                return Optional.ofNullable(event.getActors().get(roleId));
            }
        }
        return Optional.empty();
    }

    public Long getChannels(String groupCode, RankChannelLabelComponentAttr attr, String id) {
        String key = getKey(attr, id);
        if (!attr.isThreshold4EachCh()) {
            Long size;
            try {
                size = actRedisDao.getRedisTemplate(groupCode).opsForZSet().size(key);
            } catch (Exception e) {
                log.warn("get sorted set size fail with key:" + key, e);
                size = actRedisDao.getRedisTemplate(groupCode).opsForHash().size(key);
            }

            return size;
        }

        return actRedisDao.getRedisTemplate(groupCode).opsForZSet().count(key, attr.getLabelViewThreshold(), Long.MAX_VALUE);
    }


    @RequestMapping("/record/{actId}")
    public Page<LabelRecord> labelRecord(HttpServletRequest request, HttpServletResponse response,
                                         @PathVariable(name = "actId") long actId,
                                         @RequestParam(name = "cmptUseIndex") long cmptUseIndex,
                                         @RequestParam(name = "page", defaultValue = "1") int page,
                                         @RequestParam(name = "pageSize", defaultValue = "10") int pageSize) {
        long uid = getLoginYYUid(request, response);
        RankChannelLabelComponentAttr attr = getComponentAttr(actId, cmptUseIndex);
        String groupCode = redisConfigManager.getGroupCode(actId);
        String key = getKey(attr, String.valueOf(uid));
        long total = actRedisDao.getRedisTemplate(groupCode).opsForZSet().size(key);
        long start = Math.max(page - 1, 0) * Math.max(pageSize, 1);
        long end = start + pageSize - 1;

        Page<LabelRecord> result = new Page<>();
        if (total > start) {
            Set<ZSetOperations.TypedTuple<String>> tuples = actRedisDao.getRedisTemplate(groupCode).opsForZSet().reverseRangeWithScores(key, start, end);
            List<Long> sids = tuples.stream().map(ZSetOperations.TypedTuple::getValue).filter(StringUtils::isNumeric).map(Long::parseLong).collect(Collectors.toList());
            Map<Long, WebdbChannelInfo> channelInfoMap = webdbThriftClient.batchGetChannelInfo(sids);
            List<LabelRecord> records = new ArrayList<>(tuples.size());
            for (ZSetOperations.TypedTuple<String> tuple : tuples) {
                long sid = Long.parseLong(tuple.getValue());
                long score = tuple.getScore().longValue();
                LabelRecord record = new LabelRecord();
                record.setSid(sid);
                record.setScore(score);
                WebdbChannelInfo channelInfo = channelInfoMap.get(sid);
                if (channelInfo != null) {
                    String asidStr = channelInfo.getAsid();
                    String name = channelInfo.getName();
                    long asid = Optional.ofNullable(asidStr).filter(StringUtils::isNumeric).map(Long::parseLong).orElse(sid);
                    record.setAsid(asid);
                    record.setName(name);
                }

                records.add(record);
            }

            result.setList(records);
        }

        result.setPage(page);
        result.setPageSize(pageSize);
        result.setTotal(total);
        return result;
    }

    @Override
    public List<Object> handleExt(RankChannelLabelComponentAttr attr, GetRankReq rankReq,
                                  RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        if (attr.getRankId() != rankReq.getRankId() || attr.getPhaseId() != rankReq.getPhaseId()) {
            return objectList;
        }
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        objectList.stream().map(rank -> ((RankValueItemBase) rank))
                .forEach(rank -> {
                            if (rank.getValue() == null || rank.getValue() < attr.getLabelViewThreshold()) {
                                return;
                            }

                            int channelCount = getChannels(groupCode, attr, rank.getKey()).intValue();
                            if (channelCount <= 0) {
                                return;
                            }

                            Map.Entry<Integer, Integer> entry = ImmutableSortedMap.copyOf(attr.getLabelMap())
                                    .floorEntry(channelCount);
                            rank.getViewExt()
                                    .put(attr.getLabelConfig(), entry != null ? String.valueOf(entry.getValue()) : "0");
                        }
                );
        return objectList;
    }

    @Data
    public static class LabelRecord {
        protected long sid;

        protected long asid;

        protected String name;

        protected long score;
    }

    public long getLoginYYUid(HttpServletRequest req, HttpServletResponse resp) {
        if (req == null || resp == null) {
            return 0;
        }
        return loginService.getLoginYYUid(req, resp);
    }
}
