package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.activity.bean.hdzt.LotteryWelfareTaskIdPair;
import com.yy.gameecology.activity.bean.hdzt.WelfareValuePair;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvagConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;


@Data
public class AllUserTaskComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    private Integer busiId;

    @ComponentAttrField(labelText = "过任务的分值")
    private long taskThreshold;

    @ComponentAttrField(labelText = "过任务的榜单")
    private long taskRankId;

    @ComponentAttrField(labelText = "过任务的阶段")
    private long taskPhaseId;

    @ComponentAttrField(labelText = "用户角色Id")
    private long taskUserRoleId;

    @ComponentAttrField(labelText = "厅角色Id")
    private long tingRoleId;

    @ComponentAttrField(labelText = "主播角色Id",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private List<Long> taskAnchorRoleIds = Collections.emptyList();

    @ComponentAttrField(labelText = "抽奖任务Id")
    private long lotteryTaskId;

    @ComponentAttrField(labelText = "发奖任务Id")
    private long welfareTaskId;

    @ComponentAttrField(labelText = "特定日期奖池配置配置", subFields = {
            @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "yyyyMMdd", remark = "日期，日期没命中默认用抽奖任务Id"),
            @SubField(fieldName = Constant.VALUE, type = LotteryWelfareTaskIdPair.class, labelText = "奖池配置"),
    })
    private Map<String, LotteryWelfareTaskIdPair> lotteryTaskIdDateMap;

    @ComponentAttrField(labelText = "抽奖--->发奖映射",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "抽奖packageId"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "业务Id"),
                    @SubField(fieldName = Constant.VALUE, type = WelfareValuePair.class, labelText = "发奖相关配置", remark = "发奖Id大于0时有效")
            })
    private Map<Long, Map<Long, WelfareValuePair>> lotteryPackageIdMap = Collections.emptyMap();

    @ComponentAttrField(labelText = "packageId礼物时间", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "packageId"),
            @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "奖品时间"),
    })
    private Map<Long, String> unitMap;

    @ComponentAttrField(labelText = "packageId礼物时间", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "packageId"),
            @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "奖品个数"),
    })
    private Map<Long, Long> countMap;

    @ComponentAttrField(labelText = "[抽奖]必中大奖配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "日期 yyyyMMdd"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "抽奖序号"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "抽奖必中packageId", remark = "大于0时有效")
            })
    private Map<String, Map<Long, Long>> assignHit = Collections.emptyMap();

    @ComponentAttrField(labelText = "[抽奖]大奖packageId",
            subFields = {
                    @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
            })
    private Set<Long> bigAwardPackageId = Collections.emptySet();

    @ComponentAttrField(labelText = "完成任务 更新挂件的模板Id",remark = "2-宝贝 3-交友 5-语音房")
    private int refreshLayerTemplate;

    @ComponentAttrField(labelText = "是否推送APP横幅广播")
    private boolean broAppBanner;

    @ComponentAttrField(labelText = "推送APP横幅文案")
    private String broTemplate;

    @ComponentAttrField(labelText = "推送APP横幅文案Key")
    private String broSvgaTextKey;

    @ComponentAttrField(labelText = "推送APP横幅svga")
    private String svga;

    @ComponentAttrField(labelText = "广播范围",remark = " 1-子频道广播 2-顶级频道下所有子厅广播 3 -全服广播")
    private int bcType;

    @ComponentAttrField(labelText = "上报数据的giftId，如果为空代表不需要上报数据给榜单")
    private String zkGiftId;

    @ComponentAttrField(labelText = "APP横幅推送业务", remark = "需要推送的业务类型 1 -语音房 2 -交友房 4 -其他 8 -宝贝 位域表示 支持组合：交友and宝贝：2+8")
    private int appBannerBusiId;

    @ComponentAttrField(labelText = "群id")
    private long groupId;

    @ComponentAttrField(labelText = "群token")
    private String robotToken;
}
