package com.yy.gameecology.hdzj.bean.aov;

import com.yy.gameecology.common.db.model.gameecology.aov.AovMatchAwardRecord;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class AovAwardRecord {

    protected long awardId;

    protected long phaseId;

    protected int awardType;

    protected long uid;

    protected long amount;

    protected String awardDesc;

    protected int awardState;

    protected Date awardTime;

    public AovAwardRecord() {
    }

    public AovAwardRecord(AovMatchAwardRecord record) {
        this.awardId = record.getId();
        this.phaseId = record.getPhaseId();
        this.awardType = record.getAwardType();
        this.uid = record.getUid();
        this.amount = record.getAmount();
        this.awardDesc = record.getAwardDesc();
        this.awardState = record.getAwardState();
        this.awardTime = record.getAwardTime();
    }

    public long getId() {
        return this.awardId;
    }
}
