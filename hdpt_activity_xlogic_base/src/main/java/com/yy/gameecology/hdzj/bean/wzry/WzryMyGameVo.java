package com.yy.gameecology.hdzj.bean.wzry;

import lombok.Data;

/**
 * desc:我的报名赛事
 *
 * <AUTHOR>
 * @date 2024-01-12 11:26
 **/
@Data
public class WzryMyGameVo {
    /**
     * 赛事编码
     */
    private String gameCode;


    /**
     * 赛事类型 0=5V5 1=1V1 3=3V3
     */
    private int battleMode;

    /**
     * 开始比赛时间
     */
    private String startTime;

    /**
     * 5v5跳转开黑房间的频道号
     */
    private Long sid;

    /**
     * 5v5跳转开黑房间的频道号
     */
    private Long ssid;

    private String gameIcon;

    private String childid;

    /**
     * 0--赛事未开启 1--赛事已开启 900--赛事已结束
     */
    private int state;
}
