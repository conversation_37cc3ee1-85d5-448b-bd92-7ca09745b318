package com.yy.gameecology.hdzj.element.history.attr;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.hdzj.annotation.SkipCheck;
import com.yy.gameecology.hdzj.bean.BroadcastConfig;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.attrconfig.TimeKeySource;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021.10.15 10:52
 * 主播个人突破任务+全站高光任务
 */
@SkipCheck
public class SunshineTaskDailyAwardComponentAttr extends ComponentAttr {

    private Long rlGroupId = 5611212L;
    private String rlBaiduWebhook = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dd54387406ec799763cd62b4c40219dd8";
    private List<String> rlUserIds = Lists.newArrayList("lintianliang", "guoliping", "zengwenzhi", "chenxiazhuan", "yulianzhu", "zengyuan2", "wangdonghong2");
    @ComponentAttrField(labelText = "榜单id")
    private long rankId;
    @ComponentAttrField(labelText = "阶段id")
    private long phaseId;
    @ComponentAttrField(labelText = "时间分榜", dropDownSourceBeanClass = TimeKeySource.class)
    private long timeKey;

    /**
     * 发奖保留多少位。 10 代表舍弃个位，100代表舍弃百位和个位
     */
    @ComponentAttrField(labelText = "发奖保留多少位", remark = "10 代表舍弃个位，100代表舍弃百位和个位")
    private int roundingDown = 10;
    @ComponentAttrField(labelText = "奖励比例",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "任务类型"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "比例")
            })
    private Map<String, String> awardRateMap = ImmutableMap.of("single", "0.6", "all_single", "0.6", "all_tiantuan", "0.6");

    @ComponentAttrField(labelText = "默认任务分数",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "任务类型"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "任务配置json")
            })
    private Map<String, String> defaultTaskScore = ImmutableMap.of("single", "", "all_single", "", "all_tiantuan", "");

    @ComponentAttrField(labelText = "广播等级配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "任务类型"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "任务等级"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = BroadcastConfig.class, labelText = "广播类型")
            })
    private Map<String, Map<Long, List<BroadcastConfig>>> levelBro;


    /**
     * 过任务横幅默认url,如果levelBro没有配置则使用这个默认的
     */
    @ComponentAttrField(labelText = "默认横幅", remark = "过任务横幅默认url,如果levelBro没有配置则使用这个默认的")
    private String defaultBannerUrl;

    /**
     * dateStr 格式: yyyyMMdd
     * 开始时间 <taskType,List<dateStr>>
     */
    @ComponentAttrField(labelText = "开奖时间配置", remark = "开奖日期格式：yyyyMMDD ,多个用逗号分隔"
            , subFields = {
            @SubField(fieldName = Constant.KEY1, labelText = "任务类型", type = String.class),
            @SubField(fieldName = Constant.VALUE, type = List.class, skip = true),
            @SubField(fieldName = Constant.MAP_LIST_VALUE, type = String.class, labelText = "开奖日期")}
    )
    private Map<String, List<String>> openTimeMap;

    /**
     * 参与天团全站任务的角色id
     */
    @ComponentAttrField(labelText = "参与天团全站任务的角色id", subFields = {
            @SubField(labelText = "参与天团全站任务的角色id", type = Long.class, fieldName = Constant.LIST_VALUE_TYPE)
    })
    private Set<Long> allTaskTTRoleId = Sets.newHashSet();

    /**
     * 挂件完成任务提示文案
     */
    @ComponentAttrField(labelText = "挂件完成任务提示文案")
    private String awardDescTips = "今日荣耀值达%s可完成%s任务";

    /**
     * 角色日限
     */
    @ComponentAttrField(labelText = "角色奖励日限",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "角色Id"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "奖励日限额", remark = "小于等于0代表不限制")
            })
    private Map<String, Long> roleAwardDayLimit;

    /**
     * 角色日限
     */
    @ComponentAttrField(labelText = "角色默认日限")
    private long defaultRoleAwardDayLimit;

    /**
     * 结算方法
     */
    @ComponentAttrField(labelText = "结算方法")
    private String settleVersion = "V2";


    /**
     * 奖励的奖品
     * 第一层key busiId / 第二层key taskId value packageId
     */
    @ComponentAttrField(labelText = "奖励的奖品",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "业务id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "奖池id"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "奖包id")
            })
    private Map<Long, Map<Long, Long>> busiTaskIdPackageId = Maps.newHashMap();


    public long getTimeKey() {
        return timeKey;
    }

    public void setTimeKey(long timeKey) {
        this.timeKey = timeKey;
    }

    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(long phaseId) {
        this.phaseId = phaseId;
    }

    public Map<String, String> getDefaultTaskScore() {
        return defaultTaskScore;
    }

    public void setDefaultTaskScore(Map<String, String> defaultTaskScore) {
        this.defaultTaskScore = defaultTaskScore;
    }

    public Map<String, String> getAwardRateMap() {
        return awardRateMap;
    }

    public void setAwardRateMap(Map<String, String> awardRateMap) {
        this.awardRateMap = awardRateMap;
    }

    public Map<String, List<String>> getOpenTimeMap() {
        return openTimeMap;
    }

    public void setOpenTimeMap(Map<String, List<String>> openTimeMap) {
        this.openTimeMap = openTimeMap;
    }

    public String getDefaultBannerUrl() {
        return defaultBannerUrl;
    }

    public void setDefaultBannerUrl(String defaultBannerUrl) {
        this.defaultBannerUrl = defaultBannerUrl;
    }

    public Map<Long, Map<Long, Long>> getBusiTaskIdPackageId() {
        return busiTaskIdPackageId;
    }

    public void setBusiTaskIdPackageId(Map<Long, Map<Long, Long>> busiTaskIdPackageId) {
        this.busiTaskIdPackageId = busiTaskIdPackageId;
    }

    public Map<String, Map<Long, List<BroadcastConfig>>> getLevelBro() {
        return levelBro;
    }

    public void setLevelBro(Map<String, Map<Long, List<BroadcastConfig>>> levelBro) {
        this.levelBro = levelBro;
    }

    public Set<Long> getAllTaskTTRoleId() {
        return allTaskTTRoleId;
    }

    public void setAllTaskTTRoleId(Set<Long> allTaskTTRoleId) {
        this.allTaskTTRoleId = allTaskTTRoleId;
    }

    public int getRoundingDown() {
        return roundingDown;
    }

    public void setRoundingDown(int roundingDown) {
        this.roundingDown = roundingDown;
    }

    public String getAwardDescTips() {
        return awardDescTips;
    }

    public void setAwardDescTips(String awardDescTips) {
        this.awardDescTips = awardDescTips;
    }

    public Map<String, Long> getRoleAwardDayLimit() {
        return roleAwardDayLimit;
    }

    public void setRoleAwardDayLimit(Map<String, Long> roleAwardDayLimit) {
        this.roleAwardDayLimit = roleAwardDayLimit;
    }

    public long getDefaultRoleAwardDayLimit() {
        return defaultRoleAwardDayLimit;
    }

    public void setDefaultRoleAwardDayLimit(long defaultRoleAwardDayLimit) {
        this.defaultRoleAwardDayLimit = defaultRoleAwardDayLimit;
    }

    public String getRlBaiduWebhook() {
        return rlBaiduWebhook;
    }

    public void setRlBaiduWebhook(String rlBaiduWebhook) {
        this.rlBaiduWebhook = rlBaiduWebhook;
    }

    public List<String> getRlUserIds() {
        return rlUserIds;
    }

    public void setRlUserIds(List<String> rlUserIds) {
        this.rlUserIds = rlUserIds;
    }

    public Long getRlGroupId() {
        return rlGroupId;
    }

    public void setRlGroupId(Long rlGroupId) {
        this.rlGroupId = rlGroupId;
    }

    public String getSettleVersion() {
        return settleVersion;
    }

    public void setSettleVersion(String settleVersion) {
        this.settleVersion = settleVersion;
    }
}
