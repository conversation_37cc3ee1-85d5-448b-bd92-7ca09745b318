<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.pepc.PepcTeamMemberMapper">
    <!-- Result Map -->
    <resultMap id="PepcTeamMemberResult" type="com.yy.gameecology.common.db.model.gameecology.pepc.PepcTeamMember">
        <id property="id" column="id" jdbcType="BIGINT" />
        <result property="teamId" column="team_id" jdbcType="BIGINT" />
        <result property="memberUid" column="member_uid" jdbcType="BIGINT" />
        <result property="role" column="role" jdbcType="INTEGER" />
        <result property="state" column="state" jdbcType="INTEGER" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
    </resultMap>

    <!-- Insert Statement -->
    <insert id="insertPepcTeamMember" parameterType="com.yy.gameecology.common.db.model.gameecology.pepc.PepcTeamMember">
        INSERT INTO pepc_team_member (team_id, act_id, member_uid, role, state, create_time)
        VALUES (#{teamId}, #{actId}, #{memberUid}, #{role}, #{state}, #{createTime})
    </insert>

    <!-- Select Statement by Team ID and Act ID -->
    <select id="selectPepcTeamMember" resultMap="PepcTeamMemberResult">
        SELECT * FROM pepc_team_member WHERE team_id = #{teamId} order by create_time asc
    </select>

    <select id="listTeamMembers" resultMap="PepcTeamMemberResult">
        SELECT * FROM pepc_team_member
        WHERE team_id in <foreach collection="teamIds" item="teamId" separator="," open="(" close=")">#{teamId}</foreach>
        order by team_id asc, create_time asc
    </select>

    <select id="selectByUniq" resultMap="PepcTeamMemberResult">
        SELECT * FROM pepc_team_member WHERE act_id = #{actId} AND member_uid = #{uid}
    </select>
    <select id="countMembers" resultType="java.lang.Long">
        select count(1) from pepc_team_member where act_id = #{actId}
        <if test="state != null">
            and `state` = #{state}
        </if>
    </select>
    <select id="batchSelectGamedMembers" resultMap="PepcTeamMemberResult">
        select * from pepc_team_member where act_id = #{actId}
        and team_id in <foreach collection="teamIds" item="teamId" separator="," open="(" close=")">#{teamId}</foreach>
        and game_count &gt; 0
    </select>
    <select id="countGamedMembers" resultType="java.lang.Long">
        select count(1) from pepc_team_member where game_count &gt; 0
    </select>

    <!-- Update Statement -->
    <update id="updateTeamMemberStat">
        update pepc_team_member
        set game_count = game_count + #{gameCount},
            win_game_count = win_game_count + #{winGameCount},
            kill_cnt = kill_cnt + #{killCnt},
            assist_cnt = assist_cnt + #{assistCnt}
        where team_id = #{teamId} and member_uid = #{uid}
    </update>

    <delete id="deleteMembers">
        delete from pepc_team_member where team_id = #{teamId}
        <if test="memberId != null">
            and id = #{memberId}
        </if>
        <if test="role != null">
            and role = #{role}
        </if>
    </delete>

</mapper>