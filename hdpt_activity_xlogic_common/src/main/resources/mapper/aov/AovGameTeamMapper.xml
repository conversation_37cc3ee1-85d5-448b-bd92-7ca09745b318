<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.aov.AovGameTeamMapper">
  <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.aov.AovGameTeam">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="phase_id" jdbcType="BIGINT" property="phaseId" />
    <result column="game_id" jdbcType="BIGINT" property="gameId" />
    <result column="round_id" jdbcType="BIGINT" property="roundId" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="opponent_team_id" jdbcType="BIGINT" property="opponentTeamId" />
    <result column="opponent_uid" jdbcType="BIGINT" property="opponentUid" />
    <result column="node_id" jdbcType="BIGINT" property="nodeId" />
    <result column="node_index" jdbcType="INTEGER" property="nodeIndex" />
    <result column="remote_team_id" jdbcType="VARCHAR" property="remoteTeamId" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="assist_cnt" jdbcType="INTEGER" property="assistCnt" />
    <result column="dead_cnt" jdbcType="INTEGER" property="deadCnt" />
    <result column="kill_cnt" jdbcType="INTEGER" property="killCnt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, phase_id, game_id, round_id, team_id, uid, opponent_team_id, opponent_uid, node_id, 
    node_index, remote_team_id, state, assist_cnt, dead_cnt, kill_cnt
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from aov_game_team
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from aov_game_team
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovGameTeam">
    insert into aov_game_team (id, phase_id, game_id, 
      round_id, team_id, uid, 
      opponent_team_id, opponent_uid, node_id, 
      node_index, remote_team_id, state, 
      assist_cnt, dead_cnt, kill_cnt
      )
    values (#{id,jdbcType=BIGINT}, #{phaseId,jdbcType=BIGINT}, #{gameId,jdbcType=BIGINT}, 
      #{roundId,jdbcType=BIGINT}, #{teamId,jdbcType=BIGINT}, #{uid,jdbcType=BIGINT}, 
      #{opponentTeamId,jdbcType=BIGINT}, #{opponentUid,jdbcType=BIGINT}, #{nodeId,jdbcType=BIGINT}, 
      #{nodeIndex,jdbcType=INTEGER}, #{remoteTeamId,jdbcType=VARCHAR}, #{state,jdbcType=INTEGER}, 
      #{assistCnt,jdbcType=INTEGER}, #{deadCnt,jdbcType=INTEGER}, #{killCnt,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovGameTeam">
    insert into aov_game_team
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="phaseId != null">
        phase_id,
      </if>
      <if test="gameId != null">
        game_id,
      </if>
      <if test="roundId != null">
        round_id,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="opponentTeamId != null">
        opponent_team_id,
      </if>
      <if test="opponentUid != null">
        opponent_uid,
      </if>
      <if test="nodeId != null">
        node_id,
      </if>
      <if test="nodeIndex != null">
        node_index,
      </if>
      <if test="remoteTeamId != null">
        remote_team_id,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="assistCnt != null">
        assist_cnt,
      </if>
      <if test="deadCnt != null">
        dead_cnt,
      </if>
      <if test="killCnt != null">
        kill_cnt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="phaseId != null">
        #{phaseId,jdbcType=BIGINT},
      </if>
      <if test="gameId != null">
        #{gameId,jdbcType=BIGINT},
      </if>
      <if test="roundId != null">
        #{roundId,jdbcType=BIGINT},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="opponentTeamId != null">
        #{opponentTeamId,jdbcType=BIGINT},
      </if>
      <if test="opponentUid != null">
        #{opponentUid,jdbcType=BIGINT},
      </if>
      <if test="nodeId != null">
        #{nodeId,jdbcType=BIGINT},
      </if>
      <if test="nodeIndex != null">
        #{nodeIndex,jdbcType=INTEGER},
      </if>
      <if test="remoteTeamId != null">
        #{remoteTeamId,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="assistCnt != null">
        #{assistCnt,jdbcType=INTEGER},
      </if>
      <if test="deadCnt != null">
        #{deadCnt,jdbcType=INTEGER},
      </if>
      <if test="killCnt != null">
        #{killCnt,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovGameTeam">
    update aov_game_team
    <set>
      <if test="phaseId != null">
        phase_id = #{phaseId,jdbcType=BIGINT},
      </if>
      <if test="gameId != null">
        game_id = #{gameId,jdbcType=BIGINT},
      </if>
      <if test="roundId != null">
        round_id = #{roundId,jdbcType=BIGINT},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="opponentTeamId != null">
        opponent_team_id = #{opponentTeamId,jdbcType=BIGINT},
      </if>
      <if test="opponentUid != null">
        opponent_uid = #{opponentUid,jdbcType=BIGINT},
      </if>
      <if test="nodeId != null">
        node_id = #{nodeId,jdbcType=BIGINT},
      </if>
      <if test="nodeIndex != null">
        node_index = #{nodeIndex,jdbcType=INTEGER},
      </if>
      <if test="remoteTeamId != null">
        remote_team_id = #{remoteTeamId,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="assistCnt != null">
        assist_cnt = #{assistCnt,jdbcType=INTEGER},
      </if>
      <if test="deadCnt != null">
        dead_cnt = #{deadCnt,jdbcType=INTEGER},
      </if>
      <if test="killCnt != null">
        kill_cnt = #{killCnt,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovGameTeam">
    update aov_game_team
    set phase_id = #{phaseId,jdbcType=BIGINT},
      game_id = #{gameId,jdbcType=BIGINT},
      round_id = #{roundId,jdbcType=BIGINT},
      team_id = #{teamId,jdbcType=BIGINT},
      uid = #{uid,jdbcType=BIGINT},
      opponent_team_id = #{opponentTeamId,jdbcType=BIGINT},
      opponent_uid = #{opponentUid,jdbcType=BIGINT},
      node_id = #{nodeId,jdbcType=BIGINT},
      node_index = #{nodeIndex,jdbcType=INTEGER},
      remote_team_id = #{remoteTeamId,jdbcType=VARCHAR},
      state = #{state,jdbcType=INTEGER},
      assist_cnt = #{assistCnt,jdbcType=INTEGER},
      dead_cnt = #{deadCnt,jdbcType=INTEGER},
      kill_cnt = #{killCnt,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>