<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.aov.AovMatchAwardRecordMapper">
  <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.aov.AovMatchAwardRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="act_id" jdbcType="BIGINT" property="actId" />
    <result column="phase_id" jdbcType="BIGINT" property="phaseId" />
    <result column="award_type" jdbcType="INTEGER" property="awardType" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="round_num" jdbcType="INTEGER" property="roundNum" />
    <result column="amount" jdbcType="BIGINT" property="amount" />
    <result column="award_desc" jdbcType="VARCHAR" property="awardDesc" />
    <result column="award_state" jdbcType="INTEGER" property="awardState" />
    <result column="award_time" jdbcType="TIMESTAMP" property="awardTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, act_id, phase_id, award_type, uid, round_num, amount, award_desc, award_state, 
    award_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from aov_match_award_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from aov_match_award_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovMatchAwardRecord">
    insert into aov_match_award_record (id, act_id, phase_id, 
      award_type, uid, round_num, 
      amount, award_desc, award_state, 
      award_time)
    values (#{id,jdbcType=BIGINT}, #{actId,jdbcType=BIGINT}, #{phaseId,jdbcType=BIGINT}, 
      #{awardType,jdbcType=INTEGER}, #{uid,jdbcType=BIGINT}, #{roundNum,jdbcType=INTEGER}, 
      #{amount,jdbcType=BIGINT}, #{awardDesc,jdbcType=VARCHAR}, #{awardState,jdbcType=INTEGER}, 
      #{awardTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovMatchAwardRecord">
    insert into aov_match_award_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="actId != null">
        act_id,
      </if>
      <if test="phaseId != null">
        phase_id,
      </if>
      <if test="awardType != null">
        award_type,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="roundNum != null">
        round_num,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="awardDesc != null">
        award_desc,
      </if>
      <if test="awardState != null">
        award_state,
      </if>
      <if test="awardTime != null">
        award_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="actId != null">
        #{actId,jdbcType=BIGINT},
      </if>
      <if test="phaseId != null">
        #{phaseId,jdbcType=BIGINT},
      </if>
      <if test="awardType != null">
        #{awardType,jdbcType=INTEGER},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="roundNum != null">
        #{roundNum,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="awardDesc != null">
        #{awardDesc,jdbcType=VARCHAR},
      </if>
      <if test="awardState != null">
        #{awardState,jdbcType=INTEGER},
      </if>
      <if test="awardTime != null">
        #{awardTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovMatchAwardRecord">
    update aov_match_award_record
    <set>
      <if test="actId != null">
        act_id = #{actId,jdbcType=BIGINT},
      </if>
      <if test="phaseId != null">
        phase_id = #{phaseId,jdbcType=BIGINT},
      </if>
      <if test="awardType != null">
        award_type = #{awardType,jdbcType=INTEGER},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="roundNum != null">
        round_num = #{roundNum,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=BIGINT},
      </if>
      <if test="awardDesc != null">
        award_desc = #{awardDesc,jdbcType=VARCHAR},
      </if>
      <if test="awardState != null">
        award_state = #{awardState,jdbcType=INTEGER},
      </if>
      <if test="awardTime != null">
        award_time = #{awardTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovMatchAwardRecord">
    update aov_match_award_record
    set act_id = #{actId,jdbcType=BIGINT},
      phase_id = #{phaseId,jdbcType=BIGINT},
      award_type = #{awardType,jdbcType=INTEGER},
      uid = #{uid,jdbcType=BIGINT},
      round_num = #{roundNum,jdbcType=INTEGER},
      amount = #{amount,jdbcType=BIGINT},
      award_desc = #{awardDesc,jdbcType=VARCHAR},
      award_state = #{awardState,jdbcType=INTEGER},
      award_time = #{awardTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>