<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.aov.AovPhaseRoundMapper">
  <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseRound">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="phase_id" jdbcType="BIGINT" property="phaseId" />
    <result column="round_num" jdbcType="INTEGER" property="roundNum" />
    <result column="round_name" jdbcType="VARCHAR" property="roundName" />
    <result column="bo" jdbcType="INTEGER" property="bo" />
    <result column="battle_mode" jdbcType="INTEGER" property="battleMode" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_game" jdbcType="INTEGER" property="createGame" />
    <result column="state" jdbcType="INTEGER" property="state" />
  </resultMap>
  <sql id="Base_Column_List">
    id, phase_id, round_num, round_name, bo, battle_mode, start_time, end_time, create_game,
    state
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from aov_phase_round
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from aov_phase_round
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseRound">
    insert into aov_phase_round (id, phase_id, round_num,
    round_name, bo, battle_mode,
    start_time, end_time, create_game,
    state)
    values (#{id,jdbcType=BIGINT}, #{phaseId,jdbcType=BIGINT}, #{roundNum,jdbcType=INTEGER},
    #{roundName,jdbcType=VARCHAR}, #{bo,jdbcType=INTEGER}, #{battleMode,jdbcType=INTEGER},
    #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{createGame,jdbcType=INTEGER},
    #{state,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseRound">
    insert into aov_phase_round
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="phaseId != null">
        phase_id,
      </if>
      <if test="roundNum != null">
        round_num,
      </if>
      <if test="roundName != null">
        round_name,
      </if>
      <if test="bo != null">
        bo,
      </if>
      <if test="battleMode != null">
        battle_mode,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="createGame != null">
        create_game,
      </if>
      <if test="state != null">
        state,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="phaseId != null">
        #{phaseId,jdbcType=BIGINT},
      </if>
      <if test="roundNum != null">
        #{roundNum,jdbcType=INTEGER},
      </if>
      <if test="roundName != null">
        #{roundName,jdbcType=VARCHAR},
      </if>
      <if test="bo != null">
        #{bo,jdbcType=INTEGER},
      </if>
      <if test="battleMode != null">
        #{battleMode,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createGame != null">
        #{createGame,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseRound">
    update aov_phase_round
    <set>
      <if test="phaseId != null">
        phase_id = #{phaseId,jdbcType=BIGINT},
      </if>
      <if test="roundNum != null">
        round_num = #{roundNum,jdbcType=INTEGER},
      </if>
      <if test="roundName != null">
        round_name = #{roundName,jdbcType=VARCHAR},
      </if>
      <if test="bo != null">
        bo = #{bo,jdbcType=INTEGER},
      </if>
      <if test="battleMode != null">
        battle_mode = #{battleMode,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createGame != null">
        create_game = #{createGame,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseRound">
    update aov_phase_round
    set phase_id = #{phaseId,jdbcType=BIGINT},
    round_num = #{roundNum,jdbcType=INTEGER},
    round_name = #{roundName,jdbcType=VARCHAR},
    bo = #{bo,jdbcType=INTEGER},
    battle_mode = #{battleMode,jdbcType=INTEGER},
    start_time = #{startTime,jdbcType=TIMESTAMP},
    end_time = #{endTime,jdbcType=TIMESTAMP},
    create_game = #{createGame,jdbcType=INTEGER},
    state = #{state,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>