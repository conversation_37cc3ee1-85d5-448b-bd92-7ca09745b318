<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.aov.AovMatchAwardRecordExtMapper">
    <insert id="saveAwardRecord" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert ignore into aov_match_award_record (act_id, phase_id, award_type, uid, round_num, amount, award_desc, award_state, award_time)
        values (#{actId}, #{phaseId}, #{awardType}, #{uid}, #{roundNum}, #{amount}, #{awardDesc}, #{awardState}, #{awardTime})
    </insert>
    <update id="batchUpdateAwardRecordState">
        update aov_match_award_record set `award_state` = #{targetState}
        where id in <foreach collection="awardIds" item="awardId" separator="," open="(" close=")">#{awardId}</foreach>
        and `award_state` = #{sourceState}
    </update>
    <select id="countUserAwardRecord" resultType="java.lang.Integer">
        select count(1) from aov_match_award_record
        <where>
            <if test="actId != null">
                act_id = #{actId}
            </if>
            <if test="phaseId != null">
                and phase_id = #{phaseId}
            </if>
            <if test="awardType != null">
                and award_type = #{awardType}
            </if>
            <if test="uid != null">
                and uid = #{uid}
            </if>
            <if test="roundNum != null">
                and round_num = #{roundNum}
            </if>
            <if test="state != null">
                and `award_state` = #{state}
            </if>
        </where>
    </select>
    <select id="selectUserAwardList" resultMap="com.yy.gameecology.common.db.mapper.aov.AovMatchAwardRecordMapper.BaseResultMap">
        select <include refid="com.yy.gameecology.common.db.mapper.aov.AovMatchAwardRecordMapper.Base_Column_List" />
        from aov_match_award_record
        where act_id = #{actId} and uid = #{uid}
        order by award_time desc
    </select>

    <select id="selectUserPhaseAwardList" resultMap="com.yy.gameecology.common.db.mapper.aov.AovMatchAwardRecordMapper.BaseResultMap">
        select <include refid="com.yy.gameecology.common.db.mapper.aov.AovMatchAwardRecordMapper.Base_Column_List" />
        from aov_match_award_record
        where uid = #{uid} and phase_id = #{phaseId} and award_state = #{state}
        order by award_time desc
    </select>

    <select id="sumAwardAmount" resultType="java.lang.Long">
        select ifnull(sum(amount), 0) from aov_match_award_record
        <where>
            <if test="actId != null">
                act_id = #{actId}
            </if>
            <if test="phaseId != null">
                and phase_id = #{phaseId}
            </if>
            <if test="awardType != null">
                and award_type = #{awardType}
            </if>
            <if test="uid != null">
                and uid = #{uid}
            </if>
            <if test="roundNum != null">
                and round_num = #{roundNum}
            </if>
            <if test="state != null">
                and `award_state` = #{state}
            </if>
        </where>
    </select>
    <select id="selectAwardList" resultMap="com.yy.gameecology.common.db.mapper.aov.AovMatchAwardRecordMapper.BaseResultMap">
        select <include refid="com.yy.gameecology.common.db.mapper.aov.AovMatchAwardRecordMapper.Base_Column_List" />
        from aov_match_award_record
        <where>
            <if test="phaseId != null">
                phase_id = #{phaseId}
            </if>
            <if test="state != null">
                and `award_state` = #{state}
            </if>
            and id &gt; #{floorId}
        </where>
        order by id
        limit #{size}
    </select>
</mapper>