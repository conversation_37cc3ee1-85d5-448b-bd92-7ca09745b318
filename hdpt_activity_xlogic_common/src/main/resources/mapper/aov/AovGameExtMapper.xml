<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.aov.AovGameExtMapper">
    <insert id="insertGameAndReturnId" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovGame" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert ignore into aov_game
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="phaseId != null">
                phase_id,
            </if>
            <if test="roundId != null">
                round_id,
            </if>
            <if test="roundNum != null">
                round_num,
            </if>
            <if test="roundName != null">
                round_name,
            </if>
            <if test="advanceNodeIndex != null">
                advance_node_index,
            </if>
            <if test="camp1TeamId != null">
                camp1_team_id,
            </if>
            <if test="camp2TeamId != null">
                camp2_team_id,
            </if>
            <if test="bo != null">
                bo,
            </if>
            <if test="battleMode != null">
                battle_mode,
            </if>
            <if test="curBo != null">
                cur_bo,
            </if>
            <if test="childId != null">
                child_id,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="settleState != null">
                settle_state,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="readyTime != null">
                ready_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="phaseId != null">
                #{phaseId,jdbcType=BIGINT},
            </if>
            <if test="roundId != null">
                #{roundId,jdbcType=BIGINT},
            </if>
            <if test="roundNum != null">
                #{roundNum,jdbcType=INTEGER},
            </if>
            <if test="roundName != null">
                #{roundName,jdbcType=VARCHAR},
            </if>
            <if test="advanceNodeIndex != null">
                #{advanceNodeIndex,jdbcType=INTEGER},
            </if>
            <if test="camp1TeamId != null">
                #{camp1TeamId,jdbcType=BIGINT},
            </if>
            <if test="camp2TeamId != null">
                #{camp2TeamId,jdbcType=BIGINT},
            </if>
            <if test="bo != null">
                #{bo,jdbcType=INTEGER},
            </if>
            <if test="battleMode != null">
                #{battleMode,jdbcType=INTEGER},
            </if>
            <if test="curBo != null">
                #{curBo,jdbcType=INTEGER},
            </if>
            <if test="childId != null">
                #{childId,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="settleState != null">
                #{settleState,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="readyTime != null">
                #{readyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateGameState">
        update aov_game set `state` = #{targetState}
        <if test="childId != null">
            , child_id = #{childId}
        </if>
        <if test="readyTime != null">
            , ready_time = #{readyTime}
        </if>
        <if test="endTime != null">
            , end_time = #{endTime}
        </if>
        where id = #{gameId}
        <if test="sourceState != null">
            and `state` = #{sourceState}
        </if>
    </update>
    <update id="updateSettleState">
        update aov_game set settle_state = #{targetSettleState}
        where id = #{gameId} and settle_state = #{sourceSettleState}
    </update>

    <select id="selectGames" resultMap="com.yy.gameecology.common.db.mapper.aov.AovGameMapper.BaseResultMap">
        select <include refid="com.yy.gameecology.common.db.mapper.aov.AovGameMapper.Base_Column_List" />
        from aov_game
        <where>
            phase_id = #{phaseId}
            <if test="floorStartTime != null">
                and start_time &lt; #{floorStartTime}
            </if>
            <if test="ceilStartTime != null">
                and start_time &gt; #{ceilStartTime}
            </if>
            <if test="settleState != null">
                and settle_state = #{settleState}
            </if>
            <if test="state != null">
                and `state` = #{state}
            </if>
            <if test="states != null">
                and `state` in <foreach collection="states" item="st" separator="," open="(" close=")">#{st}</foreach>
            </if>
        </where>
    </select>
    <select id="batchSelectGames" resultMap="com.yy.gameecology.common.db.mapper.aov.AovGameMapper.BaseResultMap">
        select <include refid="com.yy.gameecology.common.db.mapper.aov.AovGameMapper.Base_Column_List" />
        from aov_game
        where id in <foreach collection="gameIds" item="gameId" separator="," open="(" close=")">#{gameId}</foreach>
    </select>

    <select id="selectGamesByPhaseId" resultMap="com.yy.gameecology.common.db.mapper.aov.AovGameMapper.BaseResultMap">
        select <include refid="com.yy.gameecology.common.db.mapper.aov.AovGameMapper.Base_Column_List" />
        from aov_game
        where phase_id = #{phaseId}
        <if test="states != null">
            and `state` in <foreach collection="states" item="st" separator="," open="(" close=")">#{st}</foreach>
        </if>
        order by start_time desc
    </select>

    <select id="selectGamesByPhaseIdNodeId" resultMap="com.yy.gameecology.common.db.mapper.aov.AovGameMapper.BaseResultMap">
        select <include refid="com.yy.gameecology.common.db.mapper.aov.AovGameMapper.Base_Column_List" />
        from aov_game
        where phase_id = #{phaseId}
        and advance_node_index = #{nodeId}
        order by start_time desc
        limit 1
    </select>
</mapper>