<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.aov.AovGameMobileEnrollmentMapper">
    <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.aov.AovGameMobileEnrollment">
        <id column="uid" jdbcType="BIGINT" property="uid" />
        <result column="act_tp" jdbcType="BIGINT" property="actTp" />
        <result column="mobile_hash" jdbcType="VARCHAR" property="mobileHash" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>
    <sql id="Base_Column_List">
        uid, act_tp, mobile_hash, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from aov_game_mobile_enrollment
        where uid = #{uid,jdbcType=BIGINT} and act_tp = #{actTp,jdbcType=BIGINT}
    </select>
    <select id="selectUidByMobileHash" resultType="java.lang.Long">
        select uid from aov_game_mobile_enrollment where mobile_hash = #{mobileHash} and act_tp = #{actTp,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from aov_game_mobile_enrollment
        where uid = #{uid,jdbcType=BIGINT} and act_tp = #{actTp,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovGameMobileEnrollment">
        insert ignore into aov_game_mobile_enrollment (uid, act_tp, mobile_hash, create_time
    )
    values (#{uid,jdbcType=BIGINT}, #{actTp,jdbcType=BIGINT}, #{mobileHash,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovGameMobileEnrollment">
        update aov_game_mobile_enrollment
        <set>
            <if test="mobileHash != null">
                mobile_hash = #{mobileHash,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where uid = #{uid,jdbcType=BIGINT} and act_tp = #{actTp,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovGameMobileEnrollment">
        update aov_game_mobile_enrollment
        set mobile_hash = #{mobileHash,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP}
        where uid = #{uid,jdbcType=BIGINT} and act_tp = #{actTp,jdbcType=BIGINT}
    </update>
</mapper>