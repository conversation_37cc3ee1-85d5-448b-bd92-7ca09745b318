<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.aov.AovPhaseSubscribeMapper">
  <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseSubscribe">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="prev_act_id" jdbcType="BIGINT" property="prevActId" />
    <result column="prev_phase_id" jdbcType="BIGINT" property="prevPhaseId" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, prev_act_id, prev_phase_id, uid, state, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from aov_phase_subscribe
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectUserSubscribe" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from aov_phase_subscribe
    where prev_act_id = #{actId} and prev_phase_id = #{phaseId} and uid = #{uid}
  </select>
  <select id="selectSubscribes" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from aov_phase_subscribe
    where prev_act_id = #{actId} and prev_phase_id = #{phaseId}
    <if test="state != null">
      and `state` = #{state}
    </if>
    order by id
    limit #{size}
  </select>
    <select id="countSubscribes" resultType="java.lang.Integer">
      select count(1) from aov_phase_subscribe where prev_act_id = #{actId} and prev_phase_id = #{phaseId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from aov_phase_subscribe
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseSubscribe">
    insert into aov_phase_subscribe (id, prev_act_id, prev_phase_id, 
      uid, state, create_time
      )
    values (#{id,jdbcType=BIGINT}, #{prevActId,jdbcType=BIGINT}, #{prevPhaseId,jdbcType=BIGINT}, 
      #{uid,jdbcType=BIGINT}, #{state,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseSubscribe">
    insert ignore into aov_phase_subscribe
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="prevActId != null">
        prev_act_id,
      </if>
      <if test="prevPhaseId != null">
        prev_phase_id,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="prevActId != null">
        #{prevActId,jdbcType=BIGINT},
      </if>
      <if test="prevPhaseId != null">
        #{prevPhaseId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseSubscribe">
    update aov_phase_subscribe
    <set>
      <if test="prevActId != null">
        prev_act_id = #{prevActId,jdbcType=BIGINT},
      </if>
      <if test="prevPhaseId != null">
        prev_phase_id = #{prevPhaseId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseSubscribe">
    update aov_phase_subscribe
    set prev_act_id = #{prevActId,jdbcType=BIGINT},
      prev_phase_id = #{prevPhaseId,jdbcType=BIGINT},
      uid = #{uid,jdbcType=BIGINT},
      state = #{state,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateSubscribeState">
    update aov_phase_subscribe set `state` = #{targetState}
    where id = #{id}
    <if test="sourceState != null">
      and `state` = #{sourceState}
    </if>
  </update>
</mapper>