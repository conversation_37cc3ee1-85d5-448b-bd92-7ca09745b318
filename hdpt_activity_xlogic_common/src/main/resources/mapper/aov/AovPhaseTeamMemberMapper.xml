<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.aov.AovPhaseTeamMemberMapper">
    <!-- Result Map -->
    <resultMap id="AovPhaseTeamMemberResult" type="com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseTeamMember">
        <id property="id" column="id" jdbcType="BIGINT" />
        <result property="teamId" column="team_id" jdbcType="BIGINT" />
        <result property="phaseId" column="phase_id" jdbcType="BIGINT" />
        <result property="memberUid" column="member_uid" jdbcType="BIGINT" />
        <result property="role" column="role" jdbcType="INTEGER" />
        <result property="state" column="state" jdbcType="INTEGER" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
    </resultMap>

    <!-- Insert Statement -->
    <insert id="insertAovPhaseTeamMember" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseTeamMember">
        INSERT INTO aov_phase_team_member (team_id, phase_id, member_uid, role, state, create_time)
        VALUES (#{teamId}, #{phaseId}, #{memberUid}, #{role}, #{state}, #{createTime})
    </insert>

    <!-- Select Statement by Team ID and Phase ID -->
    <select id="selectAovPhaseTeamMember" resultMap="AovPhaseTeamMemberResult">
        SELECT * FROM aov_phase_team_member WHERE team_id = #{teamId} order by create_time asc
    </select>

    <select id="listTeamMembers" resultMap="AovPhaseTeamMemberResult">
        SELECT * FROM aov_phase_team_member
        WHERE team_id in <foreach collection="teamIds" item="teamId" separator="," open="(" close=")">#{teamId}</foreach>
        order by team_id asc, create_time asc
    </select>

    <select id="selectByUniq" resultMap="AovPhaseTeamMemberResult">
        SELECT * FROM aov_phase_team_member WHERE phase_id = #{phaseId} AND member_uid = #{uid}
    </select>
    <select id="countPhaseMembers" resultType="java.lang.Long">
        select count(1) from aov_phase_team_member where phase_id = #{phaseId}
        <if test="state != null">
            and `state` = #{state}
        </if>
    </select>
    <select id="batchSelectGamedMembers" resultMap="AovPhaseTeamMemberResult">
        select * from aov_phase_team_member where phase_id = #{phaseId}
        and team_id in <foreach collection="teamIds" item="teamId" separator="," open="(" close=")">#{teamId}</foreach>
        and game_count &gt; 0
    </select>
    <select id="countGamedMembers" resultType="java.lang.Long">
        select count(1) from aov_phase_team_member where phase_id = #{phaseId} and game_count &gt; 0
    </select>

    <!-- Update Statement -->
    <update id="updateAovPhaseTeamMember">
        UPDATE aov_phase_team_member
        SET team_id = #{teamId},
            phase_id = #{phaseId},
            member_uid = #{memberUid},
            role = #{role},
            state = #{state}
        WHERE id = #{id}
    </update>
    <update id="updateTeamMemberStat">
        update aov_phase_team_member
        set game_count = game_count + #{gameCount},
            win_game_count = win_game_count + #{winGameCount},
            kill_cnt = kill_cnt + #{killCnt},
            assist_cnt = assist_cnt + #{assistCnt}
        where team_id = #{teamId} and member_uid = #{uid}
    </update>

    <delete id="deleteMembers">
        delete from aov_phase_team_member where team_id = #{teamId}
        <if test="memberId != null">
            and id = #{memberId}
        </if>
        <if test="role != null">
            and role = #{role}
        </if>
    </delete>

</mapper>