package com.yy.gameecology.common.db.mapper.aov;

import com.yy.gameecology.common.db.model.gameecology.aov.AovWhitelist;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface AovWhitelistMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AovWhitelist record);

    int insertSelective(AovWhitelist record);

    AovWhitelist selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AovWhitelist record);

    int updateByPrimaryKey(AovWhitelist record);

    int countByUniqKey(@Param("actId") long actId, @Param("phaseId") Long phaseId, @Param("uid") long uid);

    int batchInsertWhitelist(@Param("records") Collection<AovWhitelist> records);

    int deleteByUniqKey(@Param("actId") long actId, @Param("phaseId") Long phaseId, @Param("inviter") Long inviter,  @Param("uid") long uid);

    List<AovWhitelist> selectWhitelist(@Param("actId") long actId, @Param("phaseId") Long phaseId, @Param("inviter") Long inviter, @Param("offset")  int offset, @Param("limit") int limit);

    int countWhitelist(@Param("actId") long actId, @Param("phaseId") Long phaseId, @Param("inviter") Long inviter);
}