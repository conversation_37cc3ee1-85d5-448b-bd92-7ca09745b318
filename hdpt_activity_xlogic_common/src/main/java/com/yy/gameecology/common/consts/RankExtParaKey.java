package com.yy.gameecology.common.consts;


/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-20 15:24
 **/
public class RankExtParaKey {
    /**
     * ext 参数，贡献榜来源id
     */
    public final static String RANK_TYPE_HOVER_SRC_ID = "rank_type_hover_src_id";

    /**
     * ext 参数，有pk积分的，取原始的荣耀值榜，即 pk key
     */
    public final static String RANK_SCORE_SOURCE = "rank_score_source";

    /**
     * ext 参数，积分榜的荣耀值
     */
    public final static String RANK_PK_VALUE = "rank_pk_value";

    /**
     * ext 参数，查询榜单指定key
     */
    public final static String QUERY_RANK_TYPE = "query_rank_type";

    /**
     * ext 参数,查询pk失败复活榜单
     */
    public static final String RANK_PK_REVIVE = "rank_pk_revive";

    /**
     * ext 参数,查询整个阶段所有排名（没有圈定晋级名单的所有人的榜单）-没有后缀
     */
    public static final String RANK_TYPE_ALL = "rank_type_all";

    /**
     * ext 参数,查询promot（没有圈定晋级名单的所有人的榜单）-没有后缀
     */
    public static final String RANK_TYPE_PROMOT = "rank_type_promot";

    /**
     * 作为传出参数：绑定中所有member的多昵称信息
     */
    public static final String OUT_MULTI_NICK_USERS = "multiNickUsers";
}
