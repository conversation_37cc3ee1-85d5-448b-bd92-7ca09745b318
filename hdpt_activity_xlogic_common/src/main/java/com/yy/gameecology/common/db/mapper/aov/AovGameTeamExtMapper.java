package com.yy.gameecology.common.db.mapper.aov;

import com.yy.gameecology.common.db.model.gameecology.aov.AovGameTeam;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface AovGameTeamExtMapper extends AovGameTeamMapper {

    List<AovGameTeam> selectGameTeams(@Param("gameId") long gameId, @Param("state") Integer state);

    int updateGameTeamState(@Param("teamId") long teamId, @Param("sourceState") int sourceState, @Param("targetState") int targetState, @Param("assistCnt") Integer assistCnt, @Param("deadCnt") Integer deadCnt, @Param("killCnt") Integer killCnt);

    List<Long> selectGameTeamsByTeamId(@Param("phaseId") long phaseId, @Param("teamId") long teamId, @Param("state") Integer state);

    List<AovGameTeam> selectGameTeamsByPhaseId(@Param("phaseId") long phaseId);

    AovGameTeam selectGameTeamsByRoundTeamId(@Param("roundId") long roundId, @Param("teamId") long teamId);

    List<AovGameTeam> batchSelectGameTeamsByGameId(@Param("gameIds") Collection<Long> gameIds);

    AovGameTeam selectPhaseProcessingGameTeam(@Param("phaseId") long phaseId, @Param("teamId") long teamId, @Param("state") int state);

    long countGamedTeams(@Param("phaseId") long phaseId);
}
