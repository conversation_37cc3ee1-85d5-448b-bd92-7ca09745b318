package com.yy.gameecology.common.db.mapper.gameecology.cmpt;

import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2064CpInfo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface Cmpt2064CpInfoMapper {

    @Insert("""
                INSERT IGNORE INTO cmpt_2064_cp_info (act_id, cmpt_use_inx, date_code, user_uid, anchor_uid)
                VALUES (#{actId}, #{cmptUseInx}, #{dateCode}, #{userUid}, #{anchorUid})
            """)
    int insertCpInfo(@Param("actId") Long actId,
                     @Param("cmptUseInx") Long cmptUseInx,
                     @Param("dateCode") String dateCode,
                     @Param("userUid") Long userUid,
                     @Param("anchorUid") Long anchorUid);


    @Select("""
        SELECT * FROM cmpt_2064_cp_info
        WHERE act_id = #{actId}
          AND cmpt_use_inx = #{cmptUseInx}
          AND date_code = #{dateCode}
          AND (user_uid = #{uid} or anchor_uid = #{uid})
    """)
    List<Cmpt2064CpInfo> selectCpInfo(@Param("actId") Long actId, @Param("cmptUseInx") Long cmptUseInx, @Param("dateCode") String dateCode, @Param("uid") Long uid);
}
