package com.yy.gameecology.common.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class ImPushMsgExtendInfo {

    private String title;

    private String content;

    private String image;

    private String link;

    /**
     * <pre>
     * 1234567 - 追玩小助手
     * 10000   - 活动中心
     * 20000   - Yo语音小助手
     * 21000   - 1v1通话消息
     * 30000   - 语音房家族消息
     * </pre>
     */
    private Integer type;

    /**
     * 消息类型 0、默认 1、家族
     */
    private Integer msgType;
    /**
     * 语音房消息类型 1 申请加入 2 通过 3 退出
     */
    private Integer voiceChlMsgType;

    private Map<String, Object> extendInfo;
}
