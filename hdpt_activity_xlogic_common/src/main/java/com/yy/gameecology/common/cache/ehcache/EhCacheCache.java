
package com.yy.gameecology.common.cache.ehcache;

import com.yy.gameecology.common.cache.Cache;
import com.yy.gameecology.common.consts.CacheTimeOutSuffix;
import com.yy.gameecology.common.consts.Const;
import net.sf.ehcache.Ehcache;
import net.sf.ehcache.Element;
import net.sf.ehcache.Status;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.util.Assert;

public class EhCacheCache implements Cache {

    protected final Log log = LogFactory.getLog(getClass());

    private final Ehcache cache;

    /**
     * Creates a {@link EhCacheCache} instance.
     *
     * @param ehcache backing Ehcache instance
     */
    public EhCacheCache(Ehcache ehcache) {
        Assert.notNull(ehcache, "non null ehcache required");
        Status status = ehcache.getStatus();
        Assert.isTrue(Status.STATUS_ALIVE.equals(status),
            "an 'alive' ehcache is required - current cache is " + status.toString());
        this.cache = ehcache;
    }

    @Override
    public String getName() {
        return cache.getName();
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T get(Object key) {
        Element element = cache.get(key);
        if (element == null) {
            return null;
        }
        try {
//            if (SysEvHelper.isDev()) {
//                if (System.currentTimeMillis() % 10 <= 3) {
//                    // log.info("load from cache:" + key);
//                }
//            }
            return (T)element.getObjectValue();
        } catch (Exception e) {
            logException(e, key);
            return null;
        }
    }

    private void logException(Exception e, Object key) {
        log.error("memory cache receive an exception with key:" + key, e);
    }

    @Override
    public void put(Object key, Object value) {
        // 测试环境特意不缓存，方便更新数据立刻生效，提高测试效率
//        if (SysEvHelper.isDev()||SysEvHelper.isLocal()) {
//            return;
//        }
        if (key == null) {
            log.warn("key is null for set()");
            return;
        }
        if (value == null) {
            //log.warn("trying to store a null value to cache under key:" + key);
            return;
        }

        Element element = new Element(key, value);
        cache.put(element);
    }

    @Override
    public void put(Object key, Object value, long timeToLiveMillis) {
        // 测试环境特意不缓存，方便更新数据立刻生效，提高测试效率
//        if (SysEvHelper.isDev()||SysEvHelper.isLocal()) {
//            return;
//        }
        if (key == null) {
            log.warn("key is null for set()");
            return;
        }
        if (value == null) {
            //log.warn("trying to store a null value to cache");
            return;
        }
        long timeOutStrategy = timeToLiveMillis % 1000;
        if (timeOutStrategy == CacheTimeOutSuffix.HALF_RANDOM) {
            int half = (int) (timeToLiveMillis / 2);
            timeToLiveMillis = Const.SYS.RD.nextInt(half) + half;
        } else if (timeOutStrategy == CacheTimeOutSuffix.ONE_TENTH_RANDOM) {
            int min = (int) (timeToLiveMillis / 10);
            timeToLiveMillis = Const.SYS.RD.nextInt((int) timeToLiveMillis - min) + (min);
        }

        Element element = new Element(key, value);
        Integer timeToLiveSeconds = (int)(timeToLiveMillis / 1000);
        if (timeToLiveSeconds > 0) {
            element.setTimeToLive(timeToLiveSeconds);
        }
        cache.put(element);
    }

    @Override
    public Object remove(Object key) {
        Object value = null;
        if (cache.isKeyInCache(key)) {
            Element element = cache.getQuiet(key);
            value = (element != null ? element.getObjectValue() : null);
        }
        cache.remove(key);
        return value;
    }

}
