package com.yy.gameecology.common.utils;

import org.apache.commons.lang3.RandomUtils;

import java.util.Date;
import java.util.Random;

/**
 * <AUTHOR>
 * @ClassName
 * @description ID生成器
 * @date 2019/8/18 18:03
 */
public class IdGenerator {

    /**
     * 最多支持的时间
     */
    private static long MAX_TIME_MS = 1906451148000L;
    private static final int UID_BYTE_LEN = 7;
    private static final int RANDOM_BYTE_LEN = 3;
    private static final int RANDOM_MAX = 7;
    private static final Random RANDOM = new Random();


    public static long generate(long currentMillis, long uid) {
        long lastUid = uid % 100L;
        long time = (MAX_TIME_MS - currentMillis);
        long randomId1 = RandomUtils.nextInt(0, RANDOM_MAX);
        return (time << UID_BYTE_LEN << RANDOM_BYTE_LEN) | (randomId1 << UID_BYTE_LEN) | lastUid;
    }

    /**
     *
     * @param uid
     * @return
     */
    public static long generate(long uid) {
        return generate(System.currentTimeMillis(), uid);
    }

    public static long getUidSuffixFromOrderId(long orderId) {
        return orderId & 127;
    }

    public static long getTimeMillsFromOrderId(long orderId) {
        String binaryString = Long.toBinaryString(orderId);
        return MAX_TIME_MS
                - Long.parseLong(binaryString.substring(0, binaryString.length() - RANDOM_BYTE_LEN - UID_BYTE_LEN), 2);
    }

    public static Date getDateFromOrderId(long orderId) {
        return new Date(getTimeMillsFromOrderId(orderId));
    }
}
