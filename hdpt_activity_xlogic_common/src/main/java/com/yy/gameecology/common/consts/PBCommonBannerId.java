package com.yy.gameecology.common.consts;


/**
 * desc:通用横幅id
 *
 * @createBy 曾文帜
 * @create 2021-11-13 13:08
 **/
public class PBCommonBannerId {
    /**
     * 用户过任务横幅id
     */
    public final static int USER_TASK = 2;

    /**
     * 宝箱横幅
     */
    public static final int SUPPER_WINNER = 3;

    /**
     * 高光任务横幅id
     */
    public final static int SUNSHINE_TASK = 4;


    /**
     * 高光任务，全站霸屏横幅id
     */
    public final static int SUPPER_SUNSHINE_TASK = 5;


    /**
     * 许愿瓶
     */
    public final static int WISHING_BOTTLE = 6;

    /**
     * 集齐祝福
     */
    public final static int COLLECT_AWARD = 7;

    /**
     * 2022宝贝520，产品左左将赛果广播改成一个界面展示所有结果，和永保定下面新ID，后面类似情况可以复用这个ID（但要和前端确认） - added by guoliping / 2022-04-19
     */
    public final static int MATCH_RESULT_ALL = 15;

    /**
     * 2022宝贝七夕，topN 需用户或主播在线才广播
     */
    public static final int ACT_TOP_N = 16;

    //霸屏横幅
    public static final int SUPPER_HONOR_BANNER = 17;

    public static final int ALL_USER_BANNER = 19;

    /**
     * 宝箱霸屏横幅ID
     * 组件ID+编号
     */
    public final static long BP_BANNER_ID = 5074001;

    /**
     * 宝箱横幅ID
     */
    public final static long BX_BANNER_ID = 5074002;

    /**
     * 宝箱中奖横幅更新ID
     */
    public final static long BX_ZJ_BANNER_ID = 5074003;

}
