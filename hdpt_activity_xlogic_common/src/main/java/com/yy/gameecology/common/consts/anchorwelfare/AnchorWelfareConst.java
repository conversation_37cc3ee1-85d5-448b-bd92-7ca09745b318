package com.yy.gameecology.common.consts.anchorwelfare;

import java.text.DecimalFormat;

public class AnchorWelfareConst {
    public static final String POPUP_TITLE = "主持专属福利";
    public static final String POPUP_MESSAGE = "恭喜成功到账{{money}}元，奖励以{{awardName}}形式发放";
    public static final String FRIEND_AWARD_NAME = "黄水晶";
    public static final String SKILL_CARD_AWARD_NAME = "黄钻";

    /**
     * 上座入口文案
     */
    public static final String PC_UP_SEAT_ROLL_TEXT_1 = "登录可领{{money}}元";
    public static final String PC_UP_SEAT_ROLL_TEXT_2 = "今日完成领{{money}}元";

    /**
     * 上座入口气泡
     */
    public static final String PC_UP_SEAT_TEXT_1 = "奖励即将过期，请尽快领取";
    public static final String PC_UP_SEAT_TEXT_2 = "今日完成可到账";
    public static final String PC_UP_SEAT_TEXT_3 = "你有{{money}}元即将过期";

    /**
     * 下座入口气泡
     */
    public static final String PC_DOWN_SEAT_TEXT_1 = "您有{{money}}元待领取";
    public static final String PC_DOWN_SEAT_TEXT_2 = "奖励即将过期，请尽快领取";
    public static final String PC_DOWN_SEAT_TEXT_3 = "今日完成Yo语音任务，可领{{money}}元";
    public static final String PC_DOWN_SEAT_TEXT_4 = "今日完成可到账";
    public static final String PC_DOWN_SEAT_TEXT_5 = "你有{{money}}元即将过期";


    /**
     * APP入口气泡
     */
    public static final String APP_TEXT_1 = "主持福利限时上线";
    public static final String APP_TEXT_2 = "今日完成可到账";
    public static final String APP_TEXT_3 = "你有{{money}}元奖励即将过期";



    /**
     * 活动总奖池消耗，单位厘
     */
    public static final String TASK_AWARD_POOL_AMOUNT = "task_award_pool_amount";

    /**
     * 数据需求 奖池每日发放 单位厘
     */
    public static final String AWARD_POOL_CONSUME_STATISTIC = "award_pool_consume_statistics";

    /**
     * 任务累计完成情况 不包含登录的
     */
    public static final String TASK_FINISH_COUNT_STATISTIC = "task_finish_count_statistics";

    /**
     * 白名单用户总人数统计
     */
    public static final String WHITE_USER_TOTAL_STATISTIC = "white_user_total_statistics";

    /**
     * 白名单用户 日新增统计
     */
    public static final String WHITE_USER_DAY_ADD_STATISTIC = "white_user_day_add_statistics:%s";





    public static String formatMoney(long awardNum) {
        DecimalFormat df = new DecimalFormat("#.#");
        return df.format((float)awardNum/1000);
    }
}
