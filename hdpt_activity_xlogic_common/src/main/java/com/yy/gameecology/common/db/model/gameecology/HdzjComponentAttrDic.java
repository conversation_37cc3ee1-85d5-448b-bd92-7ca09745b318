package com.yy.gameecology.common.db.model.gameecology;

import org.springframework.jdbc.core.RowMapper;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/28 9:43
 **/
public class HdzjComponentAttrDic implements java.io.Serializable, Cloneable {
    public static String TABLE_NAME = "hdzj_component_attr_dic";

    // java 属性字段 和 库表字段对应关系， key-java属性， val-表字段名
    public static Map<String, String> TABLE_FIELDS = new HashMap<>();

    static {
        TABLE_FIELDS.put("dropDownSource", "dropDownSource");
        TABLE_FIELDS.put("dropDownList", "dropDownList");
    }

    public static RowMapper<HdzjComponentAttrDic> ROW_MAPPER = (rs, rowNum) -> {
        HdzjComponentAttrDic entity = new HdzjComponentAttrDic();
        entity.setDropDownSource(rs.getString("dropDownSource"));
        entity.setDropDownList(rs.getString("dropDownList"));

        return entity;
    };

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{
            "dropDownSource"
    };

    public HdzjComponentAttrDic() {

    }

    private String dropDownSource;
    private String dropDownList;

    public String getDropDownSource() {
        return dropDownSource;
    }

    public void setDropDownSource(String dropDownSource) {
        this.dropDownSource = dropDownSource;
    }

    public String getDropDownList() {
        return dropDownList;
    }

    public void setDropDownList(String dropDownList) {
        this.dropDownList = dropDownList;
    }

    @Override
    public HdzjComponentAttrDic clone() {
        try {
            HdzjComponentAttrDic clone = (HdzjComponentAttrDic) super.clone();
            // TODO: copy mutable state here, so the clone can't change the internals of the original
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}
