package com.yy.gameecology.common.db.model.gameecology.pepc;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-01 21:56
 **/
@Data
@TableColumn(underline = true)
public class PepcPhaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "pepc_phase_info";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<PepcPhaseInfo> ROW_MAPPER = null;

    private Long id;
    /**
     * act_id
     */
    private Long actId;

    /**
     * 晋级规则
     * 类型1，代表按照比赛队伍数量，决定晋级队伍数，例如   1|{"0":10,"30":20} 代表，队伍>0队的时候晋级10队，对于>30队的时候晋级20队
     */
    private String advanceRule;

    /**
     * 总轮次
     */
    private Integer totalRound;

    /**
     * 阶段id
     */
    private Integer phaseId;

    /**
     * phase_name
     */
    private String phaseName;

    /**
     * state
     */
    private Integer state;

    /**
     * start_time
     */
    private Date startTime;

    /**
     * end_time
     */
    private Date endTime;

    /**
     * create_time
     */
    private Date createTime;
}
