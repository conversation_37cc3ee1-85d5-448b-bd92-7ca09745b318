
package com.yy.gameecology.common.report;

import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.support.SpringBeanAwareFactory;
import com.yy.gameecology.common.utils.Clock;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

/**
 *
 * @desc:数据上报的切面增强类
 *
 * <AUTHOR>
 *
 * @date: 2017年6月12日
 *
 */

@Component
@Aspect
public class ReportAspect {

    @Pointcut("execution(@com.yy.gameecology.common.annotation.Report * *(..))")
    public void methodCachePointcut() {}

    @Pointcut("execution(@com.yy.gameecology.common.annotation.Report *.new(..))")
    public void method2CachePointcut() {}

    @Around("methodCachePointcut() || method2CachePointcut()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        final Clock clock = new Clock();
        try {
            final Object result = pjp.proceed();
            clock.tag();
            afterInvoke(clock, pjp, result);
            return result;
        } catch (Throwable e) {
            clock.tag();
            afterCathchException(e, clock, pjp);
            throw e;
        }

    }

    private void afterInvoke(Clock clock, ProceedingJoinPoint pjp, Object result) {
        // 获取annotation指定的 process
        MethodSignature mehod = (MethodSignature)pjp.getSignature();
        Report annotation = mehod.getMethod().getAnnotation(Report.class);
        final Class<? extends ReportPolicy>[] policys = annotation.policy();

        // 循环process
        for (Class<? extends ReportPolicy> item : policys) {
            ReportPolicy bean = SpringBeanAwareFactory.getBean(item);
            bean.after(clock, pjp, result);
        }

    }

    private void afterCathchException(Throwable e, Clock clock, ProceedingJoinPoint pjp) {
        // 获取annotation指定的 process
        MethodSignature mehod = (MethodSignature)pjp.getSignature();
        Report annotation = mehod.getMethod().getAnnotation(Report.class);
        final Class<? extends ReportPolicy>[] policys = annotation.policy();

        // 循环process
        for (Class<? extends ReportPolicy> item : policys) {
            ReportPolicy bean = SpringBeanAwareFactory.getBean(item);
            bean.exceptionCaught(e, clock, pjp);
        }

    }
}
