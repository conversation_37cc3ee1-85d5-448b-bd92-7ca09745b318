package com.yy.gameecology.common.db.model.gameecology;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowMapper;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/28 14:29
 **/
public class HdzjComponentAttrDefine implements java.io.Serializable, Cloneable {
    private static final Logger log = LoggerFactory.getLogger(HdzjComponentAttrDefine.class);

    public static String TABLE_NAME = "hdzj_component_attr_define";

    // java 属性字段 和 库表字段对应关系， key-java属性， val-表字段名
    public static Map<String, String> TABLE_FIELDS = new HashMap<>();

    static {
        TABLE_FIELDS.put("cmptId", "cmpt_id");
        TABLE_FIELDS.put("propName", "prop_name");
        TABLE_FIELDS.put("pid", "pid");
        TABLE_FIELDS.put("labelText", "label_text");
        TABLE_FIELDS.put("valueType", "value_type");
        TABLE_FIELDS.put("propType", "prop_type");
        TABLE_FIELDS.put("placeholder", "placeholder");
        TABLE_FIELDS.put("createTime", "create_time");
        TABLE_FIELDS.put("updateTime", "update_time");
        TABLE_FIELDS.put("defaultValue", "default_value");
        TABLE_FIELDS.put("level", "level");
        TABLE_FIELDS.put("remark", "remark");
        TABLE_FIELDS.put("showOrder", "show_order");
        TABLE_FIELDS.put("extJson", "extJson");
        TABLE_FIELDS.put("useDialog", "useDialog");
    }

    public static RowMapper<HdzjComponentAttrDefine> ROW_MAPPER = (rs, rowNum) -> {
        HdzjComponentAttrDefine entity = new HdzjComponentAttrDefine();
        entity.setCmptId(rs.getInt("cmpt_id"));
        entity.setPropName(rs.getString("prop_name"));
        entity.setPid(rs.getString("pid"));
        entity.setLabelText(rs.getString("label_text"));
        entity.setValueType(rs.getString("value_type"));
        entity.setPropType(rs.getString("prop_type"));
        entity.setPlaceholder(rs.getString("placeholder"));
        entity.setCreateTime(rs.getDate("create_time"));
        entity.setUpdateTime(rs.getDate("update_time"));
        entity.setDefaultValue(rs.getString("default_value"));
        entity.setLevel(rs.getInt("level"));
        entity.setRemark(rs.getString("remark"));
        entity.setShowOrder(rs.getInt("show_order"));
        entity.setExtJson(rs.getString("extJson"));
        entity.setUseDialog(rs.getInt("useDialog"));

        return entity;
    };

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{
            "cmpt_id", "prop_name", "pid"
    };

    public HdzjComponentAttrDefine() {

    }

    private Integer cmptId;
    private String propName;
    private String pid;
    private String labelText;
    private String valueType;
    private String propType;
    private String placeholder;
    private Date createTime;
    private Date updateTime;
    private String defaultValue;
    private Integer level;
    private String remark;
    private Integer showOrder;
    private String extJson;
    private Integer useDialog;

    public Integer getCmptId() {
        return cmptId;
    }

    public void setCmptId(Integer cmptId) {
        this.cmptId = cmptId;
    }

    public String getPropName() {
        return propName;
    }

    public void setPropName(String propName) {
        this.propName = propName;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getLabelText() {
        return labelText;
    }

    public void setLabelText(String labelText) {
        this.labelText = labelText;
    }

    public String getValueType() {
        return valueType;
    }

    public void setValueType(String valueType) {
        this.valueType = valueType;
    }

    public String getPropType() {
        return propType;
    }

    public void setPropType(String propType) {
        this.propType = propType;
    }

    public String getPlaceholder() {
        return placeholder;
    }

    public void setPlaceholder(String placeholder) {
        this.placeholder = placeholder;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getShowOrder() {
        return showOrder;
    }

    public void setShowOrder(Integer showOrder) {
        this.showOrder = showOrder;
    }

    public String getExtJson() {
        return extJson;
    }

    public void setExtJson(String extJson) {
        this.extJson = extJson;
    }

    public Integer getUseDialog() {
        return useDialog;
    }

    public void setUseDialog(Integer useDialog) {
        this.useDialog = useDialog;
    }
}
