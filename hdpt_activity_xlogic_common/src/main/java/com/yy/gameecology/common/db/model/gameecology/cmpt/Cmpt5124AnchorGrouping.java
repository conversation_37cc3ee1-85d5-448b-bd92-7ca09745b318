package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Getter;
import lombok.Setter;
import org.springframework.jdbc.core.RowMapper;

@Getter
@Setter
@TableColumn(underline = true)
public class Cmpt5124AnchorGrouping {

    public static final String TABLE_NAME = "cmpt_5124_anchor_grouping";

    public static final String[] TABLE_PKS = new String[]{"id"};

    public static final RowMapper<Cmpt5124AnchorGrouping> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt5124AnchorGrouping result = new Cmpt5124AnchorGrouping();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setDateCode(rs.getString("date_code"));
        result.setRoleId(rs.getInt("role_id"));
        result.setCamp(rs.getInt("camp"));
        result.setUid(rs.getLong("uid"));
        result.setSeatId(rs.getInt("seat_id"));

        return result;
    };

    protected Long id;

    protected Long actId;

    protected String dateCode;

    protected Integer roleId;

    protected Integer camp;

    protected Long uid;

    protected Integer seatId;
}
