package com.yy.gameecology.common.db.model.gameecology;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;

@Data
@TableColumn(underline = true)
public class ComponentWhitelist implements Serializable {

    public static String TABLE_NAME = "hdzj_component_whitelist";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"act_id", "cmpt_use_inx","config_value"};

    public static RowMapper<ActTaskScore> ROW_MAPPER = null;

    protected long actId;

    protected long cmptUseInx;

    protected String member;

    protected String configValue;
}
