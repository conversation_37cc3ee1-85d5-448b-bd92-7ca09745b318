/**
 * GeBridgeHelper.java / 2019年8月30日 下午4:30:28
 * <p>
 * Copyright (c) 2019, YY Inc. All Rights Reserved.
 * <p>
 * 郭立平[<EMAIL>]
 */
package com.yy.gameecology.common.support;

import com.yy.gameecology.common.utils.EncryptUtil;
import com.yy.gameecology.common.utils.JsonUtil;
import com.yy.gameecology.common.utils.SystemUtil;
import com.yy.thrift.gameecology_bridge.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019年8月30日 下午4:30:28
 */
public class GeBridgeHelper {

    private static final Logger log = LoggerFactory.getLogger(GeBridgeHelper.class);

    public static final String SHA256_KEY = SysEvHelper.isDeploy() ? "9f7d63c3e665906615338981e1ca46fc" : "8451c0ddaf8b381d67b588f2654f5133";

    /**
     * 组装签名源串
     * <AUTHOR>
     * @date 2019年9月2日 下午3:01:05
     */
    public static final String src(StringRequest request) {
        String src = String.format("%s|%s|%s|%s|%s|%s|%s|%s", request.getSeq() == null ? "" : request.getSeq(), request.getRid(), request.getFid(),
                request.getData() == null ? "" : request.getData(), request.getIp() == null ? "" : request.getIp(), request.getTs(), request.getSource(),
                request.getExtjson() == null ? "" : request.getExtjson());
        return src;
    }

    /**
     * 计算 sha256 值
     * <AUTHOR>
     * @date 2019年9月2日 下午3:22:39
     */
    public static final String sha256(StringRequest request) {
        String src = src(request);
        log.info("sha256 with src:{}, sha256Key:{}", src, SHA256_KEY);
        String my = EncryptUtil.getSHA_256(SHA256_KEY + "|" + src).toLowerCase();
        return my;
    }

    /**
     * 签名验证: 消息签名算法= SHA256(key|seq|rid|fid|data|ip|ts|source|extjson), key 两边约定
     * <AUTHOR>
     * @date 2019年9月2日 下午2:53:16
     */
    public static final boolean verify(StringRequest request) {
        String src = src(request);
        String your = request.getSha256().toLowerCase();
        String my = EncryptUtil.getSHA_256(SHA256_KEY + "|" + src).toLowerCase();
        boolean flag = your.equals(my);
        if (!flag) {
            log.error("StringRequest SHA256 verify fail@your:{}, my:{}, src:{}", your, my, src(request));
        }
        return flag;
    }

    /**
     * 签名验证: 组装签名源串
     * <AUTHOR>
     * @date 2019年9月2日 下午3:01:05
     */
    public static final String src(BinaryRequest request) {
        String hex = EncryptUtil.bytes2Hex(request.getData()).toLowerCase();
        String src = String.format("%s|%s|%s|%s|%s|%s|%s|%s", request.getSeq(), request.getRid(), request.getFid(), hex,
                request.getIp(), request.getTs(), request.getSource(), request.getExtjson());
        return src;
    }

    /**
     * 计算 sha256 值
     * <AUTHOR>
     * @date 2019年9月2日 下午3:22:39
     */
    public static final String sha256(BinaryRequest request) {
        String src = src(request);
        String my = EncryptUtil.getSHA_256(SHA256_KEY + "|" + src).toLowerCase();
        return my;
    }

    /**
     * 消息签名算法= SHA256(key|seq|rid|fid|lowercase(hex(data))|ip|ts|source|extjson), key 两边约定， lowercase(hex(data))=十六进制小写字符串
     * <AUTHOR>
     * @date 2019年9月2日 下午2:53:26
     */
    public static final boolean verify(BinaryRequest request) {
        String src = src(request);
        String your = request.getSha256().toLowerCase();
        String my = EncryptUtil.getSHA_256(SHA256_KEY + "|" + src).toLowerCase();
        boolean flag = your.equals(my);
        if (!flag) {
            log.error("BinaryRequest SHA256 verify fail@your:{}, my:{}, src:{}", your, my, src);
        }
        return flag;
    }

    /**
     * <AUTHOR>
     * @date 2019年8月30日 下午4:26:53
     */
    public static String toString(long suid, long uid, long sid, long ssid, Map<String, String> extdat,
                                  StringRequest request) {
        return String.format("suid:%s, uid:%s, sid:%s, ssid:%s, extdat:%s, request:%s", suid, uid, sid, ssid,
                JsonUtil.toJson(extdat), toString(request));
    }

    /**
     * <AUTHOR>
     * @date 2019年8月30日 下午4:37:11
     */
    public static String toString(StringResponse response) {
        if(response==null) {
            return "";
        }
        return String.format("StringResponse[seq=%s, rid=%s, fid=%s, ts=%s, result=%s, reason=%s, data=%s, extjson=%s]",
            response.getSeq(), response.getRid(), response.getFid(), response.getTs(), response.getResult(),
            response.getReason(), response.getData(), response.getExtjson());
    }

    /**
     * <AUTHOR>
     * @date 2019年8月30日 下午4:37:11
     */
    public static String toString(StringRequest request) {
        if(request==null) {
            return "";
        }
        return String.format("StringRequest[seq=%s, rid=%s, fid=%s, ip=%s, ts=%s, source=%s, sha256=%s, data=%s, extjson=%s]",
            request.getSeq(), request.getRid(), request.getFid(), request.getIp(), request.getTs(), request.getSource(),
            request.getSha256(), request.getData(), request.getExtjson());
    }

    /**
     * <AUTHOR>
     * @date 2019年8月30日 下午4:38:22
     */
    public static String toString(BinaryRequest request) {
        if(request==null) {
            return "";
        }
        byte[] data = request.getData();
        String hex = data==null ? null :  EncryptUtil.bytes2Hex(data);
        return String.format("BinaryRequest[seq=%s, rid=%s, fid=%s, ip=%s, ts=%s, source=%s, sha256=%s, data=%s, extjson=%s]",
            request.getSeq(), request.getRid(), request.getFid(), request.getIp(), request.getTs(), request.getSource(),
            request.getSha256(), hex, request.getExtjson());
    }

    /**
     * <AUTHOR>
     * @date 2019年8月30日 下午4:38:48
     */
    public static Object toString(BinaryResponse response) {
        if(response==null) {
            return "";
        }
        byte[] data = response.getData();
        String hex = data==null ? null :  EncryptUtil.bytes2Hex(data);
        return String.format("BinaryResponse[seq=%s, rid=%s, fid=%s, ts=%s, result=%s, reason=%s, data=%s, extjson=%s]",
            response.getSeq(), response.getRid(), response.getFid(), response.getTs(), response.getResult(),
            response.getReason(), hex, response.getExtjson());
    }

    /**
     * <AUTHOR>
     * @date 2019年8月30日 下午4:42:20
     */
    public static String toString(long suid, StringRequest request) {
        return String.format("suid:%s, request:%s", suid, toString(request));
    }

    /**
     * <AUTHOR>
     * @date 2019年8月30日 下午4:43:50
     */
    public static String toString(List<Long> receiveUids, long sid, StringRequest request) {
        return String.format("receiveUids:%s, sid:%s, request:%s", JsonUtil.toJson(receiveUids), sid,
               toString(request));
    }

    /**
     * <AUTHOR>
     * @date 2019年8月30日 下午4:44:42
     */
    public static String toStringA(long uid, long sid, StringRequest request) {
        return toString(request);
    }

    /**
     * <AUTHOR>
     * @date 2019年8月30日 下午4:46:01
     */
    public static String toString(List<Long> receiveSuids, StringRequest request) {
        return String.format("receiveSuids:%s, request:%s", JsonUtil.toJson(receiveSuids), toString(request));
    }

    /**
     * <AUTHOR>
     * @date 2019年8月30日 下午4:48:52
     */
    public static String toStringB(long sid, long ssid, StringRequest request) {
        return String.format("sid:%s, sid:%s, ssid:%s", sid, ssid, toString(request));
    }

    /**
     * <AUTHOR>
     * @date 2019年8月30日 下午4:51:03
     */
    public static String toStringC(List<TopSubChannel> ssids, StringRequest request) {
        return String.format("ssids:%s, request:%s", JsonUtil.toJson(ssids), toString(request));
    }

    /**
     * <AUTHOR>
     * @date 2019年8月30日 下午4:52:49
     */
    public static String toStringD(List<Long> sids, StringRequest request) {
        return String.format("sids:%s, request:%s", JsonUtil.toJson(sids), toString(request));
    }

    /**
     * 制作简单响应
     */
    public static StringResponse makeResponse(String seq, int result, String reason, int rid, int fid) {
        StringResponse resp = new StringResponse();
        resp.setSeq(seq);
        resp.setResult(result);
        resp.setReason(reason);
        resp.setRid(rid);
        resp.setFid(fid);
        resp.setData("");
        resp.setTs(System.currentTimeMillis() / 1000);
        resp.setExtjson("");
        return resp;
    }

    /**
     * 制作简单请求
     */
    public static StringRequest makeRequest(String seq, int rid, int fid, int source, String jdata) {
        StringRequest req = new StringRequest();
        req.setSeq(seq);
        req.setRid(rid);
        req.setFid(fid);
        req.setData(jdata);
        req.setIp(SystemUtil.getIp());
        req.setTs(System.currentTimeMillis() / 1000);
        req.setSource(source);
        req.setExtjson("");
        req.setSha256(EncryptUtil.getSHA_256(SHA256_KEY + "|" + src(req)).toLowerCase());
        return req;
    }

    /**
     * 制作简单响应
     */
    public static BinaryResponse makeBinaryResponse(String seq, int result, String reason, int rid, int fid) {
        BinaryResponse resp = new BinaryResponse();
        resp.setSeq(seq);
        resp.setResult(0);
        resp.setReason(reason);
        resp.setRid(rid);
        resp.setFid(fid);
        resp.setData(new byte[]{});
        resp.setTs(System.currentTimeMillis() / 1000);
        resp.setExtjson("");
        return resp;
    }

    /**
     * 制作简单请求
     */
    public static BinaryRequest makeBinaryRequest(String seq, int rid, int fid, int source, byte[] data) {
        BinaryRequest req = new BinaryRequest();
        req.setSeq(seq);
        req.setRid(rid);
        req.setFid(fid);
        req.setData(data);
        req.setIp(SystemUtil.getIp());
        req.setTs(System.currentTimeMillis() / 1000);
        req.setSource(source);
        req.setExtjson("");
        req.setSha256(EncryptUtil.getSHA_256(SHA256_KEY + "|" + src(req)).toLowerCase());
        return req;
    }

    public static void main(String[] args) {
        System.out.println(toString(new StringRequest()));
        System.out.println(toString(new StringResponse()));
        BinaryRequest request = new BinaryRequest();
        request.setData(new byte[] {'a', 'b'});
        System.out.println(toString(request));
        System.out.println(toString(new BinaryResponse()));
    }
}
