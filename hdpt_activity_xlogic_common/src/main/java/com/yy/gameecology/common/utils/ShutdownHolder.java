package com.yy.gameecology.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.logging.LoggingSystem;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.core.Ordered;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @since 2024/3/8 18:16
 */
public class ShutdownHolder implements ApplicationContextAware, ApplicationListener<ContextClosedEvent>, Ordered {

    private static final Logger log = LoggerFactory.getLogger(ShutdownHolder.class);

    public ShutdownHolder() {
        log.info("app shutdown holder instance created");
    }

    private static volatile boolean shuttingDown;

    public static boolean isShuttingDown() {
        return shuttingDown;
    }

    private static LoggingSystem loggingSystem;

    private static final CountDownLatch SPRING_CONTEXT_CLOSED_LATCH = new CountDownLatch(1);

    /** 记录每个组件停止状态 */
    private static final Map<String, AtomicBoolean> COMPONENT_STOPPED_MAP = new ConcurrentHashMap<>();

    public static void addComponent(String componentId) {
        COMPONENT_STOPPED_MAP.put(componentId, new AtomicBoolean(false));
    }

    public static void markAsStopped(String componentId) {
        AtomicBoolean v = COMPONENT_STOPPED_MAP.get(componentId);
        if (v != null) {
            v.set(true);
        }
    }

    public static boolean isAllComponentStopped() {
        if (COMPONENT_STOPPED_MAP.isEmpty()) {
            return true;
        }
        for (AtomicBoolean v : COMPONENT_STOPPED_MAP.values()) {
            if (!v.get()) {
                return false;
            }
        }
        return true;
    }

    private static void consoleLog(String msg) {
        String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date());
        System.out.println(time + " " + msg);
    }

    private static void waitAllComponentStopped() {
        long startTime = System.currentTimeMillis();
        long maxWaitMs = Long.parseLong(System.getProperty("logging.shutdown.max-wait-ms", "30000"));
        long minWaitMs = Long.parseLong(System.getProperty("logging.shutdown.min-wait-ms", "5000"));
        log.info("app shutdown wait all component stop start maxWaitMs:{} minWaitMs:{}", maxWaitMs, minWaitMs);
        consoleLog(String.format("app shutdown wait all component stop start maxWaitMs:%d minWaitMs:%d", maxWaitMs, minWaitMs));
        while (!isAllComponentStopped() && (maxWaitMs <= 0 || System.currentTimeMillis() - startTime <= maxWaitMs)) {
            sleep(1000);
        }
        long waitComponentCost = System.currentTimeMillis() - startTime;
        if (waitComponentCost < minWaitMs) {
            sleep(minWaitMs - waitComponentCost);
        }
        log.info("app shutdown wait all component stop finished cost:{}", System.currentTimeMillis() - startTime);
        consoleLog(String.format("app shutdown all component stopped cost:%d", System.currentTimeMillis() - startTime));
    }

    private static void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            log.warn("app shutdown sleep interrupted {}", e.getMessage(), e);
        }
    }

    static {
        log.info("app shutdown hook register");
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            long startTime = System.currentTimeMillis();
            Runnable handler = loggingSystem == null ? null : loggingSystem.getShutdownHandler();
            log.info("app shutdown now, will delay exit logShutdownHook:{}", handler);
            consoleLog("app shutdown now, will delay exit  logShutdownHook:" + handler);
            shuttingDown = true;
            try {
                // 等待关闭
                SPRING_CONTEXT_CLOSED_LATCH.await();
                log.info("app shutdown wait spring context closed finished, will execute logShutdownHook {} cost:{}", handler, System.currentTimeMillis() - startTime);
                consoleLog(String.format("app shutdown wait spring context closed finished, will execute logShutdownHook %s cost:%d", handler, System.currentTimeMillis() - startTime));
                if (handler != null) {
                    long logStopStartTime = System.currentTimeMillis();
                    handler.run();
                    log.info("app shutdown log shutdown finished, cost:{}", System.currentTimeMillis() - logStopStartTime);
                    consoleLog(String.format("app shutdown log shutdown finished, cost:%d", System.currentTimeMillis() - logStopStartTime));
                }
            } catch (Exception e) {
                log.warn("shutting down wait error:{}", e.getMessage(), e);
            }
        }));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        try {
            log.info("app shutdown init loggingSystem bean");
            loggingSystem = applicationContext.getBean(LoggingSystem.class);
            log.info("app shutdown init loggingSystem bean finished, loggingSystem:{}", loggingSystem);
        } catch (BeansException e) {
            log.warn("loggingSystem not found");
        }
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("app shutdown receive spring context closed event");
            waitAllComponentStopped();
        } finally {
            log.info("app shutdown wait all component stopped finished, cost:{}", System.currentTimeMillis() - startTime);
            SPRING_CONTEXT_CLOSED_LATCH.countDown();
        }
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}
