package com.yy.gameecology.common.db.model.gameecology.pepc;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-03-31 21:26
 **/
@Data
@TableColumn(underline = true)
public class PepcTeamSchedule implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "pepc_team_schedule";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<PepcTeamSchedule> ROW_MAPPER = null;

    /**
     * id
     */
    private Long id;

    /**
     * act_id
     */
    private Long actId;

    /**
     * phase_id
     */
    private Integer phaseId;

    /**
     * group_code
     */
    private String groupCode;

    /**
     * team_id
     */
    private Long teamId;

    /**
     * 剔除流局，实际成功参与游戏的场次
     */
    private Integer gameAmount;

    /**
     * 已结算轮次
     */
    private Integer settleRound;

    /**
     * 用于计算晋级的分值
     */
    private Long score;

    /**
     * 0==初始状态 100==晋级失败 200==晋级成功
     */
    private Integer state;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * last_update_time
     */
    private Date lastUpdateTime;
}
