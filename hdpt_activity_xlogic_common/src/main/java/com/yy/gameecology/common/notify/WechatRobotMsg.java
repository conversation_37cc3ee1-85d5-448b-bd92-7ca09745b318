package com.yy.gameecology.common.notify;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * <AUTHOR> 2019/12/4
 */
public class WechatRobotMsg extends JSONObject {

    private WechatRobotMsg(){}

    public static TextMsgBuilder builder(){
        return new TextMsgBuilder();
    }

    public static MarkdownBuilder markdownBuilder(){
        return new MarkdownBuilder();
    }

    public static class TextMsgBuilder {

        private JSONObject data = new JSONObject();

        private TextMsgBuilder(){
        }

        public TextMsgBuilder content(String content){
            data.put("content", content);
            return this;
        }

        public TextMsgBuilder mentionedList(List<String> mentionedList){
            data.put("mentioned_list", mentionedList);
            return this;
        }

        public TextMsgBuilder mentionedMobileList(List<String> mentionedMobileList){
            data.put("mentioned_mobile_list", mentionedMobileList);
            return this;
        }

        public WechatRobotMsg build(){
            WechatRobotMsg msg = new WechatRobotMsg();
            msg.put("msgtype", "text");
            msg.put("text", data);
            return msg;
        }

    }

    public static class MarkdownBuilder {

        private JSONObject data = new JSONObject();

        private MarkdownBuilder(){
        }

        public MarkdownBuilder content(String content){
            data.put("content", content);
            return this;
        }

        public WechatRobotMsg build(){
            WechatRobotMsg msg = new WechatRobotMsg();
            msg.put("msgtype", "markdown");
            msg.put("markdown", data);
            return msg;
        }

    }

}
