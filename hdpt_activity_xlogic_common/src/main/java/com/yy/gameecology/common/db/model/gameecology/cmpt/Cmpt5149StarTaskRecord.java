package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Getter;
import lombok.Setter;
import org.springframework.jdbc.core.RowMapper;

import java.util.Date;

@Getter
@Setter
@TableColumn(underline = true)
public class Cmpt5149StarTaskRecord {

    public static final String TABLE_NAME = "cmpt_5149_star_task_record";

    public static final String[] TABLE_PKS = {"id"};

    public static final RowMapper<Cmpt5149StarTaskRecord> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt5149StarTaskRecord record = new Cmpt5149StarTaskRecord();
        record.setId(rs.getLong("id"));
        record.setActId(rs.getLong("act_id"));
        record.setTaskLevel(rs.getLong("task_level"));
        record.setMemberId(rs.getString("member_id"));
        record.setUid(rs.getLong("uid"));
        record.setAwardType(rs.getInt("award_type"));
        record.setTaskId(rs.getLong("task_id"));
        record.setPackageId(rs.getLong("package_id"));
        record.setState(rs.getInt("state"));
        record.setCreateTime(rs.getTimestamp("create_time"));
        record.setUpdateTime(rs.getTimestamp("update_time"));
        return record;
    };

    protected Long id;

    protected Long actId;

    protected Long taskLevel;

    protected String memberId;

    protected Long uid;

    /**
     * 0 - 正常奖励，1 - 备用奖励
     */
    protected Integer awardType;

    protected Long taskId;

    protected Long packageId;

    protected Integer state;

    protected Date createTime;

    protected Date updateTime;
}
