package com.yy.gameecology.common.db.model.gameecology.wzry;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-11 15:22
 **/
@Data
@TableColumn(underline = true)
public class WzryGameTeam implements Serializable {

   public static String TABLE_NAME = "wzry_game_team";

   // 表主键属性集合
   public static final String[] TABLE_PKS = new String[]{"id"};

   public static RowMapper<WzryGameTeam> ROW_MAPPER = null;
   /**
    * id
    */
   private Long id;

   private Long actId;

   /**
    * game_id
    */
   private Long gameId;

   /**
    * 参赛用户
    */
   private Long uid;

   /**
    * 座位号，1~5
    */
   private Integer seatId;

   /**
    * 该玩家所属阵营，1表示“A战队”，2表示“B战队”
    */
   private Integer limitTeam;

   /**
    * 1==在游戏房间内
    */
   private Integer inGame;

   /**
    * 记录最后房间状态的时间
    */
   private Date inGameRecordTime;

   /**
    * 参赛时间
    */
   private Date createTime;

   public WzryGameTeam() {}
}
