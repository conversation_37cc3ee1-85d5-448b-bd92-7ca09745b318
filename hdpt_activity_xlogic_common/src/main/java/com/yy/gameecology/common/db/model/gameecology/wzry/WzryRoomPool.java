package com.yy.gameecology.common.db.model.gameecology.wzry;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-11 15:24
 **/
@Data
@TableColumn(underline = true)
public class WzryRoomPool {

   public static String TABLE_NAME = "wzry_room_pool";

   // 表主键属性集合
   public static final String[] TABLE_PKS = new String[]{"id"};

   public static RowMapper<WzryRoomPool> ROW_MAPPER = null;
   /**
    * id
    */
   private Long id;

   /**
    * 顶级频道号
    */
   private Long sid;

   /**
    * 子频道号
    */
   private Long ssid;

   /**
    * 房间号
    */
   private Long roomNo;

   /**
    * 1==可用
    */
   private Integer state;

   /**
    * 创建时间
    */
   private Date createTime;

   /**
    * 最近使用时间
    */
   private Date lastUseTime;

   public WzryRoomPool() {}
}
