package com.yy.gameecology.common.db.mapper.aov;

import com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseRound;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface AovPhaseRoundExtMapper extends AovPhaseRoundMapper {

    AovPhaseRound selectRound(@Param("phaseId") long phaseId, @Param("roundNum") int roundNum);

    List<AovPhaseRound> selectRoundsByPhaseId(@Param("phaseId") long phaseId, @Param("state") Integer state);

    int updateRoundState(@Param("roundId") long roundId, @Param("sourceState") int sourceState, @Param("targetState") int targetState);

    List<AovPhaseRound> batchSelectByRoundIds(@Param("roundIds") Collection<Long> roundIds);

    int batchInsertOrUpdate(List<AovPhaseRound> issueJobDetails);

    List<AovPhaseRound> selectRounds(Long phaseId);
}
