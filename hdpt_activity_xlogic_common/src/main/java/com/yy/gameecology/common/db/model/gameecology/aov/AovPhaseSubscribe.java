package com.yy.gameecology.common.db.model.gameecology.aov;

import java.util.Date;

public class AovPhaseSubscribe {
    /**
     * 
     */
    private Long id;

    /**
     * 预约的那一期的活动ID
     */
    private Long prevActId;

    /**
     * 预约的那一期的phase_id
     */
    private Long prevPhaseId;

    /**
     * 
     */
    private Long uid;

    /**
     * 0-未发，1-已发
     */
    private Integer state;

    /**
     * 预约时间
     */
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPrevActId() {
        return prevActId;
    }

    public void setPrevActId(Long prevActId) {
        this.prevActId = prevActId;
    }

    public Long getPrevPhaseId() {
        return prevPhaseId;
    }

    public void setPrevPhaseId(Long prevPhaseId) {
        this.prevPhaseId = prevPhaseId;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}