
package com.yy.gameecology.common.db;

import com.yy.gameecology.common.utils.DynamicSqlHelper;

/**
 * 分表逻辑处理类
 * <AUTHOR>
 * @date 2018年8月31日 下午6:45:13
 */
public class SplitTableHelper {

    /**
     * 判断是否要做分表
     * <AUTHOR>
     * @date 2018年8月31日 下午6:45:03 
     */
    public static boolean isSplitTable() {
        return true;
    }

    //    /**
    //    //     * 计算 tbl_dragon_summon_bet_record 的分表
    //    //     */
    //    //    public static String toTableName(TblDragonSummonBetRecord o, String tableName) {
    //    //        if (!isSplitTable())
    //    //            return tableName;
    //    //
    //    //        if (o == null || o.getUid() == null) {
    //    //            throw new SuperException("ERR-401：分表UID不存在", SuperException.E_UNKNOWN);
    //    //        }
    //    //        return tableName + "_" + (o.getUid() % 10);
    //    //    }
    //
    //
    //
    //    /**
    //     * 计算 tbl_game_score_used_record 的分表
    //     */
    //    public static String toTableName(TblGameScoreUsedRecord o, String tableName) {
    //        if (!isSplitTable())
    //            return tableName;
    //
    //        if (o == null || o.getUid() == null) {
    //            throw new SuperException("ERR-401：分表UID不存在", SuperException.E_UNKNOWN);
    //        }
    //        return tableName + "_" + (o.getUid() % 10);
    //    }
    //
    //
        /**
         * <p>
         * 计算表名，用于分表等场合确定分表名
         *    优先考虑用户制定的表名 tableName，
         *    其次从 me 中提取（可根据字段值决定最后的表名）
         *    最后就是 clazz 关联的默认表名
         * </p>
         * <AUTHOR>
         * @date 2017年6月30日 下午6:16:47
         */
        public static String toTableName(String tableName, Object me, Class<?> clazz) {

            if (tableName != null && tableName.trim().length() > 0) {
                return tableName;
            }

            tableName = DynamicSqlHelper.getTABLE_NAME(clazz);

//            if (TblDragonSummonBetRecord.class.isAssignableFrom(clazz)) {
//                return toTableName((TblDragonSummonBetRecord) me, tableName);
//            }
//
//
//            if (TblGameScoreUsedRecord.class.isAssignableFrom(clazz)) {
//                return toTableName((TblGameScoreUsedRecord) me, tableName);
//            }

            return tableName;
        }

}
