package com.yy.gameecology.common.bean;


/**
 * desc:lua执行 redis hash一致性操作参数
 *
 * @createBy 曾文帜
 * @create 2021-08-10 21:56
 **/
public class HashIncParameter {
    private String key;
    private String field;
    private String inc;
    /**
     * 是否有操作后的数值限制，0-没限制 1-不得大于limit, -1不得小于limit
     */
    private String limitType = "0";
    private String limit = "0";

    public HashIncParameter() {

    }

    public HashIncParameter(String key, String field, long inc) {
        this.key = key;
        this.field = field;
        this.inc = String.valueOf(inc);
    }

    public HashIncParameter(String key, String field, String inc, String limitType, String limit) {
        this.key = key;
        this.field = field;
        this.inc = inc;
        this.limitType = limitType;
        this.limit = limit;

    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getInc() {
        return inc;
    }

    public void setInc(String inc) {
        this.inc = inc;
    }

    public String getLimitType() {
        return limitType;
    }

    public void setLimitType(String limitType) {
        this.limitType = limitType;
    }

    public String getLimit() {
        return limit;
    }

    public void setLimit(String limit) {
        this.limit = limit;
    }
}
