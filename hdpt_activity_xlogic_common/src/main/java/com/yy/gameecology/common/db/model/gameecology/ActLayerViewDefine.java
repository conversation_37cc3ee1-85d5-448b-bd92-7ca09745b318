package com.yy.gameecology.common.db.model.gameecology;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yy.gameecology.common.annotation.TableColumn;
import com.yy.gameecology.common.utils.StringUtil;
import org.springframework.jdbc.core.RowMapper;

import java.util.Date;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-08-30 18:21
 **/
@TableColumn(underline = true)
public class ActLayerViewDefine {
    public static String TABLE_NAME = "act_layer_view_define";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"act_id", "item_type_key"};

    public static RowMapper<ActLayerViewDefine> ROW_MAPPER = null;

    /**
     * act_id
     */
    private Long actId;

    /**
     * 挂件tab类型-com.yy.gameecology.common.consts.layeritemtypekey
     */
    private String itemTypeKey;

    /**
     * 活动中台角色类型
     */
    private Integer roleType;

    /**
     * tab排序，小的排前面
     */
    private Integer sort;

    /**
     * 扩展字段
     */
    private String extJson;

    /**
     * 1==配置有效
     */
    private Integer status;

    /**
     * 支持 陪玩的陪陪和接待指定role id，支持取top1 逻辑
     */
    private Long roleId;

    /**
     * 限制模板业务类型才展示，不配置默认不限制。多个的时候英文逗号隔开
     */
    private String busiIds;

    /**
     * 展示top n的成员,用于陪玩陪陪、团、接待等特殊场景
     */
    private Integer showTopN;

    /**
     * 开始展示时间，开始展示判断条件之一，一旦满足不展示，空的时候代表不限制
     */
    private Date startShowTime;

    /**
     * 结束展示时间，结束展示判断条件之一，一旦满足不展示，空的时候代表不限制
     */
    private Date endShowTime;

    /**
     * remark
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    public String getBusiIds() {
        return busiIds;
    }

    public boolean containsBusiId(long busiId) {
        //没配置的时候默认全业务
        if (StringUtil.isEmpty(this.busiIds)) {
            return true;
        }
        return ("," + this.busiIds + ",").contains("," + busiId + ",");
    }

    public int resolveMainRankRoleType(){
        if(StringUtil.isBlank(extJson)){
            return 0;
        }
        //TODO 改成自动解析扩展对象
        JSONObject jsonObject = JSON.parseObject(extJson);
        return jsonObject.getIntValue("mainRankRoleType");
    }

    public void setBusiIds(String busiIds) {
        this.busiIds = busiIds;
    }

    public Long getActId() {
        return actId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }

    public String getItemTypeKey() {
        return itemTypeKey;
    }

    public void setItemTypeKey(String itemTypeKey) {
        this.itemTypeKey = itemTypeKey;
    }

    public Integer getRoleType() {
        return roleType;
    }

    public void setRoleType(Integer roleType) {
        this.roleType = roleType;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getExtJson() {
        return extJson;
    }

    public void setExtJson(String extJson) {
        this.extJson = extJson;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getStartShowTime() {
        return startShowTime;
    }

    public void setStartShowTime(Date startShowTime) {
        this.startShowTime = startShowTime;
    }

    public Date getEndShowTime() {
        return endShowTime;
    }

    public void setEndShowTime(Date endShowTime) {
        this.endShowTime = endShowTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getShowTopN() {
        return showTopN;
    }

    public void setShowTopN(Integer showTopN) {
        this.showTopN = showTopN;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }
}
