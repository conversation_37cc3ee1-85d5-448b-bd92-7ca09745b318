package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-21 20:36
 **/
@Data
@TableColumn(underline = true)
public class Cmpt5150UserPuzzleState implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5150_user_puzzle_state";


    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt5150UserPuzzleState> ROW_MAPPER = null;

    /**
     * id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long actId;

    /**
     * cmptUseInx
     */
    private Integer cmptUseInx;

    /**
     * uid
     */
    private Long userUid;

    private Long anchorUid;

    /**
     * round
     */
    private Integer round;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * update_time
     */
    private Date updateTime;
}
