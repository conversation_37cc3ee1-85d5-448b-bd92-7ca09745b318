package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Getter;
import lombok.Setter;
import org.springframework.jdbc.core.RowMapper;

import java.util.Date;

@Getter
@Setter
@TableColumn(underline = true)
public class Cmpt2062LotteryBox {

    public static final String TABLE_NAME = "cmpt_2062_lottery_box";

    public static final String[] TABLE_PKS = new String[]{"id"};

    public static final RowMapper<Cmpt2062LotteryBox> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt2062LotteryBox result = new Cmpt2062LotteryBox();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getLong("cmpt_use_inx"));
        result.setSeq(rs.getString("seq"));
        result.setMemberId(rs.getString("member_id"));
        result.setSid(rs.getLong("sid"));
        result.setSsid(rs.getLong("ssid"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        result.setExpiredTime(rs.getTimestamp("expired_time"));
        return result;
    };

    protected Long id;

    protected Long actId;

    protected Long cmptUseInx;

    protected String seq;

    protected String memberId;

    protected Long sid;

    protected Long ssid;

    protected Date createTime;

    protected Date expiredTime;
}
