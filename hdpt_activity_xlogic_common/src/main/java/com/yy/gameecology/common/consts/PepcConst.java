package com.yy.gameecology.common.consts;

import com.google.common.collect.Sets;

import java.util.Set;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-01 11:14
 **/
public interface PepcConst {
    interface ActState {

        /**
         * 报名队伍不够，赛事取消
         */
        int GAME_CANCEL = 2;

        /**
         * 分组完成
         */
        int GROUP_COMPLETED = 3;

    }

    interface PhaseTeamState {
        int INIT = 0;

        int SUCC = 1;

        /**
         * 整个赛事 报名队伍不足
         */
        int FAIL = 2;

        /**
         * 当前队伍人数不够
         */
        int NO_ENOUGH = 3;

        int OVER_LIMIT =4;

    }

    interface PhaseId {
        /**
         * 报名
         */
        int SIGNUP = 0;
    }

    interface ActInfoField {
        String ACT_INFO_KEY = "actInfo";

        String ACT_STATE = "state";

        /**
         * 不存在时，用上一期的活动id
         */
        String RANK_ACT_ID = "rankActId";
    }

    interface PhaseInfoState {
        /**
         * 初始状态
         */
        int INIT = 0;

        /*
         *可以开始初始化数据
         * 第一阶段的默认值是100，其他阶段要等晋级完成，才能设置成100去初始化数据
         */
        int NEED_INIT_DATA = 100;


        /**
         * 初始化数据完毕,等待结算
         */
        int INIT_DATA_DONE = 200;

        /**
         * 结算完成
         */
        int SETTLE = 300;

        /**
         * 数据未完成初始化
         */
        Set<Integer> BEFORE_INIT_DATA = Sets.newHashSet(INIT,NEED_INIT_DATA);
    }

    interface GameState {
        int INIT = 0;

        int GAME_CREATING = 10;

        /**
         * 腾讯的比赛创建成功
         */
        int GAME_CREATED = 20;


        /**
         * 正常打完，游戏正常结束
         */
        int CLOSE = 70;

        /**
         * 没有腾讯的比赛结果
         */
        int NO_RESULT = 80;

        /**
         * 腾讯那边的比赛结果是销毁状态
         */
        int CANCEL_DESTROYED = 90;

        Set<Integer> FINAL_STATE = Sets.newHashSet(CLOSE, NO_RESULT, CANCEL_DESTROYED);
    }

    interface GameTeamState {
        int INIT = 0;

        int TEAM_CREATED = 20;

        int TEAM_SIGNED_UP = 30;
    }

    /**
     * 0-初始化，10-队伍已报名，用户报名中，20-报名成功 80-已出游戏结果
     */
    interface GameMemberState {
        int INIT = 0;

        /**
         * 10-队伍已报名，用户报名中
         */
        int TEAM_SIGN_UP = 10;

        /*
         *20-报名成功
         */
        int MEMBER_SIGN_UP = 20;

        /**
         * 腾讯有游戏结果
         */
        int RESULTED = 80;


    }

    interface PepcTeamScheduleState {
        /**
         * 初始
         */
        int INIT = 0;
        /**
         * 淘汰
         */
        int ELIMINATE = 100;
        /**
         * 晋级
         */
        int ADVANCE = 200;

    }

    interface TeamShowState {
        // 报名
        int SIGNING = 0;
        //        报名成功 等待开始
        int SIGN_SUCC = 1;

        //       人数不足 报名失败
        int SIGN_FAIL = 2;
        //        当前时间大于比赛时间 3 进入比赛
        int GAME_PLAYING = 3;
        //        等待赛果 4
        int WAIT_RESULT = 4;
        //        获胜，还有下场 5
        int NEXT_ROUND = 5;
        //        比赛结束 6
        int END = 6;
        //      赛事取消 7
        int CANCEL = 7;
        //        超过最大报名队伍数 失
        int SIGN_TOOLATE = 8; //超过最大报名队伍数
    }

    interface PepcTeamMsgAuditState {
        int INIT = 0;

        int PASS = 1;

        int REJECT = 2;
    }

    /**
     * 战队名称审核状态
     */
    interface TeamNameStatus {
        /**
         * 审核中，不可修改
         */
        int INIT = 0;
        /**
         * 审核通过
         */
        int PASS = 1;
        /**
         * 审核不通过
         */
        int NOT_PASS = 2;
    }

    interface PepcTeamRole {
        int MATE = 0;

        int CAPTAIN = 1;
    }

    interface PepcTeamApplyState {
        int INIT = 0;

        int REJECT = 1;

        int ACCEPT = 2;

        int CANCEL = 3;
    }

    interface AwardState {
        int GRANTED = 0;

        int SUBMITTED = 10;

        int FINISH = 20;

        int FAIL = 30;
    }

    interface AwardType {
        int FIRST_GAME = 1;

        int ROUND_AWARD = 2;

        int ROUND_EXTRA_AWARD = 3;
    }

    /**
     * <pre>
     * 腾讯那边的对局（battle）状态
     * 200
     * </pre>
     */
    interface BattleStatus {
        /**
         * 在准备结束的那一刻，有至少一个队伍点了准备，都会创建一个battle，并初始化成此状态。准备倒计时结束之前，battle都没有创建
         */
        int CREATED = 200;

        /**
         * 在腾讯进游戏倒计时结束前，至少两个队伍点了进游戏（点了进游戏就会拉起游戏）。腾讯那边会创建比赛房间，battle进入此状态。
         */
        int STARTED = 300;

        /**
         * 已结束，可能结算太快了，这个状态不易拿到
         */
        int CLOSED = 400;

        /**
         * 游戏没有开起来（终态）
         */
        int DESTROYED = 500;

        /**
         * 从400而来，完成结算（终态）
         */
        int SETTLED = 600;
    }


    /**
     * <pre>
     *     腾讯那边的对局里的用户的状态
     *     100->200->500：  正常打完
     *     100->300：  开赛前退出（点了“准备”，点了“进游戏”，但是在游戏房间倒计时结束的时候没有在游戏房间里 或 比赛没有开起来解散了）
     *     100： 未进过游戏（有点“准备”没点“进游戏”）
     *     没有对应的数据：没有点“准备”
     * </pre>
     */
    interface UserStatus {
        /**
         * 在准备结束那一刻，点了准备的队伍（队伍里至少一个人点了准备）。会创建一个状态为200的battle，点了准备的队伍的队伍都进入此初始状态
         */
        int CREATED = 100;

        /**
         * 在腾讯进游戏倒计时结束前，至少两个队伍点了进游戏（点了进游戏就会拉起游戏）。会创建对应的比赛房间。用户拉起游戏后会被分配到对应的游戏房间，进入此状态。
         */
        int JOINED = 200;

        /**
         * 在腾讯进游戏倒计时结束前，点过“进游戏”。但是中途退出了（在游戏房间倒计时结束的时候没有在游戏房间里），或者比赛没有开起来解散了。都会进入此状态
         */
        int LEFT = 300;

        /**
         * 这个状态没有看到，腾讯那边说可能被去掉了
         */
        int STARTED = 400;

        /**
         * 正常打完比赛后的正常比赛用户进入此状态
         */
        int CLOSED = 500;
    }

    interface GameLiveState {
        /**
         * 1--直播中
         */
        int LIVE = 1;

        /**
         * 2--直播回放
         */
        int RECORD = 2;

        /**
         * 直播未开始
         */
        int NOT_BEGIN = 3;
    }

    /**
     * 游戏状态
     */
    interface LiveGameState {
        /**
         * 1--比赛中
         */
        int PLAY_ING = 1;

        /**
         * 2--已结束
         */
        int END = 2;

        /**
         * 未开始
         */
        int NOT_BEGIN = 3;
    }

    /*
     *游戏对局状态
     */
    interface MyGameState {
        /**
         * 未开始
         */
        int NOT_STARTED = 0;

        /**
         * 即将开始
         */
        int SOON_TO_START = 100;

        /**
         * 进行中
         */
        int PLAY_ING = 200;

        /**
         * 结算中
         */
        int SETTLE = 300;

        /**
         * 已结束
         */
        int CLOSE = 400;
    }

    interface TeamCurrentGameState {
        /**
         * 用户未参赛或赛事未开始
         */
        int NOT_BEGIN = 100;

        /**
         * 赛事开始，可进入比赛
         */
        int ENTER_GAME = 200;

        /**
         * 有下一场比赛，按钮展示为【即将开始】
         */
        int WAIT_NEXT_GAME = 300;


        /**
         * 当介于中间态时（其他队伍没有完成比赛，未完成下一阶段对局分配），按钮展示为【等待赛果】
         */
        int SETTLE = 400;

        /**
         * 本期赛事流程走完时，按钮展示为【比赛已结束】| 被淘汰+全部已结束
         */
        int GAME_OVER = 900;

    }

    interface TeamApplyShowState {
        int PUBLIC = 0;

        int NEED_APPLY = 1;

        int APPLIED = 2;
    }


}
