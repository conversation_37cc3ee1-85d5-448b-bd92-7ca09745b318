package com.yy.gameecology.common.consts;


/**
 * desc:数据上报类型
 *
 * @createBy 曾文帜
 * @create 2021-11-15 21:18
 **/
public class BigDataScoreType {
    /**
     * 周星
     **/
    public static final int WEEK_STAR = 1;
    /**
     * 乱斗
     **/
    public static final int LD = 2;
    /**
     * 多人
     **/
    public static final int MULTI_PERSON = 3;
    /**
     * pk值（可能是0）
     **/
    public static final int PK_VALUE = 4;
    /**
     * 周星积分
     **/
    public static final int WEEK_STAR_POINT = 5;
    /**
     * 乱斗积分
     **/
    public static final int LD_POINT = 6;
    /**
     * 多人积分
     **/
    public static final int MULTI_PERSON_POINT = 7;
    /**
     * 用户成功加入战力团（ext是hdid）
     **/
    public static final int USER_ADDED_BATTLE_TEAM = 8;
    /**
     * 用户每日任务，0=每日全部任务 ext是任务id
     **/
    public static final int USER_DAILY_TASK = 9;
    /**
     * 战力团用户抽奖0=加入，1=全部， ext是hdid，extLong是包名，没有抽中是0
     **/
    public static final int BATTLE_TEAM_USER_LOTTERY = 10;
    /**
     * 11-还愿屋任务 socre是  任务索引，从0开始
     **/
    public static final int VOTIVE_HOUSE_TASK = 11;
    /**
     * 告白动效  socre是   broadcastType从2
     **/
    public static final int SHOW_LOVE = 12;
    /**
     * 主播任务，socre是级别，从1开始
     **/
    public static final int ANCHOR_TASK = 13;
    /**
     * 活动飞机 socre是礼物数量，ext是礼物id
     **/
    public static final int ACTIVITY_AIRPLANE = 14;
    /**
     * 里程碑
     **/
    public static final int MILESTONE = 15;
    /**
     * 领奖资格
     **/
    public static final int AWARD_QUALIFICATIONS = 16;
    /**
     * 追玩兑换专区，用户兑换余额增加记录
     **/
    public static final int ZHUIWAN_EXCHANGE = 17;
    /**
     * 高光横幅
     */
    public static final int SUNSHINE_TASK_BANNER = 100;
    /**
     * 高光横幅
     */
    public static final int RANK_SCORE_BANNER = 101;
    /**
     * 收集碎片
     * extInt 表示收集到得等级,1/2/3分别表示收集x/y/x个获得礼物)
     **/
    public static final int COLLECT_DEBRIS = 102;
    /**
     * 碎片兑换积分
     **/
    public static final int DEBRIS_EXCHANGE_POINT = 103;
    /**
     * 周星碎片
     **/
    public static final int WEEK_STAR_DEBRIS = 104;

    /**
     * 个人任务
     **/
    public static final int PERSONAL_TASK_SETTLE = 105;

    /**
     * 强厅任务奖励
     */
    public static final int SUB_CHANNEL_AWARD = 106;

    /**
     * 达成等级
     **/
    public static final int ACHIEVE_LEVEL = 504401;
    /**
     * 威望值
     **/
    public static final int PRESTIGE = 504402;
    /**
     * 经验值
     **/
    public static final int EXPERIENCE = 504403;
    /**
     * 获得紫水晶
     **/
    public static final int GET_AMETHYST = 504404;
    /**
     * 领取紫水晶
     **/
    public static final int RECEIVE_AMETHYST = 504405;

    public static final int GET_CHAOJIHAOBIAO = 504406;

    public static final int RECEIVE_CHAOJIHAOBIAO = 504407;
}
