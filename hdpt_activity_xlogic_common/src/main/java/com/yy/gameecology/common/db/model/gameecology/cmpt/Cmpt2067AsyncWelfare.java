package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Getter;
import lombok.Setter;
import org.springframework.jdbc.core.RowMapper;

import java.util.Date;

@Getter
@Setter
@TableColumn(underline = true)
public class Cmpt2067AsyncWelfare {

    public static final String TABLE_NAME = "cmpt_2067_async_welfare";

    public static final String[] TABLE_PKS = new String[]{"id"};

    public static final RowMapper<Cmpt2067AsyncWelfare> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt2067AsyncWelfare result = new Cmpt2067AsyncWelfare();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getLong("cmpt_use_inx"));
        result.setSeq(rs.getString("seq"));
        result.setBusiId(rs.getLong("busi_id"));
        result.setUid(rs.getLong("uid"));
        result.setTaskId(rs.getLong("task_id"));
        result.setTaskPackageIds(rs.getString("task_package_ids"));
        result.setExtLong(rs.getLong("ext_long"));
        result.setExtData(rs.getString("ext_data"));
        result.setState(rs.getInt("state"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        result.setUpdateTime(rs.getTimestamp("update_time"));
        return result;
    };

    protected Long id;

    protected Long actId;

    protected Long cmptUseInx;

    protected String seq;

    protected Long busiId;

    protected Long uid;

    protected Long taskId;

    protected String taskPackageIds;

    protected Long extLong;

    protected String extData;

    protected Integer state;

    protected Date createTime;

    protected Date updateTime;
}
