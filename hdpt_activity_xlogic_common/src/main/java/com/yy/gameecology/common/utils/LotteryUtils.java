package com.yy.gameecology.common.utils;

import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.util.*;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-12-14 11:57
 **/
public class LotteryUtils {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 例如 3分之一的概率中，则bingoRatio传入1，totalRatio传入3
     * @return true ===抽中
     */
    public static boolean lottery(int bingoRatio, int totalRatio) {
        int bingo = RandomUtils.nextInt(1, totalRatio + 1);
        return bingo > 0 && bingo <= bingoRatio;
    }


    /**
     * test code<br/>
     * <pre>
     *     {@code
     *     Map<String, Integer> candidates = Map.of("one", 100, "two", 200, "three", 300, "four", 400, "five", 500, "six", 600);
     *         Map<String, Long> stat = new HashMap<>(candidates.size());
     *         int total = candidates.values().stream().reduce(0, Integer::sum);
     *         final int totalTimes = 100000000;
     *         for (int i = 0; i < totalTimes; i++) {
     *             String hit = LotteryUtils.lottery(candidates);
     *             stat.compute(hit, (key, value) -> value == null ? 1 : value + 1);
     *         }
     *
     *         stat.forEach((key, value) -> System.out.println(key + ", rate: " + value * 100.0f / totalTimes + "%, expected: " + candidates.get(key) * 100.0f / total + "%"));
     *     }
     * </pre>
     *
     * @param candidates key: the candidate item, value: the weight
     * @return
     * @param <T>
     */
    public static <T> T lottery(Map<T, Integer> candidates) {
        Assert.notEmpty(candidates, "candidates can not be null");
        TreeMap<Integer, T> treeMap = new TreeMap<>();
        List<Map.Entry<T, Integer>> entries = new ArrayList<>(candidates.entrySet());
        Collections.shuffle(entries);
        int total = 0;
        for (Map.Entry<T, Integer> entry : entries) {
            total += entry.getValue();
            treeMap.put(total, entry.getKey());
        }

        int random = RandomUtils.nextInt(0, total);
        return treeMap.ceilingEntry(random).getValue();

    }
}
