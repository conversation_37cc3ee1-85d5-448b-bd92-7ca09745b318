package com.yy.gameecology.common.bean;

import com.alibaba.fastjson.JSON;
import com.yy.thrift.gameecology_bridge.StringResponse;

import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-02-05 11:28
 **/
public class SimpleResult {
    public int code;
    public String reason;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public StringResponse toStringResponse() {
        StringResponse response = new StringResponse();
        response.setReason(this.getReason());
        response.setResult(this.getCode());
        return response;
    }

    public static StringResponse genStringResponse(int result, String reason) {
        StringResponse response = new StringResponse();
        response.setReason(reason);
        response.setResult(result);
        return response;
    }

    public static StringResponse genStringResponse(int result, String reason, Map<String, Object> data) {
        StringResponse response = new StringResponse();
        response.setReason(reason);
        response.setResult(result);
        response.setData(JSON.toJSONString(data));
        return response;
    }

    public static StringResponse genStringResponse(int result, String reason, Object data) {
        StringResponse response = new StringResponse();
        response.setReason(reason);
        response.setResult(result);
        response.setData(data == null ? "" : JSON.toJSONString(data));
        return response;
    }
}
