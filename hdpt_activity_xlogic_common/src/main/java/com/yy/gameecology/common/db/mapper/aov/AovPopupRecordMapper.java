package com.yy.gameecology.common.db.mapper.aov;

import com.yy.gameecology.common.db.model.gameecology.aov.AovPopupRecord;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

public interface AovPopupRecordMapper {

    int insert(AovPopupRecord record);

    int countByDate(@Param("actId") long actId, @Param("uid") long uid, @Param("popupDate") LocalDate popupDate);

    int countByDates(@Param("actId") long actId, @Param("uid") long uid, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    int deleteExpiredRecords(@Param("expiredDate") LocalDate expiredDate);
}