/*
 * @(#)ParameterManager.java 2008/06/19
 *
 * Copyright (c) 欢聚时代
 *
 */

package com.yy.gameecology.common.base;

import com.yy.gameecology.common.CommDao;
import com.yy.gameecology.common.support.SpringBeanAwareFactory;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.sql.DataSource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.File;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * &#64;date 2008-06-19
 * &#64;author guolp
 *
 * &#64;功能说明：
 * 统一、一致、安全、高效的系统参数获取方式，详细的参数配置解释说明。
 * 让参数有一个一致的来源，使系统的配置功能更高效灵活。
 *
 * 完成库表字段、系统参数（保存在xml文件或数据库表中）管理功能的类仅有一个名为：ParameterManager，
 * 该类实现采用单件模式（Singleton Pattern ），另外为线程安全（线程访问同步，方便多线程环境使用）。
 * 本类的所有函数的执行逻辑必须严格，不能私自做容错处理，只要有错误就抛出异常，在抛出的异常中尽可能写清楚异常的可能原因。
 *
 * 本参数管理类 ParameterManager 仅具有读功能，不能增删改已有数据
 *
 * 忽略 remark 和 desc 属性，所以不能获取到属性名为 remark 和 desc 的值（忽略大小写）
 *
 * &#64;版本更新列表
 * 修改版本: 1.0.1
 * 修改日期：2009-01-22
 * 修改人 : guolp
 * 修改说明：支持 xml 配置文件中的元素值参数解密能力（元素值是经过本类提供的加密函数加密过的）
 * 复审人：
 *
 * 修改版本: 1.0.0
 * 修改日期：2008-06-19
 * 修改人 : guolp
 * 修改说明：形成初始版本
 * 复审人：
 * </pre>
 */

public class GeParameterManager {
    private static final Logger log = LoggerFactory.getLogger(GeParameterManager.class);

    /** ParameterManager的唯一实例 */
    private static GeParameterManager singleton = null;

    /** 判断表是否存在的定制SQL语句 */
    public static final String ORACLE_TABLE_EXIST_SQL =
        "SELECT COUNT(1) FROM all_tables where UPPER(OWNER)=UPPER(?) AND UPPER(TABLE_NAME)=UPPER(?)";

    public static final String MYSQL_TABLE_EXIST_SQL = "SELECT COUNT(1) FROM %s LIMIT 1";

    public static final String SQL_FORMAT =
        "select PID, CLAZZ , NAME , VALUE , ALIAS from %s order by CLAZZ, NAME, SHWORD ";

    /** 存放来自库表中的参数、字段静值对 */
    private Map<String, Map<String, String>> tableCache = new LinkedHashMap<String, Map<String, String>>();

    /** 存放来自xml文件中的属性路径值对（每个属性路径都唯一） */
    private Map<String, String> xmlAttrCache = new LinkedHashMap<String, String>();

    /** 存放来自xml文件中的元素路径值对（每个元素路径都唯一） */
    private Map<String, String> xmlElemCache = new LinkedHashMap<String, String>();

    /** 保存 xml 配置文件全路径名，重读或写时用到 */
    private String theXmlFilePath =
        File.separator + "env" + File.separator + SysEvHelper.getEnv() + File.separator + "parameter.xml";

    /** 表字段参数存放的表名，重读或写时用到 */
    private String theTableName = "ge_parameter";

    /** 初始化效果标志 */
    private boolean init_result = false;

    /**
     * 私有构造函数，只能通过 getInstance 获取该类的实例
     */
    private GeParameterManager() {
        init(theXmlFilePath, theTableName);
    }

    /**
     * ParameterManager 实例获取的唯一方法，若初始化失败，获取的实例为 null 如此设计的目的是为了尽早暴露参数配置问题，防止运行到中途而出错的情况发生
     *
     * @return 初始化成功时返回ParameterManager的唯一实例，否则返回null
     */
    public synchronized static GeParameterManager getInstance() {
        if (singleton == null) {
            singleton = new GeParameterManager();
        }

        if (singleton.init_result) {
            return singleton;
        }

        log.error("初始化参数管理器 ParameterManager 失败, 请改正配置文件，然后重启应用！");

        return null;
    }

    /**
     * ParameterManager在使用之前必须被初始化，ParameterManager可多次调用 init 进行初始化。
     *
     * @param xmlFilePath - 参数配置文件路径，为null时忽略之
     * @param tableName - 参数配置库表名字，为null时忽略之
     * @return true-初始化成功， false-初始化失败
     */
    synchronized public boolean init(String xmlFilePath, String tableName) {
        try {
            if (xmlFilePath != null) {
                this.theXmlFilePath = xmlFilePath;
                loadFromXml();
            }
            if (tableName != null) {
                this.theTableName = tableName;
                Connection conn = null;
                try {
                    conn = getRConnection();
                    loadFromTable(conn);
                } finally {
                    CommDao.closeConnection(conn);
                }
            }
            init_result = true;
        } catch (Exception e) {
            log.error("初始化参数管理器 ParameterManager 错误！-> ", e);
            init_result = false;
        }
        return init_result;
    }

    /**
     * ParameterManager在使用之前必须被初始化，ParameterManager可多次调用 init 进行初始化。
     *
     * @param xmlFilePath - 参数配置文件路径，为null时忽略之
     * @param tableName - 参数配置库表名字，为null时忽略之
     * @param conn - 参数配置库的链接
     * @return true-初始化成功， false-初始化失败
     */
    synchronized public boolean init(String xmlFilePath, String tableName, Connection conn) {
        try {
            if (xmlFilePath != null) {
                this.theXmlFilePath = xmlFilePath;
                loadFromXml();
            }
            if (tableName != null) {
                this.theTableName = tableName;
                loadFromTable(conn);
            }
            init_result = true;
        } catch (Exception e) {
            log.error("初始化参数管理器 ParameterManager 错误！-> ", e);
            init_result = false;
        }
        return init_result;
    }

    /**************************************************************
     * 取参数值函数簇
     **************************************************************/

    /**
     * 从库表中获取系统参数的取值-别名对集合（当库表名字为空或null时候表示是系统参数，也可视需要作其它解释）
     *
     * @param paramName - 参数名字
     * @return 指定参数名字下的取值集合的 value-alias Map 对象
     * @throws Exception
     */
    public Map<String, String> getParamValues(String paramName) throws Exception {
        return getTableFieldValues(null, paramName);
    }

    /**
     * 从库表中获取系统参数的第一个取值别名串（当库表名字为空或null时候表示是系统参数，也可视需要作其它解释）
     *
     * @param paramName - 参数名字
     * @return 指定参数名字下的取值集合的 value-alias Map 对象中的第一个 alias 串值
     * @throws Exception
     */
    public String getParamAlias(String paramName) throws Exception {
        Map<String, String> map = getTableFieldValues(null, paramName);
        if (map == null || map.isEmpty()) {
            throw new IllegalArgumentException("map 不能为 null 或 空!");
        }
        String value = map.keySet().iterator().next();
        return map.get(value);
    }

    /**
     * 从库表中获取系统参数的第一个取值字符串（当库表名字为空或null时候表示是系统参数，也可视需要作其它解释）
     *
     * @param paramName - 参数名字
     * @return 指定参数名字下的取值集合的 value-alias Map 对象中的第一个 value 串值
     * @throws Exception
     */
    public String getParamValue(String paramName) throws Exception {
        Map<String, String> map = getTableFieldValues(null, paramName);
        if (map == null || map.isEmpty()) {
            throw new IllegalArgumentException("map 不能为 null 或 空!");
        }
        String s = map.keySet().iterator().next();
        return s == null ? "" : s;
    }

    /**
     * 从库表中获取系统参数的第一个取值字符串（当库表名字为空或null时候表示是系统参数，也可视需要作其它解释）
     *
     * @param paramName - 参数名字
     * @param defaultVal - 取值异常时的缺省值
     * @return 指定参数名字下的取值集合的 value-alias Map 对象中的第一个 value 串值，取值异常时返回缺省值
     */
    public String getParamValue(String paramName, String defaultVal) {
        try {
            return getParamValue(paramName);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 从库表中获取系统参数的第一个取值串的 boolean 值（当库表名字为空或null时候表示是系统参数，也可视需要作其它解释）
     *
     * @param paramName - 参数名字
     * @return true - 取值串为 "true", "t", "yes", "y", "1"， false - 其它情况
     * @throws Exception
     */
    public boolean getParamValueToBoolean(String paramName) throws Exception {
        String s = getParamValue(paramName).toLowerCase();
        return "true".equals(s) || "t".equals(s) || "yes".equals(s) || "y".equals(s) || "1".equals(s);
    }

    /**
     * 从库表中获取系统参数的第一个取值串的 boolean 值（当库表名字为空或null时候表示是系统参数，也可视需要作其它解释）
     *
     * @param paramName - 参数名字
     * @param defaultVal - 取值异常时的缺省值
     * @return true - 取值串为 "true" 或 "yes"， false - 其它情况，取值异常时返回缺省值
     */
    public boolean getParamValueToBoolean(String paramName, boolean defaultVal) {
        try {
            return getParamValueToBoolean(paramName);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 从库表中获取系统参数的第一个取值串的 int 值（当库表名字为空或null时候表示是系统参数，也可视需要作其它解释）
     *
     * @param paramName - 参数名字
     * @return 取值串的 int 值
     * @throws Exception
     */
    public int getParamValueToInt(String paramName) throws Exception {
        return Integer.parseInt(getParamValue(paramName));
    }

    /**
     * 从库表中获取系统参数的第一个取值串的 int 值（当库表名字为空或null时候表示是系统参数，也可视需要作其它解释）
     *
     * @param paramName - 参数名字
     * @param defaultVal - 取值异常时的缺省值
     * @return 取值串的 int 值，取值异常时返回缺省值
     */
    public int getParamValueToInt(String paramName, int defaultVal) {
        try {
            String value = getParamValue(paramName);
            if(StringUtil.isEmpty(value)){
                return defaultVal;
            }
            return Integer.parseInt(value);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 从库表中获取系统参数的第一个取值串的 long 值（当库表名字为空或null时候表示是系统参数，也可视需要作其它解释）
     *
     * @param paramName - 参数名字
     * @return 取值串的 long 值
     * @throws Exception
     */
    public long getParamValueToLong(String paramName) throws Exception {
        return Long.parseLong(getParamValue(paramName));
    }

    /**
     * 从库表中获取系统参数的第一个取值串的 long 值（当库表名字为空或null时候表示是系统参数，也可视需要作其它解释）
     *
     * @param paramName - 参数名字
     * @param defaultVal - 取值异常时的缺省值
     * @return 取值串的 long 值，取值异常时返回缺省值
     */
    public long getParamValueToLong(String paramName, long defaultVal) {
        try {
            String value = getParamValue(paramName);
            if(StringUtil.isEmpty(value)){
                return defaultVal;
            }
            return Convert.toLong(value,defaultVal);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 从库表中获取系统参数的第一个取值串的 float 值（当库表名字为空或null时候表示是系统参数，也可视需要作其它解释）
     *
     * @param paramName - 参数名字
     * @return 取值串的 float 值
     * @throws Exception
     */
    public float getParamValueToFloat(String paramName) throws Exception {
        return Float.parseFloat(getParamValue(paramName));
    }

    /**
     * 从库表中获取系统参数的第一个取值串的 float 值（当库表名字为空或null时候表示是系统参数，也可视需要作其它解释）
     *
     * @param paramName - 参数名字
     * @param defaultVal - 取值异常时的缺省值
     * @return 取值串的 float 值，取值异常时返回缺省值
     */
    public float getParamValueToFloat(String paramName, float defaultVal) {
        try {
            return Float.parseFloat(getParamValue(paramName));
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 从库表中获取系统参数的第一个取值串的 double 值（当库表名字为空或null时候表示是系统参数，也可视需要作其它解释）
     *
     * @param paramName - 参数名字
     * @return 取值串的 double 值
     * @throws Exception
     */
    public double getParamValueToDouble(String paramName) throws Exception {
        return Double.parseDouble(getParamValue(paramName));
    }

    /**
     * 从库表中获取系统参数的第一个取值串的 double 值（当库表名字为空或null时候表示是系统参数，也可视需要作其它解释）
     *
     * @param paramName - 参数名字
     * @param defaultVal - 取值异常时的缺省值
     * @return 取值串的 double 值，取值异常时返回缺省值
     */
    public double getParamValueToDouble(String paramName, double defaultVal) {
        try {
            return Double.parseDouble(getParamValue(paramName));
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 从库表中获取库表字段静值的取值-别名对集合（当库表名字非空非null时表示是库表字段静值，也可视需要作其它解释）
     *
     * @param tableName - 库表名字，区分大小写
     * @param fieldName - 字段名字，区分大小写
     * @return 指定库表、字段名字下的取值集合的 value-alias Map 对象
     * @throws Exception
     */
    public Map<String, String> getTableFieldValues(String tableName, String fieldName) throws Exception {
        tableName = tableName == null ? "" : tableName;
        String key = String.format("%s.%s", tableName, fieldName);
        return tableCache.get(key);
    }

    /**
     * 从库表中获取库表字段静值的第一个取值别名串（当库表名字非空非null时表示是库表字段静值，也可视需要作其它解释）
     *
     * @param tableName - 库表名字，区分大小写
     * @param fieldName - 字段名字，区分大小写
     * @return 指定库表字段名字下的取值集合的 value-alias Map 对象中的第一个 alias 串值
     * @throws Exception
     */
    public String getTableFieldAlias(String tableName, String fieldName) throws Exception {
        tableName = tableName == null ? "" : tableName;
        String key = String.format("%s.%s", tableName, fieldName);
        Map<String, String> map = tableCache.get(key);
        if (map == null || map.isEmpty()) {
            throw new IllegalArgumentException("map 不能为 null 或 空!");
        }
        String value = map.keySet().iterator().next();
        return map.get(value);
    }

    /**
     * 从库表中获取库表字段静值的第一个取值字符串（当库表名字非空非null时表示是库表字段静值，也可视需要作其它解释）
     *
     * @param tableName - 库表名字，区分大小写
     * @param fieldName - 字段名字，区分大小写
     * @return 指定库表、字段名字下的取值集合的 value-alias Map 对象中的第一个 value 串值
     * @throws Exception
     */
    public String getTableFieldValue(String tableName, String fieldName) throws Exception {
        tableName = tableName == null ? "" : tableName;
        String key = String.format("%s.%s", tableName, fieldName);
        Map<String, String> map = tableCache.get(key);
        if (map == null || map.isEmpty()) {
            throw new IllegalArgumentException("map 不能为 null 或 空!");
        }

        String s = map.keySet().iterator().next();
        return s == null ? "" : s;
    }

    /**
     * 从库表中获取库表字段静值的第一个取值字符串（当库表名字非空非null时表示是库表字段静值，也可视需要作其它解释）
     *
     * @param tableName - 库表名字，区分大小写
     * @param fieldName - 字段名字，区分大小写
     * @param defaultVal - 取值异常时的缺省值
     * @return 指定库表、字段名字下的取值集合的 value-alias Map 对象中的第一个 value 串值，取值异常时返回缺省值
     */
    public String getTableFieldValue(String paramName, String fieldName, String defaultVal) {
        try {
            return getTableFieldValue(paramName, fieldName);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 从库表中获取库表字段静值的第一个取值字符串的 boolean 值（当库表名字非空非null时表示是库表字段静值，也可视需要作其它解释）
     *
     * @param tableName - 库表名字，区分大小写
     * @param fieldName - 字段名字，区分大小写
     * @return true - 取值串为 "true" 或 "yes"， false - 其它情况
     * @throws Exception
     */
    public boolean getTableFieldValueToBoolean(String tableName, String fieldName) throws Exception {
        String text = getTableFieldValue(tableName, fieldName);
        return "true".equalsIgnoreCase(text) || "yes".equalsIgnoreCase(text);
    }

    /**
     * 从库表中获取库表字段静值的第一个取值字符串的 boolean 值（当库表名字非空非null时表示是库表字段静值，也可视需要作其它解释）
     *
     * @param tableName - 库表名字，区分大小写
     * @param fieldName - 字段名字，区分大小写
     * @param defaultVal - 取值异常时的缺省值
     * @return true - 取值串为 "true" 或 "yes"， false - 其它情况，取值异常时返回缺省值
     */
    public boolean getTableFieldValueToBoolean(String tableName, String fieldName, boolean defaultVal) {
        try {
            return getTableFieldValueToBoolean(tableName, fieldName);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 从库表中获取库表字段静值的第一个取值字符串的 int 值（当库表名字非空非null时表示是库表字段静值，也可视需要作其它解释）
     *
     * @param tableName - 库表名字，区分大小写
     * @param fieldName - 字段名字，区分大小写
     * @return 取值字符串的 int 值
     * @throws Exception
     */
    public int getTableFieldValueToInt(String paramName, String fieldName) throws Exception {
        return Integer.parseInt(getTableFieldValue(paramName, fieldName));
    }

    /**
     * 从库表中获取库表字段静值的第一个取值字符串的 int 值（当库表名字非空非null时表示是库表字段静值，也可视需要作其它解释）
     *
     * @param tableName - 库表名字，区分大小写
     * @param fieldName - 字段名字，区分大小写
     * @param defaultVal - 取值异常时的缺省值
     * @return 取值字符串的 int 值，取值异常时返回缺省值
     */
    public int getTableFieldValueToInt(String paramName, String fieldName, int defaultVal) {
        try {
            return Integer.parseInt(getTableFieldValue(paramName, fieldName));
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 从库表中获取库表字段静值的第一个取值字符串的 long 值（当库表名字非空非null时表示是库表字段静值，也可视需要作其它解释）
     *
     * @param tableName - 库表名字，区分大小写
     * @param fieldName - 字段名字，区分大小写
     * @return 取值字符串的 long 值
     * @throws Exception
     */
    public long getTableFieldValueToLong(String paramName, String fieldName) throws Exception {
        return Long.parseLong(getTableFieldValue(paramName, fieldName));
    }

    /**
     * 从库表中获取库表字段静值的第一个取值字符串的 long 值（当库表名字非空非null时表示是库表字段静值，也可视需要作其它解释）
     *
     * @param tableName - 库表名字，区分大小写
     * @param fieldName - 字段名字，区分大小写
     * @param defaultVal - 取值异常时的缺省值
     * @return 取值字符串的 long 值，取值异常时返回缺省值
     */
    public long getTableFieldValueToLong(String paramName, String fieldName, long defaultVal) {
        try {
            return Long.parseLong(getTableFieldValue(paramName, fieldName));
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 从库表中获取库表字段静值的第一个取值字符串的 float 值（当库表名字非空非null时表示是库表字段静值，也可视需要作其它解释）
     *
     * @param tableName - 库表名字，区分大小写
     * @param fieldName - 字段名字，区分大小写
     * @return 取值字符串的 float 值
     * @throws Exception
     */
    public float getTableFieldValueToFloat(String paramName, String fieldName) throws Exception {
        return Float.parseFloat(getTableFieldValue(paramName, fieldName));
    }

    /**
     * 从库表中获取库表字段静值的第一个取值字符串的 float 值（当库表名字非空非null时表示是库表字段静值，也可视需要作其它解释）
     *
     * @param tableName - 库表名字，区分大小写
     * @param fieldName - 字段名字，区分大小写
     * @param defaultVal - 取值异常时的缺省值
     * @return 取值字符串的 float 值，取值异常时返回缺省值
     */
    public float getTableFieldValueToFloat(String paramName, String fieldName, float defaultVal) {
        try {
            return Float.parseFloat(getTableFieldValue(paramName, fieldName));
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 从库表中获取库表字段静值的第一个取值字符串的 double 值（当库表名字非空非null时表示是库表字段静值，也可视需要作其它解释）
     *
     * @param tableName - 库表名字，区分大小写
     * @param fieldName - 字段名字，区分大小写
     * @return 取值字符串的 double 值
     * @throws Exception
     */
    public double getTableFieldValueToDouble(String paramName, String fieldName) throws Exception {
        return Double.parseDouble(getTableFieldValue(paramName, fieldName));
    }

    /**
     * 从库表中获取库表字段静值的第一个取值字符串的 double 值（当库表名字非空非null时表示是库表字段静值，也可视需要作其它解释）
     *
     * @param tableName - 库表名字，区分大小写
     * @param fieldName - 字段名字，区分大小写
     * @param defaultVal - 取值异常时的缺省值
     * @return 取值字符串的 double 值，取值异常时返回缺省值
     */
    public double getTableFieldValueToDouble(String paramName, String fieldName, double defaultVal) {
        try {
            return Double.parseDouble(getTableFieldValue(paramName, fieldName));
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 获取xml配置文件元素路径上的字符串值
     *
     * @param elementNodesPath - 元素节点路径，'.' 号作为路径分隔符
     * @return - 指定元素路径上的取值字符串
     * @throws Exception
     */
    public String getXmlFileElementValue(String elementNodesPath) throws Exception {
        String s = xmlElemCache.get(elementNodesPath);
        return s == null ? "" : s;
    }

    /**
     * 获取xml配置文件元素路径上的字符串值
     *
     * @param elementNodesPath - 元素节点路径，'.' 号作为路径分隔符
     * @param defaultVal - 取值异常时的缺省值
     * @return - 指定元素路径上的取值字符串，取值异常时返回缺省值
     */
    public String getXmlFileElementValue(String elementNodesPath, String defaultVal) {
        try {
            return getXmlFileElementValue(elementNodesPath);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 获取xml配置文件元素路径上的字符串值的 boolean 值
     *
     * @param elementNodesPath - 元素节点路径，'.' 号作为路径分隔符
     * @return - true - "true" 或 "yes"，false - 其它情况
     * @throws Exception
     */
    public boolean getXmlFileElementValueToBoolean(String elementNodesPath) throws Exception {
        String text = getXmlFileElementValue(elementNodesPath);
        return "true".equalsIgnoreCase(text) || "yes".equalsIgnoreCase(text);
    }

    /**
     * 获取xml配置文件元素路径上的字符串值的 boolean 值
     *
     * @param elementNodesPath - 元素节点路径，'.' 号作为路径分隔符
     * @param defaultVal - 取值异常时的缺省值
     * @return - true - "true" 或 "yes"，false - 其它情况，取值异常时返回缺省值
     */
    public boolean getXmlFileElementValueToBoolean(String elementNodesPath, boolean defaultVal) {
        try {
            return getXmlFileElementValueToBoolean(elementNodesPath);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 获取xml配置文件元素路径上的字符串值的 int 值
     *
     * @param elementNodesPath - 元素节点路径，'.' 号作为路径分隔符
     * @return - 字符串值的 int 值
     * @throws Exception
     */
    public int getXmlFileElementValueToInt(String elementNodesPath) throws Exception {
        return Integer.parseInt(getXmlFileElementValue(elementNodesPath));
    }

    /**
     * 获取xml配置文件元素路径上的字符串值的 int 值
     *
     * @param elementNodesPath - 元素节点路径，'.' 号作为路径分隔符
     * @param defaultVal - 取值异常时的缺省值
     * @return - 字符串值的 int 值，取值异常时返回缺省值
     */
    public int getXmlFileElementValueToInt(String elementNodesPath, int defaultVal) {
        try {
            return Integer.parseInt(getXmlFileElementValue(elementNodesPath));
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 获取xml配置文件元素路径上的字符串值的 long 值
     *
     * @param elementNodesPath - 元素节点路径，'.' 号作为路径分隔符
     * @return - 字符串值的 long 值
     * @throws Exception
     */
    public long getXmlFileElementValueToLong(String elementNodesPath) throws Exception {
        return Long.parseLong(getXmlFileElementValue(elementNodesPath));
    }

    /**
     * 获取xml配置文件元素路径上的字符串值的 long 值
     *
     * @param elementNodesPath - 元素节点路径，'.' 号作为路径分隔符
     * @param defaultVal - 取值异常时的缺省值
     * @return - 字符串值的 long 值，取值异常时返回缺省值
     */
    public long getXmlFileElementValueToLong(String elementNodesPath, long defaultVal) {
        try {
            return Long.parseLong(getXmlFileElementValue(elementNodesPath));
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 获取xml配置文件元素路径上的字符串值的 float 值
     *
     * @param elementNodesPath - 元素节点路径，'.' 号作为路径分隔符
     * @return - 字符串值的 float 值
     * @throws Exception
     */
    public float getXmlFileElementValueToFloat(String elementNodesPath) throws Exception {
        return Float.parseFloat(getXmlFileElementValue(elementNodesPath));
    }

    /**
     * 获取xml配置文件元素路径上的字符串值的 float 值
     *
     * @param elementNodesPath - 元素节点路径，'.' 号作为路径分隔符
     * @param defaultVal - 取值异常时的缺省值
     * @return - 字符串值的 float 值，取值异常时返回缺省值
     */
    public float getXmlFileElementValueToFloat(String elementNodesPath, float defaultVal) {
        try {
            return Float.parseFloat(getXmlFileElementValue(elementNodesPath));
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 获取xml配置文件元素路径上的字符串值的 double 值
     *
     * @param elementNodesPath - 元素节点路径，'.' 号作为路径分隔符
     * @return - 字符串值的 double 值
     * @throws Exception
     */
    public double getXmlFileElementValueToDouble(String elementNodesPath) throws Exception {
        return Double.parseDouble(getXmlFileElementValue(elementNodesPath));
    }

    /**
     * 获取xml配置文件元素路径上的字符串值的 double 值
     *
     * @param elementNodesPath - 元素节点路径，'.' 号作为路径分隔符
     * @param defaultVal - 取值异常时的缺省值
     * @return - 字符串值的 double 值，取值异常时返回缺省值
     */
    public double getXmlFileElementValueToDouble(String elementNodesPath, double defaultVal) {
        try {
            return Double.parseDouble(getXmlFileElementValue(elementNodesPath));
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 获取xml配置文件属性路径上的字符串值
     *
     * @param attributeNodesPath - 属性节点路径，'.' 号作为路径分隔符，路径末尾为属性名，其余为元素名
     * @return 指定属性路径上的取值字符串
     * @throws Exception
     */
    public String getXmlFileAttributeValue(String attributeNodesPath) throws Exception {
        String s = xmlAttrCache.get(attributeNodesPath);
        return s == null ? "" : s;
    }

    /**
     * 获取xml配置文件属性路径上的字符串值
     *
     * @param attributeNodesPath - 属性节点路径，'.' 号作为路径分隔符，路径末尾为属性名，其余为元素名
     * @param defaultVal - 取值异常时的缺省值
     * @return 指定属性路径上的取值字符串，取值异常时返回缺省值
     */
    public String getXmlFileAttributeValue(String attributeNodesPath, String defaultVal) {
        try {
            return getXmlFileAttributeValue(attributeNodesPath);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 获取xml配置文件属性路径上的字符串值的 boolean 值
     *
     * @param attributeNodesPath - 属性节点路径，'.' 号作为路径分隔符，路径末尾为属性名，其余为元素名
     * @return true - "true" 或 "yes"，false - 其它情况
     * @throws Exception
     */
    public boolean getXmlFileAttributeValueToBoolean(String attributeNodesPath) throws Exception {
        String text = getXmlFileAttributeValue(attributeNodesPath);
        return "true".equalsIgnoreCase(text) || "yes".equalsIgnoreCase(text);
    }

    /**
     * 获取xml配置文件属性路径上的字符串值的 boolean 值
     *
     * @param attributeNodesPath - 属性节点路径，'.' 号作为路径分隔符，路径末尾为属性名，其余为元素名
     * @param defaultVal - 取值异常时的缺省值
     * @return true - "true" 或 "yes"，false - 其它情况，取值异常时返回缺省值
     */
    public boolean getXmlFileAttributeValueToBoolean(String attributeNodesPath, boolean defaultVal) {
        try {
            return getXmlFileAttributeValueToBoolean(attributeNodesPath);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 获取xml配置文件属性路径上的字符串值的 int 值
     *
     * @param attributeNodesPath - 属性节点路径，'.' 号作为路径分隔符，路径末尾为属性名，其余为元素名
     * @return 取值字符串的 int 值
     * @throws Exception
     */
    public int getXmlFileAttributeValueToInt(String attributeNodesPath) throws Exception {
        return Integer.parseInt(getXmlFileAttributeValue(attributeNodesPath));
    }

    /**
     * 获取xml配置文件属性路径上的字符串值的 int 值
     *
     * @param attributeNodesPath - 属性节点路径，'.' 号作为路径分隔符，路径末尾为属性名，其余为元素名
     * @param defaultVal - 取值异常时的缺省值
     * @return 取值字符串的 int 值，取值异常时返回缺省值
     */
    public int getXmlFileAttributeValueToInt(String attributeNodesPath, int defaultVal) {
        try {
            return Integer.parseInt(getXmlFileAttributeValue(attributeNodesPath));
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 获取xml配置文件属性路径上的字符串值的 long 值
     *
     * @param attributeNodesPath - 属性节点路径，'.' 号作为路径分隔符，路径末尾为属性名，其余为元素名
     * @return 取值字符串的 long 值
     * @throws Exception
     */
    public long getXmlFileAttributeValueToLong(String attributeNodesPath) throws Exception {
        return Long.parseLong(getXmlFileAttributeValue(attributeNodesPath));
    }

    /**
     * 获取xml配置文件属性路径上的字符串值的 long 值
     *
     * @param attributeNodesPath - 属性节点路径，'.' 号作为路径分隔符，路径末尾为属性名，其余为元素名
     * @param defaultVal - 取值异常时的缺省值
     * @return 取值字符串的 long 值，取值异常时返回缺省值
     */
    public long getXmlFileAttributeValueToLong(String attributeNodesPath, long defaultVal) {
        try {
            return Long.parseLong(getXmlFileAttributeValue(attributeNodesPath));
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 获取xml配置文件属性路径上的字符串值的 float 值
     *
     * @param attributeNodesPath - 属性节点路径，'.' 号作为路径分隔符，路径末尾为属性名，其余为元素名
     * @return 取值字符串的 float 值
     * @throws Exception
     */
    public float getXmlFileAttributeValueToFloat(String attributeNodesPath) throws Exception {
        return Float.parseFloat(getXmlFileAttributeValue(attributeNodesPath));
    }

    /**
     * 获取xml配置文件属性路径上的字符串值的 float 值
     *
     * @param attributeNodesPath - 属性节点路径，'.' 号作为路径分隔符，路径末尾为属性名，其余为元素名
     * @param defaultVal - 取值异常时的缺省值
     * @return 取值字符串的 float 值，取值异常时返回缺省值
     */
    public float getXmlFileAttributeValueToFloat(String attributeNodesPath, float defaultVal) {
        try {
            return Float.parseFloat(getXmlFileAttributeValue(attributeNodesPath));
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**
     * 获取xml配置文件属性路径上的字符串值的 double 值
     *
     * @param attributeNodesPath - 属性节点路径，'.' 号作为路径分隔符，路径末尾为属性名，其余为元素名
     * @return 取值字符串的 double 值
     * @throws Exception
     */
    public double getXmlFileAttributeValueToDouble(String attributeNodesPath) throws Exception {
        return Double.parseDouble(getXmlFileAttributeValue(attributeNodesPath));
    }

    /**
     * 获取xml配置文件属性路径上的字符串值的 double 值
     *
     * @param attributeNodesPath - 属性节点路径，'.' 号作为路径分隔符，路径末尾为属性名，其余为元素名
     * @param defaultVal - 取值异常时的缺省值
     * @return 取值字符串的 double 值，取值异常时返回缺省值
     */
    public double getXmlFileAttributeValueToDouble(String attributeNodesPath, double defaultVal) {
        try {
            return Double.parseDouble(getXmlFileAttributeValue(attributeNodesPath));
        } catch (Exception e) {
            return defaultVal;
        }
    }

    /**************************************************************
     * 重新装载参数值函数簇
     **************************************************************/

    /**
     * 清除原来的库表参数，重新装载来自库表部分的参数
     */
    synchronized public void reloadOnlyTable() throws Exception {
        if (theTableName != null && theTableName.trim().length() > 0) {
            Connection conn = null;
            try {
                conn = getRConnection();
                loadFromTable(conn);
            } finally {
                CommDao.closeConnection(conn);
            }
        }
    }

    /**
     * 清除原来的库表参数，重新装载来自库表部分的参数
     */
    synchronized public void reloadOnlyTable(Connection conn) throws Exception {
        if (theTableName != null && theTableName.trim().length() > 0) {
            loadFromTable(conn);
        }
    }

    /**
     * 清除原来的xml文件中的元素和属性参数，重新装载来自xml文件部分的参数
     */
    synchronized public void reloadOnlyXmlFile() throws Exception {
        if (theXmlFilePath != null && theXmlFilePath.trim().length() > 0) {
            loadFromXml();
        }
    }

    /**
     * 清除所有参数，重新装载来自xml文件和库表中的参数
     */
    public void reloadAll() throws Exception {
        reloadOnlyXmlFile();
        reloadOnlyTable();
    }

    /**
     * 清除所有参数，重新装载来自xml文件和库表中的参数
     */
    public void reloadAll(Connection conn) throws Exception {
        reloadOnlyXmlFile();
        reloadOnlyTable(conn);
    }

    /**************************************************************
     * 助手函数簇
     **************************************************************/

    /**
     * 从数据库表加载库表参数，若所提供的库表名实际不存在，则跳过来自库表的参数装载工作。装载过程中出现任何异常，函数立即终止！
     */
    synchronized private void loadFromTable(Connection conn) throws Exception {
        Map<String, Map<String, String>> mapCache = new LinkedHashMap<String, Map<String, String>>();
        if (theTableName == null || theTableName.trim().length() == 0) {
            log.warn(String.format("所指定的表为空，来自库表的参数部分空缺！"));
            tableCache = mapCache;
            return;
        }

        log.info(String.format("开始从表[%s]加载参数", theTableName));
        String sql = String.format(SQL_FORMAT, theTableName);
        List<Map<String, Object>> list;
        if (!isExistTable(conn, theTableName)) {
            log.warn(String.format("所指定的表不存在，来自库表的参数部分空缺！"));
            return;
        }

        list = CommDao.queryForList(conn, sql);

        int count = 0;

        for (Map<String, Object> item : list) {
            // 当前table
            String clazz = toString(item.get("CLAZZ"));
            // 当前Field
            String name = toString(item.get("NAME"));
            // key
            String value = toString(item.get("VALUE"));
            // value
            String alias = toString(item.get("ALIAS"));
            // 当前map的key
            String mapKey = String.format("%s.%s", clazz, name);
            // log.info(String.format("clazz:[%s], name:[%s], %s = %s", clazz, name, value,
            // alias));

            Map<String, String> map = mapCache.get(mapKey);
            if (map == null) {
                map = new LinkedHashMap<String, String>();
                map.put(value, alias);
                count++;
                mapCache.put(mapKey, map);
                continue;
            }

            if (map.containsKey(value)) {
                log.warn(String.format("clazz:[%s], name:[%s] 下的取值 [%s] 重复！", clazz, name, value));
            } else {
                count++;
            }

            map.put(value, alias);
        }

        log.info(String.format("从表[%s]中加载到的有效参数为 %d 条", theTableName, count));

        tableCache = mapCache;
    }

    /**
     * 从xml加载元素路径参数和属性路径参数，若获取xml文件输入流失败，则跳过来自xml文件的参数装载工作。装载过程中出现任何异常，函数立即终止！
     */
    synchronized private void loadFromXml() throws Exception {
        Map<String, String> attrMapCache = new LinkedHashMap<String, String>();
        Map<String, String> elemMapCache = new LinkedHashMap<String, String>();
        if (theXmlFilePath == null || theXmlFilePath.trim().length() == 0) {
            log.warn(String.format("所指定参数配置文件为空，来自参数配置文件的参数部分空缺！"));
            xmlAttrCache = attrMapCache;
            xmlElemCache = elemMapCache;
            return;
        }

        log.info(String.format("开始从文件[%s]加载参数", theXmlFilePath));

        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = null;
        InputStream in = null;
        try {
            try {
                in = getResourceAsStream(theXmlFilePath);
            } catch (Exception e) {
                if (log.isWarnEnabled()) {
                    log.warn("读取文件" + theXmlFilePath + "出错 -> " + e);
                }
                xmlAttrCache = attrMapCache;
                xmlElemCache = elemMapCache;
                return;
            }
            document = builder.parse(in);
        } finally {
            if (in != null) {
                in.close();
            }
        }

        parseNode(document.getDocumentElement(), "", attrMapCache, elemMapCache);
        {
            log.info(String.format("从文件[%s]中加载到的有效属性参数为 %d 条", theXmlFilePath, attrMapCache.size()));
            log.info(String.format("从文件[%s]中加载到的有效元素参数为 %d 条", theXmlFilePath, elemMapCache.size()));
        }

        xmlAttrCache = attrMapCache;
        xmlElemCache = elemMapCache;
    }

    /**
     * 判断用户提供的库表名是否存在
     *
     * @param conn - 用户提供的数据库链接
     * @param table - 用户指定的库表名
     * @return ture - 在 conn 链接下找到 table 表， false - 在 conn 下没有找到 table 表或有异常
     */
    private boolean isExistTable(Connection conn, String table) {
        try {
            DatabaseMetaData meta = conn.getMetaData();
            String user = meta.getUserName();
            if (CommDao.isOracle(conn)) {
                return null != CommDao.queryForInteger(conn, ORACLE_TABLE_EXIST_SQL, user, table);
            } else if (CommDao.isMysql(conn)) {
                return null != CommDao.queryForInteger(conn, String.format(MYSQL_TABLE_EXIST_SQL, table));
            }
            log.error("只支持 Oracle 和 Mysql 数据库... ");
            return false;
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.warn(e.getMessage(), e);
            }
            return false;
        }
    }

    /**
     * parseNode 是一个递归函数，解析所有属性路径 和 元素路径，并将解析到的路径-取值对保存起来，解析过程出现异常则立即终止！
     *
     * @param node - 起始元素节点对象
     * @param currPath - node 节点之前的元素路径, '.' 号作为分隔符
     * @throws Exception
     */
    private void parseNode(Node node, String currPath, Map<String, String> attrMapCache,
        Map<String, String> elemMapCache) throws Exception {
        String elemName = node.getNodeName();
        String elemNodePath = currPath + (currPath.length() == 0 ? "" : ".") + elemName;

        NamedNodeMap nodeMap = node.getAttributes();
        int len = nodeMap == null ? 0 : nodeMap.getLength();
        for (int index = 0; index < len; index++) {
            Node attr = nodeMap.item(index);
            String attrName = attr.getNodeName();
            if ("remark".equalsIgnoreCase(attrName) || "desc".equalsIgnoreCase(attrName)) {
                // 忽略 remark 和 desc 属性
                continue;
            }
            String attrValue = attr.getNodeValue();
            String attrNodePath = elemNodePath + (elemNodePath.length() == 0 ? "" : ".") + attrName;
            if (attrMapCache.containsKey(attrNodePath)) {
                if (log.isWarnEnabled()) {
                    log.warn(String.format("属性路径 [%s] 重复！", attrNodePath));
                }
            }
            {
                // log.info(String.format("attribute path :[%s], value:[%s]", attrNodePath,
                // attrValue));
            }
            attrMapCache.put(attrNodePath, attrValue);
        }

        NodeList childrenNode = node.getChildNodes();
        len = childrenNode.getLength();
        if (len == 1) {
            String elemValue = node.getFirstChild().getNodeValue();
            if (elemMapCache.containsKey(elemNodePath)) {
                if (log.isWarnEnabled()) {
                    log.warn(String.format("元素路径 [%s] 重复！", elemNodePath));
                }
            }
            {
                // log.info(String.format("element path :[%s], value:[%s]", elemNodePath,
                // elemValue));
            }
            if (isElmValEncrypted(node)) {
                elemValue = decrypt(elemValue);
            }
            elemMapCache.put(elemNodePath, elemValue);
        } else {
            for (int index = 0; index < len; index++) {
                Node child = childrenNode.item(index);
                if (child.getNodeType() != Node.TEXT_NODE) {
                    parseNode(child, elemNodePath, attrMapCache, elemMapCache);
                }
            }
        }
    }

    /**
     * 元素节点的值是否被加密
     *
     * @param node - 元素节点对象
     * @return - true - 其属性 encrypt 的值等于 "true" 或 "yes"， false - 其它值或encrypt属性不存在
     */
    private static boolean isElmValEncrypted(Node node) {
        try {
            NamedNodeMap nodeMap = node.getAttributes();
            if (nodeMap == null) {
                return false;
            }
            Node attrNode = nodeMap.getNamedItem("encrypt");
            if (attrNode == null) {
                return false;
            }
            String encrypt = attrNode.getNodeValue();
            encrypt = encrypt == null ? "" : encrypt.trim().toLowerCase();
            return "true".equals(encrypt) || "yes".equals(encrypt);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取文件资源的输入流
     *
     * @param resource - 文件资源名
     * @return - 找到的输入流
     */
    private static InputStream getResourceAsStream(String resource) {
        String stripped = resource.startsWith("/") ? resource.substring(1) : resource;
        InputStream stream = null;
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader != null) {
            stream = classLoader.getResourceAsStream(stripped);
        }
        if (stream == null) {
            stream = GeParameterManager.class.getResourceAsStream(resource);
        }
        if (stream == null) {
            stream = GeParameterManager.class.getClassLoader().getResourceAsStream(stripped);
        }
        if (stream == null) {
            throw new RuntimeException(resource + " not found");
        }
        return stream;
    }

    /**
     * 对象字符串化，对于时间类型对象将按 yyyy-MM-dd HH:mm:ss 进行格式化
     *
     * @param value - 要被字符串化的对象
     * @return - 对象字符串化后的字符串
     */
    private static String toString(Object value) {
        if (value == null) {
            return "";
        }
        if (value instanceof String) {
            return (String)value;
        } else if (value instanceof java.util.Date || value instanceof java.sql.Timestamp
            || value instanceof java.sql.Date) {
            return DateFormatUtils.format((Date) value, "yyyy-MM-dd HH:mm:ss");
        }
        return String.valueOf(value);
    }

    /*************************************************************
     *
     * 任意字符串到 16 进制字符串间的转换，为加密解密用
     *
     *************************************************************/

    /**
     * converts a byte array to hex string (suitable for dumps and ASCII packaging of Binary fields
     *
     * @param b - byte array
     * @return String representation
     */
    public static String hexString(byte[] b) {
        final int two = 2;
        StringBuffer d = new StringBuffer(b.length * two);
        for (int i = 0; i < b.length; i++) {
            char hi = Character.forDigit((b[i] >> 4) & 0x0F, 16);
            char lo = Character.forDigit(b[i] & 0x0F, 16);
            d.append(Character.toUpperCase(hi));
            d.append(Character.toUpperCase(lo));
        }
        return d.toString();
    }

    /**
     * 将字符串转换成字节数组
     *
     * @param s source string (with Hex representation)
     * @return byte array
     */
    public static byte[] hex2byte(String s) {
        return hex2byte(s.getBytes(), 0, s.length() >> 1);
    }

    /**
     * @param b source byte array
     * @param offset starting offset
     * @param len number of bytes in destination (processes len*2)
     * @return byte[len]
     */
    public static byte[] hex2byte(byte[] b, int offset, int len) {
        byte[] d = new byte[len];
        final int two = 2;
        for (int i = 0; i < len * two; i++) {
            int shift = i % 2 == 1 ? 0 : 4;
            d[i >> 1] |= Character.digit((char)b[offset + i], 16) << shift;
        }
        return d;
    }

    /*************************************************************
     *
     * 加密、解密函数、变量等
     *
     *************************************************************/

    /** 程序内嵌的密钥，用一维数组表示的一张置换表 */
    private static final char M_KEY_ASC[] = {0x48, 0xEE, 0x76, 0x1D, 0x67, 0x69, 0xA1, 0x1B, 0x7A, 0x86, 0x47, 0xF8,
        0x54, 0x95, 0x97, 0x5F, 0x78, 0xD9, 0xDA, 0x6C, 0x59, 0xD7, 0x6B, 0x35, 0xC5, 0x77, 0x85, 0x18, 0x2A, 0x0E,
        0x52, 0xFF, 0x00, 0xE3, 0x1B, 0x71, 0x8D, 0x34, 0x63, 0xEB, 0x91, 0xC3, 0x24, 0x0F, 0xB7, 0xC2, 0xF8, 0xE3,
        0xB6, 0x54, 0x4C, 0x35, 0x54, 0xE7, 0xC9, 0x49, 0x28, 0xA3, 0x85, 0x11, 0x0B, 0x2C, 0x68, 0xFB, 0xEE, 0x7D,
        0xF6, 0x6C, 0xE3, 0x9C, 0x2D, 0xE4, 0x72, 0xC3, 0xBB, 0x85, 0x1A, 0x12, 0x3C, 0x32, 0xE3, 0x6B, 0x4F, 0x4D,
        0xF4, 0xA9, 0x24, 0xC8, 0xFA, 0x78, 0xAD, 0x23, 0xA1, 0xE4, 0x6D, 0x9A, 0x04, 0xCE, 0x2B, 0xC5, 0xB6, 0xC5,
        0xEF, 0x93, 0x5C, 0xA8, 0x85, 0x2B, 0x41, 0x37, 0x72, 0xFA, 0x57, 0x45, 0x41, 0xA1, 0x20, 0x4F, 0x80, 0xB3,
        0xD5, 0x23, 0x02, 0x64, 0x3F, 0x6C, 0xF1, 0x0F};

    /** 十六进制的数字到字符的映射数组，如数值13的字符为hex[13] */
    private static final char[] HEX = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    /**
     * 加密一个 ascii 字符串
     *
     * @param input 需要加密的<code>ascii</code>字符串
     * @return 成功时候，返回加密的字符串；失败返回null
     */
    private static String encrypt(String input) throws Exception {
        if (null == input) {
            throw new Exception("提供的明文字符串不能为 null");
        }
        // 任意字符串的16进制字符串化
        input = hexString(input.getBytes());

        if (!isAscString(input)) {
            throw new Exception("提供的明文字符串必须全部由 ascii 字符组成！");
        }

        char[] iary = input.toCharArray();

        for (int i = 0; i < iary.length; i++) {
            iary[i] = (char)(M_KEY_ASC[i] ^ iary[i]);
        }

        int inx = 0;
        char[] ohex = new char[2 * iary.length];
        for (int i = 0; i < iary.length; i++) {
            int low = iary[i] & 0x000F;
            int hight = (iary[i] & 0x00F0) >> 4;
            ohex[inx++] = HEX[hight];
            ohex[inx++] = HEX[low];
        }

        return new String(ohex);
    }

    /**
     * 解密一个由本类加密的字符串
     *
     * @param input 需要解密的<code>由本类加密的</code>字符串
     * @return 成功时候，返回解密的字符串；失败返回null
     * @throws Exception
     */
    private static String decrypt(String input) throws Exception {
        final int two = 2;
        if (null == input || 0 != (input.length() % two) || !isAllHexChar(input)) {
            throw new Exception("密文有误，不合规格！");
        }

        int ilen = input.length();
        char[] oary = new char[ilen / 2];

        for (int i = 0; i < ilen; i += two) {
            String substr = input.substring(i, i + 2);
            int val = Integer.parseInt(substr, 16);
            oary[i / 2] = (char)(M_KEY_ASC[i / 2] ^ val);
        }

        // 16进制表示的字符串到字节数组的转换
        byte[] bytes = hex2byte(new String(oary));
        return new String(bytes);
    }

    /**
     * 辅助函数，用来判定一个给定的串是否<code>只包含ascii字符</code>
     *
     * @param str 需要检测的字符串
     * @return 当str只包含ascii字符时候为true，否则为false
     */
    private static boolean isAscString(String str) {
        char[] iary = str.toCharArray();

        for (int i = 0; i < iary.length; i++) {

            if ((iary[i] & 0xFF80) != 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * 辅助函数，用来判定一个给定的串是否<code>只包含十六进制字符</code>
     *
     * @param str 需要检测的字符串
     * @return 当str只包含十六进制字符时候为true，否则为false
     */
    private static boolean isAllHexChar(String str) {
        String hexstr = "0123456789abcdefABCDEF";
        char[] iary = str.toCharArray();

        for (int i = 0; i < iary.length; i++) {
            if (hexstr.indexOf(iary[i]) == -1) {
                return false;
            }
        }

        return true;
    }

    private static DataSource dataSource;

    protected Connection getRConnection() throws SQLException {
        try {
            if (dataSource == null) {
                dataSource = (DataSource)SpringBeanAwareFactory.getBean("gameecologyDataSource");
            }
            return dataSource.getConnection();
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            return null;
        }
    }
}
