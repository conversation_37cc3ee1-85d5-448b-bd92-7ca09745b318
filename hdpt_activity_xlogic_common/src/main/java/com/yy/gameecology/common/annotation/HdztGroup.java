package com.yy.gameecology.common.annotation;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.TYPE;

/**
 * 活动平台分组注解，需要和其它框架结合，以 AOP 的方式实现代码在指定分组下运行
 * 比如和 com.yy.gameecology.activity.worker.aspect.SchedulingAspect 结合控制 @Scheduled 注解的定时器在指定分组运行
 *
 * @author: 郭立平[<EMAIL>]
 * @date: 2021/3/11 20:10
 **/
@Retention(RetentionPolicy.RUNTIME)
@Target(value = {TYPE})
public @interface HdztGroup {
    // 合法值是 1/2/3/4/5
    int[] value() default {1};
}
