package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Getter;
import lombok.Setter;
import org.springframework.jdbc.core.RowMapper;

import java.util.Date;

@Getter
@Setter
@TableColumn(underline = true)
public class Cmpt1019NoticeMsg {

    public static String TABLE_NAME = "cmpt_1019_notice_msg";

    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt1019NoticeMsg> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt1019NoticeMsg result = new Cmpt1019NoticeMsg();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getLong("cmpt_use_inx"));
        result.setUid(rs.getLong("uid"));
        result.setSeq(rs.getString("seq"));
        result.setNoticeType(rs.getString("notice_type"));
        result.setNoticeValue(rs.getString("notice_value"));
        result.setExpiredTime(rs.getTimestamp("expired_time"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        result.setUpdateTime(rs.getTimestamp("update_time"));
        return result;
    };

    private Long id;

    protected Long actId;

    protected Long cmptUseInx;

    protected Long uid;

    protected String seq;

    protected String noticeType;

    protected String noticeValue;

    /**
     * 过期时间，过期的数据进频道不会发出
     */
    protected Date expiredTime;

    protected Date createTime;

    protected Date updateTime;
}
