
package com.yy.gameecology.common.cache.interceptor;

import com.yy.gameecology.common.consts.Const;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * Title:
 * Description:
 * <p/>
 * User: xuy
 * Date: 11-12-23 下午5:30
 */
public class DefaultCacheDefinition implements CacheDefinition {

    private String cacheName;

    private String key;

    private long timeToLiveMillis;

    private String dynamicTimeToLive;

    public String getDynamicTimeToLive() {
        return dynamicTimeToLive;
    }

    public void setDynamicTimeToLive(String dynamicTimeToLive) {
        this.dynamicTimeToLive = dynamicTimeToLive;
    }

    public String getCacheName() {
        return cacheName;
    }

    public String getKey() {
        return key;
    }

    public long getTimeToLiveMillis() {
        if (StringUtils.hasLength(dynamicTimeToLive)) {
            return Const.GEPM.getParamValueToLong(dynamicTimeToLive, timeToLiveMillis);
        }
        return timeToLiveMillis;
    }

    @SuppressWarnings("deprecation")
    public void setCacheName(String cacheName) {
        Assert.notNull(cacheName);
        this.cacheName = cacheName;
    }

    @SuppressWarnings("deprecation")
    public void setKey(String key) {
        Assert.notNull(key);
        this.key = key;
    }

    public void setTimeToLiveMillis(long timeToLiveMillis) {
        this.timeToLiveMillis = timeToLiveMillis;
    }

    /**
     * This implementation compares the <code>toString()</code> results.
     * @see #toString()
     */
    @Override
    public boolean equals(Object other) {
        return (other instanceof CacheDefinition && toString().equals(other.toString()));
    }

    /**
     * This implementation returns <code>toString()</code>'s hash code.
     * @see #toString()
     */
    @Override
    public int hashCode() {
        return toString().hashCode();
    }

    /**
     * Return an identifying description for this cache operation definition.
     * <p>Has to be overridden in subclasses for correct <code>equals</code>
     * and <code>hashCode</code> behavior. Alternatively, {@link #equals}
     * and {@link #hashCode} can be overridden themselves.
     */
    @Override
    public String toString() {
        return getDefinitionDescription().toString();
    }

    /**
     * Return an identifying description for this caching definition.
     * <p>Available to subclasses, for inclusion in their <code>toString()</code> result.
     */
    protected StringBuilder getDefinitionDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append("DefaultCacheDefinition");
        sb.append("{cacheName='").append(cacheName).append('\'');
        sb.append(", key='").append(key).append('\'');
        sb.append(", timeToLiveMillis=").append(timeToLiveMillis);
        sb.append(", dynamicTimeToLive=").append(dynamicTimeToLive);
        sb.append('}');
        return sb;
    }

}
