/*
 * @(#)HdzjActivityUi.java 2021-04-13
 *
 * Copy Right@ 欢聚时代
 *
 * 代码生成: hdzj_activity_ui 表的数据模型类  HdzjActivityUi
 */

package com.yy.gameecology.common.db.model.gameecology;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * @功能说明
 * <pre>
 * hdzj_activity_ui 表的数据模型类  HdzjActivityUi
 * </pre>
 *
 * @版本更新
 * <pre>
 * 修改版本: 1.0.0 / 2021-04-13 / cgc
 * 修改说明：形成初始版本
 * 复审人：
 * </pre>
 */
@SuppressWarnings("serial")
public class HdzjActivityUi implements java.io.Serializable , Cloneable {
	private static final Logger log = LoggerFactory.getLogger(HdzjActivityUi.class);

	public static String TABLE_NAME = "hdzj_activity_ui";

	// java 属性字段 和 库表字段对应关系， key-java属性， val-表字段名
	public static Map<String, String> TABLE_FIELDS = new HashMap<String, String>();
	static {
		TABLE_FIELDS.put("actId", "act_id");
		TABLE_FIELDS.put("name", "name");
		TABLE_FIELDS.put("ctime", "ctime");
		TABLE_FIELDS.put("value", "value");
		TABLE_FIELDS.put("utime", "utime");
		TABLE_FIELDS.put("remark", "remark");
	};

	public static RowMapper<HdzjActivityUi> ROW_MAPPER = new RowMapper<HdzjActivityUi>() {
		@Override
		public HdzjActivityUi mapRow(ResultSet rs, int rowNum) throws SQLException {
			HdzjActivityUi entity = new HdzjActivityUi();
			entity.setActId(rs.getLong("act_id"));
			entity.setName(rs.getString("name"));
			entity.setCtime(rs.getTimestamp("ctime"));
			entity.setValue(rs.getString("value"));
			entity.setUtime(rs.getTimestamp("utime"));
			entity.setRemark(rs.getString("remark"));
			return entity;
		}
	};

	// 表主键属性集合
	public static final String[] TABLE_PKS = new String[]{
		 "act_id",  "name"
	};

	private Long actId;
	private String name;
	private Date ctime;
	private String value;
	private Date utime;
	private String remark;

	/** 无参数构造函数  */
	public HdzjActivityUi(){
	}

	/** 主键属性构造函数 - 自动生成参数顺序可能会变而函数签名不变，使用时可能参数错乱，若确定不再重新生成，可去掉注释！ */
	/*public HdzjActivityUi(Long actId, String name){
		this.actId = actId;
		this.name = name;
	}*/

	/** 全属性构造函数 - 自动生成参数顺序可能会变而函数签名不变，使用时可能参数错乱，若确定不再重新生成，可去掉注释！ */
	/*public HdzjActivityUi(Long actId, String name, Date ctime, String value, Date utime, String remark){
		this.actId = actId;
		this.name = name;
		this.ctime = ctime;
		this.value = value;
		this.utime = utime;
		this.remark = remark;
	}*/


	public void setActId(Long actId){
		this.actId = actId;
	}

	public Long getActId(){
		return actId;
	}

	public void setName(String name){
		this.name = name;
	}

	public String getName(){
		return name;
	}

	public void setCtime(Date ctime){
		this.ctime = ctime;
	}

	public Date getCtime(){
		return ctime;
	}

	public void setValue(String value){
		this.value = value;
	}

	public String getValue(){
		return value;
	}

	public void setUtime(Date utime){
		this.utime = utime;
	}

	public Date getUtime(){
		return utime;
	}

	public void setRemark(String remark){
		this.remark = remark;
	}

	public String getRemark(){
		return remark;
	}

	public HdzjActivityUi clone() {
		try {
			return (HdzjActivityUi) super.clone();
		} catch (CloneNotSupportedException e) {
			log.error(e.getMessage(), e);
			return null;
		}
	}

	public String toString() {
		return JSON.toJSONString(this);
	}
}
