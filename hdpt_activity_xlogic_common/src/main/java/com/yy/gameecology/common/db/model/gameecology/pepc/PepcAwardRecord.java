package com.yy.gameecology.common.db.model.gameecology.pepc;

import com.yy.gameecology.common.annotation.TableColumn;
import com.yy.gameecology.common.db.model.gameecology.aov.AovMatchAwardRecord;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-03-31 21:15
 **/
@Data
@TableColumn(underline = true)
public class PepcAwardRecord implements Serializable {

    public static String TABLE_NAME = "pepc_award_record";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<PepcAwardRecord> ROW_MAPPER = null;

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * act_id
     */
    private Long actId;

    /**
     * phase_id
     */
    private Long phaseId;

    /**
     * award_type
     * 1==首次参赛奖励 2==胜利结算奖励
     */
    private Integer awardType;

    /**
     * uid
     */
    private Long uid;


    /**
     * 奖励金额
     */
    private Long amount;

    /**
     * 奖励说明
     */
    private String awardDesc;

    /**
     * award_state
     */
    private Integer awardState;

    /**
     * 结算排名，awardType=2的时候才有值
     */
    private Integer rank;

    /**
     * award_time
     */
    private Date awardTime;

    public PepcAwardRecord(PepcAwardRecord record) {
        this.id = record.getId();
        this.actId = record.getActId();
        this.phaseId = record.getPhaseId();
        this.awardType = record.getAwardType();
        this.uid = record.getUid();
        this.amount = record.getAmount();
        this.awardDesc = record.getAwardDesc();
        this.awardState = record.getAwardState();
        this.awardTime = record.getAwardTime();
        this.rank = record.getRank();
    }

    public PepcAwardRecord() {
    }

}