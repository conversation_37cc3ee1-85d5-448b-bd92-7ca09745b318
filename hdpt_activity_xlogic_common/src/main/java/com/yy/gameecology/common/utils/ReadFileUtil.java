package com.yy.gameecology.common.utils;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 读取资源内容，注意必须在 spring 环境下使用，否则 PathMatchingResourcePatternResolver 不能正常工作
 */
public class ReadFileUtil {
    private static final Logger log = LoggerFactory.getLogger(ReadFileUtil.class);
    
    private static Map<String, String> scriptMap = new ConcurrentHashMap<String, String>();

    public static List<String> readFromFile(String filename, String encoding) {
        return readFromFile(filename, encoding, "");
    }

    /**
     * 读取指定位置的文件内容（jar包 中的也可以读取）
     * @param filename - 文件名
     * @param encoding - 字符编码
     * @param location - 为空或者类似这个格式 /lua", 表示从 lua 子目录下找;
     * @return 文件记录行（# 开头的是注释行，直接过滤掉）
     */
    public static List<String> readFromFile(String filename, String encoding, String location) {
        Clock clock = new Clock();

        // 先规范化 location
        String separator = File.separator;
        location = location==null ? "" : location.trim();
        if(!location.startsWith(separator)) {
            location = separator + location;
        }
        if(!location.endsWith(separator)) {
            location += separator;
        }

        // 加上 classpath 前缀
        location = "classpath:" + location;

        PathMatchingResourcePatternResolver prpr = new PathMatchingResourcePatternResolver();
        String fullname = location + filename;
        Resource resource = prpr.getResource(fullname);
        InputStream is = null;
        try {
            if (resource != null) {
                is = resource.getInputStream();
                @SuppressWarnings("unchecked")
				List<String> lines = IOUtils.readLines(is, encoding);
                List<String> list = new ArrayList<String>();
                for (String line : lines) {
					if(line==null) {
                        continue;
                    }
					line = line.trim();
					if(line.startsWith("#")) {
                        continue;
                    }
					list.add(line);
				}
                
                log.info("read file by fullname {}, encoding:{}, size:{} {}", fullname, encoding, list.size(), clock.tag());
                return list;
            }
        } catch (IOException e) {
            log.error("read file fullname {}, encoding:{}, error:{} {}", fullname, encoding, e.getMessage(), clock.tag());
        } finally {
            IOUtils.closeQuietly(is);
        }

        log.error("read file fullname {}, encoding:{}, fail:null {}", fullname, encoding, clock.tag());
        return null;
    }
    
    
    /**
     * 获取本项目的lua脚本
     * @param name
     * @return
     */
	public static String getLuaScript(String name) {
        String script = scriptMap.get(name);
        if (script == null) {
            PathMatchingResourcePatternResolver prpr = new PathMatchingResourcePatternResolver();
            String scriptLocation = "classpath:/lua/";
			Resource resource = prpr.getResource(scriptLocation  + name);
            InputStream is = null;
            try {
                if (resource != null) {
                    is = resource.getInputStream();
                    script = IOUtils.toString(is);
                    scriptMap.put(name, script);
                    return script;
                }
            } catch (IOException e) {
                log.error("read script file {} error", name);
            } finally {
                IOUtils.closeQuietly(is);
            }
        }
        return script;
    }
}
