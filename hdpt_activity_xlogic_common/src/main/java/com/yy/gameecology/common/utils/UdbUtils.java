package com.yy.gameecology.common.utils;

import com.google.common.collect.ImmutableList;
import org.apache.commons.lang.math.LongRange;

import java.util.List;

public final class UdbUtils {

    private static final long MIN_VALID_UID = 1;

    private static final LongRange RANGE_1 = new LongRange(2074428900L, 2114428900L - 1);
    private static final LongRange RANGE_2 = new LongRange(2117428900L, 2118428900L - 1);
    private static final LongRange RANGE_3 = new LongRange(2118429900L, 2122429900L - 1);
    private static final LongRange RANGE_4 = new LongRange(2122449900L, 2126499900L - 1);
    private static final LongRange RANGE_5 = new LongRange(2126500000L, 2127000000L - 1);
    private static final LongRange RANGE_6 = new LongRange(2140000000L, 2142000000L - 1);
    private static final LongRange RANGE_7 = new LongRange(2148000000L, 2158000000L - 1);
    private static final LongRange RANGE_8 = new LongRange(2168000000L, 2171000000L - 1);
    private static final LongRange RANGE_9 = new LongRange(4200000000L, 4294967296L - 1);

    private static final List<LongRange> UID_RANGES = ImmutableList.of(RANGE_1, RANGE_2, RANGE_3, RANGE_4, RANGE_5, RANGE_6, RANGE_7, RANGE_8, RANGE_9);

    /**
     * [2074428900,2114428900) 4000W
     * [2117428900,2118428900) 100w
     * [2118429900,2122429900) 400w
     * [2122449900,2126499900) 405w
     * [2126500000,2127000000) 50W
     * [2140000000,2142000000) 200W
     * [2148000000,2158000000) 1000万
     * [2168000000,2171000000) 300万
     * [4200000000 4294967296) 42亿~2^32
     */
    public static boolean isLoginUid(Long uid) {
        if (uid == null) {
            return false;
        }
        if (uid <= MIN_VALID_UID) {
            return false;
        }
        for (LongRange range : UID_RANGES) {
            if (range.containsLong(uid)) {
                return false;
            }
        }
        return true;
    }
}
