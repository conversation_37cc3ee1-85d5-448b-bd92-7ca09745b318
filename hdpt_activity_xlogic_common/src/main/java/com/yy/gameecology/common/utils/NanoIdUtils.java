package com.yy.gameecology.common.utils;

import cn.hutool.core.lang.id.NanoId;

public final class NanoIdUtils {

    private static final char[] DEFAULT_ALPHABET =
            "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ".toCharArray();

    public static final int DEFAULT_SIZE = 21;

    public static String id() {
        return id(DEFAULT_SIZE);
    }

    public static String id(int size) {
        return NanoId.randomNanoId(null, DEFAULT_ALPHABET, size);
    }
}
