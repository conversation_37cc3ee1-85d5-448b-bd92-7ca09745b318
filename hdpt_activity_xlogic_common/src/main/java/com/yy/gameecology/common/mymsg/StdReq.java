
package com.yy.gameecology.common.mymsg;

import com.yy.gameecology.common.utils.StringUtil;
import com.yy.jserverlib.codec.annotations.YField;
import com.yy.jserverlib.codec.annotations.YMessage;
import com.yy.jserverlib.codec.annotations.YStyle;
import com.yy.jserverlib.codec.annotations.YType;

@YMessage(StdReq.URI)
public class StdReq extends MyMessage {

    public static final long URI = MyMessage.GE_BASE_REQ;

    public StdReq() {
        super(URI);
    }

    // 构建时候初始化基类uri,再写入子类uri
    public StdReq(long subUri) {
        super(URI);
        this.setSubUri(subUri);
    }

    @YField(0)
    @YType(YStyle.U32)
    private long subUri = 0;

    @YField(1)
    private long ext = 0;

    @YField(2)
    private String data = StringUtil.EMPTY;

    public long getSubUri() {
        return subUri;
    }

    public void setSubUri(long subUri) {
        this.subUri = subUri;
    }

    public long getExt() {
        return ext;
    }

    public void setExt(long ext) {
        this.ext = ext;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

}
