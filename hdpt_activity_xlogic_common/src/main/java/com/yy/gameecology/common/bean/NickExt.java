package com.yy.gameecology.common.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <pre>
 * {
 * 	"hosts": {
 * 		"0": "yy",
 * 		"1": "yy",
 * 		"10": "baidu",
 * 		"11": "baidu",
 * 		"12": "baidu",
 * 		"13": "baidu",
 * 		"14": "baidu",
 * 		"2": "baidu",
 * 		"3": "baidu",
 * 		"4": "baidu",
 * 		"5": "tieba",
 * 		"6": "baidu",
 * 		"7": "baidu",
 * 		"9": "baidu"
 *  },
 * 	"users": {
 * 		"1319875349": {
 * 			"baidu": {
 * 				"nick": "门门💕💖"
 *            },
 * 			"tieba": {
 * 				"nick": "门门💕💖🔅"
 *            },
 * 			"yy": {
 * 				"nick": "门门"
 *            }
 *        },
 * 		"142635863900170": {
 * 			"baidu": {
 * 				"nick": "YY好友#08124455741433"
 *            },
 * 			"tieba": {
 * 				"nick": "YY好友#08124455741433"
 *            },
 * 			"yy": {
 * 				"nick": "努力啊大觅松"
 *            }
 *        }
 *    }
 * }
 * </pre>
 */
@Getter
@Setter
public class NickExt {

    /**
     * hostId -> hostName
     */
    protected Map<String, String> hosts;

    /**
     * uid -> hostName -> nickItem
     */
    protected Map<String, Map<String, MultiNickItem>> users;
}
