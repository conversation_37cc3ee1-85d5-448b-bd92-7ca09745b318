package com.yy.gameecology.common.db.model.gameecology;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.jdbc.core.RowMapper;

import java.util.Date;
import java.util.Map;

@Getter
@Setter
public class ActResource {

    public static final String TABLE_NAME = "act_resource";

    public static final String[] TABLE_PKS = {"act_id", "resource_key"};

    public static final Map<String, String> TABLE_FIELDS = Map.of("actId", "act_id",
            "resourceKey", "resource_key",
            "resourceUrl", "resource_url",
            "resourceType", "resource_type",
            "resourceDesc", "resource_desc",
            "createTime", "create_time",
            "updateTime", "update_time");

    public static final RowMapper<ActResource> ROW_MAPPER = (rs, rowNum) -> {
        ActResource actResource = new ActResource();
        actResource.setActId(rs.getLong("act_id"));
        actResource.setResourceKey(rs.getString("resource_key"));
        actResource.setResourceUrl(rs.getString("resource_url"));
        actResource.setResourceType(rs.getInt("resource_type"));
        actResource.setResourceDesc(rs.getString("resource_desc"));
        actResource.setCreateTime(rs.getTimestamp("create_time"));
        actResource.setUpdateTime(rs.getTimestamp("update_time"));
        return actResource;
    };

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 资源标识符
     */
    private String resourceKey;

    /**
     * 资源URL
     */
    private String resourceUrl;

    /**
     * 资源类型：1-图片，2-视频，3-SVGA
     */
    private Integer resourceType;

    /**
     * 资源描述
     */
    private String resourceDesc;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}