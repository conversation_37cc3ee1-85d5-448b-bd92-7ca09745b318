package com.yy.gameecology.common.db.model.gameecology.pepc;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-03-31 21:24
 **/
@Data
@TableColumn(underline = true)
public class PepcTeamGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "pepc_team_group";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<PepcTeamGroup> ROW_MAPPER = null;

    /**
     * id
     */
    private Long id;

    /**
     * act_id
     */
    private Long actId;

    /**
     * 赛程id
     */
    private Integer phaseId;

    /**
     * group_code
     */
    private String groupCode;

    /**
     * 小组名称
     */
    private String groupName;

    /**
     * team_id
     */
    private Long teamId;

    /**
     * 队长uid--冗余字段
     */
    private Long captainUid;

    /**
     * create_time
     */
    private Date createTime;
}
