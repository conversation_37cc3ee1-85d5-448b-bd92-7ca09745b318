package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Getter;
import lombok.Setter;
import org.springframework.jdbc.core.RowMapper;

import java.util.Date;

@Getter
@Setter
@TableColumn(underline = true)
public class Cmpt5146Record {

    public static final String TABLE_NAME = "cmpt_5146_record";

    public static final String[] TABLE_PKS = {"id"};

    public static final RowMapper<Cmpt5146Record> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt5146Record result = new Cmpt5146Record();
        result.id = rs.getLong("id");
        result.actId = rs.getLong("act_id");
        result.uid = rs.getLong("uid");
        result.hdid = rs.getString("hdid");
        result.sid = rs.getLong("sid");
        result.ssid = rs.getLong("ssid");
        result.createTime = rs.getTimestamp("create_time");
        return result;
    };

    protected Long id;

    protected Long actId;

    protected Long uid;

    protected String hdid;

    protected Long sid;

    protected Long ssid;

    protected Date createTime;
}
