package com.yy.gameecology.common.db.mapper.aov;

import com.yy.gameecology.common.db.model.gameecology.aov.AovMatchAwardRecord;

public interface AovMatchAwardRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AovMatchAwardRecord record);

    int insertSelective(AovMatchAwardRecord record);

    AovMatchAwardRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AovMatchAwardRecord record);

    int updateByPrimaryKey(AovMatchAwardRecord record);
}