package com.yy.gameecology.common.db.model.gameecology.pepc;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-03-31 21:25
 **/
@Data
@TableColumn(underline = true)
public class PepcTeamMember implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "pepc_team_member";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<PepcTeamMember> ROW_MAPPER = null;

    /**
     * id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long actId;

    /**
     * 队伍ID
     */
    private Long teamId;

    /**
     * 成员UID
     */
    private Long memberUid;

    /**
     * 成员状态 1 正常 0 离开
     */
    private Integer state;

    /**
     * 1队长 0队员
     */
    private String role;

    /**
     * 成功参赛数量
     */
    private Integer gameCount;

    /**
     * 胜利比赛数量
     */
    private Integer winGameCount;

    /**
     * 击杀总数
     */
    private Integer killCnt;

    /**
     * 助攻总数
     */
    private Integer assistCnt;

    /**
     * create_time
     */
    private Date createTime;

    // No-argument constructor
    public PepcTeamMember() {
    }

    public PepcTeamMember(Long teamId, Long actId, Long memberUid, String role, Integer state, Date createTime) {
        this.teamId = teamId;
        this.actId = actId;
        this.memberUid = memberUid;
        this.role = role;
        this.state = state;
        this.createTime = createTime;
    }
}
