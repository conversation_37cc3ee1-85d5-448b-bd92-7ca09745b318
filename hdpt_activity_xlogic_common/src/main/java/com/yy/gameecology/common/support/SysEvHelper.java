
package com.yy.gameecology.common.support;

import com.google.common.primitives.Longs;
import com.yy.ent.clients.bam.context.BamContext;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.SystemUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Collections;
import java.util.Iterator;
import java.util.Properties;

/**
 * 系统环境配置辅助类
 *
 * <AUTHOR>
 * @date 2018年8月28日 下午10:01:39
 */
public class SysEvHelper {
    private static final Logger log = LoggerFactory.getLogger(SysEvHelper.class);

    // 存放 application.properties 的属性对象，传递给需要的地方使用，防止多次加载，做重复工作
    public static Properties properties = new Properties();

    private static final String ENV_KEY = "env";

    /**
     * value == "1" 代表历史广场
     */
    private static final String HISTORY_KEY = "history";

    // 因个位数用于区分 com.yy.hdzt.common.support.IdGenerator.ID 生成值所在分组， 取值只能是 1 ~ 9 （0不用）
    public static final long[] GROUPS = {1, 2, 3, 4, 5, 6, 7};

    public static void checkGroup() {
        String strGgroup = SysEvHelper.getGroup();
        long group = StringUtil.isBlank(strGgroup) ? -1 : Convert.toLong(strGgroup, -1);
        if (!Longs.contains(GROUPS, group)) {
            log.error("checkGroup fail!!! your group:{} not in [{}]", strGgroup, Longs.join(",", GROUPS));
            System.exit(-1);
        }
    }

    /**
     * 使用静态代码块第一时间初始化 系统环境配置辅助类，检查相关配置参数是否正确
     */
    static {
        init();
    }

    /**
     * 封装初始化过程
     */
    public static void init() {
        try {
            // 缺省设置为本地调测环境
            if (StringUtil.isBlank(System.getProperty(ENV_KEY))) {
                if (StringUtil.hasText(System.getenv(ENV_KEY))) {
                    System.setProperty(ENV_KEY, System.getenv(ENV_KEY));
                } else {
                    System.setProperty(ENV_KEY, Const.ENV.LOCAL);
                }
            }

            // 1. 检查是否符合条件的环境指示, 务必首先执行这个检查
            SysEvHelper.checkEnv();

            // 2. 创建 application.properties 的属性对象
            String appcfg = SysEvHelper.getAppConfig();
            InputStream ins = SysEvHelper.class.getClassLoader().getResourceAsStream(appcfg);
            properties.load(ins);

            InputStream innerConfig = SysEvHelper.class.getClassLoader().getResourceAsStream(SysEvHelper.getAppConfig("application-inner.properties"));
            properties.load(innerConfig);

            //3.加载分组配置
            loadAppGroupConfigProperties(properties);

            //4. 初始化log ip
            setLogIp();
        } catch (Throwable t) {
            log.error("init exception@err:{}, system exit!!!", t.getMessage(), t);
            System.exit(1);
        }
    }

    private static void setLogIp() {
        try {
            Field ipField = BamContext.class.getField("ip");
            ipField.setAccessible(true);
            Field modifiers = Field.class.getDeclaredField("modifiers");
            modifiers.setAccessible(true);
            modifiers.setInt(ipField, ipField.getModifiers() ^ Modifier.FINAL);
            ipField.set(BamContext.class, SystemUtil.getIp());
        } catch (Exception e) {
            //ignore
        }
    }

    /**
     * jvm参数 env=xxx 指示， local:本地开发环境; dev:测试环境; deploy:生产
     * 从jvm参数上获得环境指示，默认是 local：本地调测
     */
    public static String getEnv() {
        return StringUtil.trim(System.getProperty("env", Const.ENV.LOCAL));
    }

    /**
     * 是否活动历史广场
     * @return true == 活动历史广场
     */
    public static boolean isHistory(){
        return Const.ONESTR.equals(StringUtil.trim(System.getProperty(HISTORY_KEY, "0")));
    }

    public static String historyDesc() {
        return isHistory() ? "[历史]" : "[在线]";
    }

    /**
     * 有的地方，历史环境不应该触发到，如果是历史环境触发到，则告警报错
     */
    public static boolean checkHistory(String source, boolean error) {
        boolean isHistory = isHistory();
        if(StringUtil.isBlank(source)){
            return isHistory;
        }

        if (isHistory) {
            if (error) {
                log.error("history env error,source:" + source);
            } else {
                log.warn("history env,source:" + source);
            }
        }
        return isHistory;
    }


    /**
     * 检查是否符合条件的环境指示：目前只认 local、dev、deploy 三种环境，其它值时系统直接退出
     */
    public static void checkEnv() {
        String env = getEnv();
        if (!Const.ENV.LOCAL.equals(env) && !Const.ENV.DEV.equals(env) && !Const.ENV.DEPLOY.equals(env)) {
            log.error("checkEnv fail@error env:" + env);
            System.exit(1);
        }
    }

    /**
     * 获得详细环境的中文描述
     */
    public static String getEnvDesc() {
        return getSimpleEnvDesc() + SysEvHelper.historyDesc();
    }

    /**
     * 获得环境的中文描述，仅包含生产、测试、本地信息
     */
    public static String getSimpleEnvDesc() {
        String env = getEnv();
        if (SysEvHelper.isLocal()) {
            return "本地环境[" + env + "]";
        } else if (SysEvHelper.isDev()) {
            return "测试环境[" + env + "]";
        } else {
            return "生产环境[" + env + "]";
        }
    }

    /**
     * 判断是否预发环境（预发的业务系统提供的功能可能不完整，是生产环境的一个子集，预发机不提供外部访问入口）
     * 1）警告：由于历史原因, 原来的判断是否预发有点乱，也不严谨，需要自己仔细检查验证后使用！！！！
     * 2）早期分了不同系统看是否预发ip，实际上若一台服务器定位为预发机就不会再部署生产系统，所有本函数汇总各种预发函数，简化判断（使用时还是要验证下这个假设）
     * 3）为了解决通过ip判断是否预发的混乱，这里增加一个 isYuFaFlag 的环境变量来明确指示是否预发机（兼容 ip 判断）
     */
    public static boolean isYuFa() {
        // 检查预发布环境指示变量  1 - 预发， 非1 - 不是预发
        return StringUtil.trim(System.getProperty("isYuFaFlag", "0")).equals("1");
    }

    /**
     * 是否为本地调测环境:env=local
     */
    public static boolean isLocal() {
        return Const.ENV.LOCAL.equals(getEnv());
    }

    /**
     * 判断是否需要跳过 svc sdk 的 初始化（因公司容器现在不能很好的支持 service平台，需要按需跳过初始化）
     */
    public static boolean skipSvcsdkInit() {
        return isHistory() || StringUtil.trim(System.getProperty("SkipSvcsdkInit", "0")).equals("1");
    }

    /**
     * 控制是否走 svc gateway - true：走， false：不走
     */
    public static boolean isSvcSDKServiceEnableSvcGateway() {
        return StringUtil.trim(System.getProperty("SvcSDKServiceEnableSvcGateway", "1")).equals("1");
    }

    /**
     * 是否为开发测试环境:env=dev
     */
    public static boolean isDev() {
        return Const.ENV.DEV.equals(getEnv());
    }

    /**
     * 是否为生产部署环境:env=deploy
     */
    public static boolean isDeploy() {
        return Const.ENV.DEPLOY.equals(getEnv());
    }

    /**
     * 获得奥秘的配置文件
     */
    public static String getAomiConfig() {
        return "env" + File.separator + getEnv() + File.separator + "aomi-sdk";
    }

    /**
     * 获得应用的配置文件
     */
    public static String getAppConfig() {
        return "env" + File.separator + getEnv() + File.separator + "application.properties";
    }

    public static String getAppConfig(String fileName) {
        return "env" + File.separator + getEnv() + File.separator + fileName;
    }

    /**
     * 获取实例分组配置
     */
    public static String getGroup() {
        return StringUtil.trim(System.getProperty("group", ""));
    }

    /**
     * 获取实例分组配置文件地址
     */
    public static String getAppGroupConfig() {
        return "env" + File.separator + getEnv() + File.separator + "group-setting-" + getGroup() + ".properties";
    }


    /**
     * @param properties 获取分支配置文件会合并主文件，并且不允许和主文件存在重复的key
     */
    private static void loadAppGroupConfigProperties(Properties properties) {
        try {
            Properties groupProperties = new Properties();
            // 缺省设置为本地调测环境
            String group = getGroup();
            if (!StringUtil.isBlank(group)) {
                String appGroupConfig = SysEvHelper.getAppGroupConfig();
                InputStream ins = SysEvHelper.class.getClassLoader().getResourceAsStream(appGroupConfig);

                if (ins == null) {
                    log.error("loadAppGroupConfigProperties local fail@error env:{}，group:{}", getEnv(), group);
                    System.exit(1);
                }

                groupProperties.load(ins);

                if (groupProperties != null && !groupProperties.isEmpty()) {
                    Iterator iterator = Collections.list(groupProperties.propertyNames()).iterator();

                    while (iterator.hasNext()) {
                        Object key = iterator.next();
                        if (properties.containsKey(key)) {
                            String message = "properties key repetition,key = " + key.toString();
                            throw new Exception(message);
                        }

                        properties.put(key, groupProperties.get(key));
                    }
                }
            }
        } catch (Throwable t) {
            log.error("loadAppGroupConfigProperties exception env:{},appConfig:{},group:{},groupConfig:{} @err:{}, system exit!!!"
                    , getEnv(), getAppGroupConfig(), getGroup(), getAppGroupConfig(), t.getMessage(), t);
            System.exit(1);
        }
    }

    /**
     * 有限睡眠等待
     *
     * @param mills - 单位毫秒
     */
    public static void waiting(long mills) {
        try {
            Thread.sleep(mills);
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 无限睡眠等待函数
     */
    public static void waiting() {
        while (true) {
            try {
                Thread.sleep(86400 * 1000);
            } catch (Throwable e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 获得当前方法名字
     */
    public static String getCurrMethodName(StackTraceElement[] targets) {
        try {
            StackTraceElement target = targets[1];
            String className = target.getClassName();
            String methodName = target.getMethodName();
            return className + "." + methodName;
        } catch (Throwable t) {
            log.error("getCurrMethodName exception@err:" + t, t);
        }
        return "_unknown_class_method_";
    }
}
