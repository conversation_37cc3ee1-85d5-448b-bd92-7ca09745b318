package com.yy.gameecology.common.support;

import com.yy.gameecology.common.consts.Const;
import com.yy.thrift.webdbservice.AuthorizeMsg;

import java.util.HashMap;

public class WebdbSaHelper {
    public static AuthorizeMsg getAuthorizeMsg() {
        AuthorizeMsg authorizeMsg = new AuthorizeMsg();
        authorizeMsg.setAuthKey(Const.APP_SECRET);
        authorizeMsg.setAuthUser(Const.APP_ID);

        HashMap<String, String> keyvalue = new HashMap<>(1);
        keyvalue.put("auth-type", "4");
        authorizeMsg.setKeyValue(keyvalue);
        return authorizeMsg;
    }
}
