package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-26 15:28
 **/
@Data
@TableColumn(underline = true)
public class Cmpt2066MemContrib implements Serializable {

    public static String TABLE_NAME = "cmpt_2066_mem_contrib_";


    public static String getTableName(long actId) {
        return TABLE_NAME + actId;
    }

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt2066MemContrib> ROW_MAPPER = null;


    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * act_id
     */
    private Long actId;

    /**
     * cmpt_use_inx
     */
    private Integer cmptUseInx;

    /**
     * member
     */
    private String member;

    /**
     * contribute
     */
    private String contribute;

    /**
     * score
     */
    private Long score;
}
