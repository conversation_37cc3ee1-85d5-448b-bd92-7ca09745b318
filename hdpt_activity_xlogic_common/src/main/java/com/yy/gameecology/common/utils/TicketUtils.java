package com.yy.gameecology.common.utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.HashMap;
import java.util.Iterator;
import java.util.SortedSet;
import java.util.TreeSet;


/**
 * @Description
 * <AUTHOR>
 * @Date 2019/8/6
 **/
public class TicketUtils {
    private static final String HMAC_SHA1_ALGORITHM = "HmacSHA1";

    protected static String byteArrayToHexStr(byte[] byteArray) {
        StringBuilder hex = new StringBuilder();
        byte[] var5 = byteArray;
        int var4 = byteArray.length;

        for(int var3 = 0; var3 < var4; ++var3) {
            byte b = var5[var3];
            hex.append(String.format("%02X", b));
        }

        return new String(hex.toString());
    }

    protected static byte[] getHmacSHA1Signature(byte[] buffer, byte[] keyBytes) throws Exception {
        SecretKeySpec signingKey = new SecretKeySpec(keyBytes, HMAC_SHA1_ALGORITHM);
        Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
        mac.init(signingKey);
        return mac.doFinal(buffer);
    }

    protected static String generateDigest(HashMap<String, String> params) {
        String digest = "";
        SortedSet<String> keys = new TreeSet(params.keySet());
        Iterator var4 = keys.iterator();

        while(var4.hasNext()) {
            String key = (String)var4.next();
            String value = (String)params.get(key);
            if (value != null && !value.isEmpty()) {
                digest = digest + key + value;
            }
        }
        return digest;
    }

    public static String generateSignature(String digest, String skey) throws Exception {

        byte[] hashmac = getHmacSHA1Signature(digest.getBytes("UTF-8"), skey.getBytes("UTF-8"));
        String sign = byteArrayToHexStr(hashmac);
        return sign;
    }

    public static String generateSignature(HashMap<String, String> params, String skey) throws Exception {
        String digest = generateDigest(params);
        byte[] hashmac = getHmacSHA1Signature(digest.getBytes("UTF-8"), skey.getBytes("UTF-8"));
        String sign = byteArrayToHexStr(hashmac);
        return sign;
    }
}
