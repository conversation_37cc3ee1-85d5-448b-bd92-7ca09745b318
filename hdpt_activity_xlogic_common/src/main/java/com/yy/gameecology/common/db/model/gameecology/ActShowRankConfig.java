package com.yy.gameecology.common.db.model.gameecology;

import com.yy.gameecology.common.annotation.TableColumn;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-06-29 10:51
 **/
@TableColumn(underline = true)
public class ActShowRankConfig implements Serializable {
    public static String TABLE_NAME = "act_show_rank_config";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<ActShowRankConfig> ROW_MAPPER = null;

    /**
     * id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long actId;

    /**
     * name
     */
    private String name;

    /**
     * 展示配置
     */
    private String config;

    /**
     * 1===有效
     */
    private Integer status;

    /**
     * 展示开始时间
     */
    private Date startTime;

    /**
     * 展示结束时间
     */
    private Date endTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 排序，从小到大排序
     */
    private Integer sort;

    /**
     * 扩展参数
     */
    private String ext;


    public ActShowRankConfig() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getActId() {
        return actId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }
}
