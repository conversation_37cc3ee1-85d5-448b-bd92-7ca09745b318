package com.yy.gameecology.common.bean;

import java.util.Date;

public class AwardIssue {
    private Long issueId;
    private Long recordId;
    private Long uid;
    private Integer busiId;
    private Integer packageId;
    private Integer packageNum;
    private Integer itemId;
    private Integer giftType;
    private String giftCode;
    private String giftName;
    private Long giftNum;
    private Integer status;
    private String issueConfig;
    private String issueSeq;
    private Integer taskId;
    private String receipt;
    private Integer exint;
    private String extjson;
    private String remark;
    private Date ctime;
    private Date utime;

    public Long getIssueId() {
        return issueId;
    }

    public void setIssueId(Long issueId) {
        this.issueId = issueId;
    }

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Integer getBusiId() {
        return busiId;
    }

    public void setBusiId(Integer busiId) {
        this.busiId = busiId;
    }

    public Integer getPackageId() {
        return packageId;
    }

    public void setPackageId(Integer packageId) {
        this.packageId = packageId;
    }

    public Integer getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(Integer packageNum) {
        this.packageNum = packageNum;
    }

    public Integer getItemId() {
        return itemId;
    }

    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }

    public Integer getGiftType() {
        return giftType;
    }

    public void setGiftType(Integer giftType) {
        this.giftType = giftType;
    }

    public String getGiftCode() {
        return giftCode;
    }

    public void setGiftCode(String giftCode) {
        this.giftCode = giftCode;
    }

    public String getGiftName() {
        return giftName;
    }

    public void setGiftName(String giftName) {
        this.giftName = giftName;
    }

    public Long getGiftNum() {
        return giftNum;
    }

    public void setGiftNum(Long giftNum) {
        this.giftNum = giftNum;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getIssueConfig() {
        return issueConfig;
    }

    public void setIssueConfig(String issueConfig) {
        this.issueConfig = issueConfig;
    }

    public String getIssueSeq() {
        return issueSeq;
    }

    public void setIssueSeq(String issueSeq) {
        this.issueSeq = issueSeq;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getReceipt() {
        return receipt;
    }

    public void setReceipt(String receipt) {
        this.receipt = receipt;
    }

    public Integer getExint() {
        return exint;
    }

    public void setExint(Integer exint) {
        this.exint = exint;
    }

    public String getExtjson() {
        return extjson;
    }

    public void setExtjson(String extjson) {
        this.extjson = extjson;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    public Date getUtime() {
        return utime;
    }

    public void setUtime(Date utime) {
        this.utime = utime;
    }
}
