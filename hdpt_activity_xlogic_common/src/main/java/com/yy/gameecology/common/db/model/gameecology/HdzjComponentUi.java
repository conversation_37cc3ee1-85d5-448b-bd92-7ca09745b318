/*
 * @(#)HdzjComponentUi.java 2021-04-13
 *
 * Copy Right@ 欢聚时代
 *
 * 代码生成: hdzj_component_ui 表的数据模型类  HdzjComponentUi
 */

package com.yy.gameecology.common.db.model.gameecology;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * @功能说明
 * <pre>
 * hdzj_component_ui 表的数据模型类  HdzjComponentUi
 * </pre>
 *
 * @版本更新
 * <pre>
 * 修改版本: 1.0.0 / 2021-04-13 / cgc
 * 修改说明：形成初始版本
 * 复审人：
 * </pre>
 */
@SuppressWarnings("serial")
public class HdzjComponentUi implements java.io.Serializable , Cloneable {
	private static final Logger log = LoggerFactory.getLogger(HdzjComponentUi.class);

	public static String TABLE_NAME = "hdzj_component_ui";

	// java 属性字段 和 库表字段对应关系， key-java属性， val-表字段名
	public static Map<String, String> TABLE_FIELDS = new HashMap<String, String>();
	static {
		TABLE_FIELDS.put("cmptUseInx", "cmpt_use_inx");
		TABLE_FIELDS.put("actId", "act_id");
		TABLE_FIELDS.put("name", "name");
		TABLE_FIELDS.put("cmptId", "cmpt_id");
		TABLE_FIELDS.put("value", "value");
		TABLE_FIELDS.put("utime", "utime");
		TABLE_FIELDS.put("remark", "remark");
		TABLE_FIELDS.put("ctime", "ctime");
	};

	public static RowMapper<HdzjComponentUi> ROW_MAPPER = new RowMapper<HdzjComponentUi>() {
		@Override
		public HdzjComponentUi mapRow(ResultSet rs, int rowNum) throws SQLException {
			HdzjComponentUi entity = new HdzjComponentUi();
			entity.setCmptUseInx(rs.getLong("cmpt_use_inx"));
			entity.setActId(rs.getLong("act_id"));
			entity.setName(rs.getString("name"));
			entity.setCmptId(rs.getLong("cmpt_id"));
			entity.setValue(rs.getString("value"));
			entity.setUtime(rs.getTimestamp("utime"));
			entity.setRemark(rs.getString("remark"));
			entity.setCtime(rs.getTimestamp("ctime"));
			return entity;
		}
	};

	// 表主键属性集合
	public static final String[] TABLE_PKS = new String[]{
		 "cmpt_use_inx",  "act_id",  "name",  "cmpt_id"
	};

	private Long cmptUseInx;
	private Long actId;
	private String name;
	private Long cmptId;
	private String value;
	private Date utime;
	private String remark;
	private Date ctime;

	/** 无参数构造函数  */
	public HdzjComponentUi(){
	}

	/** 主键属性构造函数 - 自动生成参数顺序可能会变而函数签名不变，使用时可能参数错乱，若确定不再重新生成，可去掉注释！ */
	/*public HdzjComponentUi(Long cmptUseInx, Long actId, String name, Long cmptId){
		this.cmptUseInx = cmptUseInx;
		this.actId = actId;
		this.name = name;
		this.cmptId = cmptId;
	}*/

	/** 全属性构造函数 - 自动生成参数顺序可能会变而函数签名不变，使用时可能参数错乱，若确定不再重新生成，可去掉注释！ */
	/*public HdzjComponentUi(Long cmptUseInx, Long actId, String name, Long cmptId, String value, Date utime, String remark, Date ctime){
		this.cmptUseInx = cmptUseInx;
		this.actId = actId;
		this.name = name;
		this.cmptId = cmptId;
		this.value = value;
		this.utime = utime;
		this.remark = remark;
		this.ctime = ctime;
	}*/


	public void setCmptUseInx(Long cmptUseInx){
		this.cmptUseInx = cmptUseInx;
	}

	public Long getCmptUseInx(){
		return cmptUseInx;
	}

	public void setActId(Long actId){
		this.actId = actId;
	}

	public Long getActId(){
		return actId;
	}

	public void setName(String name){
		this.name = name;
	}

	public String getName(){
		return name;
	}

	public void setCmptId(Long cmptId){
		this.cmptId = cmptId;
	}

	public Long getCmptId(){
		return cmptId;
	}

	public void setValue(String value){
		this.value = value;
	}

	public String getValue(){
		return value;
	}

	public void setUtime(Date utime){
		this.utime = utime;
	}

	public Date getUtime(){
		return utime;
	}

	public void setRemark(String remark){
		this.remark = remark;
	}

	public String getRemark(){
		return remark;
	}

	public void setCtime(Date ctime){
		this.ctime = ctime;
	}

	public Date getCtime(){
		return ctime;
	}

	public HdzjComponentUi clone() {
		try {
			return (HdzjComponentUi) super.clone();
		} catch (CloneNotSupportedException e) {
			log.error(e.getMessage(), e);
			return null;
		}
	}

	public String toString() {
		return JSON.toJSONString(this);
	}
}
