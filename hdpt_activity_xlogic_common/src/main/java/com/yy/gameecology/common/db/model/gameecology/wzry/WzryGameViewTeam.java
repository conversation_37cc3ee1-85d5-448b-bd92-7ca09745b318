package com.yy.gameecology.common.db.model.gameecology.wzry;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-11 20:22
 **/
@Data
@TableColumn(underline = true)
public class WzryGameViewTeam implements Serializable {
   public static String TABLE_NAME = "wzry_game_view_team";

   // 表主键属性集合
   public static final String[] TABLE_PKS = new String[]{"id"};

   public static RowMapper<WzryGameViewTeam> ROW_MAPPER = null;

   /**
    * id
    */
   private Long id;

   private Long actId;

   /**
    * game_view_id
    */
   private Long gameViewId;

   /**
    * 参赛用户
    */
   private Long uid;

   /**
    * 用户已分配到具体赛事
    */
   private Integer allocationTeam;

   /**
    * 参赛时间
    */
   private Date createTime;

   public WzryGameViewTeam() {}
}