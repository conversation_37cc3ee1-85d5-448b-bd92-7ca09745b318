package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Getter;
import lombok.Setter;
import org.springframework.jdbc.core.RowMapper;

import java.util.Date;

@Getter
@Setter
@TableColumn(underline = true)
public class Cmpt2061LatestCp {

    public static final String TABLE_NAME = "cmpt_2061_latest_cp";

    public static final String[] TABLE_PKS = {"id"};

    public static final RowMapper<Cmpt2061LatestCp> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt2061LatestCp result = new Cmpt2061LatestCp();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getLong("cmpt_use_inx"));
        result.setDateCode(rs.getString("date_code"));
        result.setRankId(rs.getLong("rank_id"));
        result.setAnchorUid(rs.getLong("anchor_uid"));
        result.setUserUid(rs.getLong("user_uid"));
        result.setSid(rs.getLong("sid"));
        result.setSsid(rs.getLong(("ssid")));
        result.setGiftTime(rs.getTimestamp("gift_time"));
        return result;
    };

    protected Long id;

    protected Long actId;

    protected Long cmptUseInx;

    protected String dateCode;

    protected Long rankId;

    protected Long anchorUid;

    protected Long userUid;

    protected Long sid;

    protected Long ssid;

    protected Date giftTime;
}
