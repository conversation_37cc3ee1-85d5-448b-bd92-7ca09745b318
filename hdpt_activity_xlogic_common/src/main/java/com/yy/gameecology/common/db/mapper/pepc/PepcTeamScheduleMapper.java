package com.yy.gameecology.common.db.mapper.pepc;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface PepcTeamScheduleMapper {

    @Select("""
                  <script>
                  select count(1) from pepc_team_schedule
                  where act_id =#{actId}
                  and phase_id=#{phaseId}
                 <if test="state != null">
                  and state = #{state}
                 </if>
                 <if test="settleRound != null">
                 and settle_round &gt;= #{settleRound}
                 </if>
            </script>
                  """)
    int countPepcTeamSchedule(@Param("actId") long actId,
                              @Param("phaseId") int phaseId,
                              @Param("state") Integer state,
                              @Param("settleRound") Integer settleRound);


    @Update("""
                     <script>
            update pepc_team_schedule 
            set state = #{newState}
            where act_id =#{actId}
            and state =#{oldState}
               and id in 
                          <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'>
                              #{item}
                          </foreach>
                          
                  </script>
                  """)
    int updateStatus(@Param("actId") long actId, @Param("ids") List<Long> ids, @Param("newState") Integer newState, @Param("oldState") Integer oldState);

    @Update("""
                     <script>
            update pepc_team_schedule 
            set settle_round = settle_round+1,
            game_amount = game_amount + #{addGameCount},
            score = score +   #{addScore}
            where act_id =#{actId}
            and group_code = #{groupCode}
            and phase_id = #{phaseId}
            and team_id in
          <foreach item='item' index='index' collection='teamId' open='(' separator=',' close=')'>
              #{item}
          </foreach>
                  </script>
                  """)
    int addGameScore(@Param("actId") long actId,
                     @Param("groupCode") String groupCode,
                     @Param("phaseId") int phaseId,
                     @Param("teamId") List<Long> teamId,
                     @Param("addScore") Long addScore,
                     @Param("addGameCount") Integer addGameCount);


}
