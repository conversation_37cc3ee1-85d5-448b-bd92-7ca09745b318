package com.yy.gameecology.common.client;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.bean.UserInfo;
import com.yy.gameecology.common.consts.CacheTimeout;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.MyListUtils;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.BatchUserInfoWithNickExt;
import com.yy.java.webdb.UserInfoWithNickExt;
import com.yy.java.webdb.WebdbQueryColumnEnum;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.java.webdb.sa.SaWebdbResult;
import com.yy.java.webdb.sa.SaWebdbUserInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户类信息服务接口
 *
 * <AUTHOR>
 * @date 2021年06月01日 上午10:33:27
 **/
@Component
public class WebdbUinfoClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private SaWebdbUserInfoService saWebdbUserInfoService;

    // webdb 用户信息列
    private final static Set<WebdbQueryColumnEnum> USER_INFO_COLUMNS = Sets.newHashSet(
            // 用户uid
            WebdbQueryColumnEnum.UID,
            // 通行证
            WebdbQueryColumnEnum.PASSPORT,
            // 邮箱
            WebdbQueryColumnEnum.ACCOUNT,
            // 用户昵称
            WebdbQueryColumnEnum.NICK,
            // 性别
            WebdbQueryColumnEnum.SEX,
            // 签名
            WebdbQueryColumnEnum.SIGN,
            // 个人积分,按分钟计算
            WebdbQueryColumnEnum.JIFEN,
            // 注册时间
            WebdbQueryColumnEnum.REGISTER_TIME,
            // 个人高清头像图片url
            WebdbQueryColumnEnum.LOGO_HD,
            // yy号
            WebdbQueryColumnEnum.YYNO,
            //  比老webdb client 多加的：用户个人普通自定义头像url(60*60)
            WebdbQueryColumnEnum.CUSTOM_LOGO,
            //  比老webdb client 多加的：个人系统头像类型。0为自定义头像，即为hdlogo；其他值为系统定义头像
            WebdbQueryColumnEnum.LOGO_INDEX,
            //个人高清头像 144
            WebdbQueryColumnEnum.HD_LOGO_144
    );

    private static final Set<WebdbQueryColumnEnum> USER_INFO_EXT_COLUMNS = Sets.newHashSet(
            WebdbQueryColumnEnum.LOGO_HD,
            WebdbQueryColumnEnum.LOGO_INDEX,
            WebdbQueryColumnEnum.CUSTOM_LOGO,
            WebdbQueryColumnEnum.HD_LOGO_100,
            WebdbQueryColumnEnum.HD_LOGO_144,
            WebdbQueryColumnEnum.NICK,
            WebdbQueryColumnEnum.STAGE_NAME,
            WebdbQueryColumnEnum.SEX,
            WebdbQueryColumnEnum.BIRTHDAY,
            WebdbQueryColumnEnum.YYNO,
            WebdbQueryColumnEnum.BAIDU_NICK,
            WebdbQueryColumnEnum.TIEBA_NICK,
            WebdbQueryColumnEnum.UID
    );

    /**
     * 批量获取用户信息
     */
    public Map<Long, WebdbUserInfo> batchGetUserInfo(List<Long> uids) {
        Map<Long, WebdbUserInfo> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(uids)) {
            return result;
        }
        final int maxBatchSize = 499;
        List<List<Long>> batchUids = MyListUtils.subList(uids, maxBatchSize);
        for (List<Long> uidList : batchUids) {
            Map<Long, WebdbUserInfo> userInfoMap = batchGetUserInfoSplit(uidList);
            if (MapUtils.isNotEmpty(userInfoMap)) {
                result.putAll(userInfoMap);
            }
        }

        return result;
    }

    private Map<Long, WebdbUserInfo> batchGetUserInfoSplit(List<Long> uids) {
        if (CollectionUtils.isEmpty(uids)) {
            return Collections.emptyMap();
        }
        Clock clock = new Clock();
        try {
            SaWebdbResult<Map<String, WebdbUserInfo>> webdbResult = saWebdbUserInfoService.getUserInfo(uids, USER_INFO_COLUMNS);

            if (webdbResult == null || webdbResult.getCode() != 0) {
                log.warn("batchGetUserInfo empty,result:{},req:{}", webdbResult, uids);
                return Collections.emptyMap();
            }

            Map<Long, WebdbUserInfo> result = new HashMap<>(webdbResult.getData().size());
            for (Map.Entry<String, WebdbUserInfo> entry : webdbResult.getData().entrySet()) {
                long uid = Long.parseLong(entry.getKey());
                // 填充用户 yylogo，使用WebdbUtils.getLogo
//                fillUserYylogo(entry.getValue());
                result.put(uid, entry.getValue());
            }

            return result;
        } catch (Exception e) {
            log.error("batchGetUserInfo exception@firstUid:{} {}", uids.get(0), clock.tag(), e);
        }

        return Collections.emptyMap();
    }

    public WebdbUserInfo getUserInfo(long uid) {
        if(uid <= 0) {
            return null;
        }
        SaWebdbResult<WebdbUserInfo> webdbResult = saWebdbUserInfoService.getUserInfo(uid, USER_INFO_COLUMNS);
        if (webdbResult == null || webdbResult.getCode() != 0) {
            log.warn("getUserInfo result unexpected uid={}", uid);
            return null;
        }
        return webdbResult.getData();
    }

    public Long getUidByYYNum(String yynum) {
        SaWebdbResult<Long> webdbResult = saWebdbUserInfoService.getUidByYyno(yynum);
        if (webdbResult == null || webdbResult.getCode() != 0) {
            log.error("getUidByYYNum result unexpected yynum={}", yynum);
            return null;
        }
        return webdbResult.getData();
    }


    public Map<Long, UserInfo> batchGetUserInfo2(List<Long> uids) {
        Clock clock = new Clock();
        Map<Long, UserInfo> ret = Maps.newHashMap();
        Map<Long, WebdbUserInfo> userMaps = batchGetUserInfo(uids);
        if (userMaps != null) {
            for (Long key : userMaps.keySet()) {
                UserInfo userInfo = toUserInfo(key, userMaps.get(key));
                ret.put(key, userInfo);
            }
        }

        log.info("batchGetUserInfo2,size:{},cost:{}", ret.keySet().size(), clock.tag());

        return ret;
    }

    private static UserInfo toUserInfo(long uid, WebdbUserInfo webdbUserInfo) {
        UserInfo userInfo = new UserInfo();
        userInfo.setUid(uid);
        final String avatar = WebdbUtils.getLogo(webdbUserInfo), hdAvatar, nick;
        final long yyno;
        if (webdbUserInfo != null) {
            hdAvatar = webdbUserInfo.getLogohd();
            nick = webdbUserInfo.getNick();
            yyno = Long.parseLong(webdbUserInfo.getYyno());
        } else {
            hdAvatar = StringUtils.EMPTY;
            nick = StringUtils.EMPTY;
            yyno = 0;
        }

        userInfo.setAvatar(avatar);
        userInfo.setHdAvatar(hdAvatar);
        userInfo.setNickName(nick);
        userInfo.setYyno(yyno);

        return userInfo;
    }

    @Report
    @Cached(timeToLiveMillis = CacheTimeout.YY_USER_INFO)
    public BatchUserInfoWithNickExt batchGetUserInfoWithNickExt(List<Long> uids) {
        return batchGetUserInfoWithNickExt(uids, null);
    }

    @Report
    @Cached(timeToLiveMillis = CacheTimeout.YY_USER_INFO)
    public BatchUserInfoWithNickExt batchGetUserInfoWithNickExt(List<Long> uids, Integer hostId) {
        if (CollectionUtils.isEmpty(uids)) {
            return null;
        }
        Clock clock = new Clock();
        if (hostId == null || hostId <= 0) {
            hostId = 1;
        }
        try {
            SaWebdbResult<BatchUserInfoWithNickExt> webdbResult = saWebdbUserInfoService.getUserInfoWithNickExt(uids, USER_INFO_EXT_COLUMNS, hostId.shortValue());
            if (webdbResult == null || webdbResult.getCode() != 0) {
                log.warn("batchGetUserInfoWithNickExt result unexpected");
                return null;
            }

            return webdbResult.getData();
        } catch (Exception e) {
            log.error("batchGetUserInfo exception@firstUid:{} {}", uids, clock.tag(), e);
        }

        return null;
    }

    @Report
    @Cached(timeToLiveMillis = CacheTimeout.YY_USER_INFO)
    public UserInfoWithNickExt getUserInfoWithNickExt(long uid) {
        return getUserInfoWithNickExt(uid, null);
    }

    @Report
    @Cached(timeToLiveMillis = CacheTimeout.YY_USER_INFO)
    public UserInfoWithNickExt getUserInfoWithNickExt(long uid, Integer hostId) {
        if (hostId == null || hostId <= 0) {
            hostId = 1;
        }

        try {
            SaWebdbResult<UserInfoWithNickExt> webdbResult = saWebdbUserInfoService.getUserInfoWithNickExt(uid, USER_INFO_EXT_COLUMNS, hostId.shortValue());
            if (webdbResult == null || webdbResult.getCode() != 0) {
                log.warn("getUserInfoWithNickExt result unexpected uid={} hostId={}", uid, hostId);
                return null;
            }

            return webdbResult.getData();
        } catch (Exception e) {
            log.error("getUserInfoWithNickExt exception:", e);
        }

        return null;
    }

    public Long getUidByImid(long yyno) {
        String yynoStr = Convert.toString(yyno);


        try {
            SaWebdbResult<Long> result = saWebdbUserInfoService.getUidByYyno(yynoStr);
            if (result == null || result.getCode() != 0) {
                log.error("getUidByImid empty,result:{},req:{}", result, yynoStr);
                return 0L;
            }
            return result.getData();

        } catch (Exception e) {
            log.error("getUidByImid error,yyno:{},e:{}", yyno, e.getMessage(), e);
            return 0L;
        }
    }

    public Map<Long, Long> getUidByYyno(Collection<Long> yynos) {
        if (CollectionUtils.isEmpty(yynos)) {
            return Collections.emptyMap();
        }

        try {
            SaWebdbResult<Map<String, Long>> result = saWebdbUserInfoService.getUidByYyno(yynos.stream().map(String::valueOf).toList());
            if (result == null || result.getCode() != 0) {
                log.error("getUidByYyno fail:{}", yynos);
                return Collections.emptyMap();
            }

            if (MapUtils.isEmpty(result.getData())) {
                return Collections.emptyMap();
            }

            return result.getData().entrySet().stream().collect(Collectors.toMap(e -> Long.parseLong(e.getKey()), Map.Entry::getValue, (k1, k2) -> k1));
        } catch (Exception e) {
            log.error("getUidByYyno error:{}", yynos, e);

            return Collections.emptyMap();
        }
    }
}
