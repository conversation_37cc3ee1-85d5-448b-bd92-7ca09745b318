1. AccessDeny
	- IP访问计数器，可用来做IP方法限制，可按小时和按天做计数控制

2. AESUtil 
	- AES 加解密工具

3. Clock 
	- 指令执行耗时计时器
	
4. Convert 
	- 丰富的数据类型转器，可节省大量代码，使得代码简洁美观
	
5. CRC32Util
	- CRC32 求模工具，可用来做分表计算
	
6. DateUtil
	- 丰富的时间处理类

7. HMacSHA1
	- 新的SHA 算法类，【公司充值支付平台】那边强制指定的消息摘要算法
	
8. IpConvert
	- ip 在数值 和 文本间互相转换的类
	
9. JsonUtil
	- 封装  org.codehaus.jackson 的处理类

10. MD5SHAUtil
	- 封装了 MD5/SHA1/SHA256 消息摘要处理方法
	
11. MyListUtils
	- List slice 处理类
	
12. PageInfo
	- 查询分页处理对象
	
13. RandomUtil
	- 随机串生成类
	
14. ReadFileUtil
	- 文件行读取类

15. ResourceUtil
	- 资源文件读取类，可从 jar 包中直接读取资源文件内容
	
16. StringUtil
	- 字符串处理类，提供了丰富的函数处理各种常见的字符串操作

17. SystemPropertiesUtil
	- 系统属性读取类，获得ip、主机名字
	
18. UploadImageUtil
	- 图片文件上传处理类
	
19. XmlUtil
	- 提供 xml 读取功能，可根据 a.b.c 形式的路径读取响应节点内容