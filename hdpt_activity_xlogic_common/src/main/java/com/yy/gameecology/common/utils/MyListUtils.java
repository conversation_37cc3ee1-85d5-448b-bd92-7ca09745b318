
package com.yy.gameecology.common.utils;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class MyListUtils {

    private static final Log log = LogFactory.getLog(MyListUtils.class);

    private MyListUtils() {}

    /**
     * 欺骗编译器
     *
     * @param strList
     * @return
     */
    public static  List<Object> toObjectList(List<?> strList) {
        return (List<Object>) strList;
    }


    /**
     * 获取子列表
     *
     * @param list
     * @param index - 偏移值
     * @param fetchs - 获取数量
     * @return
     */
    public static <T> List<T> getSubList(List<T> list, int index, int fetchs) {
        int total = list.size();
        int end = index + fetchs;
        if (end < total) {
            return list.subList(index, end);
        } else if (index < total) {
            return list.subList(index, total);
        } else {
            return Lists.newArrayList();
        }
    }

    public static <T> List<T> subList(final List<? extends T> list, int fromIndex, int toIndex) {
        if (list == null) {
            return null;
        }

        // 处理负值
        if (toIndex < 0) {
            toIndex = list.size() + toIndex;
        }
        if (fromIndex < 0) {
            fromIndex = list.size() + fromIndex;
        }

        if (toIndex > list.size()) {
            toIndex = list.size();
        }

        if (fromIndex > toIndex) {
            return Collections.emptyList();
        }

        if (fromIndex < 0) {
            fromIndex = 0;
        }
        if (toIndex < 0) {
            toIndex = 0;
        }

        List<T> result = new ArrayList<T>(toIndex - fromIndex);
        for (int i = fromIndex; i < toIndex; i++) {
            result.add(list.get(i));
        }
        return result;
    }

    public static <T> List<T> left(final List<? extends T> list, int num) {
        if (list == null) {
            return null;
        }
        return subList(list, 0, num);
    }

    public static <T> List<T> right(final List<? extends T> list, int num) {
        if (list == null) {
            return null;
        }
        int formIndex = list.size() - num;
        int toIndex = list.size();
        return subList(list, formIndex, toIndex);
    }

    public static <T> T first(final List<? extends T> list) {
        return get(list, 0);
    }

    public static <T> T last(final List<? extends T> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return list.get(list.size() - 1);
    }

    public static <T> T randomGet(List<? extends T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(RandomUtils.nextInt(0, list.size()));
    }

    /* 根据下标选择元素 */
    public static <T> List<T> select(List<? extends T> elements, int[] indices) {
        if (CollectionUtils.isEmpty(elements) || ArrayUtils.isEmpty(indices)) {
            return Collections.emptyList();
        }
        List<T> list = new ArrayList<T>(indices.length);
        for (int index : indices) {
            if (0 <= index && index < elements.size()) {
                list.add(elements.get(index));
            }
        }
        return list;
    }

    public static <T> T get(List<? extends T> list, int index) {
        // empty list
        if (list == null || list.isEmpty()) {
            return null;
        }

        // index out of bounds
        if (index < 0 || index > list.size() - 1) {
            log.error("index out of bounds: " + index);
            return null;
        }

        return list.get(index);
    }


    /**
     * 把list分割成几段
     *
     * @param list      list
     * @param batchSize 没段的最大数量
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> subList(final List<T> list, int batchSize) {
        List<List<T>> res = Lists.newArrayList();
        if (CollectionUtils.isEmpty(list) || batchSize == 0) {
            return res;
        }
        int startIndex = 0;
        int endIndex = list.size() > batchSize ? batchSize : list.size();
        while (endIndex <= list.size()) {
            List<T> slice = list.subList(startIndex, endIndex);
            res.add(slice);

            if (endIndex == list.size()) {
                break;
            }

            startIndex = endIndex;
            endIndex = (endIndex + batchSize) > list.size() ? list.size() : endIndex + batchSize;
        }


        return res;
    }

    /**
     * 第一个list是前N个元素，剩余的元素放在第二个list
     */
    public static <T> List<List<T>> splitList(List<T> inputList, int n) {
        List<List<T>> result = Lists.newArrayList();
        if (inputList == null || inputList.isEmpty()) {
            result.add(Lists.newArrayList());
            result.add(Lists.newArrayList());
            return result;
        }
        if (n <= 0) {
            result.add(Lists.newArrayList());
            result.add(Lists.newArrayList(inputList));
            return result;
        }
        if (n >= inputList.size()) {
            result.add(Lists.newArrayList(inputList));
            result.add(Lists.newArrayList());
            return result;
        }
        List<T> firstList = new ArrayList<>(inputList.subList(0, n));
        List<T> secondList = new ArrayList<>(inputList.subList(n, inputList.size()));
        result.add(firstList);
        result.add(secondList);
        return result;
    }
}
