
package com.yy.gameecology.common.cache.support;

import com.yy.gameecology.common.cache.Cache;
import com.yy.gameecology.common.cache.CacheManager;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Abstract base class implementing the common CacheManager methods. Useful for 'static'
 * environments where the backing caches do not change.
 *
 * <AUTHOR> Leau
 */
public abstract class AbstractCacheManager implements CacheManager, InitializingBean {

    // fast lookup by name map
    private final ConcurrentMap<String, Cache> caches = new ConcurrentHashMap<String, Cache>();

    private Collection<String> names;

    @SuppressWarnings("deprecation")
    public void afterPropertiesSet() {
        Collection<Cache> cacheSet = loadCaches();

        Assert.notEmpty(cacheSet);

        caches.clear();

        // preserve the initial order of the cache names
        Set<String> cacheNames = new LinkedHashSet<String>(cacheSet.size());

        for (Cache cache : cacheSet) {
            caches.put(cache.getName(), cache);
            cacheNames.add(cache.getName());
        }

        names = Collections.unmodifiableSet(cacheNames);
    }

    /**
     * Loads the caches into the cache manager. Occurs at startup. The returned collection should
     * not be null.
     */
    protected abstract Collection<Cache> loadCaches();

    /**
     * Returns the internal cache map.
     *
     * @return internal cache map
     */
    protected final ConcurrentMap<String, Cache> getCacheMap() {
        return caches;
    }

    public Cache getCache(String name) {
        return caches.get(name);
    }

    public Collection<String> getCacheNames() {
        return names;
    }
}
