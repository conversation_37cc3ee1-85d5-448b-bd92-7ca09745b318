package com.yy.gameecology.common.client;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.bean.GuildInfoBean;
import com.yy.gameecology.common.bean.MyChannelInfo;
import com.yy.gameecology.common.consts.CacheTimeout;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.WebdbChannelColumnEnum;
import com.yy.java.webdb.WebdbChannelInfo;
import com.yy.java.webdb.WebdbSubChannelColumnEnum;
import com.yy.java.webdb.WebdbSubChannelInfo;
import com.yy.java.webdb.sa.SaWebdbChannelInfoService;
import com.yy.java.webdb.sa.SaWebdbResult;
import com.yy.thrift.webdbservice.SaResponseSet;
import com.yy.thrift.webdbservice.StringList;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * 频道类信息服务接口
 *
 * <AUTHOR>
 * @date 2021年06月01日 上午10:33:27
 **/
@Component
public class WebdbSinfoClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private SaWebdbChannelInfoService saWebdbChannelInfoService;

    @Autowired
    @Lazy
    private WebdbSinfoClient webdbSinfoClient;

    /**
     * webdb 子频道信息列
     */
    private final static Set<WebdbSubChannelColumnEnum> SUB_CHANNEL_INFO_COLUMNS = Sets.newHashSet(
            /* 顶级频道id*/
            WebdbSubChannelColumnEnum.TID,
            /* 频道id*/
            WebdbSubChannelColumnEnum.SID,
            /* 父频道id*/
            WebdbSubChannelColumnEnum.PID,
            /* 频道名字*/
            WebdbSubChannelColumnEnum.NAME,
            /*子频道人数限制*/
            WebdbSubChannelColumnEnum.MAXMAN,
            /* 频道密码*/
            WebdbSubChannelColumnEnum.PASSWD,
            /* 频道创建时间*/
            WebdbSubChannelColumnEnum.CREATE_TIME,
            /* 麦序模式*/
            WebdbSubChannelColumnEnum.STYLE,
            /* 麦序时间*/
            WebdbSubChannelColumnEnum.MICROTIME,
            /* 是否限制文字聊天速度*/
            WebdbSubChannelColumnEnum.IS_LIMIT_TXT,
            /* 限制文字聊天速度每句间隔秒数*/
            WebdbSubChannelColumnEnum.TXT_LIMITTIME,
            /* 排序序号*/
            WebdbSubChannelColumnEnum.SORT,
            /* 收费频道类型*/
            WebdbSubChannelColumnEnum.CHARGE,
            /* 频道模板*/
            WebdbSubChannelColumnEnum.TEMPLATE_ID,
            /* 子频道是否有设置密码*/
            WebdbSubChannelColumnEnum.IS_PASSWD_SET,
            /* 子频道是否有限制游客进入*/
            WebdbSubChannelColumnEnum.IS_GUEST_ACCESS_LIMIT);

    /* webdb 顶级频道信息列*/
    private final static Set<WebdbChannelColumnEnum> CHANNEL_INFO_COLUMNS = Sets.newHashSet(
            WebdbChannelColumnEnum.SID,
            WebdbChannelColumnEnum.ASID,
            WebdbChannelColumnEnum.NAME,
            WebdbChannelColumnEnum.OWNERID,
            WebdbChannelColumnEnum.TYPE,
            WebdbChannelColumnEnum.TYPESTR,
            WebdbChannelColumnEnum.LOGO_INDEX,
            WebdbChannelColumnEnum.LOGO_URL,
            WebdbChannelColumnEnum.CREDIT,
            WebdbChannelColumnEnum.TEMPLATE_ID,
            WebdbChannelColumnEnum.JIEDAI_SID
    );

    /**
     * <p>批量获取子频道信息</p>
     *
     * @param sidSsids 是顶级频道_子频道
     * @return 参数传入 顶级频道默认子频道的时候， key：顶级频道_顶级频道 , 查询不到数据返回空
     */
    @Report
    public Map<String, WebdbSubChannelInfo> batchGetSubChannelInfo(List<String> sidSsids) {
        return webdbSinfoClient.batchGetSubChannelInfoNoCache(sidSsids);
    }

    @Report
    @Cached(timeToLiveMillis = CacheTimeout.YY_SINGLE_SUB_CHANNEL_INFO)
    public WebdbSubChannelInfo getSubChannelInfo(String sidSsids) {
        return webdbSinfoClient.getSubChannelInfoNoCache(sidSsids);
    }

    public WebdbSubChannelInfo getSubChannelInfoNoCache(String sidSsid) {
        if (!StringUtils.contains(sidSsid, StringUtil.UNDERSCORE)) {
            return null;
        }

        String[] arr = sidSsid.split(StringUtil.UNDERSCORE);
        long sid = Convert.toLong(arr[0]);
        long ssid = Convert.toLong(arr[1]);
        SaWebdbResult<WebdbSubChannelInfo> webdbResult = saWebdbChannelInfoService.getSubChannelInfo(sid, ssid, SUB_CHANNEL_INFO_COLUMNS);
        if (webdbResult == null || webdbResult.getCode() != 0) {
            return null;
        }

        return webdbResult.getData();
    }

    /**
     *
     * @param sidSsids
     * @return key = sid_ssid (原始：tid_sid)
     */
    @Report
    public Map<String, WebdbSubChannelInfo> batchGetSubChannelInfoNoCache(List<String> sidSsids) {
        if (CollectionUtils.isEmpty(sidSsids)) {
            return Collections.emptyMap();
        }

        Clock clock = new Clock();
        try {
            List<List<Long>> sidSsidList = sidSsids.stream()
                    .filter(sidSsid -> StringUtils.contains(sidSsid, StringUtil.UNDERSCORE))
                    .map(sidSsid -> {
                        String[] arr = sidSsid.split(StringUtil.UNDERSCORE);
                        long sid = Convert.toLong(arr[0]);
                        long ssid = Convert.toLong(arr[1]);
                        return List.of(sid, ssid);
                    }).toList();
            SaWebdbResult<Map<String, WebdbSubChannelInfo>> webdbResult = saWebdbChannelInfoService.getSubChannelInfo(sidSsidList, SUB_CHANNEL_INFO_COLUMNS);
            if (webdbResult == null || webdbResult.getCode() != 0) {
                return Collections.emptyMap();
            }
            return webdbResult.getData();

        } catch (Exception e) {
            log.error("batchGetSubChannelInfo exception@sidSsids:{} {}", sidSsids, clock.tag(), e);
        }

        return Collections.emptyMap();
    }

    public Map<Long, WebdbChannelInfo> batchGetTopChannelInfo(List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return Collections.emptyMap();
        }

        try {
            SaWebdbResult<Map<String, WebdbChannelInfo>> webdbResult = saWebdbChannelInfoService.getChannelInfo(sids, CHANNEL_INFO_COLUMNS);
            if (webdbResult == null || webdbResult.getCode() != 0) {
                log.error("batchGetTopChannelInfo result unexpected");
                return Collections.emptyMap();
            }

            Map<Long, WebdbChannelInfo> result = new HashMap<>(webdbResult.getData().size());
            for (Map.Entry<String, WebdbChannelInfo> entry : webdbResult.getData().entrySet()) {
                long sid = Convert.toLong(entry.getKey());
                WebdbChannelInfo channelInfo = entry.getValue();
                channelInfo.setLogoUrl(WebdbUtils.getLogo(channelInfo));
                result.put(sid, channelInfo);
            }
            return result;
        } catch (Exception e) {
            log.error("batchGetTopChannelInfo exception:", e);
        }

        return Collections.emptyMap();
    }

    @Cached(timeToLiveMillis = CacheTimeout.YY_TOP_CHANNEL_INFO)
    public WebdbChannelInfo getChannelInfoByAsId(Long asId) {
        try {
            SaWebdbResult<WebdbChannelInfo> webdbResult = saWebdbChannelInfoService.getChannelInfoByAsid(asId, CHANNEL_INFO_COLUMNS);
            if (webdbResult == null || webdbResult.getCode() != 0) {
                log.warn("getChannelInfoByAsId result unexpected asid:{}", asId);
                return null;
            }

            WebdbChannelInfo channelInfo = webdbResult.getData();
            channelInfo.setLogoUrl(WebdbUtils.getLogo(channelInfo));
            return channelInfo;
        } catch (Exception e) {
            log.error("getChannelInfoByAsId exception:", e);
        }
        return null;
    }

    @Cached(timeToLiveMillis = CacheTimeout.YY_TOP_CHANNEL_INFO)
    public WebdbChannelInfo getChannelInfo(long sid) {
        if (sid <= 0) {
            log.warn("getChanelInfo sid is illegal sid = {}", sid);
            return null;
        }
        try {
            SaWebdbResult<WebdbChannelInfo> webdbResult = saWebdbChannelInfoService.getChannelInfo(sid, CHANNEL_INFO_COLUMNS);
            if (webdbResult == null || webdbResult.getCode() != 0) {
                log.warn("getChannelInfo result unexpected");
                return null;
            }

            WebdbChannelInfo channelInfo = webdbResult.getData();
            channelInfo.setLogoUrl(WebdbUtils.getLogo(channelInfo));
            return channelInfo;
        } catch (Exception e) {
            log.error("getChannelInfoByAsId exception:", e);
        }
        return null;
    }

    /**
     * 获取子频道加载的模板ID
     */
    public String getSessionTemplate(Long sid, Long ssid) {
        try {
            // 相等时认为是查顶级子频道（使用 sa_get_subsess_info 是查不到顶级子频道信息的，这个不合理但没办法！）
            if (sid.equals(ssid)) {
                WebdbChannelInfo channelInfo = webdbSinfoClient.getChannelInfo(sid);
                return channelInfo == null ? StringUtils.EMPTY : channelInfo.getTemplateId();
            } else {
                String sidSSid = sid + StringUtil.UNDERSCORE + ssid;
                WebdbSubChannelInfo subChannelInfo = webdbSinfoClient.getSubChannelInfo(sidSSid);

                return subChannelInfo == null ? StringUtils.EMPTY : subChannelInfo.getTemplateId();
            }
        } catch (Exception e) {
            log.error("getSessionTemplate error,sid:{},ssid:{},e:{}", sid, ssid, e.getMessage(), e);
        }

        return StringUtils.EMPTY;
    }


    /**
     * 通过用户uid查询用户拥有的所有频道的信息，包括长位id、频道名字和短位id
     */
    public Map<Long, GuildInfoBean> getSessionListByOwnerid(long ownerUid) {
        Clock clock = new Clock();
        try {
            SaWebdbResult<List<WebdbChannelInfo>> webdbResult = saWebdbChannelInfoService.getChannelsByOwnerUid(ownerUid, Set.of(WebdbChannelColumnEnum.SID, WebdbChannelColumnEnum.NAME, WebdbChannelColumnEnum.ASID));
            if (webdbResult == null || webdbResult.getCode() != 0) {
                log.error("getSessionListByOwnerid result unexpected");
                return Collections.emptyMap();
            }

            Map<Long, GuildInfoBean> result = new HashMap<>(webdbResult.getData().size());
            for (WebdbChannelInfo channelInfo : webdbResult.getData()) {
                long sid = Convert.toLong(channelInfo.getSid()), asid = Convert.toLong(channelInfo.getAsid());
                GuildInfoBean bean = new GuildInfoBean(sid, asid, channelInfo.getName());
                result.put(sid, bean);
            }
            log.info("getSessionListByOwnerid ok@ownerUid:{}, size:{} {}", ownerUid, result.size(), clock.tag());

            return result;
        } catch (Exception e) {
            log.error("getSessionListByOwnerid error@ownerUid:{} {}", ownerUid, clock.tag(), e);
            // 表示发生错误
        }
        return Collections.emptyMap();
    }

    //是否是ow
    @Cached(timeToLiveMillis = 10000)
    public long getOwUid(Long sid) {
        WebdbChannelInfo channelInfo = this.getChannelInfo(sid);
        if (channelInfo == null) {
            return 0L;
        }

        String owUid = channelInfo.getOwnerid();
        return Convert.toLong(owUid, 0);
    }

    public boolean isOw(Long sid, Long uid) {
        return uid.equals(getOwUid(sid));
    }

    // 校验子频道合法性
    @Cached(timeToLiveMillis = 10000)
    public boolean validateSubSession(String topSid, String ssid) {
        try {
            //都是顶级频道
            if (topSid.equals(ssid)) {
                return true;
            }
            //读取父频道

            String subsid = topSid + StringUtil.UNDERSCORE + ssid;
            WebdbSubChannelInfo subChannelInfo = webdbSinfoClient.getSubChannelInfo(subsid);

            //数据不存在
            if (subChannelInfo == null) {
                return false;
            }

            //1级子频道
            String parentSid = subChannelInfo.getPid();
            if (topSid.equals(parentSid)) {
                return true;
            }

            //2级子频道 ？？？
            String tid = subChannelInfo.getTid();
            if (topSid.equals(tid)) {
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("validateSubSession error,topsid:{},ssid:{},e:{}", topSid, ssid, e.getMessage(), e);
            return false;
        }
    }

    /**
     * SaResponseSet 转成 List<Map>
     */
    private List<Map<String, String>> getSaResponseMaps(SaResponseSet saResponseSet) {
        Map<String, Integer> keyIndex = saResponseSet.getKeyIndex();
        List<StringList> dataSet = saResponseSet.getDataSet();
        if (MapUtils.isEmpty(keyIndex) || CollectionUtils.isEmpty(dataSet)) {
            return Lists.newArrayList();
        }
        List<Map<String, String>> saResponseMaps = Lists.newArrayList();
        for (StringList data : dataSet) {
            Map<String, String> dataMap = Maps.newHashMap();
            List<String> strList = data.getStrList();
            for (Map.Entry<String, Integer> entry : keyIndex.entrySet()) {
                String key = entry.getKey();
                String value = strList.get(entry.getValue());
                if ("logo_url".equals(key) && StringUtil.isBlank(value)) {
                    value = Const.IMAGE.DEFAULT_CHANNEL_LOGO;
                }
                dataMap.put(key, value);
            }
            saResponseMaps.add(dataMap);
        }
        return saResponseMaps;
    }

    public List<MyChannelInfo> getRecursessInfo(long sid) {
        try {
            SaWebdbResult<List<WebdbSubChannelInfo>> webdbResult = saWebdbChannelInfoService.getSubChannelList(sid, sid, false,
                    Set.of(WebdbSubChannelColumnEnum.SID, WebdbSubChannelColumnEnum.PID, WebdbSubChannelColumnEnum.TID, WebdbSubChannelColumnEnum.NAME, WebdbSubChannelColumnEnum.TEMPLATE_ID));
            if (webdbResult == null || webdbResult.getCode() != 0) {
                log.error("getRecursessInfo result unexpected");
                return Collections.emptyList();
            }

            List<MyChannelInfo> result = new ArrayList<>(webdbResult.getData().size());
            for (WebdbSubChannelInfo subChannelInfo : webdbResult.getData()) {
                MyChannelInfo channelInfo = new MyChannelInfo();
                channelInfo.setName(subChannelInfo.getName());
                channelInfo.setTemplateId(subChannelInfo.getTemplateId());
                long pid = Convert.toLong(subChannelInfo.getPid(), 0);
                long ssid = Convert.toLong(subChannelInfo.getSid(), 0);
                channelInfo.setSid(pid);
                channelInfo.setSsid(ssid);
                result.add(channelInfo);
            }

            return result;
        } catch (Exception ex) {
            log.error("getRecursessInfo error sid={}", sid, ex);
        }

        return Collections.emptyList();
    }

    public long createChannel(long uid, String channelName) {
        throw new UnsupportedOperationException("暂无支持");
    }

    public static Map<String, String> toSubChannelMap(WebdbSubChannelInfo subChannelInfo) {
        if (subChannelInfo == null) {
            return Collections.emptyMap();
        }

        Map<String, String> result = new HashMap<>(SUB_CHANNEL_INFO_COLUMNS.size());
        result.put(WebdbSubChannelColumnEnum.TID.getCol(), subChannelInfo.getTid());
        result.put(WebdbSubChannelColumnEnum.SID.getCol(), subChannelInfo.getSid());
        result.put(WebdbSubChannelColumnEnum.PID.getCol(), subChannelInfo.getPid());
        result.put(WebdbSubChannelColumnEnum.NAME.getCol(), subChannelInfo.getName());
        result.put(WebdbSubChannelColumnEnum.MAXMAN.getCol(), subChannelInfo.getMaxman());
        result.put(WebdbSubChannelColumnEnum.PASSWD.getCol(), subChannelInfo.getPasswd());
        result.put(WebdbSubChannelColumnEnum.CREATE_TIME.getCol(), subChannelInfo.getCreateTime());
        result.put(WebdbSubChannelColumnEnum.STYLE.getCol(), subChannelInfo.getStyle());
        result.put(WebdbSubChannelColumnEnum.MICROTIME.getCol(), subChannelInfo.getMicroTime());
        result.put(WebdbSubChannelColumnEnum.IS_LIMIT_TXT.getCol(), subChannelInfo.getIsLimitTxt());
        result.put(WebdbSubChannelColumnEnum.TXT_LIMITTIME.getCol(), subChannelInfo.getTextLimitTime());
        result.put(WebdbSubChannelColumnEnum.SORT.getCol(), subChannelInfo.getSort());
        result.put(WebdbSubChannelColumnEnum.CHARGE.getCol(), subChannelInfo.getCharge());
        result.put(WebdbSubChannelColumnEnum.TEMPLATE_ID.getCol(), subChannelInfo.getTemplateId());
        result.put(WebdbSubChannelColumnEnum.IS_PASSWD_SET.getCol(), subChannelInfo.getIsPasswdSet());
        result.put(WebdbSubChannelColumnEnum.IS_GUEST_ACCESS_LIMIT.getCol(), subChannelInfo.getIsGuestAccessLimit());
        return result;
    }
}
