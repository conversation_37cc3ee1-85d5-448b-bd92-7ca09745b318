package com.yy.gameecology.common.db.model.gameecology;

import com.yy.gameecology.common.annotation.TableColumn;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;


/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-09-27 20:56
 **/
@TableColumn(underline = true)
public class ActTaskScore implements Serializable {
    public static String TABLE_NAME = "act_task_score";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"act_id", "task_time","task_type", "uid", "level"};

    public static RowMapper<ActTaskScore> ROW_MAPPER = null;
    /*new RowMapper<ActTaskScore>() {
        @Override
        public ActTaskScore mapRow(ResultSet rs, int rowNum) throws SQLException {
            ActTaskScore entity = new ActTaskScore();
            entity.setActId(rs.getLong("act_id"));
            entity.setUid(rs.getLong("uid"));
            entity.setTaskTime(rs.getString("task_time"));
            entity.setTaskType(rs.getString("task_type"));
            entity.setLevel(rs.getLong("level"));
            entity.setScore(rs.getLong("score"));
            entity.setBusiId(rs.getLong("busi_id"));
            entity.setCompleted(rs.getLong("completed"));
            entity.setAward(rs.getString("award"));
            entity.setRemark(rs.getString("remark"));
            entity.setcTime(rs.getTimestamp("c_time"));
            entity.setuTime(rs.getTimestamp("u_time"));
            return entity;
        }
    };*/

    private Long actId;

    private String taskTime;

    private String taskType;

    private Long uid;

    private Long level;

    /**
     * 过任务的分值，榜单达到这个分值的时候可过任务
     */
    private Long score;

    private Long busiId;

    private Long completed;

    private String remark;

    private String award;

    private Date cTime;

    private Date uTime;

    private String extJson;

    public ActTaskScore() {
    }

    public ActTaskScore(Long actId, String taskType, Long level, Long score, String award) {
        this.actId = actId;
        this.taskType = taskType;
        this.level = level;
        this.score = score;
        this.award = award;
        this.cTime = new Date();
    }

    public ActTaskScore(Long actId, String taskTime, String taskType, Long uid, Long level, Long score, String award) {
        this.actId = actId;
        this.taskTime = taskTime;
        this.taskType = taskType;
        this.uid = uid;
        this.level = level;
        this.score = score;
        this.award = award;
        this.cTime = new Date();
    }

    public Long getActId() {
        return actId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getLevel() {
        return level;
    }

    public void setLevel(Long level) {
        this.level = level;
    }

    public Long getScore() {
        return score;
    }

    public void setScore(Long score) {
        this.score = score;
    }

    public Long getBusiId() {
        return busiId;
    }

    public void setBusiId(Long busiId) {
        this.busiId = busiId;
    }

    public String getTaskTime() {
        return taskTime;
    }

    public void setTaskTime(String taskTime) {
        this.taskTime = taskTime;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public Long getCompleted() {
        return completed;
    }

    public void setCompleted(Long completed) {
        this.completed = completed;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getcTime() {
        return cTime;
    }

    public void setcTime(Date cTime) {
        this.cTime = cTime;
    }

    public Date getuTime() {
        return uTime;
    }

    public void setuTime(Date uTime) {
        this.uTime = uTime;
    }

    public String getAward() {
        return award;
    }

    public void setAward(String award) {
        this.award = award;
    }

    public String getExtJson() {
        return extJson;
    }

    public void setExtJson(String extJson) {
        this.extJson = extJson;
    }
}
