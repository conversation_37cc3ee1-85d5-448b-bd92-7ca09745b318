package com.yy.gameecology.common.db.mapper.aov;

import com.yy.gameecology.common.db.model.gameecology.aov.AovAwardAccount;
import org.apache.ibatis.annotations.Param;

public interface AovAwardAccountExtMapper extends AovAwardAccountMapper{

    Long selectUidByAccount(@Param("account") String account);

    String selectAccountByUid(@Param("uid") long uid);

    int saveAwardAccount(AovAwardAccount record);
}
