package com.yy.gameecology.common.db.mapper.aov;

import com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseRound;

public interface AovPhaseRoundMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AovPhaseRound record);

    int insertSelective(AovPhaseRound record);

    AovPhaseRound selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AovPhaseRound record);

    int updateByPrimaryKey(AovPhaseRound record);

}