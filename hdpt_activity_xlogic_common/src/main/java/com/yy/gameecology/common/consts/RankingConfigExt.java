package com.yy.gameecology.common.consts;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-08-20 17:04
 **/
public class RankingConfigExt {
    /**
     * 是否替换榜单基础字段
     */
    public static final String REPLACE_BASE_FIELD_KEY = "tb_ranking_config:replaceBaseField_";

    /**
     * 榜单类型
     */
    public static final String RANK_TYPE_KEY = "tb_ranking_config:rankType";

    /**
     * 成员类型
     */
    public static final String MEMBER_TYPE_KEY = "tb_ranking_config:memberType";


    /**
     * 模板类型
     */
    public static final String TEMPLATE_TYPE_KEY = "tb_ranking_config:templateType";

    /**
     * 榜单显示扩展数据来源 0-基础 1-交友 2-约战 3-宝贝 4-陪玩
     */
    public static final String RANK_DATA_SOURCE = "tb_ranking_config:data_source_";


    /**
     * 是否分组
     */
    public static final String NEED_GROUP_KEY = "tb_ranking_config:needGroup";

    /**
     * 自定义PK pair的展示顺序
     */
    public static final String PK_ITEM_SHOW_ORDER = "tb_ranking_config:pk_rank_show_order";


    /**
     *是否需要参赛资格检查, 1:需要, 0:不需要(默认)
     */
    public static final String RACE_QUALIFICATION_FLAG = "tb_ranking_config:race_qualification_flag";

}
