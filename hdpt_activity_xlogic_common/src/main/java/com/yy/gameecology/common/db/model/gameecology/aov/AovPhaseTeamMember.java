package com.yy.gameecology.common.db.model.gameecology.aov;

import java.util.Date;

public class AovPhaseTeamMember {
    private Long id;
    private Long teamId;    // Corresponds to team_id in the database
    private Long phaseId;   // Corresponds to phase_id in the database
    private Long memberUid; // Corresponds to member_uid in the database
    private String role; // 0 - 队员， 1 - 队长
    private Integer state;   // Corresponds to state in the database

    private Date createTime;

    // No-argument constructor
    public AovPhaseTeamMember() {
    }

    public AovPhaseTeamMember(Long teamId, Long phaseId, Long memberUid, String role, Integer state, Date createTime) {
        this.teamId = teamId;
        this.phaseId = phaseId;
        this.memberUid = memberUid;
        this.role = role;
        this.state = state;
        this.createTime = createTime;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(Long phaseId) {
        this.phaseId = phaseId;
    }

    public Long getMemberUid() {
        return memberUid;
    }

    public void setMemberUid(Long memberUid) {
        this.memberUid = memberUid;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
