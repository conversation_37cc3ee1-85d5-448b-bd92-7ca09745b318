package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Getter;
import lombok.Setter;
import org.springframework.jdbc.core.RowMapper;

import java.util.Date;

@Getter
@Setter
@TableColumn(underline = true)
public class Cmpt5148TopCoupleRecord {

    public static final String TABLE_NAME = "cmpt_5148_top_couple_record";

    public static final String[] TABLE_PKS = {"id"};

    public static final RowMapper<Cmpt5148TopCoupleRecord> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt5148TopCoupleRecord result = new Cmpt5148TopCoupleRecord();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setDateCode(rs.getString("date_code"));
        result.setRankId(rs.getLong("rank_id"));
        result.setAnchorUid(rs.getLong("anchor_uid"));
        result.setUserUid(rs.getLong("user_uid"));
        result.setSid(rs.getLong("sid"));
        result.setSsid(rs.getLong("ssid"));
        result.setScore(rs.getLong("score"));
        result.setPackageId(rs.getLong("package_id"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        return result;
    };

    protected Long id;

    protected Long actId;

    protected String dateCode;

    protected Long rankId;

    protected Long anchorUid;

    protected Long userUid;

    protected Long sid;

    protected Long ssid;

    protected Long score;

    protected Long packageId;

    protected Date createTime;
}
