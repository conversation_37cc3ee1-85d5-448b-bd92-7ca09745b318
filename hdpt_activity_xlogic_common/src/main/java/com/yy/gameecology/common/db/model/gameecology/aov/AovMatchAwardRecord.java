package com.yy.gameecology.common.db.model.gameecology.aov;

import java.util.Date;

public class AovMatchAwardRecord {
    /**
     * 
     */
    private Long id;

    /**
     * 
     */
    private Long actId;

    /**
     * 
     */
    private Long phaseId;

    /**
     * 
     */
    private Integer awardType;

    /**
     * 
     */
    private Long uid;

    /**
     * 奖励所属轮次，0-首胜奖励
     */
    private Integer roundNum;

    /**
     * 奖励金额
     */
    private Long amount;

    /**
     * 奖励说明
     */
    private String awardDesc;

    /**
     * 
     */
    private Integer awardState;

    /**
     * 
     */
    private Date awardTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getActId() {
        return actId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }

    public Long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(Long phaseId) {
        this.phaseId = phaseId;
    }

    public Integer getAwardType() {
        return awardType;
    }

    public void setAwardType(Integer awardType) {
        this.awardType = awardType;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Integer getRoundNum() {
        return roundNum;
    }

    public void setRoundNum(Integer roundNum) {
        this.roundNum = roundNum;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getAwardDesc() {
        return awardDesc;
    }

    public void setAwardDesc(String awardDesc) {
        this.awardDesc = awardDesc == null ? null : awardDesc.trim();
    }

    public Integer getAwardState() {
        return awardState;
    }

    public void setAwardState(Integer awardState) {
        this.awardState = awardState;
    }

    public Date getAwardTime() {
        return awardTime;
    }

    public void setAwardTime(Date awardTime) {
        this.awardTime = awardTime;
    }
}