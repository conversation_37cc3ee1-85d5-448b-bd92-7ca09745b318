package com.yy.gameecology.common.db.mapper.aov;

import com.yy.gameecology.common.db.model.gameecology.aov.AovGame;

public interface AovGameMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AovGame record);

    int insertSelective(AovGame record);

    AovGame selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AovGame record);

    int updateByPrimaryKey(AovGame record);
}