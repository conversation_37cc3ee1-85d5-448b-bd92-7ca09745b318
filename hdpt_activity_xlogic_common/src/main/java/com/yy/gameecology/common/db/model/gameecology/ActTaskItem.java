package com.yy.gameecology.common.db.model.gameecology;

import com.yy.gameecology.common.annotation.TableColumn;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-10-23 17:22
 **/
@TableColumn(underline = true)
public class ActTaskItem implements Serializable {
    public static String TABLE_NAME = "act_task_item";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"task_id", "level", "type"};

    public static RowMapper<ActTaskItem> ROW_MAPPER = null;



    /**
     * task_id
     */
    private Long taskId;

    /**
     * 任务等级
     */
    private Integer level;

    /**
     * 过关类型 1-榜单分值 2-赛程榜单排名
     */
    private Integer type;

    /**
     * 子任务名称
     */
    private String itemName;

    /**
     * 过关策略
     */
    private String passPolicy;

    /**
     * 规则展示描述文案
     */
    private String desc;

    /**
     * ext_json
     */
    private String extJson;

    /**
     * create_time
     */
    private Date createTime;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getPassPolicy() {
        return passPolicy;
    }

    public void setPassPolicy(String passPolicy) {
        this.passPolicy = passPolicy;
    }



    public String getExtJson() {
        return extJson;
    }

    public void setExtJson(String extJson) {
        this.extJson = extJson;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
