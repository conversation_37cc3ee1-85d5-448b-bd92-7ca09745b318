package com.yy.gameecology.common.db.mapper.pepc;

import com.yy.gameecology.common.db.model.gameecology.pepc.PepcGameTeam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface PepcGameTeamMapper {
    @Update("""   
            <script>
                            update  pepc_game_team set state= #{newStatus} 
                         <if test="matchId != null">
                           , match_id = #{matchId}
                         </if>
                        <if test="remoteTeamId != null">
                             , tx_team_id = #{remoteTeamId}
                           </if>
                            where act_id= #{actId} 
                            and id= #{id} 
                            and state=  #{oldStatus}
                            </script>
            """)
    int updateStatus(@Param("actId") long actId,
                     @Param("id") long id,
                     @Param("newStatus") int newStatus,
                     @Param("oldStatus") int oldStatus,
                     @Param("remoteTeamId") String remoteTeamId,
                     @Param("matchId") Long matchId);

    @Select("""   
            <script>
                            select * from  pepc_game_team 
                            where act_id= #{actId} 
                            and game_id in 
                           <foreach item='item' index='index' collection='gameIds' open='(' separator=',' close=')'>
                                #{item}
                           </foreach>
                            </script>
            """)
    List<PepcGameTeam> selectPepcGameTeamByGameId(@Param("actId") long actId, @Param("gameIds") List<Long> gameIds);

    @Update("""   
            <script>
                            update  pepc_game_team
                            set score = #{score}
                            where act_id= #{actId} 
                            and game_id = #{gameId}
                            and team_id in 
                           <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'>
                                #{item}
                           </foreach>
                            </script>
            """)
    long updateGameTeamScore(@Param("actId") long actId, @Param("gameId") long gameId, @Param("ids") List<Long> teamIds, @Param("score") long score);

}
