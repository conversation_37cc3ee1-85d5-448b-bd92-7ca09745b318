package com.yy.gameecology.common.bean;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UpdateMatchNode {

    protected long nodeId;

    protected int sourceState;

    protected Integer targetState;

    protected Long teamId;

    protected Long uid;

    protected Integer advanceType;

    protected Integer teamState;

    protected Integer score;

    protected Integer aliveScore;
}
