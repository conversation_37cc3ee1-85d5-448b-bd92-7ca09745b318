
package com.yy.gameecology.common.mymsg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.jserverlib.codec.annotations.YField;
import com.yy.jserverlib.codec.annotations.YMessage;
import com.yy.jserverlib.codec.annotations.YStyle;
import com.yy.jserverlib.codec.annotations.YType;
import com.yy.jserverlib.common.BaseMessage;

@YMessage(StdResp.URI)
public class StdResp extends BaseMessage {

    public static final long URI = MyMessage.GE_BASE_RESP;

    public StdResp() {
        super(URI);

    }

    // 构建时候初始化基类uri,再写入子类uri
    public StdResp(long subUri) {
        super(URI);
        this.setSubUri(subUri);
    }

    // 构建时候初始化基类uri,再写入子类uri
    public StdResp(StdResp msg) {
        super(URI);
        this.setSubUri(msg.getSubUri());
        this.data = JSON.toJSONString(msg);
    }

    @JSONField(serialize = false)
    @YField(0)
    @YType(YStyle.U32)
    private long subUri = 0;

    @JSONField(serialize = false)
    @YField(1)
    private long ext = 0;

    @JSONField(serialize = false)
    @YField(2)
    private String data = StringUtil.EMPTY;

    public long getSubUri() {
        return subUri;
    }

    public void setSubUri(long subUri) {
        this.subUri = subUri;
    }

    public long getExt() {
        return ext;
    }

    public void setExt(long ext) {
        this.ext = ext;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

}
