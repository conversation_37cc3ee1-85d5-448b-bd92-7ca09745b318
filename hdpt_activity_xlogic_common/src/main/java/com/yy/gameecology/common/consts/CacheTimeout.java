package com.yy.gameecology.common.consts;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-08-07 11:29
 **/
public class CacheTimeout {

    /**
     * 通用数据库配置
     */
    public static final long COMMON_DB_STATIC_CONFIG = 10 * 60 * 1000;


    /**
     * 榜单阶段配置信息
     */
    public static final long HDZT_RANKING_PHASE_INFOS = 10 * 1000;

    /**
     * 榜单阶段配置信息
     */
    public static final long HDZT_RANKING_PHASE_INFO = 60 * 1000;


    /**
     * 榜单配置信息
     */
    public static final long HDZT_RANKING_CONFIG = 10 * 1000;

    /**
     * 榜单数据信息
     */
    public static final long HDZT_RANKING_INFO = 5 * 1000;

    /**
     * 榜单最外围缓存信息
     */
    public static final long HDZK_RANKING_INFO = 5 * 1000;

    /**
     * 交友用户资料信息
     */
    public static final long FTS_USER_INFO = 10 * 1000;

    /**
     * YY用户信息
     */
    public static final long YY_USER_INFO = 10 * 1000;

    /**
     * YY频道信息
     */
    public static final long YY_CHANNEL_INFO = 10 * 1000;

    /**
     * 顶级频道信息
     */
    public static final long YY_TOP_CHANNEL_INFO = 10 * 1000;

    /**
     * 陪玩团
     */
    public static final long PW_TUAN = 5 * 60 * 1000;
    /**
     * 奖品数据信息
     */
    public static final long HDZT_AWARD_TASK = 600 * 1000;

    /**
     * yy子频道信息（单位：秒）
     */
    public static final long YY_SINGLE_SUB_CHANNEL_INFO = 15 * 60 * 1000 + CacheTimeOutSuffix.ONE_TENTH_RANDOM;

    /**
     * 奖品数据信息
     */
    public static final long PW_PLUGIN_CHANNEL = 60 * 1000;

    /**
     * 麦位信息
     */
    public static final long PW_CHANNEL_SEAT = 5 * 1000;

    public static final long AWARD_ROLL = 60 * 1000;


    //房管厅昵称、资料图
    public static final long FTS_ROOM_MANAGER_INFO = 60 * 1000;


}
