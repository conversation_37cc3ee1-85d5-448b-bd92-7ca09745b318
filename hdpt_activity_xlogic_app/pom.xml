<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.yy.hdzk</groupId>
		<artifactId>hdpt_activity_xlogic_parent</artifactId>
		<version>1.0.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>hdpt_activity_xlogic_app</artifactId>
	<packaging>jar</packaging>
	<name>HDPT Activity Xlogic - App</name>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.yy.hdzk</groupId>
			<artifactId>hdpt_activity_xlogic_base</artifactId>
			<version>${project.version}</version>
		</dependency>
	</dependencies>

	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
				<excludes>
					<exclude>**/*.so.2</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>false</filtering>
				<includes>
					<include>**/*.so.2</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.so</include>
					<include>**/*.html</include>
				</includes>
				<filtering>false</filtering>
			</resource>

			<resource>
				<directory>src/main/resources</directory>
				<filtering>false</filtering>
				<includes>
					<include>**/*.js</include>
					<include>**/*.css</include>
					<include>/static/</include>
				</includes>
			</resource>
		</resources>

		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${maven.compiler.source}</source>
					<target>${maven.compiler.target}</target>
					<encoding>${project.build.sourceEncoding}</encoding>
				</configuration>
			</plugin>

			<plugin><!-- 构造MANIFEST.MF文件 -->
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifest>
							<addClasspath>true</addClasspath>
							<classpathPrefix>lib/</classpathPrefix><!-- 潜龙将使用此目录作为启动的classpath参数 -->
							<mainClass>com.yy.gameecology.activity.Main</mainClass><!-- 此处根据项目的Main类进行修改 -->
							<useUniqueVersions>false</useUniqueVersions><!-- 解决snapshot的jar文件名与MANIFEST.MF文件中不一致的问题 -->
						</manifest>
					</archive>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>exec-maven-plugin</artifactId>
				<version>1.6.0</version>
				<executions>
					<execution>
						<goals>
							<goal>java</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<mainClass>com.yy.gameecology.activity.Main</mainClass>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<!-- 可在Windows下跑 java -cp .;lib\* org.ruanwei.assembly.jartest.App -->
					<!-- 或Linux下跑 java -cp .:lib/* org.ruanwei.assembly.jartest.App -->
					<!-- 注意：需先cd到target/classes目录下 -->
					<!-- 附：在eclipse里用maven命令运行main()的插件：exec-maven-plugin，解决本地运行程序需要变量配置和替换等maven支持功能 -->
					<execution>
						<id>copy-dependencies-local</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/classes/lib</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
