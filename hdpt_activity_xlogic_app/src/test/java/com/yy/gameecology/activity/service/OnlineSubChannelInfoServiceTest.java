package com.yy.gameecology.activity.service;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.Main;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-08-05 12:54
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-3.properties"})
public class OnlineSubChannelInfoServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private OnlineSubChannelCacheService onlineSubChannelInfoService;

    @Test
    public void test() {
        //yy://pd-[sid=1353441873&subid=2767645110]
        int loops = 100;
        List<String> testSids = Lists.newArrayList();
        testSids.add("1");
        testSids.add("1353441873_2767645110");
        testSids.add("2");
        testSids.add("1353441873_1353441873");
        for (int i = 0; i < loops; i++) {
            onlineSubChannelInfoService.getSubChannelInfoFromRedis(testSids);
            onlineSubChannelInfoService.getSubChannelInfo("1353441873L_2767645110");
        }
    }

    @Test
    public void test2() {
        //yy://pd-[sid=1353441873&subid=2767645110]
        int loops = 100;
        for (int i = 0; i < loops; i++) {
            onlineSubChannelInfoService.load2Memory();
        }
    }
}
