package com.yy.gameecology.activity.service;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.utils.StringUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {"group=7"})
@TestPropertySource(value = {
        "classpath:env/local/application.properties",
        "classpath:env/local/application-inner.properties",
        "classpath:env/local/group-setting-7.properties"
})
public class BaiduInfoFlowRobotServiceTest {

    static {
        System.setProperty("group", "7");
    }

    @Autowired
    private CommonService commonService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Test
    public void notifyTest() {
//        baiduInfoFlowRobotService.sendNotify(4420756L,"http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dd1500458aef932bd0d80a83b95c7affe","test", Lists.newArrayList("chenxiazhuan"));
    }

    @Test
    public void sendNotifyByConfigKeyTest() {
        long actId = 2023107001;
        String msg = commonService.buildActRuliuMsg(actId, false, "如流通知测试", "新接口at测试");
        baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, commonService.getActRuliuNotifyUids(actId));
    }


}
