package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.service.layer.BroActLayerService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-01-24 11:06
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-4.properties"})
public class ActLayerRefreshServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BroActLayerService broActLayerService;

    @Test
    public void getNextInvokeSecondsTest() {
        int maxLoops = 100;
        for (int i = 0; i <= maxLoops; i++) {
            long result = broActLayerService.getNextInvokeSeconds(888888, 111111, 22222, 10);
            System.out.println("ActLayerRefreshServiceTest,result:" + result);
        }
    }

    static {
        System.setProperty("group", "4");
    }

    @Test
    public void testBro() {

        for (int i = 0; i < 10; i++) {
            OnlineChannelInfo onlineChannel = new OnlineChannelInfo();

            onlineChannel.setSid(26003885L);
            onlineChannel.setSsid(26003885L);
            broActLayerService.broChannel(2022094001L, onlineChannel);
        }

    }
}
