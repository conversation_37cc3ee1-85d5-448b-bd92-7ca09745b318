package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.RankShowConfigVo;
import com.yy.gameecology.hdzj.element.component.RankingShowComponent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-06-30 16:27
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties"})
public class HdztPhaseConfigServiceTest {
    static {
        System.setProperty("group", "1");}
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztPhaseConfigService hdztPhaseConfigService;

    @Autowired
    private RankingShowComponent rankingShowComponent;

    @Test
    public void test() {
        List<RankShowConfigVo> effectShowRankConfig = rankingShowComponent.getEffectShowRankConfig(2022051001L, 500L);
        System.out.println(effectShowRankConfig);

        // hdztPhaseConfigService.getRankCurPhaseId(2021083001L, 1L);
    }
}
