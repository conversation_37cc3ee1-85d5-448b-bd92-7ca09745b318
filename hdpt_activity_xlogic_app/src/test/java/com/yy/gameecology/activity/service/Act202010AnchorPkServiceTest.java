package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-24 20:28
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource("classpath:env/local/application.properties")
public class Act202010AnchorPkServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ApplicationEventPublisher publisher;


    @Test
    public void test() {
        PhaseTimeEnd phaseTimeEnd1 =new PhaseTimeEnd();
        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(41);
        phaseTimeEnd1.setPhaseId(20);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(42);
        phaseTimeEnd1.setPhaseId(20);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(44);
        phaseTimeEnd1.setPhaseId(20);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(45);
        phaseTimeEnd1.setPhaseId(20);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(45);
        phaseTimeEnd1.setPhaseId(21);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);










        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(81);
        phaseTimeEnd1.setPhaseId(32);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(82);
        phaseTimeEnd1.setPhaseId(32);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(83);
        phaseTimeEnd1.setPhaseId(32);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(84);
        phaseTimeEnd1.setPhaseId(32);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);






        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(81);
        phaseTimeEnd1.setPhaseId(33);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(82);
        phaseTimeEnd1.setPhaseId(33);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(83);
        phaseTimeEnd1.setPhaseId(33);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(84);
        phaseTimeEnd1.setPhaseId(33);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);






        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(81);
        phaseTimeEnd1.setPhaseId(34);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(82);
        phaseTimeEnd1.setPhaseId(34);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(83);
        phaseTimeEnd1.setPhaseId(34);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(84);
        phaseTimeEnd1.setPhaseId(34);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);




        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(81);
        phaseTimeEnd1.setPhaseId(35);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(82);
        phaseTimeEnd1.setPhaseId(35);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(83);
        phaseTimeEnd1.setPhaseId(35);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(84);
        phaseTimeEnd1.setPhaseId(35);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);



        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(81);
        phaseTimeEnd1.setPhaseId(36);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(82);
        phaseTimeEnd1.setPhaseId(36);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(83);
        phaseTimeEnd1.setPhaseId(36);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);

        phaseTimeEnd1.setActId(202010002L);
        phaseTimeEnd1.setRankId(84);
        phaseTimeEnd1.setPhaseId(36);
        phaseTimeEnd1.setEkey(phaseTimeEnd1.getActId()+"_"+phaseTimeEnd1.getRankId()+"_"+phaseTimeEnd1.getPhaseId());
        publisher.publishEvent(phaseTimeEnd1);
        System.out.println();



    }






}
