package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.Main;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-4.properties"})
public class OfferIpServiceTest {

    @Autowired
    private OfferIpService offerIpService;

    @Test
    public void test() {
        List<String> ips = offerIpService.queryOfferIpList();
        System.out.println(JSON.toJSONString(ips));
    }
}
