package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.common.bean.Template;
import org.assertj.core.util.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Set;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-23 10:34
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource("classpath:env/local/application.properties")
public class UserSubServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private UserSubService userSubService;

    @Test
    public void getTest() {

        Set<Long> babyUids = Sets.newHashSet();
//        babyUids.add(1424707733L);
//        babyUids.add(1424707732L);
        babyUids.add(2184888908L);
        userSubService.getUserSub(20200801001L, 50075550L, babyUids, Template.makefriend.getCode());
    }


}
