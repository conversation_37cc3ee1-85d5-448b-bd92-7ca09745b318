package com.yy.gameecology.activity.service;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.rankroleinfo.RoleItem;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-08-25 16:23
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-3.properties"})
public class HdztRankGenRoleServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztRankGenRoleService hdztRankGenRoleService;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Test
    public void genGuildRankItemTest() {
        Long actId = 2021103002L;
        Long roleType = 202L;
        Long roleId = 50070L;
        Long busiId = 500L;
        String memberId = "2183586041";
        while (true) {
            Map<String, ? extends RoleItem> roleMap = hdztRankGenRoleService.genRankItemMap(actId, 0L, busiId, roleType, roleId, 2
                    , Lists.newArrayList(memberId), 0L, "");
            Map<String, ? extends RoleItem> roleMap1 = hdztRankGenRoleService.genRankItemMap(actId, 0L, busiId, roleType, roleId, 12
                    , Lists.newArrayList(memberId), 0L, "");
            System.out.println();
        }




    }

}
