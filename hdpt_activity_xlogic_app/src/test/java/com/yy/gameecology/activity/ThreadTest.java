package com.yy.gameecology.activity;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-01-28 17:04
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-2.properties"})
public class ThreadTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    public static final ScheduledExecutorService EXECUTOR_DELAY_GENERAL = Executors.newScheduledThreadPool(1);

    @Test
    public void delayTest() {
        EXECUTOR_DELAY_GENERAL.schedule(() -> {
            log.info("ThreadTest1=================================");
        }, 1000000, TimeUnit.SECONDS);

        EXECUTOR_DELAY_GENERAL.schedule(() -> {
            log.info("ThreadTest2=================================");
        }, 1, TimeUnit.SECONDS);

        int maxLoops = 1000;
        for (int i = 0; i < maxLoops; i++) {
            EXECUTOR_DELAY_GENERAL.schedule(() -> {
                log.info("ThreadTest5=================================");

            }, 2, TimeUnit.SECONDS);
        }


        EXECUTOR_DELAY_GENERAL.schedule(() -> {
            log.info("ThreadTest1=================================");
        }, 1000000, TimeUnit.SECONDS);


        for (int i = 0; i < maxLoops; i++) {
            EXECUTOR_DELAY_GENERAL.schedule(() -> {
                log.info("ThreadTest3=================================");

            }, 1, TimeUnit.SECONDS);
        }
    }
}
