package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.Main;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-25 13:02
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-3.properties"})
public class OnMicServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private OnMicService onMicService;

    @Autowired
    private CommonService commonService;

    @Test
    public void reloadTest() {
        int loops = 100;
        while (loops > 0) {
            loops--;
            onMicService.getOnMicChannel(50042952L);
        }
    }
}
