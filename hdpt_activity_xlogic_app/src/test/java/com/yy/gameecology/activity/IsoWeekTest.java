package com.yy.gameecology.activity;

import com.yy.gameecology.common.utils.DateUtil;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2021/3/23 20:31
 * @Modified:
 */
public class IsoWeekTest {
    public static void main(String[] args) {

        LocalDate  day1 = LocalDate.parse("20170101", DateUtil.YYYY_MM_DD);
       int dayOne1 = day1.get(ChronoField.ALIGNED_WEEK_OF_YEAR);
       int dayTwo1 =  day1.get(ChronoField.ALIGNED_DAY_OF_WEEK_IN_YEAR);
       String  dayThree1 =  day1.format(DateTimeFormatter.ofPattern("w"));

        LocalDate day2 = LocalDate.parse("20170102", DateUtil.YYYY_MM_DD);
        int dayOne2 = day1.get(ChronoField.ALIGNED_WEEK_OF_YEAR);
        int dayTwo2 =  day1.get(ChronoField.ALIGNED_DAY_OF_WEEK_IN_YEAR);
        String  dayThree2 =  day1.format(DateTimeFormatter.ofPattern("w"));
        System.out.println();

    }
}
