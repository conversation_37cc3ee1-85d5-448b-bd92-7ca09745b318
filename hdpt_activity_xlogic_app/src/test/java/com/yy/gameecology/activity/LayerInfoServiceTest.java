package com.yy.gameecology.activity;

import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.client.thrift.TurnoverFamilyThriftClient;
import com.yy.gameecology.activity.client.thrift.WebdbServiceClientest;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.HdztRankService;
import com.yy.gameecology.activity.service.OnlineChannelService;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.activity.service.layer.BroActLayerService;
import com.yy.gameecology.common.consts.Const;
import com.yy.protocol.pb.layer.LayerInfo;
import com.yy.thrift.broadcast.Template;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-2.properties",
        "classpath:env/local/application-inner.properties"})
public class LayerInfoServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ActLayerInfoService actLayerInfoService;

    @Autowired
    private BroActLayerService broActLayerService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private TurnoverFamilyThriftClient turnoverFamilyThriftClient;

    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;

    @Autowired
    private OnlineChannelService onlineChannelService;


    static {
        System.setProperty("group", "2");
    }

    @Test
    public void layerInfo2() {
//        onlineChannelService.loadDataFromSource();


        zhuiWanPrizeIssueServiceClient.queryChannelSeat(1454054224L,2793038756L);
//        commonService. queryOnlineChannelNew(Template.SkillCard);
        long familyId = turnoverFamilyThriftClient.queryContractFamilyId(50042952);
//        var family = turnoverFamilyThriftClient.queryFamilySsid(1454054224);
        //yy://pd-[sid=1451119354&subid=2798785410]
        LocalDateTime localDateTime = LocalDateTime.of(2024, 1, 6, 0, 1);
        Date date = new Date(localDateTime.atZone(ZoneId.systemDefault()).toEpochSecond() * 1000);

        date = commonService.getNow(2023112001L);
        for (int i = 0; i < Const.OK_200; i++) {
            //yy://pd-[sid=267818&subid=2807602905]
            OnlineChannelInfo onlineChannelInfo =new OnlineChannelInfo();
            onlineChannelInfo.setSid(1451119354L);
            onlineChannelInfo.setSsid(2820664865L);
            //broActLayerService.broChannel(2023114001L, onlineChannelInfo);

            actLayerInfoService.buildLayerInfo(2023112001L,1456158774L,2811009561L,date,"");

        }
    }

    @Test
    public void layerInfo() {
        LocalDateTime localDateTime = LocalDateTime.of(2022, 1, 22, 20, 0);
        Date date = new Date(localDateTime.atZone(ZoneId.systemDefault()).toEpochSecond() * 1000);
        LayerBroadcastInfo layerBroadcastInfo = actLayerInfoService.buildLayerInfo(2022013001L, 1452273082L, 1452273082L, date, "web");
        System.out.println(layerBroadcastInfo);
    }

    @Test
    public void pk() {
        GetRankReq getRankReq = new GetRankReq();
        getRankReq.setActId(2021084001L);
        getRankReq.setRankId(11L);
        getRankReq.setPhaseId(13L);
        getRankReq.setRankCount(100L);
        getRankReq.setDateStr("20210811");

        // hdztRankService.getRankInfo(getRankReq, 1);
    }

    @Test
    public void batchQueryUsrInfo(){
        commonService.batchGetUserInfos(Lists.newArrayList(2997224440L),false);
    }


}
