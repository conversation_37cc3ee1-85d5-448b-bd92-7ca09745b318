package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSONObject;
import com.yy.gameecology.activity.Main;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;


/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-03-05 16:46
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties"})
public class DelayQueueServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private DelayQueueService delayQueueService;
    @Test
    public void testSettleJSON(){
        String key = "test_delay_settle";
        JSONObject data = new JSONObject();
        data .put("1","1");
        delayQueueService.setDelayEvent(key,3,data);
    }

    public void handle(JSONObject data) {
        System.out.println("handle:"+data);
    }
}
