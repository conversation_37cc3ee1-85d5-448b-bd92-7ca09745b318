package com.yy.gameecology.activity.componenttest;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.MD5SHAUtil;
import com.yy.gameecology.hdzj.element.component.PuzzleComponent;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.BusiId;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-25 16:31
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {"group=1"})
@TestPropertySource(value = {
        "classpath:env/local/application.properties",
        "classpath:env/local/application-inner.properties",
        "classpath:env/local/group-setting-1.properties"
})
public class PuzzleComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "1");
    }

    @Autowired
    private PuzzleComponent puzzleComponent;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    private final long actId = 2025051001L;

    @Test
    public void runTest() {
        String eventContent = "{\"actId\":2025051001,\"actors\":{80003:\"2959049318\",50003:\"2959049318\",50011:\"50042952\",50059:\"87814665_2795294281\"},\"busiId\":500,\"currRound\":2,\"currTaskIndex\":1,\"ekey\":\"\",\"index\":0,\"itemCurrNumMap\":{\"WBRW\":0},\"itemLevelPassMap\":{\"WBRW\":[20000,30000,20000,30000,100000]},\"member\":\"2959049318|50042952\",\"occurTime\":\"2025-05-13 08:00:05\",\"phaseId\":100,\"phaseScore\":200030,\"rankId\":19,\"rankScore\":200030,\"roundComplete\":1,\"seq\":\"b4dfd145-9509-430b-a7cf-8d0968b939fd\",\"startTaskIndex\":0,\"taskLevelCount\":5,\"timeKey\":1,\"timestamp\":\"2025-05-13 08:00:05\",\"uri\":2001}";

        TaskProgressChanged event = JSON.parseObject(eventContent, TaskProgressChanged.class);

        puzzleComponent.onTaskProgressChanged(event, puzzleComponent.getUniqueComponentAttr(actId));

    }


    @Test
    public void awardTest() {
        String lotterySeq = MD5SHAUtil.getMD5("lottteryPiece_dup_test");
        long testUid = 50042952;
        Map<String, String> extData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(testUid));
        BatchLotteryResult result = hdztAwardServiceClient.doLottery(DateUtil.getNowYyyyMMddHHmmss(),
                BusiId.GAME_ECOLOGY.getValue(), testUid, 15426, 1, 0, lotterySeq, extData);


        String anchorAwardSeqMd5 = MD5SHAUtil.getMD5("doWelfare_dup_test");
        Map<String, String> anchorExtData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(testUid));
        BatchWelfareResult anchorResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), 200, testUid, 15428, 1, 15682, anchorAwardSeqMd5, anchorExtData, 2);
        //TODO seq重复code
        if (anchorResult == null || anchorResult.getCode() != 0) {
            throw new RuntimeException("welfare exception");
        }
    }

    @Test
    public void doLotteryTest(){
        String lotterySeq = System.currentTimeMillis()+"";
        long testUid = 50042952;
        Map<String, String> extData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(testUid));
        BatchLotteryResult result = hdztAwardServiceClient.doLottery(DateUtil.getNowYyyyMMddHHmmss(),
                BusiId.GAME_ECOLOGY.getValue(), testUid, 15424L, 1, 0, lotterySeq, extData);
    }
}
