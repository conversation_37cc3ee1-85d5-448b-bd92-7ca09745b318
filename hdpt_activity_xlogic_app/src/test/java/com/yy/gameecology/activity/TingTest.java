package com.yy.gameecology.activity;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yy.gameecology.common.db.model.gameecology.ActTaskScore;
import com.yy.gameecology.hdzj.element.history.attr.NianshouComponentAttr;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021.09.06 18:04
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = Main.class)
//@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-2.properties"})
public class TingTest {

    private long rankId;
    private long phaseId;

    private long timeKey;

    //private double awardRate;

    private Map<String, Double> awardRateMap;

    private Map<String, String> defaultTaskScore;

    /**
     * dateStr 格式: yyyyMMdd
     * 开始时间 <taskType,List<dateStr>>
     */
    private Map<String, List<String>> openTimeMap;

    public static void main1(String[] args) {
        Map<String, Double> map = new HashMap<>();
        map.put("single", 0.00006);
        map.put("all_single", 0.00006);
        System.out.println(JSON.toJSONString(map));
        //{"single":0.00006,"all_single":0.00006}
        Map<String, List<ActTaskScore>> defaultTaskScore = new HashMap<>();

        List<ActTaskScore> singleList = new ArrayList<>();
        singleList.add(new ActTaskScore(2021103001L, "single", 1L, 1_000_000L, "600"));
        singleList.add(new ActTaskScore(2021103001L, "single", 2L, 1_300_000L, "780"));

        List<ActTaskScore> all_single = new ArrayList<>();
        all_single.add(new ActTaskScore(2021103001L, "all_single", 1L, 6_000_000_000L, "50000"));
        all_single.add(new ActTaskScore(2021103001L, "all_single", 2L, 8_000_000_000L, "80000"));

        List<ActTaskScore> all_tiantuan = new ArrayList<>();
        all_tiantuan.add(new ActTaskScore(2021103001L, "all_tiantuan", 1L, 7_000_000_000L, "60000"));
        all_tiantuan.add(new ActTaskScore(2021103001L, "all_tiantuan", 2L, 9_000_000_000L, "90000"));

        defaultTaskScore.put("single", singleList);
        defaultTaskScore.put("all_single", all_single);
        defaultTaskScore.put("all_tiantuan", all_tiantuan);

        System.out.println(JSON.toJSONString(defaultTaskScore));

       // {"single":[{"actId":2021103001,"award":"600","level":1,"score":1000000,"taskType":"single"},{"actId":2021103001,"award":"780","level":2,"score":1300000,"taskType":"single"}],"all_tiantuan":[{"actId":2021103001,"award":"60000","level":1,"score":7000000000,"taskType":"all_tiantuan"},{"actId":2021103001,"award":"90000","level":2,"score":9000000000,"taskType":"all_tiantuan"}],"all_single":[{"actId":2021103001,"award":"50000","level":1,"score":6000000000,"taskType":"all_single"},{"actId":2021103001,"award":"80000","level":2,"score":8000000000,"taskType":"all_single"}]}

        Map<String, List<String>> openTime = new HashMap<>();
        ArrayList<String> strings = Lists.newArrayList("20211109", "20211111", "20211112");
        openTime.put("all_single", strings);
        openTime.put("all_tiantuan", strings);

        System.out.println(JSON.toJSONString(openTime));

    }

    public static void maissn1(String[] args) {

        BigDecimal bigDecimal = new BigDecimal("0.0006");
        System.out.println(bigDecimal.multiply(new BigDecimal(2660000)));
    }

    public static void mainssss(String[] args) {
        Random random = new Random();

        Map<Integer, Map<Long, Long>> ab = new HashMap<>();
        for (int i = 0; i < 3; i++) {
            Map<Long, Long> longLongMap = new HashMap<>();
            for (int j = 0; j < 5; j++) {
                longLongMap.put( random.nextLong(), random.nextLong());
            }
            ab.put(i + 1, longLongMap);
        }
        System.out.println(JSON.toJSONString(ab));

    }

    public static void main(String[] args) {
        String abc = "{\"template\":3,\"playerTaskId\":30209,\"playerPackageId\":500,\"playerNormalTaskId\":30208," +
                "\"playerTotalAwardCount\":200000,\"anchorTaskId\":30210,\"anchorPackageId\":507," +
                "\"anchorTotalAwardCount\":200000,\"checkMobileFlag\":0,\"strickCheckMobile\":0," +

                "\"preTime\":60,\"fightTime\":600,\"showResultTime\":60,\"busiId\":500}";
        NianshouComponentAttr nianshouInfo = JSON.parseObject(abc, NianshouComponentAttr.class);
        System.out.println(nianshouInfo);
    }

}
