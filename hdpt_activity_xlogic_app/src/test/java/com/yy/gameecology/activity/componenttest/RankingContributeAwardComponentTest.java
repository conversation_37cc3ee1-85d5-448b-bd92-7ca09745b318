package com.yy.gameecology.activity.componenttest;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.hdzj.element.component.RankingContributeAwardComponent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-12-07 17:15
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {"group=2"})
@TestPropertySource(value = {
        "classpath:env/local/application.properties",
        "classpath:env/local/application-inner.properties",
        "classpath:env/local/group-setting-2.properties"
})
public class RankingContributeAwardComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "2");
    }

    @Autowired
    private RankingContributeAwardComponent rankingContributeAwardComponent;

    @Test
    public void test() {
        String event = "{\"actId\":2023112001,\"ekey\":\"[1006]PhaseTimeEnd#2023112001:10:10:ALL\",\"endTime\":\"2023-12-29 23:59:59\",\"index\":1,\"phaseId\":10,\"rankId\":10,\"seq\":\"b8ae42af-7aa0-4a35-bada-2157a13561e1\",\"timeKey\":0,\"timestamp\":\"2023-12-30 00:00:05\",\"uri\":1006}";
        PhaseTimeEnd eventObject = JSON.parseObject(event, PhaseTimeEnd.class);
        rankingContributeAwardComponent.onPhaseTimeEnd(eventObject, rankingContributeAwardComponent.getComponentAttr(2023112001L, 810));
    }
}
