package com.yy.gameecology.activity.service.gensql;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.common.bean.ChannelBaseInfo;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.*;
import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-10-10 21:46
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties"})
public class GenEnrollmentSqlTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private final Map<String, String> nameRoleMap = Maps.newHashMap();




    @Before
    public void before(){
        nameRoleMap.put("交友公会", "50004");
        nameRoleMap.put("约战公会", "60004");
        nameRoleMap.put("宝贝公会", "40004");
        nameRoleMap.put("陪玩公会", "90004");
        nameRoleMap.put("陪玩广播", "99999");


        nameRoleMap.put("宝贝主播", "40001");
        nameRoleMap.put("约战主播", "60001");

        nameRoleMap.put("交友男主持", "50013");
        nameRoleMap.put("交友女主持", "50015");
        nameRoleMap.put("交友女视频", "50011");
        nameRoleMap.put("交友女声音", "50012");

        nameRoleMap.put("交友天团", "50014");




    }

    @Autowired
    private CommonService commonService;

    @Test
    public void startGenSql() {
        //交友 约战 宝贝
        genChannelSql("2021053001");
//        genAnchorSql("2021053001");

        //陪玩
        //genChannelSql("2021054001");
    }


    /**
     * input:
     * 交友	29903812
     * 约战	25986929
     * 宝贝	94012517
     * 陪玩
     */
    public void genChannelSql(String actId) {


        File fileSource = new File("D:\\tmp\\" + actId + "_input_channel.txt");

        String sqlTmp = "insert into ranking_enrollment(act_id,member_id,src_role_id,dest_role_id,status,sign_sid,sign_asid,remark)" +
                "  values({actId},{member_id},{src_role_id},{dest_role_id},1,{sign_sid},{sign_asid},'{remark}');";

        try {
            InputStreamReader reader = new InputStreamReader(new FileInputStream(fileSource));
            BufferedReader br = new BufferedReader(reader);
            String line = br.readLine();
            StringBuilder sqlOutPut = new StringBuilder();

            Map<String, ChannelBaseInfo> channelBaseInfoMap = Maps.newHashMap();

            while (line != null) {
                String[] content = line.split("\t");

                line = br.readLine();


                String roleName = content[0].trim();
                String sid = content[1].trim();

                ChannelBaseInfo channelBaseInfo = channelBaseInfoMap.get(sid);
                if (channelBaseInfo == null) {
                    channelBaseInfo = commonService.getChannelInfo(Convert.toLong(sid), false);
                }
                if (channelBaseInfo != null) {
                    channelBaseInfoMap.put(sid, channelBaseInfo);
                }

                if (channelBaseInfo == null) {
                    sqlOutPut.append("sid is not found,sid:" + sid);
                    sqlOutPut.append("\r\n");
                    continue;
                }

                String roleId = nameRoleMap.get(roleName);
                if (StringUtil.isBlank(roleId)) {
                    throw new Exception("role name error:" + roleName);
                }


                String sql = sqlTmp.replace("{member_id}", sid)
                        .replace("{src_role_id}", roleId)
                        .replace("{dest_role_id}", roleId)
                        .replace("{sign_sid}", sid)
                        .replace("{sign_asid}", channelBaseInfo.getAsid() == 0 ? sid : channelBaseInfo.getAsid() + "")
                        .replace("{remark}", roleName).replace("{actId}", actId);

                sqlOutPut.append(sql);
                sqlOutPut.append("\r\n");
            }


            //写入文件
            File writename = new File("D:\\tmp\\" + actId + "_output_channel.sql");
            // 创建新文件
            writename.createNewFile();
            BufferedWriter out = new BufferedWriter(new FileWriter(writename));
            out.write(sqlOutPut.toString());


            reader.close();
            out.flush();
            out.close();


        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }


    /**
     * 分组   uid     signSid
     * 约战	2277372187	31106915
     */
    public void genAnchorSql(String actId) {


        File fileSource = new File("D:\\tmp\\" + actId + "_input_anchor.txt");

        String sqlTmp = "insert into ranking_enrollment(act_id,member_id,src_role_id,dest_role_id,status,sign_sid,sign_asid,remark)" +
                "  values({actId},{member_id},{src_role_id},{dest_role_id},1,{sign_sid},{sign_asid},'{remark}');";

        try {
            InputStreamReader reader = new InputStreamReader(new FileInputStream(fileSource));
            BufferedReader br = new BufferedReader(reader);
            String line = br.readLine();
            StringBuilder sqlOutPut = new StringBuilder();

            Map<String, ChannelBaseInfo> channelBaseInfoMap = Maps.newHashMap();

            while (line != null) {
                String[] content = line.split("\t");

                line = br.readLine();

                if (content.length < 3) {
                    log.error("genAnchorSql error,contentlen:{},content:{}", content.length, line);
                    continue;
                }

                String roleName = content[0].trim();
                String sid = content[1].trim();
                String uid = content[2].trim();


                String memberId = uid;

                ChannelBaseInfo channelBaseInfo = channelBaseInfoMap.get(sid);
                if (channelBaseInfo == null) {
                    channelBaseInfo = commonService.getChannelInfo(Convert.toLong(sid), false);
                }
                if (channelBaseInfo != null) {
                    channelBaseInfoMap.put(sid, channelBaseInfo);
                }

                if (channelBaseInfo == null) {
                    sqlOutPut.append("sid is not found,sid:" + sid);
                    sqlOutPut.append("\r\n");
                    continue;
                }

                String roleId = nameRoleMap.get(roleName);
                if (StringUtil.isBlank(roleId)) {
                    throw new Exception("role name error:" + roleName);
                }


                String sql = sqlTmp.replace("{member_id}", memberId)
                        .replace("{src_role_id}", roleId)
                        .replace("{dest_role_id}", roleId)
                        .replace("{sign_sid}", sid)
                        .replace("{sign_asid}", channelBaseInfo.getAsid() == 0 ? sid : channelBaseInfo.getAsid() + "")
                        .replace("{remark}", roleName + "-主播").replace("{actId}", actId);

                sqlOutPut.append(sql);
                sqlOutPut.append("\r\n");
            }


            //写入文件
            File writename = new File("D:\\tmp\\" + actId + "_output_anchor.sql");
            // 创建新文件
            writename.createNewFile();
            BufferedWriter out = new BufferedWriter(new FileWriter(writename));
            out.write(sqlOutPut.toString());


            reader.close();
            out.flush();
            out.close();


        } catch (Exception e) {
            log.error("genAnchorSql error,e:{}", e.getMessage(), e);
        }
    }


}
