package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.Main;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-10-21 15:50
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource("classpath:env/local/application.properties")
public class NdGrActServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    //@Autowired
   // private ActLayerInfoService ndGrActLayerInfoService;

    @Autowired
    private CommonService commonService;

    private static final long actId = 202012001;

    @Test
    public void buildLayerInfoTest() {


        //
        Date now = commonService.getNow(actId);
        //now = DateUtil.getDate("2020-12-15 00:00:06");
        //yy://pd-[sid=1452390116&subid=2761400957]

        //龙龙
        //LayerBroadcastInfo layerBroadcastInfo = ndGrActLayerInfoService.buildLayerInfo(actId, 63649476L, 2698856910L, now);


        //LayerBroadcastInfo layerBroadcastInfo = ndGrActLayerInfoService.buildLayerInfo(actId, 33470162L, 33470162L, now);

        //yy://pd-[sid=33470162&subid=33470162]
        // 罗静 约战 yy://pd-[sid=67071592&subid=67071592]
        //LayerBroadcastInfo layerBroadcastInfo = ndGrActLayerInfoService.buildLayerInfo(actId, 67071592L, 67071592L, now);

        //文帜
        //LayerBroadcastInfo layerBroadcastInfo = ndGrActLayerInfoService.buildLayerInfo(actId, 1452390116L, 2761400957L, now);


    }

    @Test
    public void buildPwLayerInfoTest() {


        // Date now = DateUtil.getDate("2020-11-09 17:00:00");
        Date now = commonService.getNow(202012002);
//            now = DateUtil.getDate("2020-12-02 00:10:00");
        //yy://pd-[sid=1452390116&subid=2761400957]
        //LayerBroadcastInfo layerBroadcastInfo = ndGrActLayerInfoService.buildLayerInfo(202012002L, 1451425009L, 0L, now);


    }
}
