package com.yy.gameecology.activity.service;

import com.yy.gameecology.common.utils.Clock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Vector;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-08-04 15:49
 **/
public class ThreadTest {
    private static final Logger log = LoggerFactory.getLogger(ThreadTest.class);

    public static void main(String[] args) {
        ExecutorService executorService = Executors.newFixedThreadPool(10);

        Clock clock = new Clock();

        try {
            throw new RuntimeException("test");
        } catch (Exception e) {
            log.error("test:1{}" , e);
            log.error("test:2{}" , e.toString());
            log.error("test3{}" + e.getMessage());
        }

        Vector<Long> tmp = new Vector<>();
        for (int i = 0; i < 100; i++) {
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        Thread.sleep(50);
                    } catch (Exception e) {
                    }
                    tmp.add(System.currentTimeMillis());
                }
            });
        }

        for (int i = 0; i < 10; i++) {
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        Thread.sleep(1000);
                    } catch (Exception e) {
                    }
                    tmp.add(System.currentTimeMillis());
                }
            });
        }
        System.out.print("=========1cost:" + clock.tag() + "tmpsize:" + tmp.size());

        executorService.shutdown();

        try {
            executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
        } catch (InterruptedException e) {
        }


        System.out.print("=========2cost:" + clock.tag() + "tmpsize:" + tmp.size());

        System.out.print("=========end:" + clock.tag() + "tmpsize:" + tmp.size());

    }
}
