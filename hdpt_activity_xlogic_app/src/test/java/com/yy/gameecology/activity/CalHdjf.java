package com.yy.gameecology.activity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2022-10-19 17:54
 **/
public class CalHdjf {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public static void main(String[] args) throws Exception {
//        List<String> contents = FileUtils.readLines(new File("F:\\project\\20220913交友强厅赛场\\厅补数据.txt"), "utf-8");
        List<String> contents = FileUtils.readLines(new File("F:\\project\\20220913交友强厅赛场\\厅补数据2.txt"), "utf-8");

        List<String> contasMembers = Lists.newArrayList("1462361597_2786122683", "75777786_2724676020", "1353102597_2807115750", "4378478_2774208370"
                , "4378478_2620263068", "75777786_2466778726", "1353102597_2799963953");

        Map<String, Long> memberScore = Maps.newHashMap();
        for (String content : contents) {
            JSONObject item = JSON.parseObject(content);
            long sid = item.getLong("sid");
            long ssid = item.getLong("ssid");
            String memberId = sid + "_" + ssid;

            if (!contasMembers.contains(memberId)) {
                continue;
            }

            JSONObject giftItem = item.getJSONArray("giftList").getJSONObject(0);

            int GiftId = giftItem.getIntValue("GiftId");
            int GiftCount = giftItem.getIntValue("GiftCount");
            int score = 0;
            switch (GiftId) {
                //0.1
                case 20433:
                    score = GiftCount * 10;
                    break;
                //20434 520
                case 20434:
                    score = GiftCount * 520 * 100;
                    break;
                //免费礼物
                case 20435:
                    score = GiftCount * 10;
                    break;
                //粉丝票
                case 20418:
                    score = GiftCount * 5;
                    break;

            }

            long itemScore = memberScore.getOrDefault(memberId, 0L);
            itemScore = itemScore + score;
            memberScore.put(memberId, itemScore);
        }

        System.out.println(JSON.toJSONString(memberScore));
    }
}
