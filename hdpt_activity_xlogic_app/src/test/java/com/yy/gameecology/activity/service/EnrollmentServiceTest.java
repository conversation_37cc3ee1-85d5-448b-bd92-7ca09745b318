package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.Main;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-12-18 18:34
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-2.properties"})
public class EnrollmentServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private EnrollmentService enrollmentService;


    @Test
    public void upateTest() {

        int loopTimes = 10;
        for (int i = 0; i <= loopTimes; i++) {
            enrollmentService.updateEnrollSignSid(2020122001);
        }

    }
}
