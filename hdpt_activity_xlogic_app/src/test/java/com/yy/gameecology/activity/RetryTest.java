package com.yy.gameecology.activity;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;


/**
 * <AUTHOR>
 * @date 2021.06.21 16:34
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-3.properties"})
public class RetryTest {





    @Test
    public void manualRetryTest() throws InterruptedException {

    }

    @Test
    public void deletePastDueLogTest() throws InterruptedException {

    }

}
