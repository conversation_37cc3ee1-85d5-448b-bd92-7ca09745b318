package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.Main;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-29 12:17
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource("classpath:env/local/application.properties")
public class OnlineChannelServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private OnlineChannelService onlineChannelService;

    @Test
    public void test(){
        onlineChannelService.update2memory();
    }
}
