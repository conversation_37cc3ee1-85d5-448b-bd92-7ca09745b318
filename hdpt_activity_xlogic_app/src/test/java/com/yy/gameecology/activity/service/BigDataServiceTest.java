package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.bigdata.ActivityReport;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties","classpath:env/local/group-setting-${group}.properties"})
public class BigDataServiceTest {
    @Autowired
    private BigDataService bigDataService;

    @Test
    public void testLog() {
        for(int i=0; i<10000;) {
            ActivityReport data = new ActivityReport();
            data.setSeq(String.valueOf((long)i++));
            data.setActId(1L);
            data.setRankId(1);
            data.setBusiId(1);
            data.setReportType(1);
            data.setActor(i + "-s1-" + DateUtil.today("yyyyMMddHH"));
            data.setActorType(1);
            data.setScore(100L);
            data.setScoreType(1);
            data.setBusiTime(1000L);
            data.setReportTime(1000L);
            bigDataService.saveDataToFile(data);

            data.setSeq(String.valueOf((long)i++));
            data.setActor(i + "-s2-" + DateUtil.today("yyyyMMddHH"));
            data.setBusiTime(2000L);
            data.setReportTime(2000L);
            bigDataService.saveDataToFile(data);

            data.setSeq(String.valueOf((long)i++));
            data.setActor(i + "-s3-" + DateUtil.today("yyyyMMddHH"));
            data.setBusiTime(3000L);
            data.setReportTime(3000L);
            bigDataService.saveDataToFile(data);
            SysEvHelper.waiting(1000);
        }
    }
}
