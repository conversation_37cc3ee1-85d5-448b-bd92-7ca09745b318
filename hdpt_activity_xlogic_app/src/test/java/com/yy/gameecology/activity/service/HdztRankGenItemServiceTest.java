package com.yy.gameecology.activity.service;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.RankItem;
import com.yy.gameecology.activity.bean.rank.GuildRankItem;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.thrift.hdztranking.RankingInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-08-25 16:23
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource("classpath:env/local/application.properties")
public class HdztRankGenItemServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztRankGenItemService hdztRankGenItemService;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Test
    public void genGuildRankItemTest() {
        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(202008002L, 5);
        List<RankItem> rankBaseItems = Lists.newArrayList();
        RankItem item = new RankItem();
        item.setGroupId("90041");
        item.setMember("24183992");
        rankBaseItems.add(item);
        List<GuildRankItem> guildRankItems = hdztRankGenItemService.genGuildRankItems(rankingInfo, rankBaseItems);


    }

}
